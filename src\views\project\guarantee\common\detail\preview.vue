<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON>qiang
 * @Date: 2020-08-06 18:47:13
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-10-19 15:23:09
-->
<template>
  <el-dialog class="CAmodel" :visible.sync="visible" title="提示" :before-close='beforeClose' :fullscreen="fullscreen" :close-on-click-modal="false" :close-on-press-escape="false" width="850">
    <div slot='title'>
      <h3>{{name}}预览</h3>
      <!-- <button @click="page()"></button> -->
      <el-button @click="fullscreen=!fullscreen" class="el-dialog__headerbtn diaFull" type="text"><i class="el-icon-full-screen"></i></el-button>
    </div>
    <div class="min550">
      <iframe  :src="url" id="iframe"  class="min550" style="width:100%;height:100%;border:none;"></iframe>
    </div>
  </el-dialog>
</template>
<script>
import { getUUID } from '@/utils'
export default {
  data () {
    return {
      fullscreen: false,
      visible: false,
      id: '',
      url: '',
      name: '',
      fileViewUrl: '',
      apiURL: ''
    }
  },
  mounted () {
  },

  methods: {
    init () {
      this.visible = true
      this.fullscreen = false
      this.url = ''
      this.fileViewUrl = window.SITE_CONFIG['fileView']
      this.apiURL = window.SITE_CONFIG['apiURL']
      this.$nextTick(() => {
        this.url = this.fileViewUrl + encodeURIComponent(this.apiURL + '/sys/oss/minioPreview?id=' + this.id + '&uuid=' + getUUID() + `&fullfilename=file${new Date().getTime()}.pdf` + '&_t=' + new Date().getTime())
      })
    },
    beforeClose (done) {
      this.visible = false
      this.$emit('refresh')
      done()
    }
  }
}

</script>
<style lang="scss" scoped>
  .CAmodel .el-dialog__body{
    max-height: none;
    padding: 5px 20px;
    min-height: 550px;
    height: calc(100% - 81px);
  }
  .min550{
    min-height: 550px;
    height: 100%;
  }

</style>
