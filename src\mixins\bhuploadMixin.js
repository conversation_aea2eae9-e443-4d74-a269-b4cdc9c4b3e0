/*
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-10-31 09:16:54
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-10-31 10:40:55
 */
import Cookies from 'js-cookie'
export default {
  data () {
    return {
      uploadUrl: '',
      upData: {},
      fileList: [],
      myHeaders: {
        token: Cookies.get('token') || ''
      }
    }
  },
  created () {
    this.uploadUrl = window.SITE_CONFIG['apiURL'] + '/letter/guarantee/bhFileupload'
  },
  methods: {
    // 公用获取文件
    async getBhFile (type) {
      let { data } = await this.$http.get(`letter/guarantee/getBhFile?letterId=${this.letterId}&type=${type}`)
      if (data.data) {
        // this.$emit('businessLicenseId', data.data.id)
        this.fileList = [{ name: '说明文件', id: data.data.id, suffix: this.filterType(data.data.url), type: type }]
      }
    },
    handleRemove (file, fileList) {
      if (file && file.status === 'success') {
        // 移除方法
        var deleteId = file.id ? file.id : file.response.data.ossId
        return this.deleteFileCQ(deleteId, 9)
      }
    },
    BeforeRemove (file, fileList) {
      console.log(file, fileList)
      let a = true
      if (file && file.status === 'success') {
        a = this.$confirm(`确定移除 ${file.name}？`)
      }
      return a
    },
    SuccessHandle (res, file, fileList) {
      if (res.data.flag !== true) {
        this.fileList = []
        return this.$message.error(res.data.msg)
      }
      this.$message.success('上传成功')
    },
    beforeUpload (file) {
      var extension = file.name.substring(file.name.lastIndexOf('.') + 1)
      var lastName = extension !== 'jpg' &&
        extension !== 'JPG' &&
        extension !== 'jpeg' &&
        extension !== 'JPEG' &&
        extension !== 'png' &&
        extension !== 'PNG' &&
        extension !== 'pdf' &&
        extension !== 'PDF' &&
        extension !== 'zip'
      if (
        lastName
      ) {
        this.$message.warning('文件要求格式为：jpg,jpeg,png,pdf,zip')
      }

      return !lastName
    },
    onPreview (file) {
      if (file.response) {
        window.open(`${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${file.response.data.ossId}`)
      } else {
        window.open(`${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${file.id}`)
      }
    },
    handleExceed (files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件`
      )
    },
    deleteFile (id, type) {
      return new Promise(async (resolve, reject) => {
        let { data } = await this.$http.get('letter/guarantee/deleteBhFile?letterId=' + id + '&type=' + type)
        if (data.code !== 0) {
          this.$message.error(data.msg)
          resolve(false)
        }
        // type === 1 ? this.$emit('biddingDocumentId', '') : this.$emit('businessLicenseId', '')
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
        resolve(true)
      })
    }
  }
}
