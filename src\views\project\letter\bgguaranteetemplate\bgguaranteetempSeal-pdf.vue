<template>
  <div>
    <div style="padding: 3px;">
        <div style="padding: 3px; border-color: grey">
          <object ref="webPDFObj" classid="CLSID:6EE0E832-A96C-480C-AE0A-EB35A9BB9651" width="100%" style="height:500px;float: left" >
          </object>
        </div>
        <el-button type="primary" @click="submitHandle()">{{ $t('confirm') }}</el-button>
    </div>

  </div>
</template>
<script>
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      id: '',
      webPDFObj: '',
      src: `${window.SITE_CONFIG['apiURL']}` + '/letter/bgguaranteetemplate/signPDF2/',
      savesrc: `${window.SITE_CONFIG['apiURL']}` + '/letter/bgguaranteetemplate/savePDF2/',
      height: '900px'
    }
  },
  created () {
    this.id = this.$route.params.tid
  },
  mounted () {
    this.init()
  },
  methods: {
    init () {
      try {
        this.$nextTick(() => {
          this.webPDFObj = this.$refs['webPDFObj']
          this.webPDFObj.OpenNetFile(this.src + this.id)
          this.webPDFObj.ShowToolbar(true)
        })
      } catch (e) {
        // alert(e);
      }
    },

    ShowExceptionError (e) {
      if (e.name === 'TypeError') {
        alert('JS错误：' + e.message)
      } else {
        try {
          alert('WebPDF错误：' + this.webPDFObj.GetErrMsg())
        } catch (ee) {
          alert('JS内部错误：' + ee.message)
        }
      }
    },
    submitHandle () {
      try {
        // this.webPDFObj.SealKeyWord('法定代表人', 1)
        var result = this.webPDFObj.SaveNetFile(this.savesrc + this.id)
        if (result === null || result === '') {
          this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
            item => item.name !== 'signPdf2'
          )
          this.$router.push({
            name: `bgguaranteeletterSqf`
          })
        } else {
          alert(result)
        }
      } catch (e) {
        this.ShowExceptionError(e)
        return false
      }
      return true
    },
    changePdfPage (val) {
      console.log(val)
      if (val === 0 && this.currentPage > 1) {
        this.currentPage--
        console.log(this.currentPage)
      }
      if (val === 1 && this.currentPage < this.pageCount) {
        this.currentPage++
        console.log(this.currentPage)
      }
    },
    loadPdfHandler (e) {
      this.currentPage = 1
    }
  }
}
</script>
