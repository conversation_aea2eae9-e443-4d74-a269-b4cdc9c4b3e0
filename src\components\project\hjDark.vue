<template>

  <div
    class="dark"
    :style="dark?'display:block;opcity: 0.9;':'display:none;opcity: 0;'"
  >
    <div v-if="detailed" class="detailed" style="padding:20px;">
      <span>抽取库总数:{{detailed.cqzs}}&emsp; 抽取方式:{{ detailed.scType | scTypeFilter }}&emsp; 应抽取数:{{detailed.ycqs}}</span>

    </div>
    <div class='loading'>
      <p
        class="daoJiShi"
        v-if="daoJiShi"
      >{{ timeOut }}</p>
      <div
        class='logo'
        ref='arrRandom'
        v-if="!daoJiShi"
      >抽取数据中</div>

    </div>
  </div>

</template>
<script>
export default {
  data () {
    return {
    //   dark: true,
      timeOut: 4, // init倒计时设置
      //   nameArr: []
      daoJiShi: true,
      stopTime: false

    }
  },
  props: {
    dark: {
      type: Boolean,
      default: false
    },
    detailed: {
      type: Object
    },
    nameArr: {
      type: Array
    }

  },
  filters: {
    scTypeFilter (scType) {
      const scTypeMap = {
        1: '按比例抽取',
        2: '按数目抽取'
      }
      return scTypeMap[scType]
    }
  },
  mounted () {
    // // // console.log(this.dark)
  },
  destroyed () {
    // // console.log(1)
    this.stopTime = true
  },
  activated () {

  },
  watch: {
    dark (newVal, oldVal) {
      this.timeRun()
    }
  },
  methods: {

    timeRun () { // 请求倒计时
      if (this.timeOut !== 0) {
        this.daoJiShi = true
        this.timeOut--
        setTimeout(() => {
          this.timeRun()
        }, 1000)
      } else {
        this.daoJiShi = false
        this.intervalArr()
        this.timeOut = 4
      }
    },
    intervalArr () {
      // clearInterval(timer)
      if (!this.daoJiShi && this.dark) {
        if (!this.stopTime) {
          setTimeout(() => {
            this.changeArr()
            this.intervalArr()
          }, 100)
        }
      }
    },
    changeArr () {
      if (this.nameArr.length > 0) {
        var randomVal = Math.ceil(Math.random() * this.nameArr.length - 1)
        var prizeName = this.nameArr[randomVal]
        // // // console.log(this.$refs.arrRandom)
        // this.$ne
        if (this.$refs.arrRandom !== undefined) {
          this.$refs.arrRandom.innerHTML = prizeName
        } else {
          this.stopTime = true
        }
      }
    }
  }
}
</script>
<style lang="scss">
@import "@/assets/scss/colorBg.scss";
.cj_box {
  /* background-image: url(~@/assets/img/1.png);*/
  background-size: cover;
  text-align: center;
  height: auto;
  width: 100%;
  display: block;
  margin: 10px auto;
  position: relative;
}
.operation {
  span {
    display: inline-block;
    width: 80px;
    color: #222;
  }
}

#timer {
  color: white;
}

.daoJiShi {
  position: fixed;
  top: 50%;
  left: 50%;
  font-size: 150px;
  height: 150px;
  color: white;
  animation: flipOutY 1s ease both infinite;
  -webkit-animation: flipOutY 1s ease both infinite;
  -moz-animation: flipOutY 1s ease both infinite;
}

.arrRandom {
  text-align: center;
  margin-top: 20vh;
  font-weight: bold;
  font-size: 50px;
}
.detailed{
        color: white;
    background: rgba(0,0,0,0.2);
    font-size: 18px;
    font-weight: bold;
    text-align: center;
}
</style>
