<template>
  <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="165px">
    <el-row :gutter="20">
      <el-col :xs="24" :lg="24">
        <div class="temp-subtitle">基本信息</div>
        <div class="formCon">
          <el-col :xs="24" :lg="24 ">
            <el-form-item label="出具方">
              {{$route.params.insuranceName}}
            </el-form-item>
          </el-col>
          <el-col :xs="24" :lg="24 ">
            <el-form-item label="项目标段名称" prop="projectName">
              <el-input v-model.trim="dataForm.projectName" @keyup.native="trimLR('projectName')" size="small" class="wd180" style="width:100%;" placeholder="请输入项目标段名称"></el-input>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :lg="24 ">
            <el-form-item label="申请方名称" prop="applyName">
              <el-input v-model.trim="dataForm.applyName" size="small" class="wd180" style="width:100%;" placeholder="请输入申请方名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :lg="24 ">
            <el-form-item label="被保人（招标方）名称" prop="bbrName">
              <el-input v-model.trim="dataForm.bbrName" size="small" class="wd180" style="width:100%;" placeholder="请输入被保人（招标方）名称"></el-input>
            </el-form-item>
          </el-col>

          <!-- <el-col :xs="24" :lg="24 ">
            <el-form-item label="使用方名称" prop="platformCode">
              <el-select v-model="dataForm.platformCode" disabled placeholder="请选择" size="small" style="width:100%;" class="wd240">
                <el-option v-for="item in platformOptions" :key="item.code" :label="item.name" :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :xs="24" :lg="24 ">
            <el-form-item label="担保金额" prop="guaranteeAmount">
              <el-input-number placeholder="请输入担保金额" @change='change' size="small" style='width:180px;' v-model="guaranteeAmount" controls-position="right" :precision="6" :step="0.000001" :min="0"
                :max="1000"> <template slot="append">万元</template></el-input-number> 万元
              <el-tooltip content="建设类担保金额最大80万元，交通类担保金额最大1000万元。" placement="bottom">
                <i class="el-icon-question" style="font-size:16px;color:#409EFF;"></i>
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :lg="24 ">
            <el-form-item label="经办人名称" prop="name">
              <el-input v-model.trim="dataForm.name" size="small" class="wd180" style="width:100%;" placeholder="请输入经办人名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :lg="24 ">
            <el-form-item label="经办人手机号" prop="mobile">
              <el-input v-model.trim="dataForm.mobile" size="small" class="wd180" style="width:100%;" placeholder="请输入经办人手机号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :lg="24 ">
            <el-form-item label="联系服务专员">
              <span>杜晓敏，17736060503</span>
            </el-form-item>
          </el-col>
        </div>
      </el-col>
    </el-row>
  </el-form>
</template>
<script>
export default {
  data () {
    return {
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() < Date.now()
        }
      },
      guaranteeAmount: '',
      dataForm: {
        name: '',
        mobile: '',
        guaranteeType: this.$route.params.type,
        issueCode: this.$route.params.insuranceCode,
        projectName: '',
        applyName: '',
        platformCode: 'zbt',
        bbrName: '',
        issueName: '',
        platformName: '招标通',
        guaranteeAmount: ''
      },
      platformOptions: []
    }
  },
  props: {
    options: {
      type: Object
    }
  },
  created () {
    this.getplatformList()
  },
  computed: {
    dataRule () {
      var checkPhone = (rule, value, callback) => {
        if (!value) {
          return callback(new Error('手机号不能为空'))
        } else {
          const reg = /^1[3|4|5|7|8|9][0-9]\d{8}$/
          console.log(reg.test(value))
          if (reg.test(value)) {
            callback()
          } else {
            return callback(new Error('请输入正确的手机号'))
          }
        }
      }
      var checkGuaranteeAmount = (rule, value, callback) => {
        if (!value) {
          return callback(new Error('担保金额必须大于0元'))
        }
        if (!Number.isInteger(value)) {
          callback(new Error('请输入数字值'))
        } else {
          if (value < 0) {
            callback(new Error('担保金额必须大于0元'))
          } else {
            callback()
          }
        }
      }
      return {
        name: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        guaranteeAmount: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          },
          { validator: checkGuaranteeAmount, trigger: 'blur' }
        ],
        platformCode: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        projectName: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        applyName: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        bbrName: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        mobile: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          },
          { validator: checkPhone, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    trimLR (val) {
      this.dataForm[val] = this.dataForm[val].replace(/\s+/g, '')
    },
    getplatformList () {
      this.$http
        .get('/letter/bgguaranteeplatform/platformList')
        .then(({ data: res }) => {
          this.platformOptions = res.data.list
          this.dataForm.platformCode = 'zbt'
          this.dataForm.platformName = '招标通'
          // console.log(platformOptions)
        })
        .catch(() => {})
    },
    change (val) {
      this.$set(this.dataForm, 'guaranteeAmount', val * 10000)
      // this.dataForm.guaranteeAmount = val * 10000
      // console.log(this.dataForm.guaranteeAmount)
    },
    push () {
      return new Promise((resolve, reject) => {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
</style>
