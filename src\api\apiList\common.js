/*
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-24 16:32:06
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-06-24 16:43:15
 */
import http from '@/utils/request'
/**
 * @name: getpakeage
 * @msg: 获取保函配置
 * @param {*} params
 * @return {*}
 */
export const getpakeage = (params) => {
  return http
    .get(`/letter/bgissueconfigure/getInfoByIssueCode`, {
      params: params
    })
}
