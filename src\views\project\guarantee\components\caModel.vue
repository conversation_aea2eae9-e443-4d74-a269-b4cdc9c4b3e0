<!--
 * @Descripttion:
 * @Author: kong<PERSON>qiang
 * @Date: 2020-08-06 18:47:13
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2022-01-20 10:43:41
-->
<template>
  <el-dialog class="CAmodel" :visible.sync="visible" title="提示" append-to-body :before-close='beforeClose' :fullscreen="fullscreen" :close-on-click-modal="false" :close-on-press-escape="false" width="1200px">
    <div slot='title'>
      <h3>签章</h3>
      <!-- {{type}} -->
      <el-button @click="fullscreen=!fullscreen" class="el-dialog__headerbtn diaFull" type="text"><i class="el-icon-full-screen"></i></el-button>
    </div>
    <div class="min550">
      <iframe v-if="dataForm" :src="src+'ca.html?_t='+type" :data-letterid='JSON.stringify(dataForm.letterId)' :data-type='JSON.stringify(type)' :data-issuecode='JSON.stringify(dataForm.key)' :data-guaranteetype='JSON.stringify(dataForm.guaranteeType)' id="caModel" class="min550" style="width:100%;height:100%;border:none;"></iframe>
    </div>
  </el-dialog>
</template>
<script>
export default {
  data () {
    return {
      fullscreen: false,
      visible: false,
      guaranteeApi: '',
      dtoName: '',
      type: '',
      dataForm: {},
      webPDFObj: '',
      businessLicenseId: '',
      biddingDocumentId: '',
      src: ''
    }
  },
  mounted () {
    this.src = process.env.BASE_URL
  },
  watch: {
    dataForm: {
      handler (a) {
        this.dataForm = a
      },
      deep: true
    },
    type (a) {
      this.type = a
    },
    guaranteeApi (a) {
      this.guaranteeApi = a
    },
    dtoName (a) {
      this.dtoName = a
    }
  },
  methods: {
    init () {
      this.visible = true
      this.fullscreen = false
      this.$nextTick(() => {
        // this.getApi()
      })
    },
    getApi () {
      console.log(this.$route.params)
      if (this.$route.params.type === 'tbdbbh') {
        this.guaranteeApi = '/letter/guarantee/generateGuaranteeByTable'
        this.dtoName = 'guaranteeDTO'
      }
      if (this.$route.params.type === 'tbbhyh') {
        this.guaranteeApi = '/letter/bank/generateSqs'
        this.dtoName = 'bankDTO'
      }
      console.log(this.guaranteeApi, this.dtoName)
    },
    beforeClose (done) {
      this.visible = false
      this.$emit('reFresh')
      done()
    }

  }
}
</script>
<style lang="scss" scoped>
  .CAmodel .el-dialog__body{
    max-height: none;
    padding: 5px 20px;
    min-height: 650px;
    height: calc(100% - 81px);
  }
  .min550{
    min-height: 650px;
    height: 100%;
  }

</style>
