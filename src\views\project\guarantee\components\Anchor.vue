<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON>ang
 * @Date: 2020-08-07 15:45:19
 * @LastEditors: kong<PERSON>qiang
 * @LastEditTime: 2022-01-20 08:51:38
-->

<template>
  <el-timeline :reverse="reverse" class="step" :class="isFix?'fixed':'abso'">
    <el-timeline-item v-for="(item, index) in activities" :key="index" :color="item.color">
      <div style="cursor:pointer;" :style="`color:${item.color}`" @click="getJump(item.index)">{{item.name}}</div>
    </el-timeline-item>
  </el-timeline>
</template>

<script>
export default {
  data () {
    return {
      reverse: false,
      index: 0,
      activities: [],
      isFix: false
    }
  },
  props: {
    isBottom: Boolean
  },
  mounted () {
    this.$nextTick(function () {
      window.addEventListener('scroll', this.linstener)
    })
  },
  watch: {
    isBottom (a) {
      if (a) {
        this.activeChange(this.activities.length - 1)
      }
    }
  },
  methods: {
    // toNode (i) {
    //   console.log(i)
    //   this.activeChange(i.index)
    //   document.documentElement.scrollTop = i.nodeH
    // },
    activeChange (index) {
      this.index = index
      this.activities[index].color = '#409EFF'
      this.activities
        .filter((a) => a.index !== index)
        .map((a) => {
          a.color = '#909399'
        })
    },
    linstener () {
      let scrolled =
        document.documentElement.scrollTop || document.body.scrollTop
      if (scrolled >= 118) {
        this.isFix = true
      } else {
        this.isFix = false
      }
      // console.log(this.activities[this.activities.length - 1])
      this.activities.map((a) => {
        if (scrolled >= a.nodeH) {
          this.activeChange(a.index)
        }
        if (this.isBottom) {
        }
        // console.log(this.activities[2].nodeH, scrolled)
        // if (this.index === 2 && this.activities[2].nodeH < scrolled) {
        //   this.activeChange(this.activities.length - 1)
        // }
      })
      // console.log(this.index, scrolled)
    },
    setJump () {
      let jump = window.document.querySelectorAll('.anchor')
      console.log(jump)
      Array.from(jump).map((item, index) => {
        this.activities.push({
          name: item.dataset.name,
          index: index,
          nodeH: item.offsetTop,
          offsetHeight: item.offsetHeight,
          color: index === 0 ? '#409EFF' : '#909399'
        })
      })
    },
    getJump (index) {
      this.activeChange(index)
      this.index = index
      let jump = window.document.querySelectorAll('.anchor')
      console.log(jump)
      // let arr = []

      let total = jump[index].offsetTop
      console.log(total)
      let distance =
        document.documentElement.scrollTop || document.body.scrollTop
      // 平滑滚动，时长500ms，每10ms一跳，共50跳
      let step = total / 50
      // 判断小导航距离顶部的位置
      let newDistance = this.NavDistance
      if (index === 0 && newDistance < total) {
        newDistance = 321
      } else if (index > 0 || index <= 6) {
        this.NavDistance = total
      } else {
        this.NavDistance = total - 600
      }

      if (total > distance) {
        smoothDown()
      } else {
        let newTotal = distance - total
        step = newTotal / 50
        smoothUp()
      }
      function smoothDown () {
        if (distance < total) {
          distance += step
          document.body.scrollTop = distance
          document.documentElement.scrollTop = distance
          setTimeout(smoothDown, 10)
        } else {
          document.body.scrollTop = total
          document.documentElement.scrollTop = total
        }
      }
      function smoothUp () {
        if (distance > total) {
          distance -= step
          document.body.scrollTop = distance
          document.documentElement.scrollTop = distance
          setTimeout(smoothUp, 10)
        } else {
          document.body.scrollTop = total
          document.documentElement.scrollTop = total
        }
      }
    }
  }
}
</script>
<style  scoped>
.step {
  background: white;
  padding: 10px;
  padding-top: 30px;
  right: 25px;
  transition: all 0.3s ease-in-out;
}
.fixed {
  position: fixed;
  top: 100px;
}
.abso {
  position: fixed;
  top: 205px;
}
</style>
