<template>
  <el-dialog :visible.sync="visible" :title="'绑定CA'" :close-on-click-modal="false" :close-on-press-escape="false" :append-to-body="true">
    <div>
      点击绑定CA来进行CA绑定
    </div>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="bindCA()">绑定CA</el-button>
    </template>
  </el-dialog>
</template>

<script>
// import debounce from 'lodash/debounce'
// import Cookies from 'js-cookie'
import { getUUID } from '@/utils'
import HebcaClient from '@/api/HebcaClient'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        uuid: '',
        signCert: '',
        random: '',
        signData: '',
        captcha: '',
        isBinding: ''
      }
    }
  },
  computed: {},
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
      })
    },
    bindCA () {
      this.dataForm.random = getUUID()
      var isIE = window.ActiveXObject !== undefined
      var hebcaClient = new HebcaClient()
      if (isIE) {
        /**
               *    身份认证流程第二步，登陆页面对后台产生的随机数进行签名
               *    并将签名结果及其证书提交到后台验证
               */

        try {
          var signCert = hebcaClient.GetSignCert()
          var source = this.dataForm.random
          var signData = hebcaClient.Sign(source)
          this.dataForm.signCert = signCert
          this.dataForm.signData = signData
        } catch (e) {
          return this.$message.error(
            '证书信息获取失败,' + e.getMessages()
          )
        }
      } else {
        hebcaClient.Sign(this.dataForm.random, function (signature) {
          this.dataForm.signData = signature
          hebcaClient.GetCertB64(function (cert) {
            this.dataForm.signCert = cert
          })
        })
      }
      if (
        this.dataForm.signData !== '' &&
              this.dataForm.random !== '' &&
              this.dataForm.signCert !== ''
      ) {
        this.dataForm.isBinding = 1
        this.$http
          .post('/caBinding', this.dataForm)
          .then(({ data: res }) => {
            if (res.code !== 0) {
              this.btnLoading = false
              this.visible = false
              return
            }
            this.$message({
              message: 'CA绑定成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
              }
            })
          })
          .catch(() => {})
      } else {
        return this.$message.error('证书信息获取失败请重新点击登录按钮')
      }
    }
  }
}
</script>
