<template>
  <el-dialog :visible.sync="visible" title="信息详情" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" v-loading='loading' ref="dataForm" :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="colConfig">
          <!-- <el-form-item label="收费编码" prop="code" label-width="190px">
            <span>{{dataForm.code}}</span>
          </el-form-item> -->
          <el-form-item label="收费名称" prop="name" label-width="190px">
            <span>{{dataForm.name}}</span>
          </el-form-item>
          <el-form-item label="出具机构" prop="issueId" label-width="190px">
            <span>{{dataForm.issueId}}</span>
          </el-form-item>
          <el-form-item label="出具机构编码" prop="issueCode" label-width="190px">
            <span>{{dataForm.issueCode}}</span>
          </el-form-item>
          <el-form-item label="出具机构名称" prop="issueName" label-width="190px">
            <span>{{dataForm.issueName}}</span>
          </el-form-item>
          <el-form-item label="保函类型" prop="guaranteeType" label-width="190px">
            <span>{{dataForm.guaranteeType}}</span>
          </el-form-item>
          <el-form-item label="担保金额区间" prop="guaranteeAmount" label-width="190px">
            <span>{{dataForm.guaranteeAmount}}</span>
          </el-form-item>
          <el-form-item label="状态" prop="status" label-width="190px">
            <span v-if="dataForm.status == 0">停用</span>
            <span v-if="dataForm.status == 1">正常</span>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :lg="colConfig" label-width="190px">
          <el-form-item label="收取费率" prop="percentValue" label-width="190px">
            <span>{{dataForm.percentValue}}</span>
          </el-form-item>
          <el-form-item label="保证金" prop="letterBond" label-width="190px">
            <span>{{dataForm.letterBond}}</span>
          </el-form-item>
          <el-form-item label="保函金额" prop="letterPrice" label-width="190px">
            <span>{{dataForm.letterPrice}}</span>
          </el-form-item>
          <el-form-item label="平台使用费" prop="platformPrice" label-width="190px">
            <span>{{dataForm.platformPrice}}</span>
          </el-form-item>
          <el-form-item label="收费类型" prop="priceType" label-width="190px">
            <span v-if="dataForm.priceType == 1">定额</span>
            <span v-if="dataForm.priceType == 2">按比例</span>
          </el-form-item>
          <el-form-item label="服务描述" prop="description" label-width="190px">
            <span>{{dataForm.description}}</span>
          </el-form-item>
          <!-- <el-form-item label="出具方编码" prop="platformCode" label-width="190px">
            <span>{{dataForm.platformCode}}</span>
          </el-form-item>
          <el-form-item label="排序" prop="orderCode" label-width="190px">
            <span>{{dataForm.orderCode}}</span>
          </el-form-item> -->

        </el-col>
      </el-row>
    </el-form>
    <template slot="footer">
      <el-button type="primary" @click="visible = false">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      colConfig: 2, // 列配置 1 2列
      visible: false,
      loading: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        issueId: '',
        issueCode: '',
        issueName: '',
        guaranteeType: '',
        guaranteeAmount: '',
        percentValue: '',
        letterBond: '',
        letterPrice: '',
        platformPrice: '',
        priceType: '',
        description: '',
        platformCode: '',
        orderCode: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.loading = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http
        .get(`/letter/bgguaranteeprice/${this.dataForm.id}`)
        .then(({ data: res }) => {
          this.loading = false

          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
        })
        .catch(() => {})
    }
  }
}
</script>
