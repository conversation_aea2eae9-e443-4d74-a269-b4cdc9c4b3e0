<template>
  <div class="site-wrapper site-page--login">
    <div class="site-content__wrapper">
      <div class="site-content">
        <!-- <el-button @click="getUrl"></el-button> -->
        <!-- <div class="brand-info">
                      <h2 class="brand-info__text">应用场景</h2>
                    <h3 class="brand-info__intro">各级政府采购办、各级公共资源管理中心、各级招标代理机构、各大保险机构</h3>
                    <h2 class="brand-info__text">标准化流程</h2>
                    <h3 class="brand-info__intro">保函申请→信息确认→保费支付→保函下发→缴纳保证金时使用</h3>
                    <h2 class="brand-info__text">流程简化   操作简单   管理规范</h2>
                 </div> -->
        <div class="login-main">
          <h2 class="login-brand">注册</h2>
          <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="100px">
            <input type="password" class="hide" id="loginPassword" />
            <input type="text" class="hide" id="loginUserName" />
            <el-form-item prop="username" :label="$t('user.username')">
              <el-input v-model="dataForm.username" size="small" :placeholder="$t('user.username')"></el-input>
            </el-form-item>

            <el-form-item prop="password" :label="$t('user.password')" :class="{ 'is-required': !dataForm.id }">
              <el-input v-model="dataForm.password" size="small" type="password" placeholder="请输入至少8位数字字母组合密码"></el-input>
            </el-form-item>
            <el-form-item prop="comfirmPassword" :label="$t('user.comfirmPassword')" :class="{ 'is-required': !dataForm.id }">
              <el-input v-model="dataForm.comfirmPassword" size="small" @change="checkpwd" type="password" :placeholder="$t('user.comfirmPassword')"></el-input>
            </el-form-item>
            <el-form-item prop="mobile" :label="$t('user.mobile')">
              <el-input v-model="dataForm.mobile" size="small" :placeholder="$t('user.mobile')">
              </el-input>
            </el-form-item>
            <el-form-item prop="smsyzm" label="验证码">
              <el-input v-model="dataForm.smsyzm" size="small" style="width:205px;margin-right:15px;" placeholder="手机验证码"></el-input>
              <el-button v-if="btnShow" type="primary" size="small" @click="sendMsg">发送短信</el-button>
              <el-button v-else type="primary" style="width:80px;" size="small" disabled>{{count}} s</el-button>
            </el-form-item>
            <el-form-item label="注册角色" prop="regist" clearable>
              <el-select v-model="dataForm.regist" clearable size="small" placeholder="请选择注册角色">
                <el-option v-for="item in regestoptions" :key="item.code" :label="item.name" :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="xieyi" class="role-list">
              <el-checkbox size="small" v-model="dataForm.xieyi"> </el-checkbox>
              <el-link type="primary" @click.stop="innerDrawer = true" :underline="false">《注册协议》</el-link>
            </el-form-item>

            <div prop="xieyi" style="text-align: center;" class="role-list">
              <el-button @click="toLogin">取消</el-button>
              <el-button type="primary" @click="dataFormSubmitHandle()" :loading="loading">{{ loading ? '提交中 ...' :$t('login.register') }}</el-button>
            </div>

            <el-dialog title="注册协议" :before-close="handleClose2" size='450px' :visible.sync="innerDrawer">
              <xieyi></xieyi>
              <div class="innerFoot">
                <el-button type="primary" @click="innerClose">同意</el-button>
              </div>

            </el-dialog>
          </el-form>
        </div>
      </div>
    </div>

  </div>
</template>
<script>
import debounce from 'lodash/debounce'
import { isMobile } from '@/utils/validate'
import { getUUID } from '@/utils'
import xieyi from './xieyi'
export default {
  data () {
    return {
      visible: false,
      innerDrawer: false,
      btnShow: true,
      count: '',
      timer: null,
      dataForm: {
        id: '',
        username: '',
        regist: 'bhsq',
        deptId: '0',
        deptName: '',
        password: '',
        smsyzm: '',
        uuid: '',
        comfirmPassword: '',
        realName: '',
        mobile: '',
        status: 1,
        xieyi: false
      },
      regestoptions: [
        { code: 'bhsq', name: '保函申请方' },
        { code: 'bhcj', name: '保函出具方' },
        { code: 'bhsy', name: '保函使用方' }
      ],
      loading: false,
      formLabelWidth: '80px'
    }
  },
  components: {
    xieyi
  },

  computed: {
    dataRule () {
      var validatePassword = (rule, value, callback) => {
        if (!this.dataForm.id && !/\S/.test(value)) {
          return callback(new Error(this.$t('validate.required')))
        }
        callback()
      }
      var validateComfirmPassword = (rule, value, callback) => {
        if (!this.dataForm.id && !/\S/.test(value)) {
          return callback(new Error(this.$t('validate.required')))
        }
        if (this.dataForm.password !== value) {
          return callback(new Error(this.$t('user.validate.comfirmPassword')))
        }
        callback()
      }
      var validateMobile = (rule, value, callback) => {
        if (!isMobile(value)) {
          return callback(
            new Error(
              this.$t('validate.format', { attr: this.$t('user.mobile') })
            )
          )
        }
        callback()
      }
      return {
        username: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        regist: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        password: [{ validator: validatePassword, trigger: 'blur' }],
        smsyzm: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        comfirmPassword: [
          { validator: validateComfirmPassword, trigger: 'blur' }
        ],
        mobile: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          },
          { validator: validateMobile, trigger: 'blur' }
        ],
        xieyi: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ]
      }
    }
  },
  created () {
    this.init()
  },

  methods: {
    init () {
      this.dataForm.username = ''
      this.dataForm.password = ''
      this.dataForm.comfirmPassword = ''
      this.dataForm.mobile = ''
      this.dataForm.regist = 'bhsq'
      this.dataForm.xieyi = false
    },
    // getUrl () {
    //   window.open('https://te.zhibaoke.com/web/static/img/loginlogo.247e396.png')
    // },
    getCode () {
      const TIME_COUNT = 60
      if (!this.timer) {
        this.count = TIME_COUNT
        this.btnShow = false
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--
          } else {
            this.btnShow = true
            clearInterval(this.timer)
            this.timer = null
          }
        }, 1000)
      }
    },
    toLogin () {
      this.$router.push({
        name: 'login'
      })
    },
    handleClose2 (done) {
      done()
    },
    sendMsg () {
      if (!this.dataForm.mobile) {
        this.$message.error('请先输入手机号')
        return false
      }
      this.dataForm.uuid = getUUID()
      this.$http
        .get(
          `${window.SITE_CONFIG['apiURL']}/smsyzm?uuid=${this.dataForm.uuid}&tel=${this.dataForm.mobile}`
        )
        .then(({ data: res }) => {
          this.getCode()
          this.$message.success('发送短信成功')
        })
        .catch(() => {})
    },
    innerClose () {
      this.dataForm.xieyi = true
      this.innerDrawer = false
    },
    checkpwd () {
      if (this.dataForm.password.length < 1) {
        this.dataForm.password = ''
        this.dataForm.comfirmPassword = ''
        return this.$message.error('用户登录密码不能为空')
      } else if (this.dataForm.password !== this.dataForm.comfirmPassword) {
        this.dataForm.password = ''
        this.dataForm.comfirmPassword = ''
        return this.$message.error('两次密码不一致！')
      } else if (this.dataForm.password.length < 8) {
        this.dataForm.password = ''
        this.dataForm.comfirmPassword = ''
        return this.$message.error('用户登录密码必须在8位以上')
      } else {
        var value = this.dataForm.password // 得到输入框中的值
        var pattern = /(?!^[0-9]*$)(?!^[a-zA-Z]*$)^([a-zA-Z0-9]{8,})$/ // 创建正则表达式对象
        var flag = pattern.test(value) // 测试匹配

        if (!flag) {
          // 判断匹配
          this.dataForm.password = ''
          this.dataForm.comfirmPassword = ''
          return this.$message.error(
            '格式错误，请使用数字加字母的格式，请重新输入！'
          )
        }
      }
    },

    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate((valid) => {
          if (!this.dataForm.xieyi) {
            this.$message.error('请同意用户协议后进行注册')
            return false
          }
          if (!valid) {
            return false
          }
          this.loading = true
          this.$http[!this.dataForm.id ? 'post' : 'put'](
            '/sys//user/register',
            {
              ...this.dataForm
            }
          )
            .then(({ data: res }) => {
              this.loading = false
              if (res.code !== 0) {
                if (res.code === 10002) {
                  return this.$message.error('该手机号已注册，不能继续！')
                } else {
                  return
                }
              }

              this.$message({
                message: '注册成功，请登录',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.toLogin()
                }
              })
            })
            .catch(() => {})
        })
      },
      1000,
      { leading: true, trailing: false }
    )
  }
}
</script>
<style lang="scss" scoped>
.el-form {
  padding: 10px 100px;
}
.site-wrapper.site-page--login {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(38, 50, 56, 0.6);
  overflow: hidden;
  &:before {
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    content: '';
    background-image: url(~@/assets/img/bg.jpg);
    background-size: 100% 100%;
  }
  .login-brand {
    // margin: 81px 0 60px 0;
    padding-top: 15px;
    font-size: 30px;
    font-weight: bold;
    line-height: 34px;
    /* color: #396afe; */
    color: rgb(47, 177, 236) !important;
    text-align: center;
  }
  .site-content__wrapper {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 0;
    margin: 0;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: transparent;
  }
  .site-content {
    /* padding: 30px 500px 30px 30px; */
    width: 600px;
    margin: 15vh auto;
    overflow: hidden;
  }
  .brand-info {
    margin: 220px 100px 0 90px;
    color: #fff;
  }
  .brand-info__text {
    margin: 0 0 22px 0;

    font-weight: 700;
    font-size: 28px;
    text-transform: uppercase;
    position: relative;
    .line {
      position: absolute;
      left: 55px;
      transform: translate(-50%, 0);
      top: 40px;
      width: 60px;
      height: 0;
      border-top: 1px solid white;
      opacity: 0.6;
    }
  }
  .brand-info__intro {
    margin: 0 auto;
    line-height: 26px;
    font-size: 16px;
    padding-bottom: 30px;
  }
  .login-main {
    border-radius: 10px;
    // width: 470px;
    background-color: rgba(255, 255, 255, 1);
  }
  .login-title {
    font-size: 16px;
  }
  .login-captcha {
    overflow: hidden;
    > img {
      width: 100%;
      cursor: pointer;
    }
  }
  .login-btn-submit {
    width: 100%;
    margin-top: 38px;
  }
}
.innerFoot {
  text-align: center;
}
.hide {
  width: 0;
  position: absolute;
  border: none;
}
</style>
