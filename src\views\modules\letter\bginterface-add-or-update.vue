<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"  :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
        <el-row :gutter="20">
            <el-col :xs="24" :lg="colConfig">
<!--          <el-form-item label="编码" prop="code">-->
<!--     <el-input v-model="dataForm.code" placeholder="编码"></el-input>-->
<!--</el-form-item>-->
                <el-form-item label="名称" prop="name">
     <el-input v-model="dataForm.name" placeholder="名称"></el-input>
</el-form-item>
                <el-form-item label="接口类型 1保函出具方 2保函使用方 3平台方" prop="interfaceType">
     <el-input v-model="dataForm.interfaceType" placeholder="接口类型 1保函出具方 2保函使用方 3平台方"></el-input>
</el-form-item>
<!--                <el-form-item label="平台id" prop="pid">-->
<!--     <el-input v-model="dataForm.pid" placeholder="平台id"></el-input>-->
<!--</el-form-item>-->
<!--                <el-form-item label="平台编码" prop="pcode">-->
<!--     <el-input v-model="dataForm.pcode" placeholder="平台编码"></el-input>-->
<!--</el-form-item>-->
                <el-form-item label="平台名称" prop="pname">
     <el-input v-model="dataForm.pname" placeholder="平台名称"></el-input>
</el-form-item>
                <el-form-item label="接口方式 1post 2get" prop="interfaceMethod">
     <el-input v-model="dataForm.interfaceMethod" placeholder="接口方式 1post 2get"></el-input>
</el-form-item>
                <el-form-item label="接口url" prop="interfaceUrl">
     <el-input v-model="dataForm.interfaceUrl" placeholder="接口url"></el-input>
</el-form-item>
                <el-form-item label="请求参数" prop="reqParameter">
     <el-input v-model="dataForm.reqParameter" placeholder="请求参数"></el-input>
</el-form-item>
                <el-form-item label="返回参数" prop="resParameter">
     <el-input v-model="dataForm.resParameter" placeholder="返回参数"></el-input>
</el-form-item>
                <el-form-item label="接口描述" prop="description">
     <el-input v-model="dataForm.description" placeholder="接口描述"></el-input>
</el-form-item>
                <el-form-item label="排序" prop="orders">
     <el-input v-model="dataForm.orders" placeholder="排序"></el-input>
</el-form-item>
                  <el-form-item label="状态" prop="status" size="mini">
   <el-radio-group v-model="dataForm.status">
       <el-radio :label="0">停用</el-radio>
       <el-radio :label="1">正常</el-radio>
   </el-radio-group>
</el-form-item>
                            </el-col>
        </el-row>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        interfaceType: '',
        pid: '',
        pcode: '',
        pname: '',
        interfaceMethod: '',
        interfaceUrl: '',
        reqParameter: '',
        resParameter: '',
        description: '',
        orders: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        code: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        interfaceType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        pid: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        // pcode: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        // pname: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        interfaceMethod: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        interfaceUrl: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        reqParameter: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        resParameter: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        description: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        orders: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        status: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]

      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/letter/bginterface/${this.dataForm.id}`).then(({ data: res }) => {
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/letter/bginterface/', this.dataForm).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
