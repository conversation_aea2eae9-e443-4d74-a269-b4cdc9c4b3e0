<template>
  <el-dialog :visible.sync="visible" append-to-body :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"  :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
        <el-row :gutter="20">
            <el-col :xs="24" :lg="colConfig">
          <el-form-item label="编码" hidden prop="code">
     <el-input v-model="dataForm.code" placeholder="编码"></el-input>
</el-form-item>
                <!-- <el-form-item label="名称" prop="name">
     <el-input v-model="dataForm.name" placeholder="名称"></el-input>
</el-form-item>
                <el-form-item label="邮编" prop="mailingCode">
     <el-input v-model="dataForm.mailingCode" placeholder="邮编"></el-input>
</el-form-item> -->
                <el-form-item label="收件人" prop="mailingUser">
     <el-input v-model="dataForm.mailingUser" placeholder="收件人"></el-input>
</el-form-item>
                <el-form-item label="收件手机" prop="mailingTel">
     <el-input v-model="dataForm.mailingTel" placeholder="收件手机"></el-input>
</el-form-item>
                <el-form-item label="收件地址" prop="mailingAddress">
     <el-input v-model="dataForm.mailingAddress" placeholder="收件地址"></el-input>
</el-form-item>
                  <!-- <el-form-item label="状态" prop="status" size="mini">
   <el-radio-group v-model="dataForm.status">
       <el-radio :label="0">停用</el-radio>
       <el-radio :label="1">正常</el-radio>
   </el-radio-group>
</el-form-item> -->
                            </el-col>
        </el-row>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" :loading="btnLoadng" @click="dataFormSubmitHandle()">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      btnLoadng: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        mailingCode: '',
        mailingUser: '',
        mailingTel: '',
        mailingAddress: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      var checkZuoPhone = (rule, value, callback) => {
        if (!value) {
          return callback(new Error('请输入正确的手机号'))
        } else {
          // eslint-disable-next-line no-useless-escape
          const reg = /^1[3|4|5|7|8|9][0-9]\d{8}$/
          console.log(reg.test(value))
          if (reg.test(value)) {
            callback()
          } else {
            return callback(new Error('请输入正确的手机号'))
          }
        }
      }
      return {
        // code: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        // name: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        // mailingCode: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        mailingUser: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        mailingTel: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: checkZuoPhone, trigger: 'blur' }
        ],
        mailingAddress: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
        // status: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ]

      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/letter/bgguaranteemailing/${this.dataForm.id}`).then(({ data: res }) => {
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.btnLoadng = true
        this.$http[!this.dataForm.id ? 'post' : 'put']('/letter/bgguaranteemailing/', this.dataForm).then(({ data: res }) => {
          this.btnLoadng = false

          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
