<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2021-03-10 18:54:25
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2024-03-27 08:48:24
-->
<template>
  <el-table size='mini' stripe v-loading="dataListLoading" :cell-style="TableCellStyle" :data="dataList" border style="width: 100%;zoom:0.9">
    <el-table-column
        type="index"
        width="50">
        <template slot-scope="scope">
          <span v-show="dataList.length !=scope.$index+1">{{scope.$index+1}}</span>
        </template>
    </el-table-column>
    <el-table-column prop="regoinName" label="区域名称" header-align="center" align="center" ></el-table-column>
    <el-table-column prop="guaranteePrice" label="保函金额（元）" header-align="center" align="center" ></el-table-column>
    <el-table-column prop="revenue" label="宏筑收入（元）" header-align="center" align="center" ></el-table-column>
    <!-- <el-table-column prop="backAgentInvoicing" label="代理开票（元）" header-align="center" align="center" ></el-table-column> -->
    <el-table-column prop="backAgent" label="代理结算金额（元）" header-align="center" align="center" ></el-table-column>
    <el-table-column prop="regionalBonus" label="区域奖金（元）" header-align="center" align="center" ></el-table-column>
  </el-table>
</template>
<script>
import { getDict } from '@/utils/index'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins: [ mixinViewModule ],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgSettlement/revenueAreaDetails',
        getDataListIsPage: false,
        exportURL: '/letter/bgguaranteeletter/accountDataExport',
        deleteIsBatch: true
      }
    }
  },
  methods: {
    TableCellStyle ({ rowIndex }) {
      if (rowIndex === this.dataList.length - 1) {
        return 'background-color:#f1824126'
      }
    },
    dict (val) {
      var aa = getDict('电子保函类型').filter((a) => a.dictCode === val)
      return aa.length > 0 ? aa[0].dictName : '-'
    }
  }
}
</script>
