<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-03-19 09:19:44
 * @LastEditors: k<PERSON>weiqiang
 * @LastEditTime: 2022-01-21 11:18:22
-->
<!--
 * @Descripttion:
 * @Author: kong<PERSON><PERSON><PERSON>
 * @Date: 2020-08-06 18:47:13
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-02-25 11:07:09
-->
<template>
  <el-dialog class="CAmodel" :visible.sync="visible" title="提示" :before-close='beforeClose' :fullscreen="fullscreen" append-to-body :close-on-click-modal="false" :close-on-press-escape="false"
    width="1200px">
    <div slot='title'>
      <h3>签章</h3>
      <el-button @click="fullscreen=!fullscreen" class="el-dialog__headerbtn diaFull" type="text"><i class="el-icon-full-screen"></i></el-button>
    </div>
    <!-- {{form}} -->
    <!-- {{dataForm}} -->
    <el-tabs v-model="activeName" class="min550" type="card">
      <!-- {{issueCode}}{{type}}1111 -->
      <!-- {{dataForm}} -->
      <div id="type-examineCAChrome" :data-type='activeName' :data-issuecode='JSON.stringify(issueCode)' :data-retype="JSON.stringify(type == 'lydbbh'?dataForm.performanceType:'')"
        :data-guaranteetype="JSON.stringify(dataForm.guaranteeTypeCode?dataForm.guaranteeTypeCode:dataForm.guaranteeType)" :data-letterid='JSON.stringify(dataForm.letterId)'></div>
      <el-tab-pane label="电子保函" v-if="form.bhid" name="0">
        <div style="padding:30px 0;">
          <iframe v-if="form" :src="src+'examineCA.html'+'?_t='+new Date().getTime()" :data-type='JSON.stringify(0)' :data-form='JSON.stringify(form)' data-isCheckLegalSign='1'
            data-isCheckOfficialSign='1' id="examineCAChromebh" class="min550" style="width:100%;height:100%;border:none;"></iframe>
        </div>
      </el-tab-pane>
      <el-tab-pane label="担保合同" v-if="form.wtht" name="4">
        <div style="padding:30px 0;">
          <iframe v-if="form" :src="src+'examineCA.html'+'?_t='+new Date().getTime()" :data-type='JSON.stringify(4)' :data-form='JSON.stringify(form)' :data-isCheckLegalSign='pakeage.isCheckLegalSign'
            :data-isCheckOfficialSign='pakeage.isCheckLegalSign' id="examineCAChromewt" class="min550" style="width:100%;height:100%;border:none;"></iframe>
        </div>
      </el-tab-pane>
      <el-tab-pane label="委托担保合同" v-if="form.dbht" name="14000">
        <div style="padding:30px 0;">
          <iframe v-if="form" :src="src+'examineCA.html'+'?_t='+new Date().getTime()" :data-type='JSON.stringify(14000)' :data-form='JSON.stringify(form)'
            :data-isCheckLegalSign='pakeage.isCheckLegalSign' :data-isCheckOfficialSign='pakeage.isCheckLegalSign' id="examineCAChromedb" class="min550"
            style="width:100%;height:100%;border:none;"></iframe>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>
<script>
export default {
  data () {
    return {
      activeName: '0',
      fullscreen: false,
      visible: false,
      form: {
        bhid: '',
        wtht: '',
        dbht: ''
      },
      dataForm: {},
      pakeage: {},
      type: '',
      issueCode: '',
      webPDFObj: '',
      businessLicenseId: '',
      biddingDocumentId: '',
      src: ''
    }
  },
  mounted () {
    this.src = process.env.BASE_URL
  },
  watch: {
    bhid: {
      handler (a) {
        this.bhid = a
        console.log(this.bhid)
      }
    },
    dataForm: {
      handler (a) {
        this.dataForm = a
      },
      deep: true
    },
    form: {
      handler (a) {
        this.form = a
        if (a.bhid) {
          this.activeName = '0'
        } else {
          this.activeName = '4'
        }
      },
      deep: true
    },
    type (a) {
      this.type = a
    },
    guaranteeApi (a) {
      this.guaranteeApi = a
    },
    dtoName (a) {
      this.dtoName = a
    }
  },
  methods: {
    init () {
      this.visible = true
      this.fullscreen = false
      this.$nextTick(() => {
        // this.getpakeage()
      })
    },
    getpakeage () {
      this.$http
        .get(
          `/letter/bgissueconfigure/getInfoByIssueCode?issueCode=${this.issueCode}&guaranteeTypeCode=${this.type}`
        )
        .then(async ({ data: res }) => {
          this.pakeage = res.data
        })
    },

    beforeClose (done) {
      this.visible = false
      if (this.type === 'pg') {
        this.$http.post('letter/guarantee/updateCorrectStatus', {
          id: this.$route.query.seeLetterId
        })
      }
      this.$emit('refresh', this.form.bhid)
      done()
    }
  }
}
</script>
<style lang="scss" scoped>
.min550 {
  min-height: 650px;
  height: 100%;
}
.CAmodel .el-tabs__header {
  top: 0px;
}
</style>
