<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2021-03-11 14:58:28
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-03-17 17:58:07
-->
<template>
  <el-col :span="24">
    <el-col :span="24" class="con-title" :class="showDel?'pdL20':''">
      被申请人信息
    </el-col>
     <el-col :span="24">
    <p>被申请人人数：{{dataList.length}}人</p>
    <el-table :data="dataList" height="280" border style="width: 100%">
      <el-table-column type="index" width="50">
      </el-table-column>
      <el-table-column label="被申请人类型" width="110">
        <template slot-scope="scope">
          {{scope.row.groupOrIndividual==1?'公司':'自然人'}}
        </template>
      </el-table-column>
      <el-table-column label="被申请人名称" prop='userName' width="180">
      </el-table-column>
      <el-table-column label="证件类型" prop='documentType'>
        <template slot-scope="scope">
          {{scope.row.documentType=='1'?'身份证':'社会统一信用代码'}}
        </template>
      </el-table-column>
      <el-table-column label="证件号">
        <template slot-scope="scope">
          {{scope.row.cardNumber}}
        </template>
      </el-table-column>
      <el-table-column label="联系方式" prop='userTel' width="140">
      </el-table-column>
      <el-table-column label="法定代表人" prop='legalPerson' width="110">
      </el-table-column>
      <el-table-column label="住址" prop='address'>
      </el-table-column>
      <el-table-column width="50" align='center' fixed="right" v-if="showDel">
        <!-- eslint-disable-next-line vue/no-unused-vars -->
        <template slot-scope="scope">
          <el-popconfirm title="确认删除此条数据？" @confirm="deleteCol(scope.$index)">
            <i class="el-icon-close" slot="reference"></i>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
</el-col>
  </el-col>
</template>
<script>
export default {
  props: {
    dataList: Array,
    showDel: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    deleteCol (index) {
      // console.log(this.dataList, index)
      this.dataList.splice(index, 1)
    }
  }
}
</script>
<style lang="scss" scoped>
.con-title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
border-left: 6px solid rgb(241, 130, 65);  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin: 10px 0;
}
.pdL20{
  display: none;
}
</style>
