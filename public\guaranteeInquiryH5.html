<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-03 18:10:17
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-11-06 09:01:00
-->
<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>招标保函通</title>

  <script src="https://cdn.bootcss.com/babel-polyfill/7.8.7/polyfill.js"></script>
  <link rel="stylesheet" href="css/index.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vant@2.10/lib/index.css" />

  <!-- 引入 Vue 和 Vant 的 JS 文件 -->
  <script src="https://cdn.jsdelivr.net/npm/vue@2.6/dist/vue.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/vant@2.10/lib/vant.min.js"></script>
  <script src="js/common.js"></script>
  <script src="js/axios.min.js"></script>
</head>
<style>
  html,
  body,
  #app {
    height: 100%;
  }
  .headImg{
    width: 80px;
  }
  .van-doc-demo-block__title {
    margin: 0;
    padding: 10px;
    color: rgba(69, 90, 100, 0.6);
    font-weight: normal;
    font-size: 14px;
    line-height: 16px;
}
</style>

<body>
  <div id="app" style="background: #eee;">
    <van-nav-bar title="标题" >
      <template #title>
        <img class="headImg" src="img/logo.png" alt="">
      </template>
    </van-nav-bar>
    <h2 class="van-doc-demo-block__title">保函验真（基础信息）</h2>

    <van-cell-group >
      <van-cell title="保单号" :value="getQueryVariable('policyNo')"></van-cell>
      <van-cell title="验证码" :value="getQueryVariable('verificationCode')"></van-cell>
    </van-cell-group>
    <h2 class="van-doc-demo-block__title">保函验真（保函概要）</h2>
    <van-cell-group >
      <van-cell title="保函类型：" :value="detail.guaranteeType"></van-cell>
      <van-cell title="项目名称：" :value="detail.projectName"></van-cell>
      <van-cell title="申请人名称：" :value="detail.applyName"></van-cell>
      <van-cell title="保函出具方：" :value="detail.issueName"></van-cell>
      <van-cell title="保险起止日期：" :value="detail.startDate+' - '+detail.endDate"></van-cell>
      <van-cell title="被保人名称：" :value="detail.bbrName"></van-cell>
      <van-cell title="保证人金额：" :value="detail.guaranteeAmount+'元'"></van-cell>
    </van-cell-group>
  </div>

</body>
<script>

  new Vue({
    el: '#app',
    data() {
      return {
        detail:{}
      }
    },
    mounted() {
      this.getDetail()
    },
    methods: {
      getQueryVariable(variable) {
        var query = window.location.search.substring(1);
        var vars = query.split("&");
        for (var i = 0; i < vars.length; i++) {
          var pair = vars[i].split("=");
          if (pair[0] == variable) { return pair[1]; }
        }
        return (false);
      },
      getDetail() {
        axios.get(`${'http://www.hebhzjt.com/hzjt-dzbh'}/letter/bgguaranteeletter/guaranteeInquiry?policyNo=${this.getQueryVariable('policyNo')}&verificationCode=${this.getQueryVariable('verificationCode')}`).then(({ data: res }) => {
          this.loading = false
          if (res.code !== 0) {
            return false
          }
          this.detail = res.data
        }).catch(() => { })
      },
    },
  });
</script>
<style lang="scss">
  .ret div {
    margin: 15px 0;
  }
</style>
