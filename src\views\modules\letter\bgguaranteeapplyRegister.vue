<template>
  <div>
    <el-card class="box-card">
      <div class="temp-content">
         <h3 class="temp-subtitle">基本信息</h3>
        <el-form :model="dataForm" class="formCon" :rules="dataRule" ref="dataForm"  :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">

          <el-row :gutter="20" >
            <el-form-item label="状态" prop="status" size="mini">
              <span v-if="dataForm.status == 2">
                <i class="el-icon-success" style="color:#67C23A;"></i>&nbsp;已认证
              </span>
              <span v-if="dataForm.status == 1">
                <i class="el-icon-warning" style="color:#E6A23C;"></i>&nbsp;认证审核中
              </span>
              <span v-if="dataForm.status == 0">
                <i class="el-icon-error" style="color:#F56C6C;"></i>&nbsp;待提交审核
              </span>
            </el-form-item>
            <el-col :xs="24" :lg="colConfig">

              <el-form-item label="编码" hidden prop="code">
                <el-input v-model="dataForm.code" size="medium" placeholder="编码"></el-input>
              </el-form-item>
              <el-form-item label="机构名称" prop="name">
                <el-input :disabled="dataForm.status == 2" v-model="dataForm.name" size="medium" placeholder="名称"></el-input>
              </el-form-item>
              <el-form-item label="机构证件类型" prop="certType">
                <el-select v-model="dataForm.certType" size="medium" placeholder="证件类型">
                  <el-option v-for="item in certTypeoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="联系人" prop="linkman">
                <el-input v-model="dataForm.linkman" size="medium" placeholder="联系人"></el-input>
              </el-form-item>
              <el-form-item label="有效期类型" prop="certTermType">
                <!-- {{dataForm.certTermType}} -->
                <el-select @change='seleChange' v-model="dataForm.certTermType" size="medium" placeholder="证件有效期类型">
                  <el-option v-for="item in certTermTypeoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
                  </el-option>
                </el-select>
              </el-form-item>
              <!--<el-form-item label="注册资金" prop="registerCapital">
                <el-input v-model="dataForm.registerCapital" size="medium" placeholder="注册资金"><template slot="append">万元</template></el-input>
              </el-form-item>-->
              <el-form-item label="公司电话" prop="phoneNumber">
                <el-input v-model="dataForm.phoneNumber" size="medium" placeholder="公司电话"></el-input>
              </el-form-item>
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="dataForm.email" size="medium" placeholder="邮箱"></el-input>
              </el-form-item>
             <!-- <el-form-item label="注册时间" prop="registerTime">
                <el-date-picker v-model="dataForm.registerTime" size="medium" type="date" value-format="yyyy-MM-dd" placeholder="注册时间"></el-date-picker>
              </el-form-item>-->
            </el-col>
            <el-col :xs="24" :lg="colConfig">
              <el-form-item label="法定代表人" prop="corporation">
                <el-input v-model="dataForm.corporation" size="medium" placeholder="法定代表人"></el-input>
              </el-form-item>
              <el-form-item label="机构证件号码" prop="certCode">
                <el-input v-model="dataForm.certCode" size="medium" placeholder="证件号码"></el-input>
              </el-form-item>

              <el-form-item label="联系人电话" prop="linkmanTel" >
                <el-input v-model="dataForm.linkmanTel" size="medium" placeholder="联系人电话"></el-input>
              </el-form-item>
              <el-form-item label="证件有效期" prop="certTerm" v-if="dataForm.certTermType == '0'">
                <el-date-picker v-model="dataForm.certTerm" type="date" size="medium" value-format="yyyy-MM-dd" placeholder="证件有效期"></el-date-picker>
                <!--<el-date-picker v-model="dataForm.certTerm" type="date" value-format="yyyy-MM-dd" placeholder=""></el-date-picker>-->
              </el-form-item>

              <el-form-item label="传真" hidden prop="fax">
                <el-input v-model="dataForm.fax" size="medium" placeholder="传真"></el-input>
              </el-form-item>
              <el-form-item label="邮编" hidden prop="postcode">
                <el-input v-model="dataForm.postcode" size="medium" placeholder="邮编"></el-input>
              </el-form-item>
              <el-form-item label="所属平台编码" hidden prop="platformCode">
                <el-input v-model="dataForm.platformCode" size="medium" placeholder="所属平台编码"></el-input>
              </el-form-item>
              <el-form-item label="地址" prop="address">
                <el-input type="textarea" rows="3" v-model="dataForm.address" size="medium" placeholder="地址"></el-input>
              </el-form-item>
              <el-form-item label="注册地址" hidden prop="registerAddress">
                <el-input v-model="dataForm.registerAddress" size="medium" placeholder="注册地址"></el-input>
              </el-form-item>
              <el-form-item label="是否vip" hidden prop="isVip">
                <el-input v-model="dataForm.isVip" size="medium" placeholder=""></el-input>
              </el-form-item>

            </el-col>
          </el-row>
          <el-form-item label="经营范围" prop="description">
            <!-- 富文本编辑器, 容器 -->
            <!-- <div id="J_quillEditor"></div> -->
            <el-input
            type="textarea"
            :autosize="{ minRows: 5, maxRows: 5}"
            placeholder="请输入经营范围"
            v-model="dataForm.description"></el-input>
            <!-- 自定义上传图片功能 (使用element upload组件) -->
            <!-- <el-form-item label="经营范围" hidden prop="description"> -->
              <!--<textarea  rows="10" cols="210" placeholder="请输入" maxlength="1000" @input="descInput" v-model="dataForm.description" />-->
              <!-- <span class="numberV"></span>
            </el-form-item> -->
          </el-form-item>
        </el-form>
      </div>
      <audit-instance v-if="auditInstanceVisible" ref="auditInstance" @refreshDataList="init"></audit-instance>
    </el-card>

    <div class="foot">
      <span>
        <el-button type="primary" v-if="dataForm.status==0" :loading='btnLoading' @click="dataFormSubmitHandle()">保存</el-button>
        <el-button type="primary" v-if="dataForm.id&&dataForm.status==0" :loading='btnLoading' @click="dataSubmitHandle()">提交</el-button>
        <el-button v-if="dataForm.id&&dataForm.auditId !==''&&dataForm.auditId !==null" type="primary" @click="auditNodeHandle()">审核记录</el-button>
        <el-button v-if="dataForm.id&&dataForm.status == 2" type="primary" @click="alteration(dataForm.id)">变更</el-button>
      </span>
    </div>
  </div>
</template>
<script>
import debounce from 'lodash/debounce'
import AuditInstance from '@/views/modules/demo/tbauditinstance-displayaudit'
import 'quill/dist/quill.snow.css'
import Quill from 'quill'
export default {
  data () {
    return {
      colConfig: 2, // 列配置 1 2列
      visible: false,
      auditInstanceVisible: false,
      // quillEditor: null,
      // quillEditorToolbarOptions: [
      //   ['bold', 'italic', 'underline', 'strike'],
      //   [{ header: 1 }, { header: 2 }],
      //   [{ list: 'ordered' }, { list: 'bullet' }],
      //   [{ script: 'sub' }, { script: 'super' }],
      //   [{ indent: '-1' }, { indent: '+1' }],
      //   [{ direction: 'rtl' }],
      //   [{ size: ['small', false, 'large', 'huge'] }],
      //   [{ header: [1, 2, 3, 4, 5, 6, false] }],
      //   [{ color: [] }, { background: [] }],
      //   [{ font: [] }],
      //   [{ align: [] }],
      //   ['clean']
      // ],
      btnLoading: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        certType: '',
        certCode: '',
        certTerm: '',
        certTermType: '',
        linkman: '',
        linkmanTel: '',
        corporation: '',
        email: '',
        phoneNumber: '',
        fax: '',
        postcode: '',
        platformCode: '',
        address: '',
        description: '',
        registerAddress: '',
        registerCapital: '',
        registerTime: '',
        isVip: '',
        deptId: '',
        status: 0,
        auditId: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      getDicListURL: '/sys/dict/type/',
      getAlterationURL: '/letter/bgguaranteeapply/alteration/',
      certTermTypeoptions: [],
      certTypeoptions: []
    }
  },
  props: {
    dialog: {
      type: Boolean,
      default: false
    }
  },
  components: {
    AuditInstance
  },
  computed: {
    dataRule () {
      var checkPhone = (rule, value, callback) => {
        if (!value) {
          return callback(new Error('手机号不能为空'))
        } else {
          const reg = /^1[3|4|5|7|8|9][0-9]\d{8}$/
          console.log(reg.test(value))
          if (reg.test(value)) {
            callback()
          } else {
            return callback(new Error('请输入正确的手机号'))
          }
        }
      }
      var checkZuoPhone = (rule, value, callback) => {
        if (!value) {
          return callback(new Error('请输入正确的电话号'))
        } else {
          // eslint-disable-next-line no-useless-escape
          const reg = /^[0-9-]*$/
          if (reg.test(value)) {
            callback()
          } else {
            return callback(new Error('请输入正确的电话号'))
          }
        }
      }
      var checkEmail = (rule, value, callback) => {
        if (!value) {
          callback()
        } else {
          const reg = /^([a-zA-Z0-9._-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/
          if (reg.test(value)) {
            callback()
          } else {
            return callback(new Error('请输入正确的邮箱格式'))
          }
        }
      }
      var certCodeRule = (rule, value, callback) => {
        if (this.dataForm.certType === '2') {
          if (value.length !== 18) {
            return callback(new Error('统一社会信用代码必须是18位'))
          } else {
            callback()
          }
        } else {
          if (value.length < 7) {
            return callback(new Error('证件号码长度最小7位'))
          } else {
            callback()
          }
        }
      }
      return {
        // code: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        name: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        certType: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        certCode: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          },
          { validator: certCodeRule, trigger: 'blur' }
        ],
        certTerm: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        certTermType: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        linkman: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        linkmanTel: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          },
          { validator: checkPhone, trigger: 'blur' }
        ],
        corporation: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        phoneNumber: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          },
          { validator: checkZuoPhone, trigger: 'blur' }
        ],
        address: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        email: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          },
          { validator: checkEmail, trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
    this.init()
    this.getCertTermTypeInfo()
    this.getCertTypeTTInfo()
  },
  methods: {
    descInput () {
      this.txtVal = this.dataForm.description.length
    },
    seleChange (val) {
      if (val === '1') {
        this.dataForm.certTerm = '2099-12-31'
      } else {
        this.dataForm.certTerm = ''
      }
    },
    handleClose (done) {
      this.$emit('dialogClose', false)
    },
    init () {
      this.visible = true
      this.$nextTick(() => {
        // this.$refs['dataForm'].resetFields()
        // if (this.quillEditor) {
        //   this.quillEditor.deleteText(0, this.quillEditor.getLength())
        // } else {
        //   this.quillEditorHandle()
        // }
        this.getInfo()
      })
    },
    // 证件有效期信息
    getCertTermTypeInfo () {
      this.$http
        .get(this.getDicListURL + 'certTermType')
        .then(({ data: res }) => {
          this.certTermTypeoptions = {
            ...this.certTermTypeoptions,
            ...res.data.list
          }
        })
        .catch(() => {})
    },
    // 证件信息
    getCertTypeTTInfo () {
      this.$http
        .get(this.getDicListURL + 'certTypeTT')
        .then(({ data: res }) => {
          this.certTypeoptions = {
            ...this.certTypeoptions,
            ...res.data.list
          }
        })
        .catch(() => {})
    },
    // 富文本编辑器
    quillEditorHandle () {
      this.quillEditor = new Quill('#J_quillEditor', {
        modules: {
          toolbar: this.quillEditorToolbarOptions
        },
        theme: 'snow'
      })
      // 监听内容变化，动态赋值
      this.quillEditor.on('text-change', () => {
        this.dataForm.description = this.quillEditor.root.innerHTML
      })
    },
    // 获取信息
    getInfo () {
      this.$http
        .get(`/letter/bgguaranteeapply/getInfo`)
        .then(({ data: res }) => {
          this.pageLoading = false
          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
          if (res.data.status === null) {
            this.dataForm.status = 0
          }
          this.quillEditor.root.innerHTML = this.dataForm.description
        })
        .catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate(valid => {
          if (!valid) {
            return false
          }
          this.btnLoading = true
          this.$http[!this.dataForm.id ? 'post' : 'put'](
            !this.dataForm.id
              ? '/letter/bgguaranteeapply/register/'
              : '/letter/bgguaranteeapply/',
            this.dataForm
          )
            .then(({ data: res }) => {
              this.btnLoading = false

              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.getInfo()
                }
              })
            })
            .catch(() => {})
        })
      },
      1000,
      { leading: true, trailing: false }
    ),
    // 表单提交进入流程审核环节
    dataSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate(valid => {
          if (!valid) {
            return false
          }
          this.$http['put']('/letter/bgguaranteeapply/submit/', this.dataForm)
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.getInfo()
                }
              })
            })
            .catch(() => {})
        })
      },
      1000,
      { leading: true, trailing: false }
    ),
    auditNodeHandle () {
      this.auditInstanceVisible = true
      this.$nextTick(() => {
        this.$refs.auditInstance.dataForm.id = this.dataForm.auditId
        this.$refs.auditInstance.init()
      })
    },
    // 变更
    alteration (id) {
      this.$refs['dataForm'].validate(valid => {
        if (!valid) {
          return false
        }
        this.$confirm('确定要变更信息吗?, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.put(this.getAlterationURL, this.dataForm)
            .then(({ data: res }) => {
              this.$message({
                type: 'success',
                message: '操作成功!',
                duration: 500
              })
            })
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.el-card {
  margin-bottom: 53px;
}
.temp-content {
  // padding: 24px;
  width: 50vm;
  margin: 0 auto;
  .titlename {
    width: 100%;
    height: 60px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    font-weight: bold;
    border-bottom: 1px solid #e6e6e6;
    font-size: 20px;
    p {
      margin: 10px 0;
    }
  }
  .temp-line {
    padding-bottom: 32px;
  }
  .temp-line:after {
    position: absolute;
    right: 0;
    bottom: 0;
    content: '';
    display: block;
    clear: both;
    width: 100%;
    min-width: 100%;
    height: 1px;
    margin: 8px 0 24px;
    background: #e8e8e8;
  }
  .temp-subtitle {
      border-bottom: 1px dashed rgb(241, 130, 65);
          padding: 15px;
          text-align: center;
  }
  .formCon{
        width: 55vw;
    min-width: 750px;
    margin: 30px auto;
    .el-select,.el-input{
      width: 100%;
    }
  }
  .temp-form {
    position: relative;

    .el-date-editor.el-input {
      width: auto !important;
    }
    .el-date-editor {
      max-width: 280px;
    }
    .el-input {
      max-width: 280px;
    }
    .el-date-editor--daterange {
      max-width: 240px;
    }
  }
}
.foot {
  width: 100%;
  background: #fff;
  position: fixed;
  bottom: 0;
  padding: 10px 0;
  border-top: 1px solid #ddd;
  background: #f9f9f9;
  text-align: center;
  left: 80px;
  z-index: 999;
  span {
    text-align: center;
  }
}
.btn {
  .el-input-group__append {
    color: #fff;
    background-color: #409eff;
    border-color: #409eff;
    padding: 0 8px;
  }
}
.el-loading-mask {
  z-index: 998 !important;
}
</style>
