<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2021-08-18 09:04:36
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-08-18 09:19:49
-->
<template>
  <el-row :gutter="20" style="margin:15px 20px 0;">
    <el-card class="box-card" shadow='never'>
      <div slot="header" class="clearfix">
        <span class="title">用户信息</span>
      </div>
      <div>
        <el-col :xs="24" :lg="24">
          <div class="formCon">
            <template>
              <el-form :model="dataForm" ref="dataForm"  label-position="left" style="margin-bottom:15px;" :rules="dataRule" label-width="140px">
                <el-col :xs="24" :lg="12"  >
                  <el-form-item label="联系人" prop="linkman">
                    <el-input v-model="dataForm.linkman" class="wd180" size="mini"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg="12">
                  <el-form-item label="联系方式"  prop="linkmanTel">
                    <el-input v-model="dataForm.linkmanTel" class="wd180" size="mini"></el-input>
                  </el-form-item>
                </el-col>
              </el-form>
            </template>
          </div>
        </el-col>
      </div>
    </el-card>
  </el-row>
</template>
<script>
export default {
  data () {
    return {
    }
  },
  props: {
    dataForm: {
      type: Object
    }
  },
  computed: {
    dataRule () {
      return {
        linkman: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        linkmanTel: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ]
      }
    }
  },
  // props: ['dataForm'],
  methods: {
    push () {
      return new Promise((resolve, reject) => {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.wd180 {
  width: 260px !important;
}
.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
</style>
