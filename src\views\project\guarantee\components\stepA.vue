<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON>ang
 * @Date: 2019-12-18 16:52:10
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-12-16 16:23:49
 -->
<template>
  <div class="inSteps">
    <el-steps :active="active" v-if="customList" align-center ref='steps' finish-status="success">
      <!-- {{this.$store.state.pdfType}} -->
      <el-step :title="item" v-for="(item,index) in customList" :key="index"></el-step>
    </el-steps>
    <el-steps :active="active" v-else align-center ref='steps'  finish-status="success">
      <!-- {{this.$store.state.pdfType}} -->
      <el-step :title="item" v-for="(item,index) in stepList" :key="index"></el-step>
    </el-steps>
  </div>
</template>
<script>
export default {
  data () {
    return {
      stepList: ['选择保函', '提交申请', '上传文件', '等待审核']
    }
  },
  watch: {
    customList (a) {
      this.stepList = a
    }
  },
  props: {
    active: {
      type: Number
    },
    customList: {
      type: Array
    }
  }
}
</script>
<style lang="scss" scoped>
.inSteps /deep/ .el-step__title{
    line-height: 25px;
    font-size: 15px;
}
</style>
