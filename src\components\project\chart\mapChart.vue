<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-10-15 08:51:57
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-10-22 16:05:31
-->
<style>
    .o-echarts {
        min-width: 30px;
        min-height: 30px;
        width: 100%;
        height: 100%;
    }
</style>
<template>
    <div :id="id" class="o-echarts">
    </div>
</template>

<script>
import echarts from 'echarts'
// import JSON from './js/hebei.json'
import 'echarts/map/js/china'
require('echarts/theme/macarons')

export default {
  name: 'echart-map',
  data () {
    return {
      id: 'echarts_' + new Date().getTime() + Math.floor(Math.random() * 1000),
      echartObj: null,
      province: 'china',
      radioActive: 'A',
      option: {},
      MData: []
    }
  },
  props: {
    mapData: {
      type: Array
    },
    max: Number,
    time: Array,
    timeType: String
  },
  watch: {
    mapData: {
      deep: true,
      handler (val) {
        this.mapData = val
        this.init()
      }
    }
  },
  mounted () {
    this.init()
    window.addEventListener('resize', () => {
      if (this.echartObj && this.echartObj.resize) {
        this.echartObj.resize()
      }
    })
    this.echartObj.on('click', (params) => {
      console.log(params)
      if (Number(params.value) === 0) {
        return this.$message.error('下级数据为0')
      }
      if (params.name === '北京' || params.name === '天津' || params.name === '重庆' || params.name === '台湾' || params.name === '海南' || params.name === '香港' || params.name === '上海' || params.name === '澳门') {
        return this.$message.error('直辖市等特殊省份不支持下级预览')
      }
      // 逻辑控制
      this.getChildData(params.data.id).then(data => {
        this.MData = data
        this.province = params.name
        this.changeMap()
      })
    })
  },
  methods: {
    getChildData (code) {
      console.log(code)
      return new Promise((resolve, reject) => {
        // letter/statistics/countProjectRegion
        this.$http.get('/letter/statistics/countProjectRegion', {
          params: {
            parentCode: code,
            startTime: this.time[0],
            endTime: this.time[1],
            timeType: this.timeType
          }
        }).then(({ data: res }) => {
          resolve(res)
        })
      })
    },
    changeMap () {
      this.$nextTick(() => {
        if (this.province === 'china') {
          this.mapData = this.mapData
          this.init()
        } else {
          if (this.initImportFile()) {
            this.initImportFile()
            this.init()
          } else {
            this.$message.error('暂无下级数据')
          }
        }
      })
    },
    init () {
      this.echartObj = echarts.init(this.$el, 'macarons')
      // echarts.registerMap('china', JSON)
      // this.changeMap()
      this.echartObj.setOption(this.initOption(), true)
    },
    initImportFile () {
      var filaName = {
        浙江: 'zhejiang.js',
        安徽: 'anhui.js',
        澳门: 'aomen.js',
        北京: 'beijing.js',
        重庆: 'chongqing.js',
        福建: 'fujian.js',
        甘肃: 'gansu.js',
        广东: 'guangdong.js',
        广西: 'guangxi.js',
        贵州: 'guizhou.js',
        海南: 'hainan.js',
        河北: 'hebei.js',
        黑龙江: 'heilongjiang.js',
        河南: 'henan.js',
        湖北: 'hubei.js',
        湖南: 'hunan.js',
        江苏: 'jiangsu.js',
        江西: 'jiangxi.js',
        吉林: 'jilin.js',
        辽宁: 'liaoning.js',
        内蒙古: 'neimenggu.js',
        宁夏: 'ningxia.js',
        青海: 'qinghai.js',
        山东: 'shandong.js',
        上海: 'shanghai.js',
        山西: 'shanxi.js',
        山西1: 'shanxi1.js',
        四川: 'sichuan.js',
        台湾: 'taiwan.js',
        天津: 'tianjin.js',
        香港: 'xianggang.js',
        新疆: 'xinjiang.js',
        西藏: 'xizang.js',
        云南: 'yunnan.js'
      }
      console.log(filaName[this.province])
      return filaName[this.province] ? require(`echarts/map/js/province/${filaName[this.province]}`) : false
    },
    initOption () {
      this.option = {
        title: {
          text: '地域项目数量',
          textStyle: {
            fontSize: 18,
            fontWeight: 300
            // color: '#b6d7ff'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}<br/>{c}条'
        },
        toolbox: {
          show: true,
          feature: {
            myTool2: {// 自定义按钮 danielinbiti,这里增加，selfbuttons可以随便取名字
              show: this.province !== 'china', // 是否显示
              title: '返回', // 鼠标移动上去显示的文字
              icon: 'path://M584.884104 272.714912V0L0 477.152657l584.884104 477.073906V674.817811c406.119203 0 690.568023 108.991464 893.588249 347.528417-81.271091-340.755826-324.926863-681.354149-893.588249-749.631316', // 图标
              onclick: () => { // 点击事件,这里的option1是chart的option信息
                this.province = 'china'
                this.changeMap()
              }
            }
          }
        },
        legend: {
          orient: 'vertical',
          top: '9%',
          left: '5%',
          icon: 'circle',
          data: [],
          selectedMode: 'single',
          selected: {},
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 30,
          inactiveColor: '#fff',
          textStyle: {
            color: '#ec808d',
            fontSize: 14,
            fontWeight: 300,
            padding: [0, 0, 0, 15]
          }
        },
        visualMap: {
          min: 0,
          max: this.max,
          text: ['最高', '最低'],
          realtime: false,
          calculable: true,
          inRange: {
            color: ['#fff', 'yellow', 'orange', 'orangered']
          }
        },
        geo: {
          map: this.province,
          label: {
            normal: {
              show: true,
              color: '#000'
            },
            emphasis: {
              show: true,
              color: '#fff'
            }
          },
          zoom: 1, // 当前视角的缩放比例
          roam: true, // 是否开启平游或缩放
          scaleLimit: { // 滚轮缩放的极限控制
            min: 1,
            max: 2
          },

          itemStyle: {
            normal: {
              areaColor: '#8db200',
              borderColor: '#6367ad',
              borderWidth: 1 // 设置外层边框
            },
            emphasis: {
              areaColor: '#feb6aa' // hover效果
            }
          },
          //   itemStyle: {
          //   normal: {
          //     areaColor: "#0d0059",
          //     borderColor: "#389dff",
          //     borderWidth: 1, //设置外层边框
          //     shadowBlur: 5,
          //     shadowOffsetY: 8,
          //     shadowOffsetX: 0,
          //     shadowColor: "#01012a"
          //   },
          //   emphasis: {
          //     areaColor: "#184cff",
          //     shadowOffsetX: 0,
          //     shadowOffsetY: 0,
          //     shadowBlur: 5,
          //     borderWidth: 0,
          //     shadowColor: "rgba(0, 0, 0, 0.5)"
          //   }
          // }
          left: '20%',
          right: '5%',
          top: '5%',
          bottom: '5%'
        },
        series: [{
          name: '年度总项目数据查询',
          type: 'map',
          geoIndex: 0, // 不可缺少，否则无tooltip 指示效果

          data: this.province === 'china' ? this.mapData : this.MData
        }]
      }
      return this.option
    }
  }
}
</script>
