<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2021-03-09 17:40:21
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2022-05-10 10:32:46
-->
<template>
  <el-drawer class="previewFile" :title="name+'预览'" :visible.sync="visible" size='80%' append-to-body>
    <el-container v-loading='loading'>
        <!-- {{choseData}} -->

      <el-aside width="200px">
         <!-- <el-button size="small" @click="up">上传</el-button> -->
         <!-- <el-button size="small">批量下载</el-button> -->

        <el-tree :data="fileData"  highlight-current :props="defaultProps" @node-click="handleNodeClick">
          <div slot-scope="{node}">
            {{node.label}}
          </div>
        </el-tree>
      </el-aside>
      <div style="width:100%;height:calc(100vh - 112px);border:none;padding:10px;" >
        <iframe v-if="filterType(choseData.suffixFileName ||choseData.url)==1" :src="url + fomateUrl(choseData.id)" id="iframe" style="width:100%;height:100%;border:none;"></iframe>
         <div style="height:100%;" v-else-if="filterType(choseData.suffixFileName||choseData.url)==2">
            <div class="imgBar" >
              <i class="el-icon-refresh-left" @click="rotateChange(90)"></i>
              <i class="el-icon-refresh-right" @click="rotateChange(-90)"></i>
              <i class="el-icon-circle-plus-outline" @click="zoomChange(10,'add')"></i>
              <i class="el-icon-remove-outline" @click="zoomChange(-10,'de')"></i>
              <i class="el-icon-download" @click="download(choseData.id)"></i>
              <!-- <i class="el-icon-full-screen" @click="miner(choseData.id)"></i> -->
            </div>
        <div style="height:100%;width:100%;overflow:auto;" >
          <img style="margin-top:35px;" :style="`transform:rotate(${rotate}deg);width:${zoom}%`" :src="`${aipUrl}/sys/oss/minioPreview?id=${choseData.id}&letterId=${params.letterId}`" alt="">
        </div>
        </div>
        <div style="text-align:center;" v-else>
          暂不支持预览该类型文件
        </div>
      </div>
    </el-container>
  </el-drawer>
</template>
<script>
import { getUUID } from '@/utils'
export default {
  data () {
    return {
      name: '',
      visible: false,
      url: '',
      aipUrl: '',
      loading: false,
      params: {},
      fileData: [],
      choseData: {},
      checkDefault: [],
      defaultProps: {
        label: 'name'
      },
      rotate: 0,
      zoom: 100

    }
  },
  created () {
    this.url = window.SITE_CONFIG['fileView']
    this.aipUrl = window.SITE_CONFIG['apiURL']
  },
  watch: {
    'checkDefault': function (newVal, oldVal) {
      if (newVal) {
        this.$nextTick(() => {
          document.querySelector('.el-tree-node__content').click()
        })
      }
    }
  },
  methods: {
    init () {
      this.getFileData()
    },
    up () {
      this.$parent.importHandle()
    },
    rotateChange (reg) {
      this.rotate = this.rotate + reg
    },

    zoomChange (zoom, type) {
      if (this.zoom === 150 && type === 'add') {
        this.$message.warning('已放大至最大')
        return
      }
      if (this.zoom === 70 && type === 'de') {
        this.$message.warning('已缩小至最小')
        return
      }
      this.zoom = this.zoom + zoom
    },
    download (file) {
      window.open(
        `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${file}`
      )
    },
    getFileData () {
      this.loading = true
      this.$http
        .get(`/letter/guarantee/getOssByFileType`, {
          params: this.params
        })
        .then(({ data: res }) => {
          this.loading = false
          this.fileData = res.data
          this.checkDefault.push(this.fileData[0].id)
          this.visible = true
          this.handleNodeClick(this.fileData[0])
        })
    },
    fomateUrl (id) {
      let apiURL = window.SITE_CONFIG['apiURL']
      return encodeURIComponent(
        apiURL +
          '/sys/oss/minioPreview?id=' +
          id +
          `&letterId=${this.params.letterId}` +
          '&uuid=' +
          getUUID() +
          `&fullfilename=file${new Date().getTime()}.pdf` +
          '&_t=' +
          new Date().getTime()
      )
    },
    handleNodeClick (data) {
      this.choseData = data
    },
    filterType (file) {
      if (file) {
        var extension = file.substring(file.lastIndexOf('.') + 1)
        if (extension === 'pdf' || extension === 'PDF') {
          return 1
        } else if (
          extension === 'png' ||
          extension === 'jpg' ||
          extension === 'jpeg'
        ) {
          return 2
        } else {
          return 0
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.previewFile /deep/ .is-current .el-tree-node__content{
  background: #ddebfc;
}
.imgBar{
  background-color: rgba(71, 71, 71, 1);
  height:32px;
  text-align: center;
  color: white;
  line-height: 32px;
  i{
    display: inline-block;
    font-size: 16px;
  margin: 0 5px;
  cursor: pointer;
  }
}
</style>
