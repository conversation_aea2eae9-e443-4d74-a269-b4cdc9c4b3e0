<template>
  <div class="zoBox">

    <div class="zonging" style="">
      <el-input placeholder="输入关键字进行过滤" v-model="filterText">
      </el-input>
      <el-scrollbar style="height: 100%">
        <el-tree v-loading="treeLoading" class="filter-tree" :data="treeData" :expand-on-click-node="false" :props="defaultProps" default-expand-all :current-node-key="currentNodekey" node-key="projectCode" :highlight-current=true
          :filter-node-method="filterNode" @node-click="handleNodeClick" ref="tree">
        </el-tree>
      </el-scrollbar>
    </div>

    <div class="zonging" style="" v-if="multiple">
      <el-input placeholder="输入关键字进行过滤" v-model="filterText2">
      </el-input>
      <el-scrollbar style="height: 100%">

        <el-tree v-loading="treeLoadingEstimation" class="filter-tree" :data="treeDataEstimation" :props="defaultProps2" default-expand-all :current-node-key="currentNodekey" node-key="projectCode"
          :highlight-current=true :filter-node-method="filterNodeEstimation" @node-click="handleNodeEstimation" ref="tree2">
        </el-tree>
      </el-scrollbar>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      treeLoading: false,
      treeLoadingEstimation: false,
      dataForm: {},
      filterText: '',
      filterText2: '',
      treeData: [],
      treeDataEstimation: [],
      currentNodekey: '' // 默认选中的节点树
    }
  },
  props: {
    getTreeDataURL: {
      type: String,
      default () {
        return '/sys/dept/tree'
      }
    },
    getTreeDataEstimationURL: {
      type: String,
      default: '/monitoring/etlproject/tree'
    },
    defaultProps: {
      type: Object,
      default () {
        return {
          children: 'children',
          label: 'name'
        }
      }
    },
    defaultProps2: {
      type: Object,
      default () {
        return {
          children: 'children',
          label: 'name'
        }
      }
    },
    multiple: {
      type: Boolean,
      default: false
    },
    defaultActive: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val)
    },
    filterText2 (val) {
      this.$refs.tree2.filter(val)
    }
  },
  created () {},
  activated () {
    this.getTreeDataList()
  },
  methods: {
    getTreeDataList: function () {
      this.treeLoading = true
      this.$http
        .get(this.getTreeDataURL + `/${this.$store.state.user.id}`, {
          params: {}
        })
        .then(({ data: res }) => {
          this.treeLoading = false
          if (res.code !== 0) {
            this.treeData = []
            return
          }
          this.treeData = res.data
          // 默认显示第一节点的数据

          // // // console.log(this.treeData[0])

          if (this.treeData && this.treeData.length > 0) {
            this.currentNodekey = this.treeData[0].projectCode
            this.$nextTick(() => {
              this.$refs.tree.setCurrentKey(this.currentNodekey)
            })
            // // // console.log('this.treeData:', this.treeData)
            if (this.defaultActive) {
              this.emitData(res.data[0])
            }
          }
        })
        .catch(() => {
          this.treeLoading = false
        })
    },
    // multiple
    getTreeDataListEstimation (projectCode, code) {
      this.treeLoadingEstimation = true
      this.$http
        .get(this.getTreeDataEstimationURL, {
          params: {
            projectCode: projectCode,
            code: code
          }
        })
        .then(({ data: res }) => {
          this.treeLoadingEstimation = false
          if (res.code !== 0) {
            this.treeDataEstimation = []
            return
          }
          this.treeDataEstimation = res.data
        })
        .catch(() => {
          this.treeLoadingEstimation = false
        })
    },
    emitData (data) {
      // console.log('1', data)
      this.$emit('handleNodeClick', data)
    },

    handleNodeClick (data, node) {
      this.$emit('handleNodeClick', data)
      if (this.multiple) {
        this.getTreeDataListEstimation(data.projectCode, '')
      }
    },
    handleNodeEstimation (data) {
      // this.emit(data)
      this.$emit('handleNodeEstimation', data)
    },
    filterNodeEstimation (value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    filterNode (value, data) {
      if (!value) return true
      return data.projectName.indexOf(value) !== -1
    }
  }
}
</script>
<style lang='scss'>
.zoBox {
  display: flex;
  .zonging {
    width: 200px;
    background-color: #f6f6f6;
    border: 1px solid #ddd;
    // border-right: none;
    height: 85vh;
    .el-scrollbar {
      height: calc(calc(85vh - 65px)) !important;
    }
    .el-input {
      padding: 20px 10px 5px;
    }
    .el-tree {
      background-color: #f6f6f6 !important;
    }
  }
}
</style>
