<!--
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-08-26 09:43:10
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-07-06 15:54:56
-->
<template>
  <div class="pdf" v-show="fileType === 'pdf'">
    <pdf :src="src" :page="currentPage" @num-pages="pageCount=$event" @page-loaded="currentPage=$event" @loaded="loadPdfHandler">
    </pdf>

  </div>
</template>
<script>
// import pdf from 'vue-pdf'
export default {
  // components: { pdf },
  data () {
    return {
      id: '',
      currentPage: 0, // pdf文件页码
      pageCount: 0, // pdf文件总页数
      fileType: 'pdf', // 文件类型
      src: '' // pdf文件地址
    }
  },
  created () {
    // 有时PDF文件地址会出现跨域的情况,这里最好处理一下
    this.id = this.$route.query.seeLetterId
    this.src =
      `${window.SITE_CONFIG['apiURL']}` +
      '/letter/bgguaranteeletter/getPDFUrlById/' +
      this.id
  },
  method: {
    // 改变PDF页码,val传过来区分上一页下一页的值,0上一页,1下一页
    changePdfPage (val) {
      // console.log(val)
      if (val === 0 && this.currentPage > 1) {
        this.currentPage--
        // console.log(this.currentPage)
      }
      if (val === 1 && this.currentPage < this.pageCount) {
        this.currentPage++
        // console.log(this.currentPage)
      }
    },

    // pdf加载时
    loadPdfHandler (e) {
      this.currentPage = 1 // 加载的时候先加载第一页
    }
  }
}
</script>
