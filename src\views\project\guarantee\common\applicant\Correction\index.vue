<template>
  <div>
    <el-form :model="dataForm" ref="dataForm" :rules="dataRule" style="margin:15px 30px 50px;" label-width="140px">
      <el-row :gutter="20">
        <el-card class="box-card" shadow="never">
          <div slot="header" class="clearfix">
            <span class="title">批改</span>
          </div>
          <el-col :xs="12" :lg="12">
            <div class="formCon">
              <el-col :xs="24" :lg="24">
                <el-form-item label="批改类型" prop="type">
                  <el-select v-model="dataForm.type" size="small" placeholder="请选择">
                    <span v-for="item in options" :key="item.value">
                      <el-option :label="item.label" :value="item.value" v-if="includeOp(item.key)">
                      </el-option>
                    </span>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :lg="24" v-if="dataForm.type ==='01'&&$route.query.type=='tbbxbh'">
                <el-form-item label="保函截止时间" prop="enddate">
                  <el-date-picker :picker-options="pickerOptions" default-time="23:59:59" v-model="time" clearable size="small" @change="timeChange" type="datetime" placeholder="选择日期时间">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :lg='24' :xl="24" v-if="dataForm.type ==='01'&&$route.query.type!='tbbxbh'">
                <el-form-item label="开标日期" prop="startDate">
                  <el-date-picker :value-format="$route.query.key==='XTDB'?'yyyy-MM-dd':'yyyy-MM-dd HH:mm:ss'" v-model="startDateV" :type="$route.query.key==='XTDB'?'date':'datetime'"
                    placeholder="选择日期时间">
                  </el-date-picker>
                </el-form-item>

                <el-form-item
                  :label="$route.query.key==='HZDB'||$route.query.key==='JXDB'||$route.query.key==='HAZDB' || $route.query.key==='JHY' || $route.query.key==='YFDDB'|| $route.query.key==='TAIBDB'|| $route.query.key==='GS'|| $route.query.key==='SHB'?'投标有效期':'保函有效期'"
                  prop="tenderValid">
                  <el-autocomplete class="inline-input" v-model.trim="dataForm.tenderValid" :fetch-suggestions="querySearch" placeholder="请输入内容"></el-autocomplete> &emsp;天
                </el-form-item>
                <el-form-item label="保函有效截止日期">
                  {{enddate}}
                  <!-- {{this.dataForm.enddate}} -->
                </el-form-item>
              </el-col>

              <el-col :xs="24" :lg="24" v-if="dataForm.type ==='04'">
                <el-form-item label="被保人名称" prop="insuredname">
                  <el-input v-model="dataForm.insuredname" size="small" class="wd180" style="width:100%;" placeholder="请输入被保人名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :lg="24" v-if="dataForm.type ==='60'">
                <el-form-item label="投保人名称" prop="insuredname">
                  <el-input v-model="dataForm.insuredname" size="small" class="wd180" style="width:100%;" placeholder="请输入投保人名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :lg="24" v-if="dataForm.type ==='08'">
                <el-form-item label="项目名称" prop="businessdetail">
                  <!-- <el-input v-model="dataForm.businessdetail" size="small" class="wd180" style="width:100%;" placeholder="请输入项目名称"></el-input> -->

                  <el-input clearable v-model.trim="dataForm.businessdetail" size="medium" class="wd180" style="width:100%;" placeholder="请输入要办理的项目及标段名称">
                  </el-input>
                  <el-col :span="24">
                    <div style="color:#909399;font-size:12px;line-height:26px;">注：请严格按照招标文件填写项目名称！</div>
                  </el-col>

                </el-form-item>
              </el-col>
              <el-col :xs="24" :lg="24" v-if="dataForm.type ==='14'">
                <xxUp ref="xxUp" :dataForm="dataForm"></xxUp>

              </el-col>
              <el-col :xs="24" :lg="24" v-if="dataForm.type ==='15'">
                <el-form-item label="担保金额" prop="guaranteeAmount">

                  <el-input-number placeholder="请输入担保金额" size="medium" style='width:180px;' v-model="guaranteeAmount" @change='change' controls-position="right" :precision="6" :step="0.000001"
                    :min="0" :max="1000"> <template slot="append">万元</template></el-input-number> 万元
                </el-form-item>
                <!-- <xxUp ref="xxUp" :dataForm="dataForm"></xxUp> -->
              </el-col>
              <el-col :xs="24" :lg="24" v-if="pakeage.isUploadCorrectionFile&&dataForm.type">
                <el-form-item label="说明文件">
                  <el-upload class="upload-demo" ref="cqupload" :on-preview='onPreview' :on-remove='handleRemove' :action="uploadUrl" :data='upData' :headers="myHeaders" :before-remove="BeforeRemove"
                    :on-success='SuccessHandle' :before-upload="$route.query.key === 'hybx'?beforeUpload2:beforeUpload" :limit="1" :on-exceed="handleExceed" :file-list="fileList">
                    <el-button size="small" type="primary">点击上传</el-button>
                    <div slot="tip" class="el-upload__tip">要求格式：{{$route.query.key === 'hybx'?'zip':'jpg,jpeg,png,pdf,zip'}}</div>
                  </el-upload>
                </el-form-item>
              </el-col>
            </div>
          </el-col>
        </el-card>
      </el-row>

    </el-form>

    <div class="foot">
      <span>
        <el-button type="danger" size="small" @click="goPrveStep()">返回</el-button>
        <el-button type="primary" :loading="btnLoading" size="small" @click="submit()">提交</el-button>
      </span>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
import debounce from 'lodash/debounce'
import bhuploadMixin from '@/mixins/bhuploadMixin'
import xxUp from '@/views/project/guarantee/components/xxUp'
import { getpakeage } from '@/api/apiList/common'
export default {
  mixins: [bhuploadMixin],
  components: { xxUp },
  data () {
    return {
      dataForm: {
        id: '',
        type: '',
        key: '',
        insuredname: '',
        businessdetail: '',
        guaranteeAmount: '',
        enddate: '',
        endhour: '',
        bankAccount: '',
        moneyAmount: '',
        payTime: '',
        contacts: '',
        mobile: ''
      },
      guaranteeAmount: '',
      startDateV: '',
      pakeage: {},
      restaurants: [],
      formReset: ['bgGuaranteeApply', 'projectInfo', 'bgGuaranteeMailing'],
      fileForm: {},
      filePer: {
        zbwj: 1,
        wts: 1,
        cqdy: 1,
        wtht: 1,
        wtrfront: 0,
        wtrbehind: 0,
        frfront: 0,
        frbehind: 0
      },
      fileList: [],
      visible: false,
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() < Date.now()
        }
      },
      time: '',
      projectName: '',
      bidName: '',
      btnLoading: false,
      options: []
    }
  },
  watch: {
    dataForm: {
      handler (a) {
        // if (a.startDate) {
        //   this.$set(this.dataForm, 'startDate', this.moment(a.startDate).format('YYYY-MM-DD HH:mm:ss'))
        // }
      },
      deep: true
    },
    startDateV (a) {
      // console.log(a)
      this.$set(
        this.dataForm,
        'startDate',
        a ? this.moment(a).format('YYYY-MM-DD HH:mm:ss') : ''
      )
    }
  },
  activated () {
    this.init()
  },
  created () {
    console.log(this.dataForm)
    this.getDetail()
    this.setOptions()
    this.restaurants = this.loadAll()
  },
  computed: {
    enddate () {
      if (this.dataForm.startDate && this.dataForm.tenderValid) {
        let startDate = this.moment(this.dataForm.startDate).format(
          'YYYY/MM/DD HH:mm:ss'
        )
        let date = new Date(startDate).setTime(
          new Date(startDate).getTime() +
            (this.$route.query.key === 'HZDB' ||
            this.$route.query.key === 'JXDB' ||
            this.$route.query.key === 'HAZDB' ||
            this.$route.query.key === 'JHY' ||
            this.$route.query.key === 'YFDDB' ||
            this.$route.query.key === 'TAIBDB' ||
            this.$route.query.key === 'GS' ||
            this.$route.query.key === 'SHB'
              ? 3600 * 1000 * 24 * 30
              : 0) +
            3600 * 1000 * 24 * (Number(this.dataForm.tenderValid) - 1)
        )
        console.log(date)
        this.$set(
          this.dataForm,
          'endDate',
          this.moment(date).format('YYYY-MM-DD') === 'Invalid date'
            ? ''
            : this.moment(date).format('YYYY-MM-DD')
        )
      } else {
        this.$set(this.dataForm, 'endDate', '')
      }
      return this.dataForm.endDate === '' ? '--' : this.dataForm.endDate
    },
    dataRule () {
      return {
        insuredname: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        businessdetail: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        startDate: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        tenderValid: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        enddate: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ]
      }
    }
  },
  methods: {
    moment,
    setOptions () {
      getpakeage({
        issueCode: this.$route.query.key,
        guaranteeTypeCode: this.$route.query.type
      }).then(({ data: res }) => {
        this.pakeage = res.data
        this.options = [
          {
            label: '变更保函期限',
            value: '01',
            key: this.pakeage.isCorrectionDate === '01'
          },
          {
            label: '保证金额',
            value: '15',
            key: this.pakeage.isCorrectionAmount === '15'
          },
          {
            label: '批改投保人信息',
            value: '60',
            key: this.pakeage.isCorrectionTbr === '60'
          },
          {
            label: '批改被保人信息',
            value: '04',
            key: this.pakeage.isCorrectionBbr === '04'
          },
          {
            label: '批改项目信息',
            value: '08',
            key: this.pakeage.isCorrectionProjectName === '08'
          },
          {
            label: '支付方式',
            value: '14',
            key: this.pakeage.isCorrectionPayMethod === '14'
          }
        ]
      })
    },
    // projectNameChange (val) {
    //   this.$set(
    //     this.dataForm,
    //     'businessdetail',
    //     this.bidName ? this.projectName + '-' + this.bidName : this.projectName
    //   )
    // },
    // bidNameChange (val) {
    //   this.$set(
    //     this.dataForm,
    //     'businessdetail',
    //     this.bidName ? this.projectName + '-' + this.bidName : this.projectName
    //   )
    // },
    beforeUpload2 (file) {
      var extension = file.name.substring(file.name.lastIndexOf('.') + 1)
      var lastName = extension !== 'zip'
      if (lastName) {
        this.$message.warning('文件要求格式为：zip')
      }

      return !lastName
    },
    change (val) {
      if (val) {
        this.$set(this.dataForm, 'guaranteeAmount', (val * 1000000) / 100)
      } else {
        this.$set(this.dataForm, 'guaranteeAmount', 0)
      }
    },
    emitCA (type) {
      console.log(type)
      this.showCaDia('caModel', this.fileForm, type)
    },
    querySearch (queryString, cb) {
      var restaurants = this.restaurants
      cb(restaurants)
    },
    loadAll () {
      return [
        { value: '30' },
        { value: '60' },
        { value: '90' },
        { value: '120' },
        { value: '150' }
      ]
    },
    showCaDia (name, data, type) {
      this.visible = true
      console.log(this.$route.params)
      this.$nextTick(() => {
        this.$refs[name].dataForm = {}
        var obj = {
          ...data
        }
        this.$refs[name].dataForm = obj
        this.$refs[name].type = type
        this.$refs[name].init()
      })
    },
    getDetail () {
      this.loading = true
      this.$http
        .get(
          `/letter/guarantee/getDetailByLetterId/?letterId=` +
            this.$route.query.id
        )
        .then(({ data: res }) => {
          this.loading = false
          if (res.code !== 0) {
            this.setObj()
            return
          }
          this.fileForm = res.data
          this.filePermission()
          this.setObj()
        })
    },
    filePermission (code) {
      this.$http
        .get(`/sys/params/getValue/${this.$route.query.key}`)
        .then(({ data: res }) => {
          this.filePer = {
            ...this.filePer,
            ...JSON.parse(res.data)
          }
          console.log(this.filePer)
        })
    },
    setObj () {
      this.formReset.map((a) => {
        if (!this.fileForm[a]) this.$set(this.fileForm, a, {})
      })
      this.$set(this.fileForm, 'insuranceType', this.$route.query.type)
    },
    includeOp (type) {
      return type
    },
    init () {
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.upData = {
          letterId: this.$route.query.id,
          fileType: '9'
        }
        this.dataForm.id = this.$route.query.id
        this.dataForm.key = this.$route.query.key
        this.setOptions()
      })
    },

    goPrveStep () {
      this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
        (item) => item.name !== 'Correction'
      )
      this.$router.push({
        path: `letter-bgguaranteeletterSqf`
      })
    },
    timeChange (a) {
      if (a) {
        this.dataForm.enddate = this.moment(a).format('YYYY-MM-DD')
        this.dataForm.endhour = this.moment(a).format('HH:mm:ss')
      } else {
        this.dataForm.enddate = ''
        this.dataForm.endhour = ''
      }
      console.log(this.dataForm.enddate, this.dataForm.endhour)
    },
    // async submit () {
    //   let { data } = await this.$http.post('111')
    //   console.log(data)
    // },
    submit: debounce(
      function () {
        this.$refs['dataForm'].validate(async (valid) => {
          if (!valid) {
            return false
          }
          if (this.$route.query.key === 'hybx' && this.fileList === 0) {
            return this.$message.error('请上传说明文件')
          }
          this.btnLoading = true
          let { data } =
            this.$route.query.type !== 'tbbxbh'
              ? await this.$http.post(
                'letter/guarantee/guaranteeCorrect',
                this.dataForm
              )
              : await this.$http.post(
                'letter/bgguaranteeletter/correction',
                this.dataForm
              )
          // console.log(data)
          this.btnLoading = false
          if (data.code !== 0) {
            return this.$message.error(data.msg)
          }
          if (data.code === 0) {
            if (this.dataForm.type === '14') {
              this.$refs.xxUp.submit()
            }
            this.$message.success('批改申请成功！')
            this.goPrveStep()
          }
        })
      },
      1000,
      { leading: true, trailing: false }
    )
  }
}
</script>
<style lang="scss" scoped>
.foot {
  width: 100%;
  background: #fff;
  position: fixed;
  bottom: 0;
  padding: 10px 0;
  border-top: 1px solid #ddd;
  background: #f9f9f9;
  text-align: center;
  left: 0px !important;
  z-index: 999;
  span {
    text-align: center;
  }
}
.el-upload__tip {
  color: #f56c6c;
}
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
</style>
