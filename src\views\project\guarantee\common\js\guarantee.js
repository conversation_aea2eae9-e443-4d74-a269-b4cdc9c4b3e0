/*
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-10-09 09:15:53
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-06-22 08:51:27
 */
const guaranteeList = [
  {
    name: '投标保函',
    icon: require('@/assets/img/1-tbbh.png'),
    stauts: 1,
    code: 'tb'
  },
  {
    name: '履约保函',
    icon: require('@/assets/img/1-lybh.png'),
    stauts: 0,
    path: 'performance',
    code: 'ly'
  },
  {
    name: '预付款保函',
    icon: require('@/assets/img/1-yfk.png'),
    stauts: 0,
    userType: [1],
    path: 'performance',
    code: 'yfk'
  },
  {
    name: '支付保函',
    icon: require('@/assets/img/1-zf.png'),
    stauts: 0,
    userType: [1],
    path: 'performance',
    code: 'zf'
  },
  {
    name: '农民工工资保函',
    icon: require('@/assets/img/1-nmgbh.png'),
    stauts: 0,
    code: 'nmgbh'
  },
  {
    name: '质量保函',
    icon: require('@/assets/img/1-zlbh.png'),
    stauts: 0,
    code: 'zlbh'
  }
]
const mechanismList = [
  {
    name: '银行',
    icon: require('@/assets/img/2-yh.png'),
    code: 'bhyh',
    time: '48',
    explain: '银行投标保函时间安排，第一个工作日接单，支付后，第二个工作日出函。每天下午5点截止接单，每天下午四点至六点左右银行出单。月底最后一个工作日不出函。（银行保函为纸质扫描件）',
    stauts: 1,
    path: 'bankGuarantee',
    jurisdiction: ['tb']
  },
  {
    name: '担保',
    icon: require('@/assets/img/2-db.png'),
    code: 'dbbh',
    time: '24',
    explain: '担保保函开标前两日内不可退保！',
    stauts: 1,
    path: 'warrantGuarantee',
    jurisdiction: ['tb', 'ly', 'yfk', 'zf']
  },
  {
    name: '保险',
    icon: require('@/assets/img/2-bx.png'),
    code: 'bxbh',
    time: '24',
    stauts: 1,
    path: 'applyBgGuaranteeApplyFromFLogin',
    jurisdiction: ['tb']
  }
]
export { guaranteeList, mechanismList }
