<template>
  <el-dialog :visible.sync="visible" title="信息详情" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" label-position="left" v-loading='loading' ref="dataForm" label-width="120px">
      <el-card shadow='never'>
        <el-row :gutter="15">
          <div class="title">基础信息</div>
          <el-row >
            <el-col :xs="8" :lg="8">
              <el-form-item label="开票对象类型" prop="invoiceObject">
                <span>{{certType(dataForm.invoiceObject,"invoiceObjectoptions")}}</span>
              </el-form-item>
            </el-col>
            <el-col :xs="8" :lg="8">
              <el-form-item label="开票对象名称" prop="name">
                <span>{{dataForm.name}}</span>
              </el-form-item>
            </el-col>
            <el-col :xs="8" :lg="8">
              <el-form-item label="发票类型" prop="invoiceType">
                <span>{{certType(dataForm.invoiceType,"invoiceTypeoptions")}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row >
            <el-col :xs="8" :lg="8">
              <el-form-item label="发票抬头" prop="invoiceTitle">
                <span>{{dataForm.invoiceTitle}}</span>
              </el-form-item>
            </el-col>
            <el-col :xs="8" :lg="8">
              <el-form-item label="纳税人类型" prop="taxpayerType">
                <span>{{certType(dataForm.taxpayerType,"taxpayerTypeoptions")}}</span>
              </el-form-item>
            </el-col>
            <el-col :xs="8" :lg="8">
              <el-form-item label="纳税人识别号" prop="taxpayerNumber">
                <span>{{dataForm.taxpayerNumber}}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="8" :lg="8">
              <el-form-item label="接收手机号" prop="electronicInvoicePhone">
                <span>{{dataForm.electronicInvoicePhone}}</span>
              </el-form-item>
            </el-col>

            <el-col :xs="8" :lg="8">
              <el-form-item label="电话" prop="phoneNumber">
                <span>{{dataForm.phoneNumber}}</span>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :lg="24">
              <el-form-item label="地址" prop="address">
                <span>{{dataForm.address}}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-row>
        <el-row :gutter="15" v-if="dataForm.invoiceType==='2'">
          <div class="title">银行账户信息</div>
          <el-col :xs="8" :lg="8">
            <el-form-item label="开户行名称" prop="bankAccountName">
              <span>{{dataForm.bankAccountName}}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="8" :lg="8">
            <el-form-item label="银行账户号码" prop="bankAccountNo">
              <span>{{dataForm.bankAccountNo}}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="8" :lg="8">
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <div class="title">收件信息</div>
          <el-col :xs="8" :lg="8">
            <el-form-item label="收件人" prop="mailingUser">
              <span>{{dataForm.mailingUser}}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="8" :lg="8">
            <el-form-item label="收件编码" prop="mailingCode">
              <span>{{dataForm.mailingCode}}</span>
            </el-form-item>
          </el-col>

          <el-col :xs="8" :lg="8">
            <el-form-item label="接收邮箱" prop="electronicInvoiceEmail">
              <span>{{dataForm.electronicInvoiceEmail}}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="8" :lg="8">
            <el-form-item label="收件电话" prop="mailingTel">
              <span>{{dataForm.mailingTel}}</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :lg="24">
            <el-form-item label="收件地址" prop="mailingAddress">
              <span>{{dataForm.mailingAddress}}</span>
            </el-form-item>
          </el-col>

        </el-row>
      </el-card>

    </el-form>
    <template slot="footer">
      <el-button type="primary" @click="visible = false">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import dictionaries from '@/views/project/guarantee/components/dictionaries'
export default {
  mixins: [dictionaries],
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      loading: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        invoiceType: '',
        invoiceObject: '',
        taxpayerType: '',
        taxpayerNumber: '',
        phoneNumber: '',
        address: '',
        bankAccountName: '',
        bankAccountNo: '',
        mailingCode: '',
        mailingUser: '',
        mailingTel: '',
        mailingAddress: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        orgId: ''
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    certType (val, name) {
      // console.log(this.options[val])
      var aa = this.options[name].filter((a) => a.dictCode === val)
      return aa[0].dictName
    },
    init () {
      this.visible = true
      this.loading = true
      this.$nextTick(() => {
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http
        .get(`/letter/bgguaranteeinvoice/${this.dataForm.id}`)
        .then(({ data: res }) => {
          this.loading = false

          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  margin-bottom: 20px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 700;
  font-size: 16px;
  line-height: 1.6;
  border-left: 4px #409eff solid;
  text-indent: 10px;
}
</style>
