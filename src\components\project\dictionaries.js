
export default {
  data () {
    /* eslint-disable */
    return {
        getDicListURL: '/sys/dict/type/',
        certTermTypeoptions: [],
        insuranceTypeoptions: [],
        invoiceTypeoptions: [],
        taxpayerTypeoptions: [],
        invoiceObjectoptions: [],
        certTypeoptions: [],
        pickerOptions: {
          // disabledDate (time) {
          //   return time.getTime() > Date.now()
          // },
          shortcuts: [
            {
              text: '今天',
              onClick (picker) {
                picker.$emit('pick', new Date())
              }
            },
            {
              text: '昨天',
              onClick (picker) {
                const date = new Date()
                date.setTime(date.getTime() - 3600 * 1000 * 24)
                picker.$emit('pick', date)
              }
            },
            {
              text: '一周前',
              onClick (picker) {
                const date = new Date()
                date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
                picker.$emit('pick', date)
              }
            }
          ]
        },
        bgGuaranteeApply: {
          id: '',
          code: '',
          name: '',
          certType: '',
          certCode: '',
          certTerm: '',
          certTermType: '',
          linkman: '',
          linkmanTel: '',
          corporation: '',
          email: '',
          phoneNumber: '',
          fax: '',
          postcode: '',
          platformCode: '',
          address: '',
          description: '',
          registerAddress: '',
          registerCapital: '',
          registerTime: '',
          isVip: '',
          deptId: '',
          status: 1,
          creator: '',
          createDate: '',
          updater: '',
          updateDate: ''
        },
        bginsurancebbr: {
          code: '',
          name: '',
          certType: '',
          certCode: '',
          certTerm: '',
          certTermType: '',
          linkman: '',
          linkmanTel: '',
          corporation: '',
          email: '',
          phoneNumber: '',
          fax: '',
          postcode: '',
          platformCode: '',
          address: '',
          description: '',
          registerAddress: '',
          registerCapital: '',
          registerTime: '',
          isVip: '',
          deptId: '',
          status: 1,
          creator: '',
          createDate: '',
          updater: '',
          updateDate: ''
        },
        bginsurancesyr: {
          code: '',
          name: '',
          certType: '',
          certCode: '',
          certTerm: '',
          certTermType: '',
          linkman: '',
          linkmanTel: '',
          corporation: '',
          email: '',
          phoneNumber: '',
          fax: '',
          postcode: '',
          platformCode: '',
          address: '',
          description: '',
          registerAddress: '',
          registerCapital: '',
          registerTime: '',
          isVip: '',
          deptId: '',
          status: 1,
          creator: '',
          createDate: '',
          updater: '',
          updateDate: ''
        },
        bgbiddingproject: {
          code: '',
          name: '',
          bidOpenDate: '',
          biddingid: '',
          guaranteeAmount: '',
          tenderee: '',
          tendereeAgent: '',
          biddingName: '',
          biddingToOffces: '',
          remars: '',
          bidStartDate: '',
          bidEndDate: '',
          platformCode: '',
          deptId: '',
          status: 1,
          creator: '',
          createDate: '',
          updater: '',
          updateDate: ''
        },
        bginsuranceinfo: {
          code: '',
          name: '',
          insuranceType: '',
          startDate: '',
          endDate: '',
          insuranceOrgid: '',
          insuranceOrg: '',
          insuranceCode: '',
          insuranceUrl: '',
          insuranceAddr: '',
          insuranceChange: '',
          guaranteeAmount: '',
          guaranteePrice: '',
          platformCode: '',
          deptId: '',
          status: 1,
          creator: '',
          createDate: '',
          updater: '',
          updateDate: ''
        },
        bgguaranteeinvoice: {
          code: '',
          name: '',
          invoiceType: '',
          invoiceObject: '',
          taxpayerType: '',
          taxpayerNumber: '',
          phoneNumber: '',
          address: '',
          bankAccountName: '',
          bankAccountNo: '',
          mailingCode: '',
          mailingUser: '',
          mailingTel: '',
          mailingAddress: '',
          deptId: '',
          status: 1,
          creator: '',
          createDate: '',
          updater: '',
          updateDate: ''
        }
    }
  },
  activated () {

  },
  filters:{
    filterInType(val){
      // console.log(this.insuranceTypeoptions)
      // val = this.insuranceTypeoptions.filter(a=> a == val)
      return val
    }
  },
  created () {
    this.getCertTermTypeInfo()
    this.getCertTypeTTInfo()
    this.getInvoiceObjectInfo()
    this.getInsuranceTypeInfo()
    this.getInvoiceTypeInfo()
    this.getTaxpayerTypeInfo()
  },
  computed: {
    dataRule () {
      return {
        name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certTerm: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certTermType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        linkman: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        linkmanTel: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        phoneNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        address: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    },
    dataRule2 () {
      return {
        name: [
          { required:this.fromType!==2, message: '必填项不能为空', trigger: 'blur' }
        ],
        certType: [
          { required:this.fromType!==2, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certCode: [
          { required:this.fromType!==2,  message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certTerm: [
          { required:this.fromType!==2,  message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certTermType: [
          { required:this.fromType!==2,  message: this.$t('validate.required'), trigger: 'blur' }
        ],
        linkman: [
          { required:this.fromType!==2,  message: this.$t('validate.required'), trigger: 'blur' }
        ],
        linkmanTel: [
          { required:this.fromType!==2, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        phoneNumber: [
          { required:this.fromType!==2,  message: this.$t('validate.required'), trigger: 'blur' }
        ],
        address: [
          {required:this.fromType!==2,   message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    },
    // dataRule6 () {
    //     return {
    //       name: [
    //         { required: this.radio===1, message: this.$t('validate.required'), trigger: 'blur' }
    //       ],
    //       certType: [
    //         { required: this.radio===1, message: this.$t('validate.required'), trigger: 'blur' }
    //       ],
    //       certCode: [
    //         { required: this.radio===1, message: this.$t('validate.required'), trigger: 'blur' }
    //       ],
    //       certTerm: [
    //         { required: this.radio===1, message: this.$t('validate.required'), trigger: 'blur' }
    //       ],
    //       certTermType: [
    //         { required: this.radio===1, message: this.$t('validate.required'), trigger: 'blur' }
    //       ],
    //       linkman: [
    //         { required: this.radio===1, message: this.$t('validate.required'), trigger: 'blur' }
    //       ],
    //       linkmanTel: [
    //         { required: this.radio===1, message: this.$t('validate.required'), trigger: 'blur' }
    //       ],
    //       phoneNumber: [
    //         { required: this.radio===1, message: this.$t('validate.required'), trigger: 'blur' }
    //       ],
    //       address: [
    //         { required: this.radio===1, message: this.$t('validate.required'), trigger: 'blur' }
    //       ]
    //     }
    //   },
    dataRule3 () {
      return {
        name: [
          {  message: this.$t('validate.required'), trigger: 'blur' }
        ],
        biddingName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        bidStartDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        bidEndDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]

      }
    },
    dataRule4 () {
      return {
        name: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        insuranceType: [
          { required: false,message: this.$t('validate.required'), trigger: 'blur' }
        ],
        startDate: [
          { required: false,message: this.$t('validate.required'), trigger: 'blur' }
        ],
        endDate: [
          {  required: false,message: this.$t('validate.required'), trigger: 'blur' }
        ],
        guaranteeAmount: [
          {required: false, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        guaranteePrice: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' }
        ]

      }
    },
    dataRule5 () {
      return {
        invoiceType: [
          { required:this.fromType!==2, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        invoiceObject: [
          { required:this.fromType!==2, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        taxpayerType: [
          {required:this.fromType!==2,  message: this.$t('validate.required'), trigger: 'blur' }
        ],
        taxpayerNumber: [
          { required:this.fromType!==2, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        phoneNumber: [
          { required:this.fromType!==2, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        address: [
          { required:this.fromType!==2, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        bankAccountName: [
          { required:this.fromType!==2, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        bankAccountNo: [
          { required:this.fromType!==2, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
      // 纳税人类型信息
      getTaxpayerTypeInfo (status) {
        this.$http.get(this.getDicListURL + 'taxpayerType').then(({ data: res }) => {
         
          this.taxpayerTypeoptions = {
            ...this.taxpayerTypeoptions,
            ...res.data.list
          }
        }).catch(() => {})
      },
      // 发票类型信息
      getInvoiceTypeInfo () {
        this.$http.get(this.getDicListURL + 'invoiceType').then(({ data: res }) => {
         
          this.invoiceTypeoptions = {
            ...this.invoiceTypeoptions,
            ...res.data.list
          }
        }).catch(() => {})
      },
      // 开票对象信息
      getInvoiceObjectInfo () {
        this.$http.get(this.getDicListURL + 'invoiceObject').then(({ data: res }) => {
         
          this.invoiceObjectoptions = {
            ...this.invoiceObjectoptions,
            ...res.data.list
          }
        }).catch(() => {})
      },
      // 保险类型信息
      getInsuranceTypeInfo () {
        this.$http.get(this.getDicListURL + 'insuranceType').then(({ data: res }) => {
         
          this.insuranceTypeoptions = {
            ...this.insuranceTypeoptions,
            ...res.data.list
          }
        }).catch(() => {})
      },
      // 证件有效期信息
      getCertTermTypeInfo () {
        this.$http.get(this.getDicListURL + 'certTermType').then(({ data: res }) => {
         
          this.certTermTypeoptions = {
            ...this.certTermTypeoptions,
            ...res.data.list
          }
        }).catch(() => {})
      },
      // 证件信息
      getCertTypeTTInfo () {
        this.$http.get(this.getDicListURL + 'certTypeTT').then(({ data: res }) => {
         
          this.certTypeoptions = {
            ...this.certTypeoptions,
            ...res.data.list
          }
        }).catch(() => {})
      },
  },
}
