<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2021-12-16 10:16:46
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-12-17 14:47:48
-->
<template>
  <el-card shadow="never" class="aui-card--fill">
    <h4>流程图</h4>
    <img :src="getDiagramImage()" class="image" />
    <h4>流转详情</h4>
    <div class="mod-sys__dict">
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle" style="width: 100%">
        <el-table-column prop="activityName" label="当前环节" header-align="center" align="center"></el-table-column>
        <el-table-column prop="assigneeName" label="处理人" header-align="center" align="center"></el-table-column>
        <el-table-column prop="startTime" label="任务开始时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="endTime" label="任务结束时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="comment" label="审核意见" header-align="center" align="center"></el-table-column>
        <el-table-column prop="durationInSeconds" label="任务时长（秒）" header-align="center" align="center" width="180"></el-table-column>
      </el-table>
      <el-pagination v-if="dataForm.pid === '0'" :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle"> </el-pagination>
    </div>
  </el-card>
</template>
<style scoped>
.image {
  width: 60%;
  display: block;
  margin: 0 auto 30px auto;
}
</style>

<script>
import mixinViewModule from '@/mixins/view-module'
import qs from 'qs'
import Cookies from 'js-cookie'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/common/historic/list',
        getDataListIsPage: false,
        createdIsNeed: false
      },
      dataForm: {
        processInstanceId: '',
        pid: ''
      }
    }
  },
  created () {
    this.init()
  },
  methods: {
    init () {
      this.dataForm.processInstanceId = this.$route.query.processInstanceId
      this.getDiagramImage()
      this.getDataList()
    },
    getDiagramImage () {
      const params = qs.stringify({
        access_token: Cookies.get('token'),
        processInstanceId: this.dataForm.processInstanceId
      })
      return `${window.SITE_CONFIG['apiURL']}/common/diagram/image?${params}`
    }
  }
}
// import qs from "qs";
// import useView from "@/hooks/useView";
// import { defineComponent, reactive, toRefs } from "vue";
// import { getToken } from "@/utils/cache";
// import app from "@/constants/app";
// export default defineComponent({
//   name: "RenProcessDetail",
//   setup() {
//     const state = reactive({
//       getDataListURL: "/flow/common/historic/list",
//       getDataListIsPage: false,
//       createdIsNeed: false,
//       dataForm: {
//         processInstanceId: "",
//         pid: ""
//       }
//     });
//     return { ...useView(state), ...toRefs(state) };
//   },
//   created() {
//     this.init();
//   },
//   methods: {
//     init() {
//       this.dataForm.processInstanceId = this.$route.query.processInstanceId as string;
//       this.getDiagramImage();
//       this.getDataList();
//     },
//     getDiagramImage() {
//       const params = qs.stringify({
//         access_token: getToken(),
//         processInstanceId: this.dataForm.processInstanceId
//       });
//       return `${app.api}/flow/common/diagram/image?${params}`;
//     }
//   }
// });
</script>
