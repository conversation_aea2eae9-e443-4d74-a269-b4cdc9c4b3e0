<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2020-08-14 10:58:10
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-09-30 16:13:06
-->

<template style='height:100%;'>
  <el-card>
    <div class="main3">
      <div class="title">欢迎来到&nbsp;<img style="width:95px;position:relative;top:-2px;" src="@/assets/img/logo2.png">！</div>
      <div class="m3_con">

        <div class="m3_info" @click="go('letter-bgguaranteeapplyRegister')">
          <p><img
              src="@/views/modules/page/pages/homePage/img/z121.png"
              alt=""
            ></p>
          <p >填写信息</p>
        </div>
        <i><img
            src="@/views/modules/page/pages/homePage/img/sj.png"
            alt=""
          ></i>
        <div class="m3_info" @click="go('project-guarantee-common-chooiseIn')">
          <p><img
              src="@/views/modules/page/pages/homePage/img/z13.png"
              alt=""
            ></p>
          <p>选择保函</p>
        </div>
        <i><img
            src="@/views/modules/page/pages/homePage/img/sj.png"
            alt=""
          ></i>
        <div class="m3_info" @click="go('letter-bgguaranteeletterSqf','40')">
          <p><img
              src="@/views/modules/page/pages/homePage/img/z15.png"
              alt=""
            ></p>
          <p>保费支付</p>
        </div>
        <i><img
            src="@/views/modules/page/pages/homePage/img/sj.png"
            alt=""
          ></i>
        <div class="m3_info" @click="go('letter-bgguaranteeletterSqf','50')">
          <p><img
              src="@/views/modules/page/pages/homePage/img/z16.png"
              alt=""
            ></p>
          <p>保函下发</p>
        </div>
      </div>
    </div>
  </el-card>
</template>
<script>
export default {
  methods: {
    go (name, letter) {
      this.$router.push({
        path: name,
        query: {
          letterStatus: letter
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.el-card {
  .main3 {
    margin: 0 auto;
    .title {

      font-size: 30px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: rgba(51, 51, 51, 1);
      line-height: 72px;
      text-align: center;
    }
    .m3_con {
      text-align: center;
      margin: 0 auto;
      display: flex;
      .m3_info {
        cursor: pointer;
        margin: 50px auto;
        transition: .3s ease-in-out all;
        p:last-child {
          margin-top: 30px;
        }
      }
      .m3_info:hover {
          transform: scale(1.1);
          // border: 1px solid #ddd;
      }
      i {
        line-height: 246px;
        margin: 0 32px;
      }
    }
  }
}
</style>
