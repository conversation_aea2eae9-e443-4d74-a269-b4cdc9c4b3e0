<template>
  <el-row :gutter="20" style="margin:15px 20px 0;">
    <el-form :model="dataForm" label-position="left" :rules="dataRule" ref="dataForm" label-width="140px">
      <el-card class="box-card" shadow='never'>
        <div slot="header" class="clearfix">
          <span class="title">项目信息（请根据招标文件如实填写）</span>
          <!-- {{pakeage}} -->
        </div>
        <div>
          <el-col :xs="24" :lg="24">
            <div class="formCon">
              <el-row :gutter="40">
                <el-col :xs="24" :lg='24' :xl="16">
                  <el-form-item label="项目所属区域" prop="regionId">
                    <span v-if="dataForm.regionName&&casCilck">{{dataForm.regionName | regFil}} &emsp;&emsp;<el-button type="text" @click="casCilck = false">修改</el-button></span>
                    <template v-else>
                      <el-cascader @expand-change='expandChange' :options="regionIds" v-model.trim="regionId" ref='region' @change='regionIdChange' size="medium" clearable class="wd180"
                        style="width:250px;" :props="props"></el-cascader>&emsp;&emsp;<el-button type="text" v-if="dataForm.regionName" @click="casCilck = true">取消</el-button>
                    </template>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="0">
                <el-col :xs="24" :lg='24' :xl="24">
                  <el-form-item label="项目标段名称" prop="projectName">
                   <el-input clearable v-model.trim="dataForm.projectName"  @keyup.native="trimLR('projectName')" size="medium" class="wd180" style="width:100%;" placeholder="请输入要办理的项目及标段名称">
                    </el-input>
                    <el-col :span="24">
                      <div style="color:#909399;font-size:12px;line-height:26px;">注：请严格按照招标文件填写项目名称！</div>
                    </el-col>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">

                <el-col :xs="24" :lg='24'>
                  <el-form-item label="招标人名称" prop="bbrName">
                    <el-input v-model.trim="dataForm.bbrName" @input='bbrNameChange' size="medium" style="width:100%;" class="wd180" placeholder="招标人名称"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg='24'>
                  <el-form-item label="招标代理" prop="tendereeAgent">
                    <el-input v-model.trim="dataForm.tendereeAgent" size="medium" style="width:100%;" class="wd180" placeholder="招标代理"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg='24' :xl="24">
                  <el-form-item label="开标日期" prop="startDate">
                    <el-date-picker size="medium" class="wd240" @change="tbChange" value-format="yyyy-MM-dd" style="width:100%;" v-model="dataForm.startDate" type="date" placeholder="选择开标日期"
                      align="right" :picker-options="pickerOptions">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg='24' :xl="24">
                  <el-form-item label="保函有效期" prop="tenderValid">
                    <el-autocomplete class="inline-input" v-model.trim="dataForm.tenderValid" :fetch-suggestions="querySearch" placeholder="请输入内容"></el-autocomplete> &emsp;天
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg='24' :xl="24">
                  <el-form-item label="保函有效截止日期" prop="endDate">
                    {{endDate}}
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg='24' :xl="24">
                  <el-form-item label="担保金额" prop="guaranteeAmount">
                    <!-- {{dataForm.guaranteeAmountS}} -->
                    <el-input-number placeholder="请输入担保金额" size="medium" style='width:180px;' v-model="guaranteeAmount" @change='change' controls-position="right" :precision="6" :step="0.000001"
                      :min="0" :max="1000"> <template slot="append">万元</template></el-input-number> 万元
                    <el-tooltip content="建设类担保金额最大80万元，交通类担保金额最大1000万元。" placement="bottom">
                      <i class="el-icon-question" style="font-size:16px;color:#409EFF;"></i>
                    </el-tooltip>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg="24">
                  <el-form-item label="保费" prop="guaranteePrice">
                    <span style="font-size:20px;color:#e6a23c;font-weight: bold;letter-spacing: 2px;">{{guaranteeAmount?serPrice:'--'}}</span> 元
                    <el-tooltip v-if="tip!==''" :content="tip" placement="bottom">
                      <i class="el-icon-info"></i>
                    </el-tooltip>
                  </el-form-item>
                </el-col>
              </el-row>

            </div>
          </el-col>
        </div>
      </el-card>

      <choseProject v-if="visible" ref="choseProject" @getProjectData='getProjectData'></choseProject>
    </el-form>
  </el-row>
</template>
<script>
import choseProject from '../../tenderGuarantee/form/compoents/choseProject'
import moment from 'moment'
var that = null
export default {
  components: {
    choseProject
  },

  data () {
    return {
      pickerOptions: this.pickerOptionsFun(),
      serPriceId: '',
      visible: false,
      radio: 0,
      restaurants: [],
      insuranceCon: [],
      state1: '',
      tip: '',
      guaranteeAmount: '',
      regionId: '',
      regionIds: [],
      casCilck: true,
      thsAreaCode: '',
      projectName: '',
      bidName: '',
      props: {
        value: 'code',
        label: 'name',
        children: 'child',
        isLeaf: 'leaf',
        lazy: true,
        lazyLoad: this.lazyLoad,
        collapseTags: true
      }
    }
  },
  filters: {
    regFil (val) {
      return val
    }
  },
  props: {
    dataForm: {
      type: Object
    },
    options: {
      type: Object
    },
    bgGuaranteeApplyName: {
      type: String
    },
    pakeage: {
      type: Object
    }
  },
  computed: {

    endDate () {
      if (this.dataForm.startDate && this.dataForm.tenderValid) {
        console.log(this.dataForm.startDate, this.dataForm.tenderValid)
        let startDate = this.moment(this.dataForm.startDate).format('YYYY-MM-DD')
        let date = new Date(startDate).setTime(new Date(startDate).getTime() + 3600 * 1000 * 24 * Number(this.dataForm.tenderValid))
        console.log(date)
        this.$set(this.dataForm, 'endDate', this.moment(date).format('YYYY-MM-DD') === 'Invalid date' ? '' : this.moment(date).format('YYYY-MM-DD'))
      } else {
        this.$set(this.dataForm, 'endDate', '')
      }
      return this.dataForm.endDate === '' ? '--' : this.dataForm.endDate
    },
    serPrice () {
      var price = null
      console.log(this.insuranceCon.pricelist)
      if (this.insuranceCon.pricelist) {
        //
        this.insuranceCon.pricelist.map((item, index, arr) => {
          console.log(item)
          let len = this.insuranceCon.pricelist.length - 1
          // eslint-disable-next-line vue/no-side-effects-in-computed-properties
          this.insuranceCon.pricelist[len].guaranteeAmount =
            Number.POSITIVE_INFINITY
          if (Number(item.guaranteeAmount) === 0) {
            item.guaranteeAmount = Number.POSITIVE_INFINITY
          }
          if (index === 0) {
            // eslint-disable-next-line vue/no-side-effects-in-computed-properties
            this.serPriceId = item.id
            if (
              Number(this.dataForm.guaranteeAmount) <=
              Number(item.guaranteeAmount)
            ) {
              if (item.priceType === '1') {
                price = Number(item.letterPrice) + Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，${
                  item.letterPrice
                }（定额） ${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              } else if (item.priceType === '2') {
                price =
                  (Number(this.dataForm.guaranteeAmount) *
                    Number(Math.min(item.percentValue, 1) * 1000)) /
                  1000
                console.log(price)
                price =
                  Math.round(Number(price) * 100) / 100 +
                  Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，费率：${Number(
                  Math.min(item.percentValue, 1) * 1000
                )}（千分比）${item.letterPrice}（定额） ${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              }
            }
          } else if (index + 1 === arr.length) {
            if (
              Number(this.dataForm.guaranteeAmount) >
              Number(item.guaranteeAmount)
            ) {
              // eslint-disable-next-line vue/no-side-effects-in-computed-properties
              this.serPriceId = item.id
              if (item.priceType === '1') {
                price = Number(item.letterPrice) + Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，${
                  item.letterPrice
                }（定额） ${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              } else if (item.priceType === '2') {
                price =
                  (Number(this.dataForm.guaranteeAmount) *
                    Number(Math.min(item.percentValue, 1) * 1000)) /
                  1000
                console.log(price)
                price =
                  Math.round(Number(price) * 100) / 100 +
                  Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，费率：${Number(
                  Math.min(item.percentValue, 1) * 1000
                )}（千分比）${item.letterPrice}（定额） ${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              }
            } else if (
              Number(this.dataForm.guaranteeAmount) >
                Number(arr[index - 1].guaranteeAmount) &&
              Number(this.dataForm.guaranteeAmount) <=
                Number(item.guaranteeAmount)
            ) {
              // '500000 - 800000'
              // eslint-disable-next-line vue/no-side-effects-in-computed-properties
              this.serPriceId = item.id
              if (item.priceType === '1') {
                price = Number(item.letterPrice) + Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，${
                  item.letterPrice
                }（定额） ${item.letterPrice}（定额）${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              } else if (item.priceType === '2') {
                price =
                  (Number(this.dataForm.guaranteeAmount) *
                    Number(Math.min(item.percentValue, 1) * 1000)) /
                  1000
                console.log(price, Number(item.platformPrice))
                price =
                  Math.round(Number(price) * 100) / 100 +
                  Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，费率：${Number(
                  Math.min(item.percentValue, 1) * 1000
                )}（千分比）${item.letterPrice}（定额） ${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              }
            }
          } else {
            if (
              Number(this.dataForm.guaranteeAmount) >
                Number(arr[index - 1].guaranteeAmount) &&
              Number(this.dataForm.guaranteeAmount) <=
                Number(item.guaranteeAmount)
            ) {
              // eslint-disable-next-line vue/no-side-effects-in-computed-properties
              this.serPriceId = item.id
              if (item.priceType === '1') {
                price = Number(item.letterPrice) + Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，${
                  item.letterPrice
                }（定额） ${item.letterPrice}（定额）${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              } else if (item.priceType === '2') {
                price =
                  (Number(this.dataForm.guaranteeAmount) *
                    Number(Math.min(item.percentValue, 1) * 1000)) /
                  1000
                console.log(
                  price,
                  (Number(this.dataForm.guaranteeAmount) *
                    Math.min(item.percentValue, 1) *
                    1000) /
                    1000
                )
                price =
                  Math.round(Number(price) * 100) / 100 +
                  Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，费率：${Number(
                  Math.min(item.percentValue, 1) * 1000
                )}（千分比）${item.letterPrice}（定额） ${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              }
            }
          }
        })
      }
      //
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.dataForm.guaranteePrice = price

      return price || '--'
    },
    dataRule () {
      var isNum = (rule, value, callback) => {
        if (!value) {
          return callback(new Error('请输入招标有效期'))
        } else {
          // eslint-disable-next-line no-useless-escape

          if (Number(value) > 0 && Number(value) <= 150) {
            callback()
          } else {
            return callback(new Error('保函有效期必须在150天以内'))
          }
        }
      }
      return {
        projectName: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        regionId: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        tbDate: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        platformCode: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        bidOpenDate: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        bbrName: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        tendereeAgent: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        startDate: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        tenderValid: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          },
          { validator: isNum, trigger: 'change' }
        ],
        endDate: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        guaranteeAmount: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ]

      }
    }
  },

  created () {
    that = this
    this.getInsurance(this.$route.params.insuranceCode, 0)
  },
  watch: {
    dataForm: {
      handler (a) {
        // console.log(a)
        if (a.guaranteeAmount) {
          this.guaranteeAmount = a.guaranteeAmount / 10000
          this.$set(this.dataForm, 'guaranteeAmountS', this.smalltoBIG(a.guaranteeAmount))
        }

        // if (a.guaranteeAmountS) this.$set(this.dataForm, 'guaranteeAmountS', this.smalltoBIG(a.guaranteeAmountS * 10000))
      },
      deep: true
    }
  },
  mounted () {
    this.restaurants = this.loadAll()
  },
  methods: {
    moment,
    pickerOptionsFun () {
      return {
        disabledDate (time) {
          // console.log( 86400000 * that.pakeage.delayBidOpening ? that.pakeage.delayBidOpening : 0)
          return (
            time.getTime() <
            (that.pakeage.delayBidOpening
              ? Date.now() + 86400000 * Number(that.pakeage.delayBidOpening)
              : Date.now())
          )
        }
      }
    },
    trimLR (val) {
      if (this.dataForm[val]) {
        this.dataForm[val] = this.dataForm[val].replace(/\s+/g, '')
      }
    },
    bbrNameChange (val) {
      if (val === this.bgGuaranteeApplyName) {
        this.dataForm.bbrName = ''
        this.$message.error('招标人名称不能和投标人企业名称相同！')
      }
    },
    querySearch (queryString, cb) {
      var restaurants = this.restaurants
      cb(restaurants)
    },
    loadAll () {
      return [
        { value: '30' },
        { value: '60' },
        { value: '90' },
        { value: '120' },
        { value: '150' }
      ]
    },
    change (val) {
      if (val) {
        this.$set(this.dataForm, 'guaranteeAmount', val * 10000)
        this.$set(this.dataForm, 'guaranteeAmountS', this.smalltoBIG(val * 10000))
      } else {
        this.$set(this.dataForm, 'guaranteeAmount', 0)
        this.$set(this.dataForm, 'guaranteeAmountS', this.smalltoBIG(0))
      }
    },

    smalltoBIG (n) {
      var fraction = ['角', '分']
      var digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
      var unit = [ ['元', '万', '亿'], ['', '拾', '佰', '仟'] ]
      var head = n < 0 ? '欠' : ''
      n = Math.abs(n)

      var s = ''

      for (var i = 0; i < fraction.length; i++) {
        s += (digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '')
      }
      s = s || '整'
      n = Math.floor(n)
      // eslint-disable-next-line no-redeclare
      for (var i = 0; i < unit[0].length && n > 0; i++) {
        var p = ''
        for (var j = 0; j < unit[1].length && n > 0; j++) {
          p = digit[n % 10] + unit[1][j] + p
          n = Math.floor(n / 10)
        }
        s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s
      }
      return head + s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零').replace(/^整$/, '零元整')
    },
    selfo () {
      this.casCilck = false
      this.$nextTick(() => {
        console.log(this.$refs)
      })
    },
    // projectNameChange (val) {
    //   this.$set(this.dataForm, 'projectName', this.bidName ? this.projectName + '-' + this.bidName : this.projectName)
    // },
    // bidNameChange (val) {
    //   this.$set(this.dataForm, 'projectName', this.bidName ? this.projectName + '-' + this.bidName : this.projectName)
    // },
    expandChange (val) {
      console.log(val)
    },
    regionIdChange (val) {
      console.log(val)
      this.$emit('clearProAndBBr', val ? val[1] : '')
      this.$set(this.dataForm, 'regionId', val ? val[1] : '')
    },
    async lazyLoad (node, resolve) {
      // 点击节点
      const { level } = node
      let nodes = await this.lazyloadFn(level === 0 ? 100000 : node.value)
      let nodes2 = nodes.map((item) => ({
        code: item.code,
        name: item.name,
        leaf: level > 0
      }))

      resolve(nodes2)
    },
    lazyloadFn (parentCode) {
      // 获取node
      return new Promise((resolve, reject) => {
        this.$http
          .get(`/demo/tbregion/regionTree?code=${parentCode}`)
          .then(({ data: res }) => {
            resolve(res.data)
            // this.zongingList = res.data
          })
          .catch(() => {})
      })
    },
    chose () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['choseProject'].dataForm.regionId = this.dataForm.regionId
        this.$refs['choseProject'].init()
      })
    },
    tbChange (val) {
      this.$emit('gettbDate', val)
    },
    getProjectData (obj) {
      this.$set(this.dataForm, 'name', obj.name)
      this.$set(this.dataForm, 'platformCode', obj.platformCode)
      // this.$set(this.dataForm, 'tbDate', obj.tbDate)
      this.$emit('setGuaranteeAmount', obj.guaranteeAmount)
      this.$emit('getBbrId', obj.bbrId)
    },
    getInsurance (code, index) {
      this.$http
        .get(
          `/letter/bgguaranteeissue/getIssueByCode/${code}&${this.$route.params.type}`
        )
        .then(({ data: res }) => {
          this.disLoading = false

          this.insuranceCon = res.data
        })
        .catch(() => {})
    },
    push () {
      return new Promise((resolve, reject) => {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            let params = {
              cjfChargeId: this.serPriceId,
              guaranteeAmount: this.dataForm.guaranteeAmount,
              letterPrice: this.serPrice
            }

            this.$http
              .post('/letter/bgguaranteeprice/JSletterPrice', params)
              .then(({ data: res }) => {
                if (res.code !== 0) {
                  resolve(false)
                  return
                }
                this.dataForm.guaranteePrice = this.serPrice
                // this.$set(this.dataForm, 'guaranteePrice', this.serPrice)
                resolve(true)
              })
              .catch(() => {})
          } else {
            console.log(1111)
            resolve(false)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
 .line {
    text-align: center;
}
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
</style>
