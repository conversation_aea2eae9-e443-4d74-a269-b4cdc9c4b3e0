<!--
 * @Descripttion:
 * @Author: kong<PERSON><PERSON><PERSON>
 * @Date: 2021-03-31 15:09:58
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-03-31 11:19:29
-->
<template>
  <el-dialog title="选择出具机构" :close-on-click-modal="false" append-to-body :close-on-press-escape="false" :visible.sync="visible" width="800px">
    <div slot="title" >
      <span class="el-dialog__title">选择履约保函模板</span>&emsp;
      <span style="color:#909399;">{{title}}</span>
    </div>
    <div v-loading='loading'>
      <!-- {{code}} -->
      <h3 class="temp-subtitle">请选择保函模板</h3>
      <div class="guaranteeList">
        <div class="guaranteeList-item" :class="active==index?'active':''" v-for="(item,index) in (code=='lybhyh'?getDict('履约银行保函模板类型'):getDict('履约担保保函模板类型'))  " @click="guaranteeClick(index,item.dictCode)" :key="index">
          <span class="name">{{item.dictName}}</span>
          <i class="choice" v-if="active==index"><img src="@/assets/img/choice.png" alt=""></i>
        </div>
      </div>
    </div>
      <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="$emit('submit',code,'',tcode)">申请</el-button>
    </template>
  </el-dialog>
</template>
<script>
import { getDict } from '@/utils/index'
export default {
  data () {
    return {
      visible: false,
      loading: false,
      cjList: [],
      active: 0,
      code: '',
      projectId: '',
      title: '',
      tcode: '0'
    }
  },
  methods: {
    getDict,
    init () {
      this.visible = true
    },
    guaranteeClick (idx, code) {
      this.active = idx
      this.tcode = code
      console.log(this.tcode)
    }
  }
}
</script>
<style lang="scss" scoped>
.temp-subtitle {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  margin: 0;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.guaranteeList {
  display: flex;
  flex-wrap: wrap;
  // margin: 20px auto;
  .guaranteeList-item {
    width: 200px;
    box-shadow: 0 0 30px #e7e7e7;
    padding: 10px 15px 15px;
    margin: 20px;
    text-align: left;
    transition: all 0.3s ease-in-out;
    border: 1px solid transparent;
    cursor: pointer;
    position: relative;
    line-height: 22px;
    span {
      display: block;
      margin-top: 12px;
    }
    .icon {
      transition: all 0.3s ease-in-out;
      display: inline-block;
      width: 40px;
      height: 40px;
      line-height: 40px;
      img {
        transition: all 0.3s ease-in-out;
        width: 40px;
      }
    }
  }
  .guaranteeList-item:hover {
    transform: scale(1.1);
    border: 1px solid #f18241;
    .icon {
      background-color: rgba(241, 130, 65, 0.1);
      transform: scale(1.4);
      border-radius: 50%;
      img {
        width: 25px;
      }
    }
  }
  .active {
    transform: scale(1.1);
    border: 1px solid rgb(241, 130, 65);
    color: #f18241;
    .icon {
      background-color: rgba(241, 130, 65, 0.1);
      transform: scale(1.4);
      border-radius: 50%;
      img {
        width: 25px;
      }
    }
  }
  .choice {
    position: absolute;
    top: -5px;
    right: -2px;
    img {
      width: 18px;
    }
  }
}
</style>
