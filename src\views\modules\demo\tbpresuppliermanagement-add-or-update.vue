<template>
    <el-dialog
            :visible.sync="visible"
            :title="!dataForm.id ? $t('add') : $t('update')"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            width="70%"
    >
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"  :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
        <el-row :gutter="20">
      <el-collapse v-model="activeName" accordion>
        <el-collapse-item title="基础信息" name="基础信息">
            <el-col :xs="24" :lg="colConfig">
                    <el-form-item label="供应商编码" prop="code">
     <el-input v-model="dataForm.code" placeholder="供应商管理编码"></el-input>
</el-form-item>
                <el-form-item label="供应商名称" prop="name">
     <el-input v-model="dataForm.name" placeholder="供应商管理名称"></el-input>
</el-form-item>
                <el-form-item label="供应商类型" prop="supplierType">
                    <el-select v-model="dataForm.supplierType" clearable placeholder="供应商类型">
                        <el-option
                                v-for="item in supplierTypeoptions"
                                :key="item.dictCode"
                                :label="item.dictName"
                                :value="item.dictCode"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="项目编码" prop="projectCode">
     <el-input v-model="dataForm.projectCode" placeholder="项目编码"></el-input>
</el-form-item>
                <el-form-item label="项目名称" prop="projectName">
     <el-input v-model="dataForm.projectName" placeholder="项目名称"></el-input>
</el-form-item>
                <el-form-item label="合同编码" prop="contractCode">
     <el-input v-model="dataForm.contractCode" placeholder="合同编码"></el-input>
</el-form-item>
                <el-form-item label="合同名称" prop="contractName">
     <el-input v-model="dataForm.contractName" placeholder="合同名称"></el-input>
</el-form-item>
                <el-form-item label="产品与服务" prop="provideProductsService">
                    <el-input v-model="dataForm.provideProductsService" placeholder="提供的产品与服务"></el-input>
                </el-form-item>
                <el-form-item label="收费方式" prop="chargingMethod">
                    <el-input v-model="dataForm.chargingMethod" placeholder="收费方式"></el-input>
                </el-form-item>
                <el-form-item label="备注" prop="remarks">
                    <el-input v-model="dataForm.remarks" placeholder="备注"></el-input>
                </el-form-item>
            </el-col>
            <el-col  :xs="24" :lg="colConfig">
                <el-form-item label="省市区名称" prop="pcaName" style="display:none;">
                    <el-input v-model="dataForm.pcaName"  placeholder="省市区名称"></el-input>
                </el-form-item>
                 <el-form-item label="省市区选择"  prop="zonging">
                   <el-cascader
                      v-model="dataForm.zonging"
                      :options="zongingList"
                      clearable
                      ref="zonging"
                      :props="props"
                    ></el-cascader>
                 </el-form-item>
                <el-form-item label="供应商地址" prop="projectAddress">
                    <el-input v-model="dataForm.projectAddress" placeholder="供应商地址"></el-input>
                </el-form-item>
                <el-form-item label="信用等级" prop="creditRating">
                    <el-select v-model="dataForm.creditRating" clearable placeholder="供应商类型">
                        <el-option
                                v-for="item in creditRatingoptions"
                                :key="item.dictCode"
                                :label="item.dictName"
                                :value="item.dictCode"
                        >
                        </el-option>
                    </el-select>

</el-form-item>
                <el-form-item label="网站" prop="website">
     <el-input v-model="dataForm.website" placeholder="网站"></el-input>
</el-form-item>
                <el-form-item label="评价人" prop="evaluator">
     <el-input v-model="dataForm.evaluator" placeholder="评价人"></el-input>
</el-form-item>
                <el-form-item label="合作结束时间" prop="cooperationEndTime">
                    <el-input v-model="dataForm.cooperationEndTime" placeholder="合作结束时间"></el-input>
                </el-form-item>
                <el-form-item label="合作方式" prop="cooperationWays">
                    <el-input v-model="dataForm.cooperationWays" placeholder="合作方式"></el-input>
                </el-form-item>
                <el-form-item label="注册资本" prop="registeredCapital">
                    <el-input v-model="dataForm.registeredCapital" placeholder="注册资本"></el-input>
                </el-form-item>
                <el-form-item label="状态" prop="status" size="mini">
                    <el-radio-group v-model="dataForm.status">
                        <el-radio :label="0">停用</el-radio>
                        <el-radio :label="1">正常</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-col>
        </el-collapse-item>
        <el-collapse-item title="联系方式" name="联系方式">
            <el-col  :xs="24" :lg="colConfig">
                <el-form-item label="联系人1" prop="contacts1">
     <el-input v-model="dataForm.contacts1" placeholder="联系人1"></el-input>
</el-form-item>
                <el-form-item label="联系电话1" prop="telephone1">
     <el-input v-model="dataForm.telephone1" placeholder="联系电话1"></el-input>
</el-form-item>
                <el-form-item label="联系邮箱1" prop="email1">
     <el-input v-model="dataForm.email1" placeholder="联系邮箱1"></el-input>
</el-form-item>
                <el-form-item label="联系人3" prop="contacts3">
                    <el-input v-model="dataForm.contacts3" placeholder="联系人3"></el-input>
                </el-form-item>
                <el-form-item label="联系电话3" prop="telephone3">
                    <el-input v-model="dataForm.telephone3" placeholder="联系电话3"></el-input>
                </el-form-item>
            </el-col>
            <el-col  :xs="24" :lg="colConfig">
                <el-form-item label="联系人2" prop="contacts2">
     <el-input v-model="dataForm.contacts2" placeholder="联系人2"></el-input>
</el-form-item>
                <el-form-item label="联系电话2" prop="telephone2">
     <el-input v-model="dataForm.telephone2" placeholder="联系电话2"></el-input>
</el-form-item>
            <el-form-item label="联系邮箱2" prop="email2">
                <el-input v-model="dataForm.email2" placeholder="联系邮箱2"></el-input>
            </el-form-item>
                <el-form-item label="联系邮箱3" prop="email3">
                    <el-input v-model="dataForm.email3" placeholder="联系邮箱3"></el-input>
                </el-form-item>
            </el-col>
        </el-collapse-item>
        <el-collapse-item title="财务信息" name="财务信息">
                <el-form-item label="单位名称" prop="unitName">
     <el-input v-model="dataForm.unitName" placeholder="单位名称"></el-input>
</el-form-item>
                <el-form-item label="单位地址" prop="unitAddress">
     <el-input v-model="dataForm.unitAddress" placeholder="单位地址"></el-input>
</el-form-item>
                <el-form-item label="单位电话" prop="unitTelephone">
     <el-input v-model="dataForm.unitTelephone" placeholder="单位电话"></el-input>
</el-form-item>
            <el-form-item label="账号" prop="accountNumber">
                <el-input v-model="dataForm.accountNumber" placeholder="账号"></el-input>
            </el-form-item>
            <el-form-item label="纳税人识别号" prop="axpayerId">
                <el-input v-model="dataForm.axpayerId" placeholder="纳税人识别号"></el-input>
            </el-form-item>
                <el-form-item label="开户行" prop="openingBank">
     <el-input v-model="dataForm.openingBank" placeholder="开户行"></el-input>
</el-form-item>
        </el-collapse-item>
      </el-collapse>
        <el-form-item hidden label="项目id" prop="projectId">
            <el-input v-model="dataForm.projectId" placeholder="项目id"></el-input>
        </el-form-item>
        <el-form-item hidden label="合同id" prop="contractId">
            <el-input v-model="dataForm.contractId" placeholder="合同id"></el-input>
        </el-form-item>
        </el-row>
                    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import { setTimeout } from 'timers'
export default {
  data () {
    return {
      colConfig: 2,
      visible: false,
      activeName: '基础信息', // 默认展开第一个
      zongingList: [],
      props: {
        value: 'code',
        label: 'name',
        isLeaf: 'leaf',
        lazy: true,
        lazyLoad: this.lazyLoad
      },
      dataForm: {
        id: '',
        code: '',
        name: '',
        zonging: '',
        projectId: '',
        projectCode: '',
        projectName: '',
        contractId: '',
        contractCode: '',
        contractName: '',
        creditRating: '',
        website: '',
        evaluator: '',
        province: '',
        city: '',
        area: '',
        projectAddress: '',
        supplierType: '',
        contacts1: '',
        telephone1: '',
        email1: '',
        contacts2: '',
        telephone2: '',
        email2: '',
        contacts3: '',
        telephone3: '',
        email3: '',
        unitName: '',
        unitAddress: '',
        unitTelephone: '',
        openingBank: '',
        accountNumber: '',
        axpayerId: '',
        cooperationEndTime: '',
        cooperationWays: '',
        registeredCapital: '',
        provideProductsService: '',
        chargingMethod: '',
        remarks: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        pcaName: ''
      },
      getDicListURL: '/sys/dict/type/',
      creditRatingoptions: [],
      supplierTypeoptions: []
    }
  },

  computed: {
    dataRule () {
      return {
        code: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        creditRating: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        website: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        evaluator: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        // province: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        // city: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        // area: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        projectAddress: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        supplierType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        cooperationEndTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        cooperationWays: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        registeredCapital: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        provideProductsService: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        chargingMethod: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        remarks: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        status: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]

      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig

    // this.getZoning(100000)
    // this.lazyloadFn(100000)
  },
  mounted () {

  },
  methods: {
    async lazyLoad (node, resolve) {
      const { level } = node
      // const code = node.code

      setTimeout(async () => {
        let nodes = await this.lazyloadFn(level === 0 ? 100000 : node.value)
        let nodes2 = nodes.map(item => ({
          code: item.code,
          name: item.name,
          leaf: level > 1
        }))

        resolve(nodes2)
      }, 500)
    },
    lazyloadFn (parentCode) {
      return new Promise((resolve, reject) => {
        this.$http.get(`/demo/tbregion/subnode/${parentCode}`).then(({ data: res }) => {
          resolve(res.data)
          // this.zongingList =
        }).catch(() => { })
      })
    },
    handleItemChange (arr) {

    },

    init () {
      //
      this.getSupplierTypeInfo()
      this.getCreditRating()
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        let zonging = this.$refs.zonging
        //
        zonging.$children[0].$refs.input.value = ''
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取供应商类型信息
    getSupplierTypeInfo () {
      this.$http.get(this.getDicListURL + 'supplierType').then(({ data: res }) => {
        this.supplierTypeoptions = {
          ...this.supplierTypeoptions,
          ...res.data.list
        }
      }).catch(() => { })
    },
    // 获取供应商信用等级
    getCreditRating () {
      this.$http.get(this.getDicListURL + 'creditRating').then(({ data: res }) => {
        this.creditRatingoptions = {
          ...this.creditRatingoptions,
          ...res.data.list
        }
      }).catch(() => { })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/demo/tbpresuppliermanagement/${this.dataForm.id}`).then(({ data: res }) => {
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        let zonging = this.$refs.zonging

        zonging.$children[0].$refs.input.value = this.dataForm.pcaName
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(async function () {
      this.$refs['dataForm'].validate(async (valid) => {
        if (!valid) {
          return false
        }
        if (this.dataForm.zonging !== '' && this.dataForm.zonging.length > 1) {
          let res0 = await this.$http.get(`/demo/tbregion/type/${this.dataForm.zonging[0]}`)
          this.dataForm.province = res0.data.data.bimRegion.name
          let res1 = await this.$http.get(`/demo/tbregion/type/${this.dataForm.zonging[1]}`)
          this.dataForm.city = res1.data.data.bimRegion.name

          let res2 = await this.$http.get(`/demo/tbregion/type/${this.dataForm.zonging[2]}`)
          this.dataForm.area = res2.data.data.bimRegion.name
          this.dataForm.pcaName = await `${this.dataForm.province} / ${this.dataForm.city} / ${this.dataForm.area}`
        }

        let resFomit = await this.$http[!this.dataForm.id ? 'post' : 'put']('/demo/tbpresuppliermanagement/', this.dataForm)
        if (resFomit.data.code !== 0) {
          return this.$message.error(resFomit.data.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            this.visible = false
            this.$emit('refreshDataList')
          }
        })

        //
        //
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
