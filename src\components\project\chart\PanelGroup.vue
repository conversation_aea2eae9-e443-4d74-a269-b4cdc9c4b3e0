<template>
    <el-row :gutter="40" class="panel-group">
        <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
            <div class="card-panel">
                <div class="card-panel-icon-wrapper icon-people">
                    <svg class="card-panel-icon1" aria-hidden="true">
                        <use xlink:href="#icon-CI"></use>
                    </svg>
                </div>

                <div class="card-panel-description">
                    <div class="card-panel-text">
                        已出具保函总数
                    </div>
                    <count-to :start-val="0" :end-val="statisticsTotal.totalNum?statisticsTotal.totalNum:0" v-model="statisticsTotal.totalNum" :duration="2600"
                              class="card-panel-num"/>
                </div>
            </div>
        </el-col>
        <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
            <div class="card-panel">
                <div class="card-panel-icon-wrapper icon-message">
                    <svg class="card-panel-icon1" aria-hidden="true">
                        <use xlink:href="#icon-setting"></use>
                    </svg>
                </div>
                <div class="card-panel-description">
                    <div class="card-panel-text">
                        已出具保函总费用
                    </div>
                    <count-to :start-val="0" :end-val="statisticsTotal.guaranteePriceTotalAmount?statisticsTotal.guaranteePriceTotalAmount:0" v-model="statisticsTotal.guaranteePriceTotalAmount" :duration="3000"
                              class="card-panel-num"/>
                </div>
            </div>
        </el-col>
        <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
            <div class="card-panel">
                <div class="card-panel-icon-wrapper icon-money">
                    <svg class="card-panel-icon1" aria-hidden="true">
                        <use xlink:href="#icon-earth"></use>
                    </svg>
                </div>
                <div class="card-panel-description">
                    <div class="card-panel-text">
                        已出具的保额
                    </div>
                    <count-to :start-val="0" :end-val="statisticsTotal.guaranteeAmountTotalAmount?statisticsTotal.guaranteeAmountTotalAmount:0" v-model="statisticsTotal.guaranteeAmountTotalAmount" :duration="3200"
                              class="card-panel-num"/>
                </div>
            </div>
        </el-col>
        <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
            <div class="card-panel">
                <div class="card-panel-icon-wrapper icon-shopping">
                    <svg class="card-panel-icon1" aria-hidden="true">
                        <use xlink:href="#icon-setting"></use>
                    </svg>
                </div>
                <div class="card-panel-description">
                    <div class="card-panel-text">
                        当日已出具的保额
                    </div>
                    <count-to :start-val="0" :end-val="statisticsTotal.intradayGuaranteePriceTotal?statisticsTotal.intradayGuaranteePriceTotal:0" v-model="statisticsTotal.intradayGuaranteePriceTotal" :duration="3600" class="card-panel-num"/>
                </div>
            </div>
        </el-col>
    </el-row>
</template>

<script>
import CountTo from 'vue-count-to'
// import { checkSum } from '@/api/remote-search'

export default {
  components: {
    CountTo
  },
  data () {
    return {
      statisticsTotal: {
        totalNum: '', // 保函总数
        guaranteePriceTotalAmount: '', // 保函总金额
        guaranteeAmountTotalAmount: '', // 总
        intradayGuaranteePriceTotal: ''// 当日
      }
    }
  },
  props: {
    panelData: {
      type: Object,
      required: true
    }
  },
  mounted () {
    this.$nextTick(() => {
    //   console.log(this.panelData)
      this.statisticsTotal = this.panelData
    })
  },
  watch: {
    panelData: {
      deep: true,
      handler (val) {
        console.log(val)
        this.statisticsTotal = val
        // this.setOptions(val)
      }
    }
  },
  methods: {
    handleSetLineChartData (type) {
      this.$emit('handleSetLineChartData', type)
    }
  }
}

</script>

<style lang="scss" scoped>
    .panel-group {
        margin-top: 18px;

        .card-panel-col {
            margin-bottom: 32px;
        }

        .card-panel {
            height: 108px;
            cursor: pointer;
            font-size: 12px;
            position: relative;
            overflow: hidden;
            color: #666;
            background: #fff;
            box-shadow: 4px 4px 30px #e7e7e7;
            border-color: rgba(0, 0, 0, .05);

            &:hover {
                .card-panel-icon-wrapper {
                    color: #fff;
                }

                .icon-people {
                    background: #40c9c6;
                }

                .icon-message {
                    background: #36a3f7;
                }

                .icon-money {
                    background: #f4516c;
                }

                .icon-shopping {
                    background: #34bfa3
                }
            }

            .icon-people {
                color: #40c9c6;
            }

            .icon-message {
                color: #36a3f7;
            }

            .icon-money {
                color: #f4516c;
            }

            .icon-shopping {
                color: #34bfa3
            }

            .card-panel-icon1 {
                width: 50px;
                height: 50px;
                fill: currentColor;
                vertical-align: middle;
                overflow: hidden
            }

            .card-panel-icon-wrapper {
                float: left;
                margin: 14px 0 0 5px;
                padding: 16px;
                transition: all 0.38s ease-out;
                border-radius: 6px;
            }

            .card-panel-icon {
                float: left;
                font-size: 48px;
            }

            .card-panel-description {
                font-weight: bold;
                margin: 26px 20px;
                margin-left: 0px;
                text-align: right;
                .card-panel-text {
                    line-height: 18px;
                    color: rgba(0, 0, 0, 0.45);
                    font-size: 16px;
                    margin-bottom: 12px;
                }

                .card-panel-num {
                    font-size: 20px;
                }
            }
        }
    }

    @media (max-width: 550px) {
        .card-panel-description {
            display: none;
        }

        .card-panel-icon-wrapper {
            float: none !important;
            width: 100%;
            height: 100%;
            margin: 0 !important;

            .svg-icon {
                display: block;
                margin: 14px auto !important;
                float: none !important;
            }
        }
    }
</style>
