
import http from '@/utils/request'
function getInsura () {
  return new Promise((resolve, reject) => {
    http.get(`/letter/bgguaranteeissue/getAllIssue`).then(({ data: res }) => {
      if (res.code !== 0) {
        return
      }
      resolve(res.data)
    }).catch((rej) => {
    })
  })
}
var insuraData = [
  // 天安保险
  {
    code: 'tabx',
    assembly: [],
    entrance: ''
  }
]
getInsura().then(res => {
  res.map(a => {
    insuraData.push({

    })
  })
})
