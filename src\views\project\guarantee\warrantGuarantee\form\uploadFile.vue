<!--
 * @Descripttion:
 * @Author: kong<PERSON>qiang
 * @Date: 2020-08-20 11:21:16
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-12-29 17:45:57
-->
<template>
  <el-row :gutter="20" style="margin:15px 20px 0;">
    <!-- {{this.$route.params.insuranceCode}} -->
    <el-form  label-position="left" :rules="dataRule" ref="dataForm" label-width="140px">
      <el-card class="box-card" shadow='never'>
        <div slot="header" class="clearfix">
          <span class="title">上传资料</span>
        </div>
        <div>
          <el-col :xs="24" :lg="24">
            <!-- {{dataForm}} -->
            <el-form-item label="招标文件" prop="upload" v-if="filePer.zbwj===1">
              <el-upload class="upload-demo" ref="upload" v-if="!fileList[0]"  :on-remove='handleRemove'  :action="uploadUrl" :data='zbData' :headers="myHeaders" :before-remove="BeforeRemove" :on-success=' biddingDocumentSuccessHandle'
                  :before-upload="beforeUpload"  :limit="1"   :on-exceed="handleExceed" :file-list="fileList">
                <el-button size="small" type="primary" >点击上传</el-button>
                <span slot="tip" class="el-upload__tip">&emsp;要求格式：.jpg,.jpeg,.png,.pdf</span>
              </el-upload>
              <template v-else>
                 <span ><i class="el-icon-success" style="color:#67C23A;"></i>&nbsp;已上传招标文件&emsp;<el-button @click="fileList[0].type?showDia(fileList[0].url):download(fileList[0].url)" type="text">{{fileList[0].type?'查看':'下载'}}</el-button></span>
                  <div  class="el-upload__tip">如招标文件存有异议，请联系管理员</div>
              </template>
            </el-form-item>
            <el-form-item label="营业执照"  prop="yyupload" >
                <el-upload class="upload-demo" ref="yyupload" :on-preview='onPreview' :on-remove='handleRemoveyy'  :action="uploadUrl" :data='yyData' :headers="myHeaders" :before-remove="BeforeRemove" :on-success='businessLicenseSuccessHandle'
                  :before-upload="yybeforeUpload"  :limit="1"   :on-exceed="handleExceed" :file-list="yyfileList">
                <el-button size="small" type="primary">点击上传</el-button>
                <span slot="tip" class="el-upload__tip">&emsp;要求格式：jpg,jpeg,png</span>
              </el-upload>
            </el-form-item>
            <el-form-item label="澄清答疑文件" v-if="filePer.cqdy===1">
                <el-upload class="upload-demo" ref="cqupload" :on-preview='onPreview' :on-remove='handleRemovecq'  :action="uploadUrl" :data='cqData' :headers="myHeaders" :before-remove="BeforeRemove" :on-success='cqSuccessHandle'
                  :before-upload="cqbeforeUpload"  :limit="1"   :on-exceed="handleExceed" :file-list="cqfileList">
                <el-button size="small" type="primary">点击上传</el-button>
                <span slot="tip" class="el-upload__tip">&emsp;要求格式：jpg,jpeg,png,pdf,zip,rar,7z</span>
              </el-upload>
            </el-form-item>
          </el-col>
        </div>
      </el-card>
    </el-form>
    <preview v-if="visible" ref="preview"></preview>
  </el-row>
</template>
<script>
import Cookies from 'js-cookie'
import preview from '@/views/project/guarantee/common/detail/preview'
export default {
  data () {
    return {
      fileList: [],
      visible: false,
      yyfileList: [],
      cqfileList: [],
      // dataForm: {},
      uploadUrl: '',
      zbData: { type: '2' },
      yyData: { type: '0' },
      cqData: { type: '8' },

      myHeaders: {
        token: Cookies.get('token') || ''
      },
      fileSuc: false,
      yyfileSuc: false
    }
  },
  components: { preview },
  created () {
    this.uploadUrl = window.SITE_CONFIG['apiURL'] + '/letter/guarantee/bhFileupload'
    // if (this.letterId) {
    //   this.getFile()
    // }
  },
  watch: {
    active (a) {
      if (a === 3) {
        this.getFile()
      }
    }

  },
  computed: {
    dataRule () {
      return {
        upload: [{
          required: true,
          message: '请上传招标文件'
        }],
        yyupload: [{
          required: true,
          message: '请上传营业执照'
        }],
        cqupload: [{
          required: true,
          message: '请上传澄清文件'
        }]
      }
    }
  },
  props: {
    projectId: {
      type: String
    },
    letterId: {
      type: String
    },
    active: {
      type: Number
    },
    filePer: {
      type: Object
    }
  },
  methods: {
    showDia (id) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['preview'].name = '招标文件'
        this.$refs['preview'].id = id
        this.$refs['preview'].init()
      })
    },
    download (id) {
      console.log(id)
      // var url = `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${id}`
      window.open(window.SITE_CONFIG['apiURL'] + '/sys/oss/localhostDownload/' + id + '?token=' + Cookies.get('token'))
    },
    onPreview (file) {
      console.log(file)
      if (file.response) {
        window.open(`${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${file.response.data.ossId}`)
      } else {
        window.open(`${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${file.url}`)
      }
    },
    getFile () {
      this.zbData = { ...this.zbData, ...{ letterId: this.letterId } }
      this.yyData = { ...this.yyData, ...{ letterId: this.letterId } }
      this.cqData = { ...this.cqData, ...{ letterId: this.letterId } }
      this.getDocuments()
      this.getBusinessLicense()
      this.getBhFile(8)
    },
    filterType (file) {
      if (file) {
        var extension = file.substring(file.lastIndexOf('.') + 1)
        return extension === 'pdf' || extension === 'PDF'
      }
    },
    // 获取招标文件
    async getDocuments () {
      let { data } = await this.$http.get('letter/bgbiddingproject/getDocuments?projectId=' + this.projectId)
      if (data.data) {
        this.$emit('biddingDocumentId', data.data.id)
        this.fileList = [{ name: '招标文件', url: data.data.id, type: this.filterType(data.data.url) }]
      }
    },
    // 获取营业执照
    async getBusinessLicense () {
      let { data } = await this.$http.get('letter/bgguaranteeapply/getBusinessLicense')
      if (data.data) {
        this.$emit('businessLicenseId', data.data.id)
        this.yyfileList = [{ name: '营业执照', url: data.data.id, type: this.filterType(data.data.url) }]
      }
    },
    // 公用获取文件
    async getBhFile (type) {
      let { data } = await this.$http.get(`letter/guarantee/getBhFile?letterId=${this.letterId}&type=${type}`)
      if (data.data.id) {
        // this.$emit('businessLicenseId', data.data.id)
        this.cqfileList = [{ name: '澄清答疑文件', url: data.data.id, type: this.filterType(data.data.url) }]
      }
    },
    biddingDocumentSuccessHandle (res, file, fileList) {
      if (res.data.flag !== true) {
        this.fileList = []
        return this.$message.error(res.data.msg)
      }
      this.$message.success('上传成功')
      this.$emit('biddingDocumentId', res.data.ossId)
      this.getDocuments()
    },
    businessLicenseSuccessHandle (res, file, fileList) {
      if (res.data.flag !== true) {
        this.yyfileList = []
        return this.$message.error(res.data.msg)
      }
      this.$message.success('上传成功')
      console.log(res.data.id)
      this.$emit('businessLicenseId', res.data.ossId)
      this.yyfileList = fileList
    },
    BeforeRemove (file, fileList) {
      console.log(file, fileList)
      let a = true
      if (file && file.status === 'success') {
        a = this.$confirm(`确定移除 ${file.name}？`)
      }
      return a
    },
    handleRemove (file, fileList) {
      if (file && file.status === 'success') {
        // 移除方法
        // var deleteId = file.url ? file.url : file.response.data.ossId
        return this.deleteFileCQ(this.letterId, 1)
      }
    },
    handleRemoveyy (file, fileList) {
      if (file && file.status === 'success') {
        // 移除方法
        // var deleteId = file.url ? file.url : file.response.data.ossId
        return this.deleteFileCQ(this.letterId, 0)
      }
    },
    beforeUpload (file) {
      var extension = file.name.substring(file.name.lastIndexOf('.') + 1)
      var lastName = extension !== 'jpg' &&
        extension !== 'JPG' &&
        extension !== 'jpeg' &&
        extension !== 'JPEG' &&
        extension !== 'png' &&
        extension !== 'PNG' &&
        extension !== 'pdf' &&
        extension !== 'PDF'

      if (
        lastName
      ) {
        this.$message.warning('文件要求格式为：.jpg,.JPG,.jpeg,.JPEG,.png,.PNG,.pdf,PDF')
      }

      return !lastName
    },
    yybeforeUpload (file) {
      let extension = file.name.substring(file.name.lastIndexOf('.') + 1)
      var lastName = extension !== 'jpg' &&
        extension !== 'JPG' &&
        extension !== 'jpeg' &&
        extension !== 'JPEG' &&
        extension !== 'png' &&
        extension !== 'PNG'
      if (
        lastName
      ) {
        this.$message.warning('文件要求格式为：jpg,jpeg,png')
      }
      console.log(lastName)
      return !lastName
    },
    handlePreview (file) {
      console.log(file)
    },
    handleExceed (files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件`
      )
    },
    // 澄清文件
    cqSuccessHandle (res, file, fileList) {
      if (res.data.flag !== true) {
        this.cqfileList = []
        return this.$message.error(res.data.msg)
      }
      this.$message.success('上传成功')
      // this.$emit('biddingDocumentId', res.data.ossId)
      this.getDocuments()
    },
    cqbeforeUpload (file) {
      var extension = file.name.substring(file.name.lastIndexOf('.') + 1)
      var lastName = extension !== 'jpg' &&
        extension !== 'JPG' &&
        extension !== 'jpeg' &&
        extension !== 'JPEG' &&
        extension !== 'png' &&
        extension !== 'PNG' &&
        extension !== 'pdf' &&
        extension !== 'PDF' &&
        extension !== 'zip' &&
        extension !== 'rar' &&
        extension !== '7z'

      if (
        lastName
      ) {
        this.$message.warning('文件要求格式为：jpg,jpeg,png,pdf,zip,rar,7z')
      }

      return !lastName
    },
    handleRemovecq (file, fileList) {
      if (file && file.status === 'success') {
        // 移除方法
        // var deleteId = file.url ? file.url : file.response.data.ossId
        return this.deleteFileCQ(this.letterId, 8)
      }
    },
    deleteFileCQ (id, type) {
      return new Promise(async (resolve, reject) => {
        let { data } = await this.$http.get('letter/guarantee/deleteBhFile?letterId=' + id + '&type=' + type)
        if (data.code !== 0) {
          this.$message.error(data.msg)
          resolve(false)
        }
        if (type === 1) {
          this.$emit('biddingDocumentId', '')
        }
        if (type === 0) {
          this.$emit('businessLicenseId', '')
        }
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
        resolve(true)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.el-upload__tip {
  color: #F56C6C;
}
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
</style>
