<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"  label-width="120px">
      <el-form-item prop="code"  :label="$t('dept.code')">
        <el-input v-model="dataForm.code" :placeholder="$t('dept.code')"></el-input>
      </el-form-item>
      <el-form-item prop="name" :label="$t('dept.name')">
        <el-input v-model="dataForm.name" :placeholder="$t('dept.name')"></el-input>
      </el-form-item>
      <el-form-item prop="deptType" :label="$t('dept.deptType')">
      <el-select v-model="dataForm.deptType" clearable  placeholder="请选择机构类型">
        <el-option
                v-for="item in deptTypeoptions"
                :key="item.dictCode"
                :label="item.dictName"
                :value="item.dictCode">
        </el-option>
      </el-select>
    </el-form-item>
      <el-form-item label="状态" hidden prop="status" size="mini">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="0">停用</el-radio>
          <el-radio :label="1">正常</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="sort" :label="$t('dept.sort')">
        <el-input-number v-model="dataForm.sort" controls-position="right" :min="0" :label="$t('dept.sort')"></el-input-number>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      deptList: [],
      deptListVisible: false,
      getDicListURL: '/sys/dict/type/',
      deptTypeoptions: [],
      dataForm: {
        id: '',
        code: '',
        deptType: '',
        name: '',
        pid: '',
        parentName: '',
        status: 1,
        sort: 0
      }
    }
  },
  computed: {
    dataRule () {
      return {
        code: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.getDeptTypeInfo()
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取机构类型信息
    getDeptTypeInfo () {
      this.$http.get(this.getDicListURL + 'deptType').then(({ data: res }) => {
        this.deptTypeoptions = {
          ...this.deptTypeoptions,
          ...res.data.list
        }
      }).catch(() => {})
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/sys/dept/${this.dataForm.id}`).then(({ data: res }) => {
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/sys/dept', this.dataForm).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>

<style lang="scss" scoped>
.mod-sys__dept {
  .dept-list {
    .el-input__inner,
    .el-input__suffix {
      cursor: pointer;
    }
  }
}
</style>
