<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON>qiang
 * @Date: 2020-08-26 09:43:09
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-09-22 09:14:07
-->
<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : '浏览'" :close-on-click-modal="false" :close-on-press-escape="false"  width="70%">
    <el-form :model="dataForm" ref="dataForm"   :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
      <el-form-item prop="title" :label="$t('news.title')">
        <el-input v-model="dataForm.title" disabled :placeholder="$t('news.title')"></el-input>
      </el-form-item>
      <el-form-item label="类型" prop="newType">
        <el-select v-model="dataForm.newType" disabled clearable placeholder="消息类型">
          <el-option
                  v-for="item in newTypeoptions"
                  :key="item.dictCode"
                  :label="item.dictName"
                  :value="item.dictCode"
          >
          </el-option>
        </el-select>

      </el-form-item>
      <el-form-item disabled prop="content" :label="$t('news.content')">
        <div class="content" ref='box'>

        </div>
      </el-form-item>
      <el-form-item prop="pubDate" disabled :label="$t('news.pubDate')">
        {{dataForm.pubDate}}
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import 'quill/dist/quill.snow.css'
export default {
  data () {
    return {
      visible: false,
      uploadUrl: '',
      getDicListURL: '/sys/dict/type/',
      newTypeoptions: '',
      dataForm: {
        id: '',
        title: '',
        newType: '',
        content: '',
        pubDate: ''
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.getNewType()
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/demo/news/${this.dataForm.id}`).then(({ data: res }) => {
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        this.$refs.box.innerHTML = res.data.content
      }).catch(() => {
      })
    },
    getNewType () {
      this.$http.get(this.getDicListURL + 'newType').then(({ data: res }) => {
        this.newTypeoptions = {
          ...this.newTypeoptions,
          ...res.data.list
        }
        // this.$set(this.dataForm, 'newType', this.newTypeoptions[0].dictCode)
      }).catch(() => { })
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
