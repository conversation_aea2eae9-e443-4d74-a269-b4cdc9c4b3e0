<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="colConfig">
          <el-form-item label="编码" prop="code">
            <el-input v-model="dataForm.code" placeholder="编码"></el-input>
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="名称"></el-input>
          </el-form-item>
          <!-- <el-form-item label="图标" prop="icon">
            <el-input v-model="dataForm.icon" placeholder="图标"></el-input>
          </el-form-item> -->
          <el-form-item label="保函模板类型" prop="path">
            <el-input v-model="dataForm.path" placeholder="保函模板类型"></el-input>
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-radio-group v-model="dataForm.type">
              <el-radio :label="0">保函</el-radio>
              <el-radio :label="1">出具机构</el-radio>
            </el-radio-group>
            <!-- <el-input v-model="dataForm.type" placeholder="类型，0保函 1出具机构"></el-input> -->
          </el-form-item>
          <el-form-item label="保函时长" prop="time" >
            <el-input v-model="dataForm.time" placeholder="保函时长"></el-input>
          </el-form-item>
          <el-form-item label="出具机构说明" prop="tips">
            <el-input v-model="dataForm.tips" placeholder="出具机构说明"></el-input>
          </el-form-item>
            <!-- {{applyList}} -->
          <el-form-item label="出具机构可支持的保函类型" prop="jurisdiction" v-if="dataForm.type==1&&applyList.length>0">
            <!-- {{dataForm.jurisdiction}} -->
            <!-- <el-input v-model="dataForm.jurisdiction" placeholder="出具机构可支持的保函类型"></el-input> -->
            <!-- {{applyList}} -->
            <el-checkbox-group v-model="jurisdictionList" @change='jurisdictionListChange' >
              <el-checkbox v-for="item in applyList" :label="item.code" :key='item.code' :value="item.code">{{item.name}}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="状态" prop="status" size="mini">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="0">停用</el-radio>
              <el-radio :label="1">正常</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      applyList: [],
      jurisdictionList: [],
      dataForm: {
        id: '',
        code: '',
        name: '',
        icon: '',
        path: '',
        type: '',
        time: '',
        tips: '',
        jurisdiction: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        status: 1
      }
    }
  },
  computed: {
    dataRule () {
      return {
        code: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        icon: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        path: [
          {
            required: false,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        type: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        time: [
          {
            required: false,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        tips: [
          {
            required: false,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        jurisdiction: [
          {
            required: false,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        status: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
    this.getList()
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    jurisdictionListChange (val) {
      // console.log(val)
      if (val) {
        this.dataForm.jurisdiction = val.join(',')
      } else {
        this.dataForm.jurisdiction = ''
      }
    },
    // 获取信息
    getInfo () {
      this.$http
        .get(`/letter/bgApplyConfigure/${this.dataForm.id}`)
        .then(({ data: res }) => {
          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
          this.jurisdictionList = this.dataForm.jurisdiction.split(',')
        })
        .catch(() => {})
    },
    getList () {
      this.$http.get('/letter/bgApplyConfigure/page', { params: {
        order: '',
        orderField: '',
        page: 1,
        type: '0',
        limit: 50,
        name: ''
      } }).then(({ data: res }) => {
        this.applyList = Array.from(res.data.list)
      })
        .catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) {
            return false
          }
          this.$http[!this.dataForm.id ? 'post' : 'put'](
            '/letter/bgApplyConfigure/',
            this.dataForm
          )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            })
            .catch(() => {})
        })
      },
      1000,
      { leading: true, trailing: false }
    )
  }
}
</script>
