<template>
  <div>
    <el-dialog
            title="角色选择"
            :visible.sync="dialogVisible"
            width="80%" :close-on-click-modal="false" :close-on-press-escape="false"
    >
      <el-form :inline="true" :model="dataForm" >
        <el-form-item>
          <el-input v-model="dataForm.name" :placeholder="$t('role.name')" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="danger"   @click="getDataList(1)">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success"   @click="selectHandle()">选择</el-button>
        </el-form-item>
      </el-form>

      <el-table v-loading="dataListLoading" :data="dataList" border @row-dblclick="selectHandle"   @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle"   style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="name" :label="$t('role.name')" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" :label="$t('role.remark')" header-align="center" align="center"></el-table-column>
        <el-table-column prop="createDate" :label="$t('role.createDate')" sortable="custom" header-align="center" align="center" width="180"></el-table-column>
      </el-table>
      <el-pagination
              :current-page="page"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="limit"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="pageSizeChangeHandle"
              @current-change="pageCurrentChangeHandle">
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
import Bus from '@/api/bus.js'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/sys/role/page',
        getDataListIsPage: true,
        deleteURL: '/sys/role',
        deleteIsBatch: true
      },
      dialogVisible: false
    }
  },
  methods: {
    init () {
      this.dialogVisible = true
      this.getDataList()
    },
    selectHandle (row) {
      if (row) {
        Bus.$emit('send', row.id + ',' + row.name)
      } else {
        if (!row && this.dataListSelections.length !== 1) {
          return this.$message({
            message: '请选择且只选择一个操作项',
            type: 'warning',
            duration: 500
          })
        }
        this.dataListSelections.map(item =>
          Bus.$emit('send', item.id + ',' + item.name)
        )
      }
      this.dialogVisible = false
    }
  }
}

</script>
