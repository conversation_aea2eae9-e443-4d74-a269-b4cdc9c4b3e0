<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2020-08-24 15:28:48
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-06-16 15:31:13
-->
<template>
  <div class="detail">
    <template v-if="dataForm.key ==='ddbx'">
      <el-col :xs="24" :lg="draw?24:12">
        <el-form-item label="投标截止日期">
          {{dataForm.bgbiddingproject.bidOpenDate?dataForm.bgbiddingproject.bidOpenDate:'-'}}
        </el-form-item>
      </el-col>

      <el-col :xs="24" :lg="draw?24:12">
        <el-form-item label="招标有效期">
          {{dataForm.bgbiddingproject.tenderValid?dataForm.bgbiddingproject.tenderValid:'-'}} 天
        </el-form-item>
      </el-col>
      <el-col :xs="24" :lg="draw?24:12">
        <el-form-item label="承包合同价格">
          {{dataForm.bgbiddingproject.price?dataForm.bgbiddingproject.price:'-'}} 万元
        </el-form-item>
      </el-col>
    </template>
    <template v-else>
      <el-col :xs="24" :lg="draw?24:12">
        <el-form-item label="投标日期">
          {{dataForm.bgbiddingproject.tbDate?dataForm.bgbiddingproject.tbDate:'-'}}
        </el-form-item>
      </el-col>
    </template>
       <el-row>
        <el-col :span="24">
          <el-divider></el-divider>

        </el-col>
       </el-row>
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span class="title">被保人信息（招标方）</span>
      </div>
      <el-row>
        <el-col :span="24">
          <el-form-item label="名称">
            {{fomate('bginsurancebbr','name')}}
          </el-form-item>
        </el-col>
        <el-col :span="draw?24:12">
          <el-form-item label="证件号码">
            {{fomate('bginsurancebbr','certCode')}}
          </el-form-item>
        </el-col>
        <el-col :span="draw?24:12">
          <el-form-item label="证件类型">
            {{certType(dataForm.bginsurancebbr.certType,'certTypeoptions')}}
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span class="title">保函信息</span>
      </div>
      <el-row>
        <el-col :span="draw?24:12">
          <el-form-item label="保函类型">
            {{dataForm.bginsuranceinfo.insuranceType?certType(dataForm.bginsuranceinfo.insuranceType,'insuranceTypeoptions'):''}}
          </el-form-item>
        </el-col>
        <el-col :span="draw?24:12">
          <el-form-item label="担保金额">
            {{fomate('bginsuranceinfo','guaranteeAmount')}}元
          </el-form-item>
        </el-col>
        <el-col :span="draw?24:12">
          <el-form-item label="投保单号">
            {{fomate('bginsuranceinfo','propsalNo')}}
          </el-form-item>
        </el-col>
        <el-col :span="draw?24:12">
          <el-form-item label="保函开始/结束日期">
            {{dataForm.bginsuranceinfo.startDate}} - {{dataForm.bginsuranceinfo.endDate}}
          </el-form-item>
        </el-col>
        <el-col :span="draw?24:12">
          <el-form-item label="保费">
            {{fomate('bginsuranceinfo','guaranteePrice')}} 元
          </el-form-item>
        </el-col>
        <!-- <el-col :span="draw?24:12">
          <el-form-item label="保单号(验真)">
            {{fomate('bginsuranceinfo','insuranceCode')}}
          </el-form-item>
        </el-col> -->
      </el-row>
    </el-card>
    <el-card class="box-card" shadow="never"  v-if="$route.name!='openbidtimeDetail'">
      <div slot="header" class="clearfix">
        <span class="title">发票信息</span>
      </div>
      <el-row>
        <el-col :span="draw?24:12">
          <el-form-item label="发票类型">
            {{certType(dataForm.bgguaranteeinvoice.invoiceType,'invoiceTypeoptions')}}
          </el-form-item>
        </el-col>
        <el-col :span="draw?24:12">
          <el-form-item label="开票对象">
            {{certType(dataForm.bgguaranteeinvoice.invoiceObject,'invoiceObjectoptions')}}
          </el-form-item>
        </el-col>
        <el-col :span="draw?24:12">
          <el-form-item label="纳税人类型">
            {{certType(dataForm.bgguaranteeinvoice.taxpayerType,'taxpayerTypeoptions')}}
          </el-form-item>
        </el-col>
        <el-col :span="draw?24:12">
          <el-form-item label="纳税人识别号">
            {{fomate('bgguaranteeinvoice','taxpayerNumber')}}
          </el-form-item>
        </el-col>
        <template v-if="dataForm.key ==='ddbx'">
          <el-col :span="24">
            <el-form-item label="发票抬头" prop="invoiceTitle">
              {{fomate('bgguaranteeinvoice','invoiceTitle')}}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="开户行名称" prop="bankAccountName">
              {{fomate('bgguaranteeinvoice','bankAccountName')}}
            </el-form-item>
          </el-col>
          <el-col :span="draw?24:12">
            <el-form-item label="银行账户号码" prop="bankAccountNo">
              {{fomate('bgguaranteeinvoice','bankAccountNo')}}
            </el-form-item>
          </el-col>
          <el-col :span="draw?24:12">
            <el-form-item label="税务登记电话" prop="phoneNumber">
              {{fomate('bgguaranteeinvoice','phoneNumber')}}
            </el-form-item>
          </el-col>
          <el-col :span="draw?24:12">
            <el-form-item label="发票接收邮箱" prop="electronicInvoiceEmail">
              {{fomate('bgguaranteeinvoice','electronicInvoiceEmail')}}
            </el-form-item>
          </el-col>
          <el-col :span="draw?24:12">
            <el-form-item label="发票接收手机号" prop="electronicInvoicePhone">
              {{fomate('bgguaranteeinvoice','electronicInvoicePhone')}}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="税务登记地址" prop="address">
              {{fomate('bgguaranteeinvoice','address')}}
            </el-form-item>
          </el-col>
        </template>
        <template v-else>
          <template v-if="dataForm.bgguaranteeinvoice.invoiceType ==='2' ">
            <el-col :xs="24" :lg="24">
              <el-form-item label="发票抬头" prop="invoiceTitle">
                {{fomate('bgguaranteeinvoice','invoiceTitle')}}
              </el-form-item>
              <el-form-item label="开户行名称" prop="bankAccountName">
                {{fomate('bgguaranteeinvoice','bankAccountName')}}
              </el-form-item>
            </el-col>
            <el-col :xs="24" :lg="draw?24:12">
              <el-form-item label="银行账户号码" prop="bankAccountNo">
                {{fomate('bgguaranteeinvoice','bankAccountNo')}}
              </el-form-item>
              <el-form-item label="税务登记电话" prop="phoneNumber">
                {{fomate('bgguaranteeinvoice','phoneNumber')}}
              </el-form-item>
            </el-col>
            <el-col :xs="24" :lg="draw?24:12">
              <el-form-item label="发票接收邮箱" prop="electronicInvoiceEmail">
                {{fomate('bgguaranteeinvoice','electronicInvoiceEmail')}}
              </el-form-item>
              <el-form-item label="发票接收手机号" prop="electronicInvoicePhone">
                {{fomate('bgguaranteeinvoice','electronicInvoicePhone')}}
              </el-form-item>
            </el-col>
            <el-col :xs="24" :lg="24">
              <el-form-item label="税务登记地址" prop="address">
                {{fomate('bgguaranteeinvoice','address')}}
              </el-form-item>
            </el-col>
          </template>
          <template v-else>
            <el-col :xs="24" :lg="24">
              <el-form-item label="发票抬头" prop="invoiceTitle">
                {{fomate('bgguaranteeinvoice','invoiceTitle')}}
              </el-form-item>
            </el-col>
            <el-col :xs="24" :lg="draw?24:12">
              <el-form-item label="发票接收手机号" prop="electronicInvoicePhone">
                {{fomate('bgguaranteeinvoice','electronicInvoicePhone')}}
              </el-form-item>
            </el-col>
            <el-col :xs="24" :lg="draw?24:12">
              <el-form-item label="发票接收邮箱" prop="electronicInvoiceEmail">
                {{fomate('bgguaranteeinvoice','electronicInvoiceEmail')}}
              </el-form-item>
            </el-col>
          </template>
        </template>
        <el-col :xs="24" :lg="24">
          <el-form-item label="备注" prop="remark">
            {{fomate('bgguaranteeinvoice','remark')}}
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>

    <el-card class="box-card" shadow='never'  v-if="dataForm.key !=='ddbx'&&dataForm.bgGuaranteeMailing">
      <div slot="header" class="clearfix">
        <span class="title">收件信息</span>
      </div>
      <div>
        <el-col :xs="24" :lg="draw?24:12">
          <el-form-item label="收件人">
            {{fomate('bgGuaranteeMailing','mailingUser')}}
          </el-form-item>

        </el-col>
        <el-col :xs="24" :lg="draw?24:12">
          <el-form-item label="收件电话">
            {{fomate('bgGuaranteeMailing','mailingTel')}}
          </el-form-item>
        </el-col>
        <el-col :xs="24" :lg="draw?24:12">
          <el-form-item label="收件地址">
            {{fomate('bgGuaranteeMailing','mailingAddress')}}
          </el-form-item>
        </el-col>
      </div>
    </el-card>
  </div>
</template>
<script>
export default {
  props: {
    dataForm: Object,
    options: Object,
    draw: Boolean
  },
  watch: {
    options: {
      handler (a) {
        console.log(a)
        this.options = a
      },
      deep: true
    }
  },
  methods: {
    fomate (key, val) {
      return this.dataForm[key][val] ? this.dataForm[key][val] : '-'
    },
    certType (val, name) {
      console.log(this.options)
      var aa = this.options[name].filter((a) => a.dictCode === val)
      if (name === 'letterStatusoptions') {
        console.log(aa)
      }
      return aa.length > 0 ? aa[0].dictName : '-'
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  /* padding: 16px 0; */
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
