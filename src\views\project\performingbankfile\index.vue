<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="tip" v-if="iscom">
                <p>
                <span style="color:#F56C6C;">请按文件要求邮寄原件到以下地址：</span>
                <br>
                联系人:{{pakeage.contactPerson}}
                &emsp;
                联系人电话:{{pakeage.contactPhone}}
                <br>
                邮寄地址:{{pakeage.mailingAddress}}
                </p>
              </div>
    <div class="mod-letter__bgPerformanceDocuments}">

      <el-form v-if="!iscom" :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-input v-model="dataForm.name" clearable placeholder="名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model="dataForm.status" clearable placeholder="请选择启用状态">
            <el-option v-for="item in statusoptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getDataList(1)">{{
            $t('query')
          }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="danger" @click="resetHandle()">重置</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="exportHandle()">{{
            $t('export')
          }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="importHandle()">{{
            $t('import')
          }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="
            $hasPermission('letter:bgPerformanceDocuments:save')
          " type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="
            $hasPermission(
              'letter:bgPerformanceDocuments:delete'
            )
          " type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="
            $hasPermission(
              'letter:bgPerformanceDocuments:update'
            )
          " type="danger" @click="enableHandle()">{{ $t('enable') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="
            $hasPermission(
              'letter:bgPerformanceDocuments:update'
            )
          " type="danger" @click="stopHandle()">{{ $t('stop') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" sizi="mini" :data="dataList" border @selection-change="dataListSelectionChangeHandle"
        @sort-change="dataListSortChangeHandle" style="width: 100%">
        <el-table-column v-if="!iscom" type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="name" label="文件名称"  header-align="center"
          align="center"></el-table-column>
        <el-table-column prop="documentNum" label="文件数量"  header-align="center"
          align="center"></el-table-column>
        <el-table-column prop="documentDemand" width="250" label="文件要求"  header-align="center"
          align="center">
          <template slot-scope="scope">
            <span style="color:red;">{{scope.row.documentDemand}}  </span>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" width="250"  header-align="center"
          align="center"></el-table-column>
        <el-table-column prop="issueCode" label="出具方编码" v-if="!iscom" header-align="center"
          align="center"></el-table-column>
        <el-table-column prop="issueName" label="出具方名称" v-if="!iscom" header-align="center"
          align="center"></el-table-column>
        <el-table-column prop="guaranteeType" label="保函类型" v-if="!iscom" header-align="center"
          align="center"></el-table-column>
        <el-table-column prop="createDate" label="创建时间" v-if="!iscom" header-align="center"
          align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button v-if="scope.row.instructions" type="text" size="small" @click="onPreview(scope.row.instructions)">说明文件预览

            </el-button>
            <el-button v-if="scope.row.template" type="text" size="small" @click="down(scope.row.template)">下载模板</el-button>
            <el-button v-if="
              $hasPermission(
                'letter:bgPerformanceDocuments:update'
              )
            " type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
            <el-dropdown v-if="
              $hasPermission(
                'letter:bgPerformanceDocuments:update'
              )
            ">
              <span class="el-dropdown-link">
                <el-button type="text" size="mini">更多操作<i class="el-icon-arrow-down el-icon--right"></i></el-button>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="
                    $hasPermission(
                      'letter:bgPerformanceDocuments:delete'
                    )
                  " type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete')
}}</el-button></el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="
                    $hasPermission(
                      'letter:bgPerformanceDocuments:update'
                    ) && scope.row.status == 0
                  " type="text" size="small" @click="enableHandle(scope.row.id)">{{ $t('enable')
}}</el-button></el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="
                    $hasPermission(
                      'letter:bgPerformanceDocuments:update'
                    ) && scope.row.status == 1
                  " type="text" size="small" @click="stopHandle(scope.row.id)">{{ $t('stop') }}</el-button></el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination v-if="!iscom" :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total"
        layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>

      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
      <!-- 弹窗, 上传文件 -->
      <upload v-if="uploadVisible" ref="upload" :uploadUrl="uploadUrl" :downloadUrl="downloadUrl"
        @refreshDataList="refreshList"></upload>
      <!-- 浏览 -->
      <el-image
              v-show="false"
              ref="preview"
              :preview-src-list="srcList">
            </el-image>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './bgPerformanceDocuments-add-or-update'
import Upload from '@/components/project/hjUpload'
export default {
  mixins: [mixinViewModule],
  props: {
    iscom: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    iscom (val) {
      console.log('val', val)
      this.getDataList(1)
    }
  },
  data () {
    return {
      uploadUrl: '/letter/bgPerformanceDocuments/upload', // 上传链接
      downloadUrl: '/letter/bgPerformanceDocuments/downloadExcel', // 下载链接
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgPerformanceDocuments/page',
        getDataListIsPage: true,
        exportURL: '/letter/bgPerformanceDocuments/export',
        deleteURL: '/letter/bgPerformanceDocuments',
        enableURL: '/letter/bgPerformanceDocuments/enable',
        stopURL: '/letter/bgPerformanceDocuments/stop',
        deleteIsBatch: true
      },
      srcList: [

      ],
      pakeage: {},
      statusoptions: [
        {
          value: 1,
          label: '正常'
        },
        {
          value: 0,
          label: '停用'
        }
      ],
      dataForm: {
        name: '',
        status: 1
      },
      limit: 20,
      uploadVisible: false
    }
  },
  components: {
    AddOrUpdate,
    Upload
  },
  mounted () {
    console.log('val111', this.iscom)
    if (this.iscom) this.getDataList()
    console.log(this.dataList)
  },
  methods: {
    onPreview (file) {
      this.srcList = []
      file.split(',').map(a => {
        // console.log(a)
        this.srcList.push(`${window.SITE_CONFIG['apiURL']}/sys/oss/minioPreview?id=${a}`)
      })
      // console.log(this.srcList)
      setTimeout(() => {
        this.$refs.preview.clickHandler()
      }, 300)
    },
    down (id) {
      window.open(
        `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${id}`
      )
    },
    // 导入
    importHandle () {
      this.uploadVisible = true
      this.$nextTick(() => {
        this.$refs.upload.init()
      })
    },
    // 状态启用具体代码需微调格式去除空格
    enableHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(
        this.$t('prompt.info', { handle: '启用' }),
        this.$t('prompt.title'),
        {
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          this.$http
            .put(
              this.mixinViewModuleOptions.enableURL,
              id
                ? [id]
                : this.dataListSelections.map((item) => item.id)
            )
            .then(({ data: res }) => {
              if (res.code !== 0) {
                return this.$message.error(res.msg)
              }
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getDataList()
                }
              })
            })
            .catch(() => {})
        })
        .catch(() => {})
    },
    // 状态停用
    stopHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(
        this.$t('prompt.info', { handle: '停用' }),
        this.$t('prompt.title'),
        {
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          // 具体代码需微调格式去除空格
          this.$http
            .put(
              this.mixinViewModuleOptions.stopURL,
              id
                ? [id]
                : this.dataListSelections.map((item) => item.id)
            )
            .then(({ data: res }) => {
              if (res.code !== 0) {
                return this.$message.error(res.msg)
              }
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getDataList()
                }
              })
            })
            .catch(() => {})
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.tip {
    padding: 8px 16px;
    background-color: #ecf8ff;
    border-radius: 4px;
    border-left: 5px solid #50bfff;
    margin: 0px 0 20px;
    width: 100%;
    position: sticky;
    top: 0px;
    z-index: 1;
    text-align: left;
    p {
        font-size: 14px;
        color: #5e6d82;
        line-height: 1.5em;
    }
}

</style>
