<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts' // echarts theme
import resize from './mixins/resize'
// import { checkTaskResultYear } from '@/api/remote-search'

require('echarts/theme/macarons')
const PieChartData1 = {
  untreated: 3,
  processing: 4,
  processingCompleted: 6
}

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    autoResize: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      chart: null,
      PieChartData1: PieChartData1
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy () {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  watch: {
    PieChartData1: {
      deep: true,
      handler (val) {
        this.setOptions(val)
      }
    }
  },
  methods: {
    initChart () {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(PieChartData1)
      // checkTaskResultYear().then(response => {
      //   PieChartData1.untreated = response.data.data.untreated
      //   PieChartData1.processing = response.data.data.processing
      //   PieChartData1.processingCompleted = response.data.data.processingCompleted
      // })
    },
    setOptions ({ untreated, processing, processingCompleted } = {}) {
      this.chart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          left: 'center',
          bottom: '10',
          data: ['未处理', '处理中', '处理完成']
        },
        series: [
          {
            name: '客户意见处理统计',
            type: 'pie',
            roseType: 'radius',
            radius: [15, 95],
            center: ['50%', '38%'],
            data: [
              { value: untreated, name: '未处理' },
              { value: processing, name: '处理中' },
              { value: processingCompleted, name: '处理完成' }
            ],
            animationEasing: 'cubicInOut',
            animationDuration: 2600
          }
        ]
      })
    }
  }

}
</script>
