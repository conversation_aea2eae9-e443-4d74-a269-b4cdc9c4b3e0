<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2020-04-24 16:26:01
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-08-26 15:30:34
 -->
<template>
  <el-dialog :visible.sync="visible" class="qiantip" width="610px">
    <div slot="title" style="font-size:16px;font-weight:500;">签章提示</div>
    <div style="margin-top:-15px;">
      <!-- {{letterId}} -->
      <div>
         <div class="tip">
        <i class="el-icon-warning-outline" style="color:#068DFF;"></i> <div>使用河北CA签章，目前只支持在ie浏览器（需安装河北CA及签章控件）</div>
      </div>
      <div>
        <el-input v-model="url" size="mini" readonly placeholder="请输入内容">
          <el-button slot="append" type="success" style="background:#068DFF;color:white;    height: 28px;" size="mini" v-clipboard:copy="url" v-clipboard:success="onCopy" v-clipboard:error="onError">
            复制</el-button>
        </el-input>
      </div>
      </div>
      <div>
        <el-timeline :reverse="reverse" :timestamp="''">

          <el-timeline-item>
            <div slot="dot">
              <div class="dot">1</div>
            </div>
            <div class="itm">
              复制链接
            </div>
            <div><img style="width:100%;" src="@/assets/img/w1.png" alt=""></div>
          </el-timeline-item>
          <el-timeline-item>
            <div slot="dot">
              <div class="dot">2</div>
            </div>
            <div class="itm">
              打开IE浏览器（推荐IE11），将第一步复制的链接粘贴到标签页内， 进入签章页面
            </div>
            <div class="tip">
              <i class="el-icon-warning-outline" style="color:#068DFF;"></i> <div>IE浏览器不识别河北CA是，点击IE设置—“Internet选项”— “安全”—“添加平台网址为信任站点”，应用确定</div>
            </div>
            <div style="width:477px;"><img style="width:100%;" src="@/assets/img/w2.png" alt=""></div>
          </el-timeline-item>
          <el-timeline-item>
            <div slot="dot">
              <div class="dot">3</div>
            </div>
            <div class="itm">
              当所有文件完成显示签章后，点击完成签章按钮继续操作
            </div>
             <div style="width:477px;"><img style="width:100%;" src="@/assets/img/w4.png" alt=""></div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="visible = false">知道了</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  data () {
    return {
      letterId: '',
      visible: false,
      reverse: false,
      url: '',
      sqs: '',
      wthtId: ''
    }
  },
  mounted () {},
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.url =
           window.SITE_CONFIG['bhtURL'] +
                    `#/signatureIe?sqs=${this.sqs}&wthtId=${this.wthtId}`
      })
    },
    // 复制成功时的回调函数
    onCopy (e) {
      this.$message.success('内容已复制到剪切板！')
    },
    // 复制失败时的回调函数
    onError (e) {
      this.$message.error('抱歉，复制失败！')
    }
  }
}
</script>
<style scoped>
.el-dialog__body {
  padding: 15px 20px 30px;
}
.tip {
  /* height: 36px; */
  background: rgba(230, 247, 255, 1);
  border: 1px solid rgba(145, 213, 255, 1);
  line-height: 36px;
  margin-bottom: 10px;
  color: #333;
  display: flex;
}
.el-icon-warning-outline {
  color: rgb(6, 141, 255);
  margin: 0 10px 0 15px;
  font-size: 20px;
  position: relative;
  top: 8px;
}
.dot {
  width: 32px;
  height: 32px;
  background: rgba(6, 141, 255, 1);
  border-radius: 16px;
  color: white;
  line-height: 32px;
  text-align: center;
  font-size: 16px;
}
.el-timeline {
  padding-left: 0 !important;
  margin-top: 40px;
}

.itm {
  font-size: 16px;
  font-family: PingFang SC;
  font-weight: bold;
  color: rgba(51, 51, 51, 1);
  /* height: 48px; */
  line-height: 39px;
}
</style>
