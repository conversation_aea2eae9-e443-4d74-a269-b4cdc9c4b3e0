<!--
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019-09-18 14:03:54
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2025-01-03 15:34:17
 -->
<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <meta name="description" content="宏融E保是一站式银行保函办理平台，3分钟办理，24小时出函，全流程线上办理，价格透明，没有中间商赚差价。支持微信端，随时随地急速办理。">
  <meta name="keyword"
    content="河北保函，招标通保函，宏融E保，宏融e保，保函通，保函，保函办理，保函在线办理，电子保函，工程保函，保证金，投标保证金，担保公司，银行保函，投标保函，履约保函，手机办理保函，保函查询，免交保证金">
  <meta name="viewport"
    content="width=device-width,initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no">
  <link rel="shortcut icon" href="<%= BASE_URL %>favicon.ico">
  <!-- <% if (process.env.VUE_APP_NODE_ENV === 'prod:sit') { %>
  <script type="text/javascript" src="js/funny.js"></script>
  <% } %> -->
  <!-- 站点配置 -->
  <style>
    #npHebcaP11Plugin {
      visibility: hidden !important;
    }
  </style>
  <script>
    window.SITE_CONFIG = {};
    window.SITE_CONFIG['baseURL'] = '<%= BASE_URL %>';
    window.SITE_CONFIG['version'] = 'v2.0.0';
    window.SITE_CONFIG['nodeEnv'] = '<%= process.env.VUE_APP_NODE_ENV %>';
    window.SITE_CONFIG['apiURL'] = '';                      // api请求地址
    window.SITE_CONFIG['bhtURL'] = '';                      // bht请求地址
    window.SITE_CONFIG['fileView'] = '';                      // bpm请求地址
    window.SITE_CONFIG['storeState'] = {};                  // vuex本地储存初始化状态（用于不刷新页面的情况下，也能重置初始化项目中所有状态）
    window.SITE_CONFIG['contentTabDefault'] = {             // 内容标签页默认属性对象
      'name': '',        // 名称, 由 this.$route.name 自动赋值（默认，名称 === 路由名称 === 路由路径）
      'params': {},      // 参数, 由 this.$route.params 自动赋值
      'query': {},       // 查询参数, 由 this.$route.query 自动赋值
      'menuId': '',      // 菜单id（用于选中侧边栏菜单，与this.$store.state.sidebarMenuActiveName进行匹配）
      'title': '',       // 标题
      'isTab': true,     // 是否通过tab展示内容?
      'iframeURL': ''    // 是否通过iframe嵌套展示内容? (以http[s]://开头, 自动匹配)
    };
    window.SITE_CONFIG['menuList'] = [];                     // 左侧菜单列表（后台返回，未做处理）
    window.SITE_CONFIG['permissions'] = [];                  // 页面按钮操作权限（后台返回，未做处理）
    window.SITE_CONFIG['dynamicRoutes'] = [];                // 动态路由列表
    window.SITE_CONFIG['dynamicMenuRoutes'] = [];            // 动态(菜单)路由列表
    window.SITE_CONFIG['setLock'] = false;                    // 是否设置锁屏
    window.SITE_CONFIG['dynamicMenuRoutesHasAdded'] = false; // 动态(菜单)路由是否已经添加的状态标示（用于判断是否需要重新拉取数据并进行动态添加操作）
  </script>

  <!-- 开发环境 -->
  <% if (process.env.VUE_APP_NODE_ENV==='dev' ) { %>
    <script>
        window.SITE_CONFIG['apiURL'] = 'http://test.hebhzjt.com/hzjt-dzbh';
        window.SITE_CONFIG['wsURL'] = 'ws://test.hebhzjt.com:8091/hzjt-dzbh';
      // window.SITE_CONFIG['apiURL'] = 'http://************:9090/hzjt-dzbh';
      // window.SITE_CONFIG['wsURL'] = 'ws://************:9090/hzjt-dzbh';
      //  window.SITE_CONFIG['apiURL'] = 'http://***********:9099/hzjt-dzbh';
      // window.SITE_CONFIG['wsURL'] = 'ws://***********:9099/hzjt-dzbh';
      // window.SITE_CONFIG['apiURL'] = 'http://***************:9090/hzjt-dzbh';
      // window.SITE_CONFIG['wsURL'] = 'ws://test.hebhzjt.com:8091/hzjt-dzbh';
      // window.SITE_CONFIG['apiURL'] = 'http://zbbht.hebztb.com/hzjt-dzbh';
      // window.SITE_CONFIG['wsURL'] = 'ws://zbbht.hebztb.com/hzjt-dzbh'
      window.SITE_CONFIG['bhtURL'] = 'http://test.hebhzjt.com/zbbht/';
      window.SITE_CONFIG['yfdURL'] = 'http://************/dzbh-yfd-test/';
      // window.SITE_CONFIG['fileView'] = 'http://test.hebhzjt.com:8012/onlinePreview?url='
      window.SITE_CONFIG['fileView'] = 'http://dzbh-fileview.hebztb.com/onlinePreview?url='

    </script>
    <% } %>
      <!-- 集成测试环境 -->
      <% if (process.env.VUE_APP_NODE_ENV==='prod:sit' ) { %>
        <script>
          window.SITE_CONFIG['apiURL'] = 'http://test.hebhzjt.com/hzjt-dzbh';
          window.SITE_CONFIG['wsURL'] = 'ws://test.hebhzjt.com:8091/hzjt-dzbh';
          window.SITE_CONFIG['bhtURL'] = 'http://test.hebhzjt.com/zbbht/';
          window.SITE_CONFIG['yfdURL'] = 'http://************/dzbh-yfd-test/';
          // window.SITE_CONFIG['fileView'] = 'http://test.hebhzjt.com:8012/onlinePreview?url='
      window.SITE_CONFIG['fileView'] = 'http://dzbh-fileview.hebztb.com/onlinePreview?url='

        </script>
        <% } %>
          <!-- 验收测试环境 -->
          <% if (process.env.VUE_APP_NODE_ENV==='prod:uat' ) { %>
            <script>
              window.SITE_CONFIG['apiURL'] = 'http://www.hebhzjt.com/hzjt-dzbh';
              window.SITE_CONFIG['bhtURL'] = 'http://www.hebhzjt.com/zbbht/';
              // window.SITE_CONFIG['fileView'] = 'http://************:8012/onlinePreview?url='
      window.SITE_CONFIG['fileView'] = 'http://dzbh-fileview.hebztb.com/onlinePreview?url='

            </script>
            <% } %>
              <!-- 生产环境 -->
              <% if (process.env.VUE_APP_NODE_ENV==='prod' ) { %>
                <script>
                  window.SITE_CONFIG['apiURL'] = 'http://zbbht.hebztb.com/hzjt-dzbh';
                  window.SITE_CONFIG['wsURL'] = 'ws://zbbht.hebztb.com/hzjt-dzbh'
                  window.SITE_CONFIG['bhtURL'] = 'http://zbbht.hebztb.com/manage/';
                  // window.SITE_CONFIG['fileView'] = 'http://*************:8012/onlinePreview?url='
      window.SITE_CONFIG['fileView'] = 'http://dzbh-fileview.hebztb.com/onlinePreview?url='

                </script>
                <% } %>
</head>

<body>
  <div id="app"></div>
</body>

</html>
