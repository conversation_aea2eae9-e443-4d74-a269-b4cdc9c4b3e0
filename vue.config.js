/*
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON>ang
 * @Date: 2020-05-12 18:09:05
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-02-09 10:38:55
 */
/**
 * 配置参考: https://cli.vuejs.org/zh/config/
 */
const CompressionPlugin = require('compression-webpack-plugin')
const UglifyJsPlugin = require('uglifyjs-webpack-plugin')
let { openGzip } = require('./package.json')
const productionGzipExtensions = ['js', 'css']

module.exports = {
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  runtimeCompiler: true,
  chainWebpack: config => {
    const svgRule = config.module.rule('svg')
    svgRule.uses.clear()
    svgRule
      .test(/\.svg$/)
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')

    // // 移除 prefetch 插件
    // config.plugins.delete('prefetch')

    // 或者
    // 修改它的选项：
    config.plugin('prefetch').tap(options => {
      options[0].fileBlacklist = options[0].fileBlacklist || []
      options[0].fileBlacklist.push(/myasyncRoute(.)+?\.js$/)
      return options
    })
  },
  configureWebpack: (config) => {
    // 把webpack的配置写在这里 会自动合并
    // 以下库使用cdn，不会被打包
    if (process.env.NODE_ENV === 'production') {
      if (openGzip) {
        config.plugins = [
          ...config.plugins,
          new CompressionPlugin({
            test: new RegExp('\\.(' + productionGzipExtensions.join('|') + ')$'),
            threshold: 10240, // 对超过10k的数据压缩
            deleteOriginalAssets: true // 不删除源文件
          })
        ]
      }
      config.plugins.push(
        new UglifyJsPlugin({
          uglifyOptions: {
            // 生产环境自动删除console
            compress: {
              // warnings: false, // 若打包错误，则注释这行
              drop_debugger: true,
              drop_console: true,
              pure_funcs: ['console.log']
            }
          },
          exclude: /\.min\.js$/, // 过滤掉以".min.js"结尾的文件，我们认为这个后缀本身就是已经压缩好的代码，没必要进行二次压缩
          sourceMap: false,
          parallel: true
        })
      )
    }
  },
  css: {
    loaderOptions: {
      less: {
        javascriptEnabled: true
      }
    }
  },
  productionSourceMap: process.env.NODE_ENV !== 'production',
  devServer: {
    open: true,
    port: 8099,
    hot: true,
    overlay: {
      errors: true,
      warnings: true
    }
  }
}
