<template>
  <div>
    <el-card class="box-card await" v-loading='updateLoading'>
      <steps :active='2' v-if="dataForm.guaranteeType=='ssdbbh'" :customList="['选择保函', '提交申请', '初审审核','补充信息','上传文件','复审审核']"></steps>
      <steps :active='4' v-if="dataForm.guaranteeType!=='ssdbbh'"></steps>
      <el-row style="margin-top:50px;" :gutter="12">
        <div v-if="letterStatus==40" class='error'>
          <el-alert title="警告：支付时请根据招标文件要求慎重选择支付方式，如因支付方式选择错误导致保函失效，不予退款！" type="warning" center :closable="false" show-icon>
          </el-alert>
        </div>
        <el-col :span="24">
          <el-card shadow="never">
            <div class="img" v-if="letterStatus==10">
              <img src="@/assets/img/wait.png" alt="">
              <p class="tip">已提交，等待保函处理中{{dataForm.guaranteeType=='ssdbbh'?',请等待初审结果':''}}...</p>
              <el-link type="primary" @click="update(1)">刷新保函状态</el-link>
            </div>
            <div class="img" v-else-if="letterStatus==20">
              <img src="@/assets/img/wait.png" alt="">
              <p class="tip">已提交，等待保函处理中{{dataForm.guaranteeType=='ssdbbh'?',请等待初审结果':''}}...</p>
              <el-link type="primary" @click="update(1,1)"><span v-if="timer">（{{count}}秒后）</span><span v-if="!timer">手动</span>刷新保函状态</el-link>
            </div>
            <div class="img" v-else-if="letterStatus==21">
              <img src="@/assets/img/fail.png" alt="">
              <p class="tip">对接失败，查看原因并重新提交</p>
              <p style="color:#F56C6C;">错误原因：{{letterRetMsg}}</p>
              <el-link type="primary" @click="update(1,1)"><span v-if="timer">（{{count}}秒后）</span><span v-if="!timer">手动</span>刷新保函状态</el-link>
            </div>
            <div class="img" v-else-if="letterStatus==30">
              <img src="@/assets/img/wait.png" alt="">
              <p class="tip">待审核，已转人工审核...</p>
              <el-link type="primary" @click="update(1,1)"><span v-if="timer">（{{count}}秒后）</span><span v-if="!timer">手动</span>刷新保函状态</el-link>
            </div>
            <div class="img" v-else-if="letterStatus==40">

              <img src="@/assets/img/success.png" alt="">
              <p class="tip">审核通过，请支付!</p>
              <el-button type="primary" style="margin-top:15px;" :loading="btnLoading" @click="pay">支付</el-button>
            </div>
            <div class="img" v-else-if="letterStatus==41">
              <img src="@/assets/img/fail.png" alt="">
              <p class="tip">审核失败，请重新提交!</p>
            </div>
            <div class="img" v-else-if="letterStatus==80">
              <img src="@/assets/img/wait.png" alt="">
              <p class="tip">退保审核中,请稍等！</p>
            </div>
            <div class="statusBox" v-else-if="!(letterStatus===40 || letterStatus===41)" v-loading='updateLoading'>
              {{letterRetMsg}}
            </div>
            <div v-else>
              <img src="@/assets/img/wait.png" alt="">
              <p class="tip">审核中，请等待审核结果...</p>
              <el-link type="primary" @click="update(1)">刷新保函状态</el-link>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

  </div>
</template>
<script>
import moment from 'moment'
import steps from '@/views/project/guarantee/components/stepA'

export default {
  data () {
    return {
      letterId: '',
      letterStatus: '',
      payInfo: '',
      btnLoading: false,
      letterRetMsg: '暂未返回处理结果',
      active: 2,
      updateLoading: false,
      dataForm: {},
      type: 20, // 10.未提交，20.已提交，待处理，21.对接失败，30.审核中，40.审核通过
      // 倒计时
      count: '',
      timer: null,
      TIME_COUNT: 3
    }
  },
  components: { steps },
  created () {
    if (this.$route.query.id) {
      this.letterId = this.$route.query.id
      this.update()
    }
  },
  mounted () {
    // this.$refs.steps.$children.map((item, index) => {
    //   item.$el.getElementsByClassName('el-step__head')[0].lastChild.innerHTML =
    //     index + 1
    // })
  },
  methods: {
    moment,
    getCode () {
      if (!this.timer) {
        this.count = this.TIME_COUNT
        this.timer = setInterval(() => {
          // console.log(this.TIME_COUNT)
          if (this.count > 0 && this.count <= this.TIME_COUNT) {
            this.count--
          } else {
            clearInterval(this.timer)
            this.update(null, 1)
            this.timer = null
          }
        }, 1000)
      }
    },
    update (e, a) {
      this.updateLoading = true
      // 获取信息
      this.$http
        .get(`/letter/bgguaranteeletter/${this.letterId}`)
        .then(({ data: res }) => {
          if (res.code !== 0) {
            this.updateLoading = false
            return
          }
          this.letterStatus = Number(res.data.letterStatus)
          this.dataForm = res.data
          if ((this.letterStatus === 20 || this.letterStatus === 21) && !a) {
            this.getCode()
          }
          if (e) {
            this.$message.success('刷新成功！')
          }
          this.letterRetMsg = res.data.retMsg
          this.updateLoading = false
        })
        .catch(() => {})
    },
    getPayMentInfo () {
      // 获取信息
      return new Promise((resolve, reject) => {
        this.$http
          .get(
            `/letter/bgguaranteeletter/getPayUrl/${this.letterId}` +
              `?key=${this.$route.query.insuranceCode}`
          )
          .then(({ data: res }) => {
            this.updateLoading = false

            resolve(res.data)
          })
          .catch((rej) => {
            reject(rej.msg)
          })
      })
    },
    async pay () {
      this.$loading({
        lock: true,
        text: `请求支付中`
      })
      this.btnLoading = true
      var data = await this.getPayMentInfo()
      if (this.$route.query.id) {
        var payInfo = data
        this.$loading().close()
        // var href = payInfo.payUrl + '?requestDoc={"serialNo":"' + payInfo.serialNo + '","requestHead": { "timestamp":"' + payInfo.timestamp + '","sign":"' + payInfo.sign + '","tradeDate":"' + this.moment(new Date()).format('YYYY-MM-DD HH:mm:ss') + '","tradeNo":"' + payInfo.tradeNo + '","nonce":"' + payInfo.nonce + '","cooperation":"' + payInfo.cooperation + '"},"payType":"' + payInfo.payType + '"}'
        // console.log(href)
        // window.open(href, '_blank')
        window.location.href = encodeURI(payInfo)
        this.btnLoading = false
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.wd180 {
  width: 240px !important;
}
.img {
  text-align: center;
  margin: 40px;
}
.tip {
  margin-top: 30px;
  font-size: 17px;
  font-family: '黑体';
}
.statusBox {
  width: 400px;
  background: rgba(241, 130, 65, 0.15);
  margin: 30px auto;
  padding: 20px;
  color: rgb(241, 130, 65);
  border: 1px solid rgb(241, 130, 65);
}
.box-card {
  max-width: 1200px;
  margin: 15px auto;
  margin-bottom: 65px;
  h3 {
    border-bottom: 1px dashed rgb(241, 130, 65);
    padding: 15px;
    text-align: center;
  }
  .el-step__head.is-finish {
    color: rgb(241, 130, 65);
    border-color: rgb(241, 130, 65);
  }
  .el-step__title.is-finish {
    color: rgb(241, 130, 65);
  }
  .formCon {
    width: 55vw;
    min-width: 750px;
    margin: 30px auto;
    .itemCon {
      display: flex;
      // justify-content:space-between;
      flex-wrap: wrap;
      .item {
        border: 1px solid #e2e2e2;
        display: flex;
        transition: 0.3s all;
        padding: 0 20px;
        margin-right: 15px;
        margin-bottom: 15px;
        position: relative;
        cursor: pointer;
        overflow: hidden;
        .el-icon-success {
          display: none;
          position: absolute;
          top: -2px;
          right: -2px;
          color: rgb(241, 130, 65);
        }
        span {
          display: inline-block;
          height: 70px;
          line-height: 70px;
        }
        span:first-child {
          margin: 0;
          img {
            width: 35px;
            height: 35px;
          }
        }
      }
      .active {
        border: 1px solid rgb(241, 130, 65);
        .el-icon-success {
          display: block !important;
        }
      }
      .item:hover {
        border: 1px solid rgb(241, 130, 65);
      }
    }

    .disCon {
      width: 100%;
      border: 1px solid #e2e2e2;
      padding: 0 15px;
      h4 {
        margin: 0;
      }
      .des {
        text-indent: 28px;
        /* word-spacing: 20px; */
        line-height: 32px;
        letter-spacing: 1px;
        font-size: 13px;
      }
      .serTable {
        .el-table__header {
          line-height: 24px;
        }
        .aui-wrapper .el-table th {
          background-color: black !important;
        }
      }
    }
  }
}
.price {
  color: rgb(241, 130, 65);
  font-weight: bold;
  font-size: 20px;
}
.foot {
  width: 100%;
  background: #fff;
  position: fixed;
  bottom: 0;
  padding: 10px 0;
  border-top: 1px solid #ddd;
  background: #f9f9f9;
  text-align: center;
  left: 80px;
  z-index: 999;
  span {
    text-align: center;
  }
}
.error {
  margin: 0 auto 10px;
  width: 1158px;
}
</style>
