<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2021-01-04 17:28:34
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-01-05 17:22:18
-->
<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-letter__bgguaranteetemplate}">
      <el-form :inline="true" :model="dataForm">
        <!-- <el-form-item>
          <el-input size='small'  v-model="dataForm.name" clearable placeholder="名称"></el-input>
        </el-form-item> -->
        <el-form-item >
            <el-select v-model="dataForm.type" size="small" placeholder="请选择支付方式" clearable="">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
        </el-form-item>
         <el-form-item >
            <el-select v-model="dataForm.letterStatus" size="small" placeholder="出具状态" clearable="">
              <el-option
                v-for="item in payOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
        </el-form-item>
        <el-form-item >
          <el-date-picker v-model="value1" size="small" @change="dateChange" type="daterange" value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
          <el-form-item>
          <el-input size='small'  v-model="dataForm.issueName" clearable placeholder="出具方"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size='small' @click="getDataList(1)">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size='small' @click="exportHandle()">导出</el-button>
        </el-form-item>

      </el-form>
      <el-table :span-method="objectSpanMethod" v-loading="dataListLoading" size='small' :data="dataList" border @selection-change="dataListSelectionChangeHandle"
        @sort-change="dataListSortChangeHandle" style="width: 100%;">
        <!-- <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column> -->
        <el-table-column prop="issueName" label="出具方" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="applyName" label="申请单位" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="code" label="订单号" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="guaranteePrice" label="支付金额" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="payTime" label="支付时间" sortable="custom" header-align="center" align="center"></el-table-column>
      </el-table>
      <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
      <!-- 弹窗, 上传文件 -->
      <upload v-if="uploadVisible" ref="upload" :uploadUrl=uploadUrl :downloadUrl=downloadUrl @refreshDataList="refreshList"></upload>
      <!-- 浏览 -->
      <display v-if="displayVisible" ref="display" @refreshDataList="refreshList"></display>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgguaranteeletter/statisticalPaymentList',
        getDataListIsPage: true,
        exportURL: '/letter/bgguaranteeletter/statisticalPayment',
        deleteIsBatch: true
      },
      statusoptions: [
        {
          value: 1,
          label: '正常'
        },
        {
          value: 0,
          label: '停用'
        }
      ],
      limit: 10,
      regionIds: [],
      spanArr: [],
      options: [{
        value: '2',
        label: '线上'
      }, {
        value: '1',
        label: '线下'
      }],
      payOptions: [{
        value: '50,90',
        label: '全部出具状态'
      }, {
        value: '50',
        label: '已出具'
      }, {
        value: '90',
        label: '已退款'
      }],
      radio: 1,
      value1: '',
      props: {
        value: 'code',
        label: 'name',
        children: 'child',
        isLeaf: 'leaf',
        lazy: true,
        lazyLoad: this.lazyLoad,
        collapseTags: true
      },
      dataForm: {
        startDate: '',
        endDate: '',
        type: '1',
        letterStatus: '50,90',
        issueName: ''
      },
      uploadVisible: false
    }
  },
  components: {},
  methods: {
    regionIdChange (val) {
      this.$set(this.dataForm, 'regionCode', val ? val[1] : '')
    },
    getValue (val) {
      this.value1 = []
      this.value1 = val
      this.dateChange(this.value1)
    },
    dateChange (val) {
      // this.value1 = []
      if (val) {
        this.$set(this.dataForm, 'startDate', val[0])
        this.$set(this.dataForm, 'endDate', val[1])
      } else {
        this.$set(this.dataForm, 'startDate', '')
        this.$set(this.dataForm, 'endDate', '')
      }
    },
    expandChange (val) {
      console.log(val)
    },
    async lazyLoad (node, resolve) {
      // 点击节点
      const { level } = node
      let nodes = await this.lazyloadFn(level === 0 ? 100000 : node.value)
      let nodes2 = nodes.map((item) => ({
        code: item.code,
        name: item.name,
        leaf: level > 0
      }))

      resolve(nodes2)
    },
    lazyloadFn (parentCode) {
      // 获取node
      return new Promise((resolve, reject) => {
        this.$http
          .get(`/demo/tbregion/regionTree?code=${parentCode}`)
          .then(({ data: res }) => {
            resolve(res.data)
            // this.zongingList = res.data
          })
          .catch(() => {})
      })
    },
    // 获取数据列表
    getDataList (p) {
      if (this.mixinViewModuleOptions.getDataListURL !== '') {
        this.dataListLoading = true
        this.$http
          .get(this.mixinViewModuleOptions.getDataListURL, {
            params: {
              order: this.order,
              orderField: this.orderField,
              page:
                p ||
                (this.mixinViewModuleOptions.getDataListIsPage
                  ? this.page
                  : null),
              limit: this.mixinViewModuleOptions.getDataListIsPage
                ? this.limit
                : null,
              ...this.dataForm
            }
          })
          .then(({ data: res }) => {
            this.dataListLoading = false
            if (res.code !== 0) {
              this.dataList = []
              this.total = 0
              return
            }
            this.dataList = this.mixinViewModuleOptions.getDataListIsPage
              ? res.data.list
              : res.data
            this.getSpanArr(this.dataList)
            this.total = this.mixinViewModuleOptions.getDataListIsPage
              ? res.data.total
              : 0
          })
          .catch(() => {
            this.dataListLoading = false
          })
      }
    },
    getSpanArr (data) {
      this.spanArr = []
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          // 如果是第一条记录（即索引是0的时候），向数组中加入１
          this.spanArr.push(1)
          this.pos = 0
        } else {
          if (data[i].issueName === data[i - 1].issueName) {
            // 如果fatherName相等就累加，并且push 0
            this.spanArr[this.pos] += 1
            this.spanArr.push(0)
          } else {
            // 不相等push 1
            this.spanArr.push(1)
            this.pos = i
          }
        }
      }
      // console.log(this.spanArr)
    },
    objectSpanMethod ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const _row = this.spanArr[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    }

  }
}
</script>
