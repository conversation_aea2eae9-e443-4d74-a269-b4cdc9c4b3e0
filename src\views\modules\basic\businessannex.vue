<template>
    <el-container>
        <el-aside width="200px" style="background-color: rgb(255, 255, 255)">
            <el-input
                    placeholder="输入关键字进行过滤"
                    v-model="filterText">
            </el-input>
            <el-tree
                    v-loading="treeLoading"
                    class="filter-tree"
                    :data="treeData"
                    :props="defaultProps"
                    default-expand-all
                    :highlight-current=true
                    :filter-node-method="filterNode"
                    @node-click="handleNodeClick"
                    ref="tree">
            </el-tree>
        </el-aside>
        <el-main style="padding-top: 0px">
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-basic__businessannex}">
      <el-form :inline="true" :model="dataForm" >
               <el-form-item >
                  <el-input v-model="dataForm.code" clearable  placeholder="附件编码"></el-input>
              </el-form-item>
              <el-form-item >
                  <el-input v-model="dataForm.name" clearable placeholder="附件名称"></el-input>
              </el-form-item>
              <el-form-item >
                  <el-input v-model="dataForm.projectCode"  clearable placeholder="文件夹编码"></el-input>
              </el-form-item>
              <el-form-item >
                  <el-input v-model="dataForm.projectName" clearable  placeholder="文件夹名称"></el-input>
              </el-form-item>
          <el-form-item >
              <el-select v-model="dataForm.phase" clearable  placeholder="请选择类型">
                  <el-option
                          v-for="item in phaseoptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value">
                  </el-option>
              </el-select>
          </el-form-item>
              <el-form-item>
                  <el-input v-model="dataForm.ywTableName"  clearable placeholder="业务表名"></el-input>
              </el-form-item>
              <el-form-item >
                  <el-input v-model="dataForm.annexName"  clearable  placeholder="云附件名称"></el-input>
              </el-form-item>
          <el-form-item >
              <el-select v-model="dataForm.status" clearable  placeholder="请选择启用状态">
                  <el-option
                          v-for="item in statusoptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value">
                  </el-option>
              </el-select>
          </el-form-item>
        <el-form-item>
          <el-button type="danger"   @click="getDataList(1)">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
           <el-button type="primary" @click="importHandle()">附件上传</el-button>
         </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission(Permission+':update')" type="danger" @click="enableHandle()">{{ $t('enable') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission(Permission+':update')" type="danger" @click="stopHandle()">{{ $t('stop') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle"   style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="code" label="附件编码" sortable="custom" header-align="center" align="center" width="150"></el-table-column>
            <el-table-column prop="name" label="附件名称" sortable="custom" header-align="center" align="center" width="150"></el-table-column>
            <el-table-column prop="projectCode" label="文件夹编码" sortable="custom" header-align="center" align="center" width="150"></el-table-column>
            <el-table-column prop="projectName" label="文件夹名称" sortable="custom" header-align="center" align="center" width="150"></el-table-column>
            <el-table-column prop="phase" label="文件类型" sortable="custom" header-align="center" align="center" width="150">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.phase == 0" size="mini" type="danger">系统资料</el-tag>
                    <el-tag v-if="scope.row.phase == 1" size="mini" type="danger">其他类型</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="ywTableName" label="业务表名" sortable="custom"  header-align="center" align="center" width="150"></el-table-column>
            <el-table-column prop="annexName" label="云附件名称" sortable="custom" header-align="center" align="center" width="150"></el-table-column>
              <el-table-column prop="status" sortable="custom" label="状态" header-align="center" align="center">
   <template slot-scope="scope">
       <el-tag v-if="scope.row.status == 0" size="mini" type="danger">{{ $t('user.status0') }}</el-tag>
       <el-tag v-else size="mini" type="success">{{ $t('user.status1') }}</el-tag>
   </template>
</el-table-column>
              <el-table-column prop="createDate" label="创建时间" sortable="custom" header-align="center" align="center" width="150"></el-table-column>
              <el-table-column prop="updateDate" label="更新时间" sortable="custom" header-align="center" align="center" width="150"></el-table-column>
              <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="180">
          <template slot-scope="scope">
               <el-button v-if="$hasPermission(Permission+':update')" type="text" size="mini" @click="addOrUpdateHandle(scope.row.id)">浏览</el-button>
              <el-button type="text" size="mini" @click="displayHandle(scope.row.annexId,scope.row.annexName)">预览附件</el-button>
                <el-dropdown>
              <span class="el-dropdown-link">
                <el-button  type="text" size="mini">更多操作<i class="el-icon-arrow-down el-icon--right"></i></el-button>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item> <el-button v-if="$hasPermission(Permission+':delete')" type="text" size="mini" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button></el-dropdown-item>
                <el-dropdown-item> <el-button v-if="$hasPermission(Permission+':update')&&scope.row.status==0" type="text" size="mini" @click="enableHandle(scope.row.id)">{{ $t('enable') }}</el-button></el-dropdown-item>
                <el-dropdown-item>               <el-button v-if="$hasPermission(Permission+':update')&&scope.row.status==1" type="text" size="mini" @click="stopHandle(scope.row.id)">{{ $t('stop') }}</el-button></el-dropdown-item>
                <el-dropdown-item>     <el-button type="text" size="mini" @click="downloadHandle(scope.row.annexId,scope.row.name)">{{ $t('download') }}</el-button>        </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>

          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
        <!-- 弹窗, 上传文件 -->
      <upload v-if="uploadVisible" ref="upload" @refreshDataList="getDataList"></upload>
    </div>
  </el-card>
        </el-main>
    </el-container>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './businessannex-add-or-update'
import Upload from './businessannex-upload'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      Permission: 'basic:businessannex', // 权限名
      filterText: '',
      treeLoading: false,
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'projectName'
      },
      mixinViewModuleOptions: {
        getDataListURL: '/basic/businessannex/page',
        getDataListIsPage: true,
        exportURL: '/basic/businessannex/export',
        deleteURL: '/basic/businessannex',
        enableURL: '/basic/businessannex/enable',
        stopURL: '/basic/businessannex/stop',
        getTreeDataURL: '/basic/businessannex/tree',
        deleteIsBatch: true
      },
      statusoptions: [{
        value: 1,
        label: '正常'
      }, {
        value: 0,
        label: '停用'
      }],
      phaseoptions: [{
        value: 0,
        label: '系统资料'
      }, {
        value: 1,
        label: '其他类型'
      }],
      dataForm: {
        id: '',
        code: '',
        name: '',
        projectId: '',
        projectName: '',
        projectCode: '',
        phase: '',
        ywTableName: '',
        annexName: '',
        status: 1
      },
      orderField: 'phase',
      order: 'asc',
      uploadVisible: false
    }
  },
  components: {
    AddOrUpdate,
    Upload
  },
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val)
    }
  },
  activated () {
    // 通过路由参数pid, 控制列表请求操作
    this.getTreeDataList()
  },
  methods: {
    refreshList () {
      this.getTreeDataList()
      this.getDataList()
    },
    // 导入
    importHandle () {
      this.uploadVisible = true
      var params = { 'projectCode': this.dataForm.projectCode, 'projectName': this.dataForm.projectName, 'projectId': this.dataForm.projectId, 'phase': 0, 'ywTableName': '其他资料', 'ywTableId': '1111' }
      this.$nextTick(() => {
        this.$refs.upload.init(params)
      })
    },
    // 状态启用具体代码需微调格式去除空格
    enableHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(this.$t('prompt.info', { 'handle': '启用' }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.$http.put(this.mixinViewModuleOptions.enableURL, id ? [id] : this.dataListSelections.map(item => item.id)).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.getDataList()
            }
          })
        }).catch(() => {})
      }).catch(() => {})
    },
    downloadHandle (id, name) {
      var url = `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${id}`
      window.location.href = url
    },
    displayHandle (id, path) {
      var previewUrl = ''
      var url = `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${id}`
      previewUrl = url + '?fullfilename=' + path
      window.open(`${window.SITE_CONFIG['fileView']}` + encodeURIComponent(previewUrl))
    },
    // 状态停用
    stopHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(this.$t('prompt.info', { 'handle': '停用' }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        // 具体代码需微调格式去除空格
        this.$http.put(this.mixinViewModuleOptions.stopURL, id ? [id] : this.dataListSelections.map(item => item.id)).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.getDataList()
            }
          })
        }).catch(() => {})
      }).catch(() => {})
    },
    handleNodeClick (data) {
      this.dataForm.projectCode = data.projectCode
      this.dataForm.projectName = data.projectName
      this.dataForm.projectId = data.projectId
      this.getDataList()
    },
    // 树节点点击事件
    filterNode (value, data) {
      if (!value) return true
      return data.projectName.indexOf(value) !== -1
    },
    getTreeDataList: function () {
      this.treeLoading = true
      this.$http.get(
        this.mixinViewModuleOptions.getTreeDataURL,
        {
          params: {}
        }
      ).then(({ data: res }) => {
        this.treeLoading = false
        if (res.code !== 0) {
          this.treeData = []
          return
        }
        this.treeData = res.data
        // 默认显示第一节点的数据
        if (this.treeData && this.treeData.length > 0) {
          this.handleNodeClick({ projectCode: res.data[0].projectCode, projectName: res.data[0].projectName, projectId: res.data[0].projectId })
        }
      }).catch(() => {
        this.treeLoading = false
      })
    }
  }
}
</script>
