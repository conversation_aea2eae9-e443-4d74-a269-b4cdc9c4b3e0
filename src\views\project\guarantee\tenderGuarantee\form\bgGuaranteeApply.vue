<template>
  <el-row :gutter="20" style="margin:15px 20px 0;">
    <el-form v-if="dataForm" label-position="left" :model="dataForm" ref="dataForm" :rules="dataRule" label-width="140px">
      <el-card class="box-card" shadow='never'>
        <div slot="header" class="clearfix">
          <span class="title">投保人信息（投标方）</span>
          <!-- <el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button> -->
        </div>
        <div>
          <!-- <div class="temp-subtitle">投保人信息（投标方）</div> -->
          <el-col :xs="24" :xl="24">
            <div class="formCon">
              <el-row :gutter="40">
                <el-col :xs="24" :xl="16">
                  <el-form-item label="机构名称" prop="name">
                    <el-input v-model.trim="dataForm.name" size="small" :disabled="dataForm.id&&dataForm.status==2" class="wd180" style="width:100%;" placeholder="名称">
                      <el-button slot="append" icon="el-icon-search" type="success" @click="sqrcx">企查查
                      </el-button>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :xs="24" :lg='12' :xl="8">
                  <el-form-item label="法定代表人" prop="corporation">
                    <el-input v-model="dataForm.corporation" size="small" class="wd180" placeholder="法定代表人"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :xs="24" :lg='12' :xl="8">
                  <el-form-item label="机构证件类型" prop="certType">
                    <el-select v-model="dataForm.certType" size="small" :disabled="dataForm.id&&dataForm.status==2" class="wd180" style="width:100%;" placeholder="证件类型">
                      <el-option v-for="item in options.certTypeoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :xs="24" :lg='12' :xl="8">
                  <el-form-item label="机构证件号码" prop="certCode">
                    <el-input v-model.trim="dataForm.certCode" size="small" :disabled="dataForm.id&&dataForm.status==2" class="wd180" style="width:100%;" placeholder="证件号码"></el-input>
                  </el-form-item>
                </el-col>

              </el-row>
              <el-row :gutter="40">
                <el-col :xs="24" :lg='12' :xl="8">
                  <el-form-item label="联系人" prop="linkman">
                    <el-input v-model="dataForm.linkman" size="small" class="wd180" style="width:100%;" placeholder="联系人"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg='12' :xl="8">
                  <el-form-item label="联系人电话" prop="linkmanTel">
                    <el-input v-model="dataForm.linkmanTel" size="small" class="wd180" style="width:100%;" placeholder="联系人电话"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40" v-if="this.$route.params.insuranceCode == 'hybx'">
                <el-col :xs="24" :lg='16' :xl="16">
                  <el-form-item label="地址" prop="address">
                    <el-input v-model="dataForm.address" type="textarea" :rows="2" size="small" class="wd180" style="width:100%;" placeholder="联系人"></el-input>
                  </el-form-item>
                </el-col>

              </el-row>
            </div>
          </el-col>

        </div>
      </el-card>

    </el-form>
  </el-row>
</template>
<script>
export default {
  name: 'bgGuaranteeApply',
  data () {
    return {}
  },
  props: {
    dataForm: {
      type: Object
    },
    options: {
      type: Object
    }
  },
  computed: {
    dataRule () {
      var checkPhone = (rule, value, callback) => {
        if (!value) {
          return callback(new Error('手机号不能为空'))
        } else {
          const reg = /^1[3|4|5|7|8|9][0-9]\d{8}$/
          console.log(reg.test(value))
          if (reg.test(value)) {
            callback()
          } else {
            return callback(new Error('请输入正确的手机号'))
          }
        }
      }
      var checkZuoPhone = (rule, value, callback) => {
        if (!value) {
          return callback(new Error('请输入正确的电话号'))
        } else {
          // eslint-disable-next-line no-useless-escape
          const reg = /(^[0-9]{3,4}\-[0-9]{3,8}$)|(^[0-9]{3,8}$)|(^\([0-9]{3,4}\)[0-9]{3,8}$)|(^0{0,1}13[0-9]{9}$)/
          console.log(reg.test(value))
          if (reg.test(value)) {
            callback()
          } else {
            return callback(new Error('请输入正确的电话号'))
          }
        }
      }
      var certCodeRule = (rule, value, callback) => {
        var reg = /^[A-Za-z0-9]*-?[A-Za-z0-9]+$/
        if (this.dataForm.certType === '2') {
          if (value.length !== 18) {
            return callback(new Error('统一社会信用代码必须是18位'))
          } else {
            console.log()
            if (reg.test(value)) {
              callback()
            } else {
              return callback(new Error('统一社会信用代码格式不正确'))
            }
          }
        } else {
          if (value.length < 7) {
            return callback(new Error('证件号码长度最小7位'))
          } else {
            if (reg.test(value)) {
              callback()
            } else {
              return callback(new Error('证件号码格式不正确'))
            }
          }
        }
      }
      return {
        name: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        corporation: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        certType: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        certCode: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          },
          {
            validator:
              this.dataForm.id && this.dataForm.status === 2
                ? ''
                : certCodeRule,
            trigger: 'blur'
          }
        ],
        certTerm: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        certTermType: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        linkman: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        linkmanTel: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          },
          { validator: checkPhone, trigger: 'blur' }
        ],
        phoneNumber: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          },
          { validator: checkZuoPhone, trigger: 'blur' }
        ],
        address: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    sqrcx () {
      var url = 'https://www.qcc.com/search?key='
      window.open(url + this.dataForm.name)
    },
    push () {
      return new Promise((resolve, reject) => {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
</style>
