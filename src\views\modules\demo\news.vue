<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-demo__news">
      <el-form :inline="true" :model="dataForm" >
        <el-form-item>
          <el-input v-model="dataForm.title" :placeholder="$t('news.title')" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model="dataForm.newType" clearable placeholder="请选择消息类型">
            <el-option
                    v-for="item in newTypeoptions"
                    :key="item.dictCode"
                    :label="item.dictName"
                    :value="item.dictCode"
            >
            </el-option>
          </el-select>

        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="daterange"
            unlink-panels
            type="daterange"
            value-format="yyyy-MM-dd"
            :range-separator="$t('datePicker.range')"
            :start-placeholder="$t('datePicker.start')"
            :end-placeholder="$t('datePicker.end')">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button @click="getDataList(1)">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="danger" @click="resetHandle()">重置</el-button>
        </el-form-item>
         <el-form-item>
            <el-button type="primary" @click="importHandle()">{{ $t('import') }}</el-button>
          </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('demo:news:export')" type="success" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('demo:news:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('demo:news:deleteBatch')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table
        v-loading="dataListLoading"
        :data="dataList"
        border
        @selection-change="dataListSelectionChangeHandle"
        @sort-change="dataListSortChangeHandle"
        style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="title" :label="$t('news.title')" header-align="center" align="center"></el-table-column>
        <el-table-column prop="newType" :label="$t('news.newType')" sortable="custom" header-align="center" align="center" width="120">
          <template slot-scope="scope">
            <el-tag size="mini" type="danger"><span>{{fomatMethod(scope.row.newType)}}</span>
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="pubDate" :label="$t('news.pubDate')" sortable="custom" header-align="center" align="center" width="180"></el-table-column>
        <el-table-column prop="createDate" :label="$t('news.createDate')" sortable="custom" header-align="center" align="center" width="180"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="200">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="displayHandle(scope.row.id)">浏览</el-button>
            <!--<span>{{scope.row.creator}}</span>,<span>{{$store.state.user.id}}</span>-->
            <el-button v-if="$hasPermission('demo:news:update') && scope.row.creator == $store.state.user.id" type="text" size="mini" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
            <el-button v-if="$hasPermission('demo:news:delete') && scope.row.creator == $store.state.user.id" type="text" size="mini" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <display v-if="displayVisible" ref="display" @refreshDataList="getDataList"></display>
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
      <upload v-if="uploadVisible" ref="upload" @refreshDataList="refreshList"></upload>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import Display from './news-display'
import AddOrUpdate from './news-add-or-update'
import Upload from './news-upload'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/demo/news/page',
        getDataListIsPage: true,
        deleteURL: '/demo/news',
        deleteIsBatch: true,
        exportURL: '/demo/news/export'
      },
      getDicListURL: '/sys/dict/type/',
      daterange: null,
      newTypeoptions: '',
      newTypemaps: '',
      dataForm: {
        title: '',
        startDate: '',
        endDate: ''
      },
      uploadVisible: false
    }
  },
  watch: {
    daterange (val) {
      this.dataForm.startDate = val[0]
      this.dataForm.endDate = val[1]
    }
  },
  activated () {
    this.getNewType()
  },
  components: {
    AddOrUpdate,
    Display,
    Upload
  },
  methods: {
    // 导入
    importHandle () {
      this.uploadVisible = true
      this.$nextTick(() => {
        this.$refs.upload.init()
      })
    },
    fomatMethod (value) {
      return this.newTypemaps[value]
    },
    getNewType () {
      this.$http.get(this.getDicListURL + 'newType').then(({ data: res }) => {
        this.newTypeoptions = {
          ...this.newTypeoptions,
          ...res.data.list
        }
        this.newTypemaps = {
          ...this.newTypemaps,
          ...res.data.map
        }
      }).catch(() => { })
    }
  }
}
</script>
