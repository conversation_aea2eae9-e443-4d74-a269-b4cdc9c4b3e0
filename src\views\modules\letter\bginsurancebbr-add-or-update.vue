<template>
    <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false"
               :close-on-press-escape="false" width="70%">
        <el-form :model="dataForm" :rules="dataRule" ref="dataForm"
                 :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
            <el-row :gutter="20">
                <el-col :xs="24" :lg="colConfig">
                    <el-form-item label="编码" prop="code">
                        <el-input v-model="dataForm.code" placeholder="编码"></el-input>
                    </el-form-item>
                    <el-form-item label="名称" prop="name">
                        <el-input v-model="dataForm.name" placeholder="名称"></el-input>
                    </el-form-item>
                    <el-form-item label="证件类型" prop="certType">
                        <el-select v-model="dataForm.certType" clearable placeholder="请选择证件类型">
                            <el-option
                                    v-for="item in certTypeOptions"
                                    :key="item.dictCode"
                                    :label="item.dictName"
                                    :value="item.dictCode"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="证件号码" prop="certCode">
                        <el-input v-model="dataForm.certCode" placeholder="证件号码"></el-input>
                    </el-form-item>
                    <el-form-item label="传真" prop="fax">
                        <el-input v-model="dataForm.fax" placeholder="传真"></el-input>
                    </el-form-item>
                    <el-form-item label="邮编" prop="postcode">
                        <el-input v-model="dataForm.postcode" placeholder="邮编"></el-input>
                    </el-form-item>

                    <el-form-item label="法定代表人" prop="corporation">
                        <el-input v-model="dataForm.corporation" placeholder="法定代表人"></el-input>
                    </el-form-item>
                    <el-form-item label="邮箱" prop="email">
                        <el-input v-model="dataForm.email" placeholder="邮箱"></el-input>
                    </el-form-item>

                </el-col>
                <el-col :xs="24" :lg="colConfig">
                    <el-form-item label="公司电话" prop="phoneNumber">
                        <el-input v-model="dataForm.phoneNumber" placeholder="公司电话"></el-input>
                    </el-form-item>

                    <el-form-item label="联系人" prop="linkman">
                        <el-input v-model="dataForm.linkman" placeholder="联系人"></el-input>
                    </el-form-item>
                    <el-form-item label="联系人电话" prop="linkmanTel">
                        <el-input v-model="dataForm.linkmanTel" placeholder="联系人电话"></el-input>
                    </el-form-item>
                    <el-form-item label="证件有效期" prop="certTermType">
                        <el-select v-model="dataForm.certTermType" clearable placeholder="请选择证件有效期类型">
                            <el-option
                                    v-for="item in certTermTypeOptions"
                                    :key="item.dictCode"
                                    :label="item.dictName"
                                    :value="item.dictCode"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="所属平台编码" hidden prop="platformCode">
                        <el-input v-model="dataForm.platformCode" placeholder="所属平台编码"></el-input>
                    </el-form-item>
                    <el-form-item label="联系地址" prop="address">
                        <el-input v-model="dataForm.address" placeholder="联系地址"></el-input>
                    </el-form-item>
                    <el-form-item label="注册资金" prop="registerCapital">
                        <el-input v-model="dataForm.registerCapital" placeholder="注册资金"></el-input>
                    </el-form-item>
                    <el-form-item label="注册地址" prop="registerAddress">
                        <el-input v-model="dataForm.registerAddress" placeholder="注册地址"></el-input>
                    </el-form-item>
                    <el-form-item label="注册时间" prop="registerTime">
                        <el-input v-model="dataForm.registerTime" placeholder="注册时间"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template slot="footer">
            <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
            <el-button type="primary" @click="dataFormSubmitHandle()">确认</el-button>
        </template>
    </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'

export default {
  data () {
    return {
      colConfig: 2, // 列配置 1 2列
      visible: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        certType: '',
        certCode: '',
        certTerm: '',
        certTermType: '',
        linkman: '',
        linkmanTel: '',
        corporation: '',
        email: '',
        phoneNumber: '',
        fax: '',
        postcode: '',
        platformCode: '',
        address: '',
        description: '',
        registerAddress: '',
        registerCapital: '',
        registerTime: '',
        isVip: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      certTermTypeOptions: '',
      certTypeOptions: '',
      certTermTypemaps: '',
      getDicListURL: '/sys/dict/type/'
    }
  },
  computed: {
    dataRule () {
      return {
        code: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certTerm: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certTermType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        linkman: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        linkmanTel: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        phoneNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        address: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        status: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]

      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.getcertTermTypeInfo()
      this.getcertType()
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/letter/bginsurancebbr/${this.dataForm.id}`).then(({ data: res }) => {
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/letter/bginsurancebbr/', this.dataForm).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {
        })
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    // 获取机构类型
    getcertType () {
      this.$http.get(this.getDicListURL + 'certTypeTT').then(({ data: res }) => {
        this.certTypeOptions = {
          ...this.certTypeOptions,
          ...res.data.list
        }
      }).catch(() => {
      })
    },

    // 获取证件类型
    getcertTermTypeInfo () {
      this.$http.get(this.getDicListURL + 'certTermType').then(({ data: res }) => {
        this.certTermTypeOptions = {
          ...this.certTermTypeOptions,
          ...res.data.list
        }
      }).catch(() => {
      })
    },
    fomatMethod (value) {
      return this.certTermTypemaps[value]
    }
  }
}
</script>
