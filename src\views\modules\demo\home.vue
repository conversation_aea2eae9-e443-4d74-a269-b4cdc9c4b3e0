<template>
  <el-card shadow="hover" class="aui-card--fill">
    <div class="mod-home">
      <el-row :gutter="20">
        <el-col :span="12" :xs="24">
          <table>
            <tr>
              <td><h5>平台介绍</h5></td>
            </tr>
            <tr>
              <td>
                宏筑建通开发平台是一套轻量级的权限系统，主要包括用户管理、角色管理、部门管理、菜单管理、定时任务、参数管理、字典管理、文件上传、登录日志、操作日志、异常日志、文章管理、APP模块等功能。
                其中，还拥有多数据源、数据权限、国际化支持、Redis缓存动态开启与关闭、统一异常处理等技术特点。
              </td>
            </tr>
          </table>
          <table>
            <tr>
              <td colspan="2"><h5>技术选型</h5></td>
            </tr>
            <tr>
              <th>后台核心框架</th>
              <td>Spring Boot 2.X</td>
            </tr>
            <tr>
              <th>安全框架</th>
              <td>Apache Shiro</td>
            </tr>
            <tr>
              <th>持久层框架</th>
              <td>MyBatis-plus</td>
            </tr>
            <tr>
              <th>定时任务</th>
              <td>Quartz</td>
            </tr>
            <tr>
              <th>数据库连接池</th>
              <td>Druid</td>
            </tr>
            <tr>
              <th>数据库</th>
              <td>Redis</td>
            </tr>
            <tr>
              <th>工作流</th>
              <td>Activiti</td>
            </tr>
            <tr>
              <th>工具类</th>
              <td>Hutool</td>
            </tr>
            <tr>
              <th>前端框架</th>
              <td>Vue2、Vue-X</td>
            </tr>
            <tr>
              <th>前端UI框架</th>
              <td>Element-UI</td>
            </tr>
          </table>
        </el-col>
        <el-col :span="12" :xs="24">
          <table>
            <tr>
              <td><h5>更新日志</h5></td>
            </tr>
            <tr>
              <td>
                <el-collapse >
                  <el-collapse-item>
                    <template slot="title">
                      <div>
                        V1.1.0
                        <span style="float:right;width:30%">2019-06-01</span>
                      </div>
                    </template>
                    <ul>
                      <li>增加系统版本变更说明</li>
                      <li>优化logback日志管理，系统日志按info、warn和error分级存储，方便排查问题。
                      <li>增加用户登录认证功能Ehcache缓存机制的引入</li>
                      <li>增加CrudService接口，对增删改查进行封装，代码更简洁</li>
                      <li>优化主键移除uuid，基于Snowflake实现64位自增ID算法。</li>
                      <li>修复只有GET请求才携带时间戳</li>
                      <li>修复字段名creater修改为creator</li>
                      <li>修复定时任务状态显示BUG</li>
                    </ul>
                  </el-collapse-item>
                  <el-collapse-item>
                    <template slot="title">
                      <div>
                        V1.0.2
                        <span style="float:right;width:30%">2019-05-01</span>
                      </div>
                    </template>
                    <ul>
                      <li>增加多人多设备以及单人单设备登录模式功能</li>
                      <li>增加长时间不操作自动退出（时长可配置）</li>
                      <li>优化用户登录认证功能，Redis分布式缓存机制的引入。</li>
                      <li>优化FastDFS分布式文件、本地上传等功能</li>
                      <li>修复安全隐患</li>
                      <li>修复KLocwork静态代码测试问题Bug</li>
                    </ul>
                  </el-collapse-item>
                  <el-collapse-item>
                    <template slot="title">
                      <div>
                        V1.0.1
                        <span style="float:right;width:30%">2019-04-01</span>
                      </div>
                    </template>
                    <ul>
                      <li>修复API模块国际化问题Bug</li>
                      <li>修复代码生成器模板Bug</li>
                      <li>修复登录后，新建tab页系统重新登录Bug</li>
                    </ul>
                  </el-collapse-item>
                  <el-collapse-item>
                     <template slot="title">
                      <div>
                        V1.0.0
                        <span style="float:right;width:30%">2019-03-01</span>
                      </div>
                    </template>
                     <ul>
                      <li>SpringBoot新开发平台发布</li>
                    </ul>
                  </el-collapse-item>
                </el-collapse>
              </td>
            </tr>
          </table>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>
