<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON>qiang
 * @Date: 2020-12-30 16:03:27
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-12-30 18:12:04
-->
<template>
  <div>
    <el-col :xs="24" :lg="24" v-if="this.$route.params.insuranceCode === 'XTDB'">
      <!-- {{wtrfileList2[0].response.data.ossId}} -->

      <el-form-item label="委托人身份证上传" prop="wtr">
        <el-upload class="upload-demo avatar-uploader" ref="yyupload" :on-preview='onPreview' :on-remove='handleRemovewt' :action="wthtuploadUrl" :data='wtrData' :headers="myHeaders"
          :before-remove="beforeRemove" :show-file-list="false" :on-success='businessLicenseSuccessHandle' :before-upload="yybeforeUpload" :limit="1" :on-exceed="handleExceed"
          :file-list="wtrfileList">
          <!-- <el-button size="small" type="primary">点击上传</el-button> -->
          <template v-if="wtrfileList.length>0">
            <img :src="url+`${wtrfileList[0].id?wtrfileList[0].id:wtrfileList[0].response.data.ossId}`" class="avatar">
            <span class="el-upload-list__item-actions">
              <span class="el-upload-list__item-delete" @click.stop="handleRemovewt(wtrfileList[0],wtrfileList)">
                <i class="el-icon-delete"></i>
              </span>
            </span>
          </template>
          <span v-else class="avatar-uploader-icon">正面</span>

          <!-- <span slot="tip" class="el-upload__tip">&emsp;要求格式：jpg,jpeg,png</span> -->
        </el-upload>
        <el-upload class="upload-demo avatar-uploader" ref="yyupload" :on-preview='onPreview' :on-remove='handleRemovewt' :action="wthtuploadUrl" :data='wtrData2' :headers="myHeaders"
          :before-remove="beforeRemove" :show-file-list="false" :on-success='businessLicenseSuccessHandle' :before-upload="yybeforeUpload" :limit="1" :on-exceed="handleExceed"
          :file-list="wtrfileList2">
          <!-- <el-button size="small" type="primary">点击上传</el-button> -->
          <template v-if="wtrfileList2.length>0">
            <img v-if="wtrfileList2.length>0" :src="url+`${wtrfileList2[0].id?wtrfileList2[0].id:wtrfileList2[0].response.data.ossId}`" class="avatar">
            <span class="el-upload-list__item-actions">
              <span class="el-upload-list__item-delete" @click.stop="handleRemovewt(wtrfileList2[0],wtrfileList2)">
                <i class="el-icon-delete"></i>
              </span>
            </span>
          </template>
          <span v-else class="avatar-uploader-icon">反面</span>
          <span slot="tip" class="el-upload__tip">&emsp;要求格式：jpg,jpeg,png</span>
        </el-upload>
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="24" v-if="this.$route.params.insuranceCode === 'XTDB'">
      <el-form-item label="法人身份证上传" prop="fr">
        <el-upload class="upload-demo avatar-uploader" ref="yyupload" :on-preview='onPreview' :on-remove='handleRemovewt' :action="wthtuploadUrl" :data='frData' :headers="myHeaders"
          :before-remove="beforeRemove" :show-file-list="false" :on-success='businessLicenseSuccessHandle' :before-upload="yybeforeUpload" :limit="1" :on-exceed="handleExceed" :file-list="frfileList">
          <template v-if="frfileList.length>0">
            <img v-if="frfileList.length>0" :src="url+`${frfileList[0].id?frfileList[0].id:frfileList[0].response.data.ossId}`" class="avatar">
            <span class="el-upload-list__item-actions">
              <span class="el-upload-list__item-delete" @click.stop="handleRemovewt(frfileList[0],frfileList)">
                <i class="el-icon-delete"></i>
              </span>
            </span>
          </template>
          <span v-else class="avatar-uploader-icon">正面</span>
        </el-upload>
        <el-upload class="upload-demo avatar-uploader" ref="yyupload" :on-preview='onPreview' :on-remove='handleRemovewt' :action="wthtuploadUrl" :data='frData2' :headers="myHeaders"
          :before-remove="beforeRemove" :show-file-list="false" :on-success='businessLicenseSuccessHandle' :before-upload="yybeforeUpload" :limit="1" :on-exceed="handleExceed"
          :file-list="frfileList2">
          <template v-if="frfileList2.length>0">
            <img v-if="frfileList2.length>0" :src="url+`${frfileList2[0].id?frfileList2[0].id:frfileList2[0].response.data.ossId}`" class="avatar">
            <span class="el-upload-list__item-actions">
              <span class="el-upload-list__item-delete" @click.stop="handleRemovewt(frfileList2[0],frfileList2)">
                <i class="el-icon-delete"></i>
              </span>
            </span>
          </template>
          <span v-else class="avatar-uploader-icon">反面</span>
          <span slot="tip" class="el-upload__tip">&emsp;要求格式：jpg,jpeg,png</span>
        </el-upload>
      </el-form-item>
    </el-col>
  </div>
</template>
<script>
export default {
  data () {
    return {
      wtrData: { type: '10' },
      frData: { type: '11' },
      wtrData2: { type: '12' },
      frData2: { type: '13' },
      url: ''
    }
  },
  props: {
    myHeaders: {
      type: Object
    },
    wthtuploadUrl: {
      type: String
    },
    letterId: {
      type: String
    },
    wtrfileList: {
      type: Array
    },
    frfileList: {
      type: Array
    },
    wtrfileList2: {
      type: Array
    },
    frfileList2: {
      type: Array
    }
  },
  watch: {
    letterId (a) {
      this.wtrData = { ...this.wtrData, letterId: a }
      this.frData = { ...this.frData, letterId: a }
      this.wtrData2 = { ...this.wtrData2, letterId: a }
      this.frData2 = { ...this.frData2, letterId: a }
    }
  },
  mounted () {
    this.url = `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/`
  },
  methods: {
    onPreview (file) {
      console.log(file)
      if (file.ossId) {
        window.open(
          `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${file.ossId}`
        )
      } else {
        window.open(
          `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${file.id}`
        )
      }
    },
    yybeforeUpload (file) {
      let extension = file.name.substring(file.name.lastIndexOf('.') + 1)
      var lastName =
        extension !== 'jpg' &&
        extension !== 'JPG' &&
        extension !== 'jpeg' &&
        extension !== 'JPEG' &&
        extension !== 'png' &&
        extension !== 'PNG'
      if (lastName) {
        this.$message.warning('文件要求格式为：jpg,jpeg,png')
      }
      console.log(lastName)
      return !lastName
    },
    handleExceed (files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件`
      )
    },
    handleRemovewt (file, fileList) {
      console.log(fileList)
      if (file && file.status === 'success') {
        // 移除方法
        // var deleteId = file.id ? file.id : file.response.data.ossId
        return fileList.length > 0
          ? this.deleteFilewt(
            this.letterId,
            fileList[0].type
              ? fileList[0].type
              : fileList[0].response.data.type
          )
          : ''
      }
    },
    deleteFilewt (id, type) {
      return new Promise(async (resolve, reject) => {
        let { data } = await this.$http.get(
          'letter/guarantee/deleteBhFile?letterId=' + id + '&type=' + type
        )
        if (data.code !== 0) {
          resolve(false)
        }
        this.$emit('refresh')
        // type === 1 ? this.$emit('biddingDocumentId', '') : this.$emit('businessLicenseId', '')
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
        resolve(true)
      })
    },
    beforeRemove (file, fileList) {
      console.log(file, fileList)
      let a = true
      if (file && file.status === 'success') {
        console.log(file)
        a = this.$confirm(`确定移除 ${file.name}？`)
          .then(() => {
            this.handleRemovewt(file, fileList)
          })
          .catch(() => {})
      }
      return a
    },
    businessLicenseSuccessHandle (res, file, fileList) {
      console.log(res, file, fileList)
      let type = res.data.type
      if (res.data.flag !== true) {
        if (type === 10) {
          this.wtrfileList = []
          this.$emit('getFilewtr', [])
        }
        if (type === 11) {
          this.frfileList = []
          this.$emit('getFilewfr', [])
        }
        if (type === 12) {
          this.wtrfileList2 = []
          this.$emit('getFilewtr2', [])
        }
        if (type === 13) {
          this.frfileList2 = []
          this.$emit('getFilewfr2', [])
        }
        return this.$message.error(res.data.msg)
      }
      this.$message.success('上传成功')
      if (type === 10) {
        this.wtrfileList = fileList
        this.$emit('getFilewtr', fileList)
      }
      if (type === 11) {
        this.frfileList = fileList
        this.$emit('getFilewfr', fileList)
      }
      if (type === 12) {
        this.wtrfileList2 = fileList
        this.$emit('getFilewtr2', fileList)
      }
      if (type === 13) {
        this.frfileList2 = fileList
        this.$emit('getFilewfr2', fileList)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.el-upload__tip {
  color: #f56c6c;
}
</style>
