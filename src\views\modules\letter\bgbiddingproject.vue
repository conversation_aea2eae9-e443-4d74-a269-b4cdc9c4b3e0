<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-letter__bgbiddingproject}">
      <el-form :inline="true" :model="dataForm">
        <el-form-item>
          <el-input v-model="dataForm.name" style="width:400px;" clearable placeholder="标段名称"></el-input>
        </el-form-item>
        <!-- <el-form-item>
          <el-input v-model="dataForm.regionId" clearable placeholder="所属区域"></el-input>
        </el-form-item> -->
        <el-form-item>
          <el-cascader :options="regionIds" v-model.trim="dataForm.regionId" ref='region' size="medium" clearable
            class="wd180" style="width:250px;" :props="props"></el-cascader>
        </el-form-item>
        <!-- <el-form-item  >
          <el-date-picker v-model="daterange" type="daterange" unlink-panels value-format="yyyy-MM-dd" :range-separator="$t('datePicker.range')" :start-placeholder="'开工日期'" :end-placeholder="'开工日期'">
          </el-date-picker>
        </el-form-item> -->
        <!--        <el-form-item>-->
        <!--          <el-input v-model="dataForm.tenderee" clearable placeholder="招标人"></el-input>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item>-->
        <!--          <el-input v-model="dataForm.tendereeAgent" clearable placeholder="招标代理公司"></el-input>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item>-->
        <!--          <el-input v-model="dataForm.biddingName" clearable placeholder="投标工程名"></el-input>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item>-->
        <!--          <el-input v-model="dataForm.biddingToOffces" clearable placeholder="所属项目部"></el-input>-->
        <!--        </el-form-item>-->
        <el-form-item>
          <el-select v-model="dataForm.status" clearable placeholder="请选择启用状态">
            <el-option v-for="item in statusoptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="getDataList(1)">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="danger" @click="resetHandle()">重置</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>

        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgbiddingproject:save')" type="primary" @click="addOrUpdateHandle()">
            {{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgbiddingproject:delete')" type="danger" @click="deleteHandle()">{{
              $t('deleteBatch')
          }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgbiddingproject:update')" type="danger" @click="enableHandle()">{{
              $t('enable')
          }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgbiddingproject:update')" type="danger" @click="stopHandle()">{{
              $t('stop')
          }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle"
        @sort-change="dataListSortChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="name" label="标段名称" sortable="custom" header-align="center" align="center">
        </el-table-column>
        <el-table-column prop="issueCode" label="所属出具方" sortable="custom" header-align="center" align="center"
          width="170">
          <!-- <template slot-scope="scope">
            {{ fomateValue(scope.row.issueCode, gTypeList) }}
          </template> -->
        </el-table-column>
        <el-table-column prop="guaranteeType" label="可选保函类型" sortable="custom" header-align="center" align="center"
          width="170">
          <!-- <template slot-scope="scope">
            {{ fomateValue(scope.row.guaranteeType, guaranteeTypeList) }}
          </template> -->
        </el-table-column>
        <el-table-column prop="guaranteeAmount" label="担保金额（元）" sortable="custom" header-align="center" align="center"
          width="170"></el-table-column>
        <!--        <el-table-column prop="tenderee" label="招标人" sortable="custom" header-align="center" align="center" width="170"></el-table-column>-->
        <!--        <el-table-column prop="tendereeAgent" label="招标代理公司" sortable="custom" header-align="center" align="center" width="170"></el-table-column>-->
        <!--        <el-table-column prop="biddingName" label="投标工程名" sortable="custom" header-align="center" align="center" width="170"></el-table-column>-->
        <!--        <el-table-column prop="biddingToOffces" label="所属项目部" sortable="custom" header-align="center" align="center" width="170"></el-table-column>-->
        <!--        <el-table-column prop="remars" label="备注信息" sortable="custom" header-align="center" align="center" width="170"></el-table-column>-->
        <!--        <el-table-column prop="bidStartDate" label="标段开始日期" sortable="custom" header-align="center" align="center" width="170"></el-table-column>-->
        <el-table-column prop="region" label="所属区域" sortable="custom" header-align="center" align="center" width="170">
        </el-table-column>
        <el-table-column prop="platformCode" label="交易平台编码" sortable="custom" header-align="center" align="center"
          width="170"></el-table-column>
        <el-table-column prop="status" sortable="custom" label="状态" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == 0" size="small" type="danger">{{ $t('user.status0') }}</el-tag>
            <el-tag v-else size="small" type="success">{{ $t('user.status1') }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center">
          <template slot-scope="scope">
            <el-button v-if="$hasPermission('letter:bgbiddingproject:update')" type="text" size="small"
              @click="addOrUpdateHandle(scope.row.id, scope.row.region)">{{ $t('update') }}</el-button>
            <el-dropdown>
              <span class="el-dropdown-link">
                <el-button type="text" size="mini">更多操作<i class="el-icon-arrow-down el-icon--right"></i></el-button>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button type="text" size="small" @click="displayHandle(scope.row.id)">浏览</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('letter:bgbiddingproject:delete')" type="text" size="small"
                    @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('letter:bgbiddingproject:update') && scope.row.status == 0"
                    type="text" size="small" @click="enableHandle(scope.row.id)">{{ $t('enable') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('letter:bgbiddingproject:update') && scope.row.status == 1"
                    type="text" size="small" @click="stopHandle(scope.row.id)">{{ $t('stop') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total"
        layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
      <!-- 弹窗, 上传文件 -->
      <upload v-if="uploadVisible" ref="upload" @refreshDataList="getDataList"></upload>
      <!-- 浏览 -->
      <display v-if="displayVisible" ref="display" @refreshDataList="refreshList"></display>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './bgbiddingproject-add-or-update'
import Upload from './bgbiddingproject-upload'
import Display from './bgbiddingproject-display'
import { getDict } from '@/utils/index'

export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgbiddingproject/page',
        getDataListIsPage: true,
        exportURL: '/letter/bgbiddingproject/export',
        deleteURL: '/letter/bgbiddingproject',
        enableURL: '/letter/bgbiddingproject/enable',
        stopURL: '/letter/bgbiddingproject/stop',
        deleteIsBatch: true
      },
      gTypeList: getDict('出具机构'),
      guaranteeTypeList: getDict('电子保函类型'),
      statusoptions: [
        {
          value: 1,
          label: '正常'
        },
        {
          value: 0,
          label: '停用'
        }
      ],
      daterange: [],
      dataForm: {
        name: '',
        status: '',
        startDate: '',
        endDate: '',
        tenderee: '',
        tendereeAgent: '',
        biddingName: '',
        regionId: '',
        biddingToOffces: ''
      },
      orderField: 'create_date',
      order: 'desc',
      uploadVisible: false,
      regionIds: [],
      props: {
        value: 'code',
        label: 'name',
        children: 'child',
        isLeaf: 'leaf',
        lazy: true,
        lazyLoad: this.lazyLoad,
        checkStrictly: true,
        collapseTags: true
      }
    }
  },
  components: {
    AddOrUpdate,
    Upload,
    Display
  },
  watch: {
    dataForm: {
      handler (a) {
        // eslint-disable-next-line valid-typeof
        if (typeof (a.regionId) === 'object') {
          this.$set(a, 'regionId', a.regionId[a.regionId.length - 1])
        }
        // console.log(typeof (a.regionId))
      },
      deep: true
    },
    daterange (val) {
      if (val) {
        this.dataForm.startDate = val[0]
        this.dataForm.endDate = val[1]
      } else {
        this.dataForm.startDate = ''
        this.dataForm.endDate = ''
      }
    }
  },
  methods: {
    async lazyLoad (node, resolve) {
      // 点击节点
      const { level } = node
      let nodes = await this.lazyloadFn(level === 0 ? 100000 : node.value)
      let nodes2 = nodes.map((item) => ({
        code: item.code,
        name: item.name,
        leaf: level > 0
      }))

      resolve(nodes2)
    },
    lazyloadFn (parentCode) {
      // 获取node
      return new Promise((resolve, reject) => {
        this.$http
          .get(`/demo/tbregion/regionTree?code=${parentCode}`)
          .then(({ data: res }) => {
            resolve(res.data)
            // this.zongingList = res.data
          })
          .catch(() => { })
      })
    },
    // 导入
    importHandle () {
      this.uploadVisible = true
      this.$nextTick(() => {
        this.$refs.upload.init()
      })
    },
    addOrUpdateHandle (id, region) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.dataForm.id = id
        this.$refs.addOrUpdate.init()
      })
    },
    // 状态启用具体代码需微调格式去除空格
    enableHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(
        this.$t('prompt.info', { handle: '启用' }),
        this.$t('prompt.title'),
        {
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          this.$http
            .put(
              this.mixinViewModuleOptions.enableURL,
              id ? [id] : this.dataListSelections.map((item) => item.id)
            )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getDataList()
                }
              })
            })
            .catch(() => { })
        })
        .catch(() => { })
    },
    // 状态停用
    stopHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(
        this.$t('prompt.info', { handle: '停用' }),
        this.$t('prompt.title'),
        {
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          // 具体代码需微调格式去除空格
          this.$http
            .put(
              this.mixinViewModuleOptions.stopURL,
              id ? [id] : this.dataListSelections.map((item) => item.id)
            )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getDataList()
                }
              })
            })
            .catch(() => { })
        })
        .catch(() => { })
    }
  }
}
</script>
