<template>
  <el-card class="box-card" shadow="never">
     <el-card class="box-card" shadow="never">
        <!-- {{dataForm}} -->
        <bg-guarantee-apply v-if="dataForm.bgGuaranteeApply" :dataForm='dataForm.bgGuaranteeApply' :options='options' ref="bgGuaranteeApply"></bg-guarantee-apply>
        <bginsurancebbr v-if="dataForm.bginsurancebbr" :dataForm='dataForm.bginsurancebbr' :options='options' ref="bginsurancebbr" @show='showSyr'></bginsurancebbr>
        <bginsurancesyr v-if="dataForm.bginsurancesyr&&showSyrCom" :dataForm='dataForm.bginsurancesyr' :options='options' ref="bginsurancesyr"></bginsurancesyr>
        <bgbiddingproject v-if="dataForm.bgbiddingproject" @gettbDate='gettbDate' :dataForm='dataForm.bgbiddingproject' :options='options' ref="bgbiddingproject"></bgbiddingproject>
        <bginsuranceinfo v-if="dataForm.bginsuranceinfo" :tbDate='tbDate' @insuranceInfo='getInsuranceInfo' :dataForm='dataForm.bginsuranceinfo' :options='options' ref="bginsuranceinfo"></bginsuranceinfo>
        <bgguaranteeinvoice v-if="dataForm.bgguaranteeinvoice" :dataForm='dataForm.bgguaranteeinvoice' :options='options' ref="bgguaranteeinvoice"></bgguaranteeinvoice>
      </el-card>
    <div class="foot">
      <div style="margin-bottom:10px;">
         <el-checkbox v-model="checked">我已阅读并同意 <el-button type="text" @click="showDia('xieyi')">服务协议</el-button></el-checkbox>
      </div>
      <span>
        <el-button type="danger" :disabled="!checked" @click="goPrveStep()">返回</el-button>
        <el-button type="primary" :disabled="!checked" @click="dataFormSubmitHandle(0,dataForm)">保存</el-button>
        <el-button type="primary" :disabled="!checked" :loading="nextLoading" @click="applyPDF()">下一步</el-button>
        <el-button type="primary" :disabled="!checked" @click="dataFormSubmitHandle(1,dataForm)" v-if="wtsStatus == 1 && letterId !== ''">提交</el-button>
      </span>
    </div>
    <xieyi v-if="visible" ref="xieyi"></xieyi>
    <tip v-if="visible" ref="tip"></tip>
  </el-card>
</template>
<script>
import debounce from 'lodash/debounce'
import dictionaries from '@/views/project/guarantee/components/dictionaries'
import bgGuaranteeApply from './form/bgGuaranteeApply' // 投保人信息（投保方）
import bginsurancebbr from './form/bginsurancebbr' // 被保人（招标方）
import bginsurancesyr from './form/bginsurancesyr' // 受益人信息
import bgbiddingproject from './form/bgbiddingproject' // 项目信息
import bginsuranceinfo from './form/bginsuranceinfo' // 保函信息
import bgguaranteeinvoice from './form/bgguaranteeinvoice' // 发票信息
import xieyi from '../components/xieyi'
import tip from '../components/tip'
export default {
  mixins: [dictionaries],
  data () {
    return {
      radio: 0,
      dataForm: {},
      showSyrCom: false,
      checked: true,
      visible: false,
      nextLoading: false,
      tbDate: '',
      formReset: [
        'bgGuaranteeApply',
        'bginsurancebbr',
        'bginsurancesyr',
        'bgbiddingproject',
        'bginsuranceinfo',
        'bgguaranteeinvoice'
      ],
      letterId: '', // 保函id
      serPriceId: '', // 服务定价id
      guaranteeType: '', // 保函类型
      wtsStatus: 0 // 委托书状态 0：需要 1：不需要
    }
  },
  components: {
    bgGuaranteeApply,
    bginsurancebbr,
    bginsurancesyr,
    bgbiddingproject,
    bginsuranceinfo,
    bgguaranteeinvoice,
    xieyi,
    tip
  },
  activated () {
    this.getInfo()
  },
  methods: {
    getInsuranceInfo (data) {
      this.insuranfceIno = data
    },
    gettbDate  (val) {
      console.log(val)
      this.tbDate = val
    },
    showDia (name) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs[name].init()
        this.$refs[name].letterId = this.letterId
      })
    },
    getInfo () {
      // letter/bgguaranteeletter/getInfoById/{id}
      this.$http
        .get(`/letter/bgguaranteeletter/getInfoById/${this.$route.params.id}`)
        .then(({ data: res }) => {
          if (res.code !== 0) {
            this.setObj()
            return
          }
          // console.log(res)
          this.dataForm = res.data
          this.setObj()
        })
    },
    showSyr (val) {
      if (val === 1) {
        this.showSyrCom = true
      } else {
        this.showSyrCom = false
      }
    },
    applyPDF () {
      this.dataFormSubmitHandle(3, this.dataForm)
      if (this.$store.state.pdfType === '0') {
        this.nextLoading = true
        this.$http
          .get(`/letter/bgguaranteeletter/isSignature/${this.letterId}`)
          .then(({ data: res }) => {
            this.nextLoading = false

            if (res.data) {
              this.goAwit()
            } else {
              if (window.ActiveXObject !== undefined) {
                window.open(window.SITE_CONFIG['bhtURL'] + `#/pdfIe?letterId=${this.letterId}`)
              } else {
                this.showDia('tip')
              }
            }
          })
      } else {
        this.goAwit()
      }
    },
    goAwit () {
      this.$http
        .get(`/letter/bgguaranteeapply/submitById/${this.letterId}?key=${this.insuranfceIno.code}`)
        .then(({ data: res }) => {
          this.$loading().close()

          this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
            item => item.name !== 'bgguaranteeletterUpdate'
          )
          this.$router.push({
            name: 'bgguaranteeletterSqf'
          })

          const audit = this.$router.resolve({
            name: 'awaitingAudit',
            query: { id: this.letterId, insuranceCode: this.insuranfceIno.code }
          })

          this.$router.push({
            name: 'letter-bgguaranteeletterSqf'
          })
          window.open(audit.href, '_blank')
        })
    },
    goPrveStep () {
      this.$router.push({
        name: 'bgguaranteeletterSqf'
      })
    },
    dataFormSubmitHandle: debounce(
      async function (status, data) {
        this.serPriceId = this.$refs['bginsuranceinfo'].serPriceId
        console.log(data)
        var obj = {
          ...data,
          cjfChargeId: this.serPriceId,
          status: status,
          letterId: this.letterId,
          guaranteeType: this.guaranteeType
        }
        if (status === 3) {
          this.submitData(2, obj)
        } else {
          await this.Verification()
          if (!this.formArr.includes('false')) {
            this.$confirm(`该保函由${this.insuranfceIno.name}提供，是否继续？`, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
            // 验证通过，走提交
              console.log(status)
              if (status !== 3) {
                this.$loading({
                  lock: true,
                  text: `数据${status === 0 ? '保存' : '提交'}中`
                })
              }

              this.submitData(status, obj)
            }).catch(() => {

            })
          } else {
            this.$message.error('表单提交有误，请检查提交信息')
          }
        }
      },
      1000,
      { leading: true, trailing: false }
    ),
    submitData (status, obj) {
      this.$loading().close()
      this.$http
        .post('/letter/bgguaranteeapply/saveAllLogin', obj)
        .then(({ data: res }) => {
          if (status !== 2) {
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.letterId = res.data.letterId
                this.dataForm = res.data
                if (this.dataForm.status === 1) {
                  this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
                    item => item.name !== 'applyBgGuaranteeApplyFromFLogin'
                  )
                  this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
                    item => item.name !== 'bgguaranteeletterUpdate'
                  )
                  const audit = this.$router.resolve({ name: 'awaitingAudit', query: { id: this.letterId, insuranceCode: this.insuranfceIno.code } })
                  window.open(audit.href, '_blank')
                  this.$router.push({
                    name: 'home'
                  })
                }
              }
            })
          }
        })
        .catch(() => { this.$loading().close() })
    },
    radioChange (val) {
      this.radio = val
      // this.syvisible = true
    },
    setObj () {
      this.formReset.map(a => {
        if (!this.dataForm[a]) this.$set(this.dataForm, a, {})
      })
      if (this.dataForm.letterId) this.letterId = this.dataForm.letterId
    },
    Verification () {
      this.formArr = []
      this.formReset.map(async a => {
        if (this.$refs[`${a}`] !== undefined) {
          this.formArr.push(`${await this.$refs[`${a}`].push()}`)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.box-card {
  margin-bottom: 85px;
}
.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
.foot {
  width: 100%;
  background: #fff;
  position: fixed;
  bottom: 0;
  padding: 10px 0;
  border-top: 1px solid #ddd;
  background: #f9f9f9;
  text-align: center;
  left: 0px !important;
  z-index: 999;
  span {
    text-align: center;
  }
}
</style>
