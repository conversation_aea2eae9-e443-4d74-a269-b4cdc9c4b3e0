<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON>qiang
 * @Date: 2020-08-06 18:47:13
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2022-05-10 10:46:42
-->
<template>
  <el-dialog :visible.sync="visible" :title="type=='th'?'退回':'审核'"  :close-on-click-modal="false" :close-on-press-escape="false" width="500px">
    <div>
      <!-- {{this.form}} -->
      <el-form ref="form" :model="form"  :rules="dataRule" label-width="110px">

        <el-form-item :label="this.type =='tb'?'退保原因':'备注'" v-if="remark" prop="remark">
          <span>{{remark}}</span>
        </el-form-item>
        <el-form-item label="状态" prop="radio" v-if="this.type =='tb'">
          <el-radio-group v-model="form.radio">
            <el-radio :label="10">通过</el-radio>
            <el-radio :label="20">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="状态" prop="radio" v-else-if="this.type !='th'">
          <el-radio-group v-model="form.radio">
            <el-radio :label="40">通过</el-radio>
            <el-radio :label="41">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- {{this.type}} -->
        <el-form-item label="不通过原因" prop="reason" v-if="form.radio==20&&this.type =='tb'">
          <el-input type="textarea" :rows="3" placeholder="请输入不通过原因" v-model="form.reason">
          </el-input>
        </el-form-item>
         <el-form-item :label="form.radio==40?'通过原因':'不通过原因'" prop="reason" v-if="this.type !='tb'&&(dataForm.key == 'XTDB'||form.radio!=40)">
         <span v-if="this.type =='th'">注：退回将审核状态变为审核不通过，此时用户需要重新修改表单提交审核</span>

          <el-input type="textarea" :rows="3" placeholder="请输入原因" v-model="form.reason">
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      type: 'bh', // bh:保函审核 cw:财务审核 pg:批改审核
      remark: '',
      form: {
        radio: 40,
        reason: ''
      }
    }
  },
  props: {
    dataForm: Object
  },
  computed: {
    dataRule () {
      return {
        reason: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  mounted () {},
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['form'].resetFields()
        if (this.type === 'tb') { this.form.radio = 10 }
        if (this.type === 'th') { this.form.radio = 41 }
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          return false
        }
        if (this.type === 'bh') {
          this.submit(1)
        }
        if (this.type === 'th') {
          this.$confirm('退回将审核状态变为审核不通过，此时用户需要重新修改表单提交审核, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.submit(2)
          }).catch(() => {

          })
        }
        if (this.type === 'cw') { // 财务审核
          this.shenhe()
        }
        if (this.type === 'pg') { // 批改审核
          this.pg()
        }
        if (this.type === 'tb') { // 退保审核
          this.tb()
        }
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    pg () {
      this.$http
        .post(`letter/guarantee/correctAudit`, {
          letterId: this.$route.query.seeLetterId,
          status: this.form.radio,
          description: this.form.reason ? this.form.reason : ''
        })
        .then(({ data: res }) => {
          this.$message.success('审核通过！')
          this.visible = false
          this.$emit('refresh')
        })
        .catch(() => {})
    },
    shenhe () {
      this.$http
        .post(`letter/bgguaranteeletter/Approved`, {
          letterId: this.$route.query.seeLetterId,
          status: this.form.radio,
          description: this.form.reason ? this.form.reason : ''
        })
        .then(({ data: res }) => {
          this.$message.success('审核通过！')
          this.visible = false
          this.$emit('refresh')
        })
        .catch(() => {})
    },
    submit (type) {
      let params = {
        id: this.$route.query.seeLetterId
      }
      if (this.$route.query.taskId) {
        params.taskId = this.$route.query.taskId
        params.comment = this.form.reason ? this.form.reason : this.form.radio === 40 ? '通过' : ''
      } else {
        params.reason = this.form.reason ? this.form.reason : ''
      }
      console.log(this.$route.query.taskId)
      // 有taskid走流程审核接口
      let urltask = this.$route.query.taskId ? '/common/complete' : 'letter/guarantee'
      let qustype = this.$route.query.taskId ? 'post' : 'put'
      let url = type === 1 ? urltask : 'letter/guarantee/guaranteeReturn'
      this.$http[type === 1 ? qustype : 'post'](url, {
        letterStatus: this.form.radio,
        ...params
      }).then(({ data: res }) => {
        this.$message.success('审核成功！')
        this.visible = false

        this.$emit('refresh', this.form.radio)
      })
    },
    tb () {
      // /flow/common/complete
      let params = {
        letterId: this.$route.query.seeLetterId,
        reason: this.form.reason ? this.form.reason : ''
      }
      this.$http
        .post(`letter/bgguaranteeletter/surrenderAudit`, {
          auditStatus: this.form.radio,
          ...params
        })
        .then(({ data: res }) => {
          this.$message.success('审核通过！')
          this.visible = false
          this.$emit('refresh')
        })
        .catch(() => {})
    }
  }
}
</script>
