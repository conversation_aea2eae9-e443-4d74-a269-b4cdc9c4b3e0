
<template>
  <el-dialog :visible.sync="visible" title="错误提示" :close-on-click-modal="false" :close-on-press-escape="false" width="500px">
    <div>
      <div v-for="(a,index) in errorList" :key="index" class="tip">
        <h3 class="label">{{a.name}}: </h3><div style="color:#F56C6C;">{{a.tip}}</div>
      </div>
    </div>
    <template slot="footer">
      <el-button type="primary" @click="visible = false">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      errorList: []
    }
  },
  mounted () {},
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.label {
  // width: 150px;
}
.tip {
  margin-bottom: 12px;
}
</style>
