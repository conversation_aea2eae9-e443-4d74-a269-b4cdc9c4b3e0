<template>
  <el-card shadow="never" class="aui-card--fill">

    <div class="box" >
        <div class="listTab" slot="content">
          <el-tabs class="AuditTab" v-model="activeName" type="card" @tab-click="handleClick">
            <el-tab-pane label="全部" name="10001,50"></el-tab-pane>
            <el-tab-pane label="待上传保函" name="10001"></el-tab-pane>
            <el-tab-pane label="已出具" name="50"></el-tab-pane>
          </el-tabs>
        </div>

      <div class="mod-letter__bgguaranteeletter}">
        <el-form :inline="true" :model="dataForm">
          <el-form-item>
            <el-input v-model="dataForm.code" hidden clearable placeholder="保函编码"></el-input>
          </el-form-item>
          <el-form-item>
            <el-select v-model="dataForm.signStatus" placeholder="请选择签章状态">
              <el-option v-for="item in options" clearable :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-input v-model="dataForm.applyName" clearable placeholder="申请方名称"></el-input>
          </el-form-item>
          <el-form-item>
            <el-date-picker v-model="daterange" unlink-panels type="daterange" value-format="yyyy-MM-dd" :range-separator="$t('datePicker.range')" :start-placeholder="'开具开始日期'"
              :end-placeholder="'开具结束日期'">
            </el-date-picker>
          </el-form-item>
          <template v-if="searchShow">
            <el-form-item>
              <el-input v-model="dataForm.bbrName" clearable placeholder="被保人名称"></el-input>
            </el-form-item>
            <el-form-item>
              <el-input v-model="dataForm.issueName" clearable placeholder="出具方名称"></el-input>
            </el-form-item>
            <el-form-item>
              <el-input v-model="dataForm.platformName" clearable placeholder="使用方名称"></el-input>
            </el-form-item>
            <el-form-item>
              <el-input v-model="dataForm.policyNo" clearable placeholder="保单号"></el-input>
            </el-form-item>
          </template>
          <el-form-item>
            <el-button @click="getDataList(1)" type="primary">{{ $t('query') }}</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="danger" @click="resetHandle()">重置</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="success" @click="exportHandle()">{{ $t('export') }}</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="text" @click="searchShow = !searchShow">{{searchShow?'收起':'更多筛选'}}<i v-if="!searchShow" class="el-icon-arrow-down"></i><i v-else class="el-icon-arrow-up"></i></el-button>
          </el-form-item>
        </el-form>
        <el-table :header-row-style="{padding:'5px',height:'10px'}" :header-cell-style="{padding:'8px'}" show-summary :summary-method="getSummaries" v-loading="dataListLoading" :data="dataList" border
          @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle" style="width: 100%;">
          <el-table-column type="selection" fixed='left' header-align="center" align="center" width="50"></el-table-column>
          <el-table-column prop="applyName" label="申请方名称" header-align="left" align="left" min-width="200px">
            <el-table-column prop="bbrName" label="被保人名称" header-align="left" align="left" min-width="200px">
              <template slot-scope="scope">
                <div>{{scope.row.applyName?scope.row.applyName:'-'}}</div>
                <div class="line"></div>
                <div>{{scope.row.bbrName?scope.row.bbrName:'-'}}</div>
                <div class="title2"><span class="t_span">保函类型：</span>
                  <el-tag size="mini"><span>{{fomatMethod(scope.row.guaranteeType,1)}}</span>
                  </el-tag>
                </div>
              </template>
            </el-table-column>
          </el-table-column>
          <!-- <el-table-column prop="issueName" label="出具方名称" sortable="custom" header-align="center" align="center" width="180"></el-table-column> -->
          <!-- <el-table-column prop="platformName" label="使用方名称" sortable="custom" header-align="center" align="center" width="180"></el-table-column> -->
          <el-table-column prop="signStatus" label="签章状态" sortable="custom" header-align="center" align="center" width="140">
            <template slot-scope="scope">
              <el-tag size="mini" v-if="scope.row.signStatus=='30'" type="success"><span>手动上传</span>
              </el-tag>
              <el-tag size="mini" v-else-if="scope.row.signStatus=='20'" type="success"><span>签章上传</span>
              </el-tag>
              <el-tag size="mini" v-else type="info"><span>未出具</span>
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="letterStatus" label="当前状态" sortable="custom" header-align="center" align="center" width="160">
            <template slot-scope="scope">
              <el-tag size="mini" :type="statusColor(scope.row.letterStatus)"><span>{{fomatMethod(scope.row.letterStatus,2)}}</span>
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="policyNo" label="保单号" sortable="custom" header-align="center" align="center" width="180"></el-table-column>
          <!-- <el-table-column prop="guaranteeType" label="保函类型" sortable="custom" header-align="center" align="center" width="170">
          <template slot-scope="scope">
            <el-tag size="mini" type="danger"><span>{{fomatMethod(scope.row.guaranteeType,1)}}</span>
            </el-tag>
          </template>
        </el-table-column> -->
          <el-table-column prop="guaranteeAmount" label="担保金额（元）" sortable="custom" header-align="center" align="center" width="180"></el-table-column>
          <el-table-column prop="guaranteePrice" label="保函费用（元）" sortable="custom" header-align="center" align="center" width="180"></el-table-column>
          <el-table-column prop="createDate" label="创建时间" header-align="center" align="center" width="180">
            <el-table-column prop="openDate" label="开具时间" header-align="center" align="center" width="180">
              <template slot-scope="scope">
                <div>{{scope.row.createDate?scope.row.createDate:'-'}}</div>
                <div class="line"></div>
                <div>{{scope.row.openDate?scope.row.openDate:'-'}}</div>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
            <template slot-scope="scope">
              <el-button v-if="scope.row.letterStatus >= 10" type="text" size="small" @click="seeInfo(scope.row.id,scope.row.guaranteeType)">审核</el-button>

            </template>
          </el-table-column>
        </el-table>
        <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle"
          @current-change="pageCurrentChangeHandle">
        </el-pagination>
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
        <bgguaranteeletterSqf-surrender v-if="surrenderVisible" ref="bgguaranteeletterSqfSurrender" @refreshDataList="getDataList"></bgguaranteeletterSqf-surrender>
        <bgguaranteeletterSqf-status v-if="statusVisible" ref="bgguaranteeletterSqfStatus" @refreshDataList="getDataList"></bgguaranteeletterSqf-status>
      </div>
    </div>

  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from '@/views/modules/letter/bgguaranteeletter-add-or-update'
import bgguaranteeletterSqfSurrender from '@/views/modules/letter/bgguaranteeletterSqf-surrender'
import bgguaranteeletterSqfStatus from '@/views/modules/letter/bgguaranteeletterSqf-status'
import moment from 'moment'
import { getDict } from '@/utils/index'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/guarantee/page',
        getDataListIsPage: true,
        exportURL: '/letter/bgguaranteeletter/export',
        deleteURL: '/letter/bgguaranteeletter',
        enableURL: '/letter/bgguaranteeletter/enable',
        stopURL: '/letter/bgguaranteeletter/stop',
        deleteIsBatch: true
      },
      options: [
        {
          value: '10',
          label: '未签章'
        },
        {
          value: '20',
          label: '已签章'
        },
        {
          value: '30',
          label: '本地上传'
        }
      ],
      statusoptions: [
        {
          value: 1,
          label: '正常'
        },
        {
          value: 0,
          label: '停用'
        }
      ],
      daterange: null,
      searchShow: false,
      dataForm: {
        code: '',
        bbrName: '',
        signStatus: '',
        applyName: '',
        issueName: '',
        issueCode: 'XTDB',
        payStatus: '',
        letterStatus: '10001,50',
        platformName: '',
        insuranceName: '',
        guaranteeType: '',
        policyNo: '',
        startDate: '',
        endDate: ''
      },
      activeName: '10001,50',
      getDicListURL: '/sys/dict/type/',
      guaranteeTypemaps: getDict('电子保函类型'),
      guaranteeTypeoptions: [],
      letterStatusmaps: getDict('保函状态'),
      letterStatusoptions: [],
      orderField: 'create_date',
      order: 'desc',
      uploadVisible: false,
      surrenderVisible: false,
      statusVisible: false,
      moreActions: []
    }
  },
  watch: {
    daterange (val) {
      if (val) {
        this.dataForm.startDate = val[0] + ' 00:00:00'
        this.dataForm.endDate = val[1] + ' 23:59:59'
      } else {
        this.dataForm.startDate = ''
        this.dataForm.endDate = ''
      }
    }
  },
  components: {
    AddOrUpdate,
    bgguaranteeletterSqfSurrender,
    bgguaranteeletterSqfStatus
  },
  created () {
    this.dataForm.code = this.$route.query.secretParams || ''
    // eslint-disable-next-line eqeqeq
    this.init()
  },
  activated () {
    if (this.$route.query.letterStatus) {
      this.activeName = this.$route.query.letterStatus
      this.handleClick()
    }
    // eslint-disable-next-line eqeqeq
    this.init()
  },
  methods: {
    moment,
    init () {
      console.log(this.$route)
      this.dataForm.guaranteeType = 'tbbhyh'
    },
    // 下载发票
    getDownloadInvoiceInfo (id) {
      // 获取信息
      return new Promise((resolve, reject) => {
        this.$http
          .get(`/letter/bgguaranteeletter/ObtainInvoiceDownloadInfo/` + id)
          .then(({ data: res }) => {
            resolve(res.data)
          })
          .catch((rej) => {
            reject(rej.msg)
          })
      })
    },
    getCode (code, params) {
      this.dataForm.guaranteeType = code
      this.dataForm.payStatus = params.payStatus
      this.getDataList()
    },
    statusColor (code) {
      let dcode = Number(code)
      if (dcode < 30) {
        return 'info'
      } else if (dcode < 50) {
        return 'warning'
      } else if (dcode === 50) {
        return 'success'
      } else if (dcode < 100) {
        return 'danger'
      }
    },
    handleClick () {
      this.dataForm.letterStatus = this.activeName
      this.getDataList()
    },
    // 合计行
    getSummaries (param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        const values = data.map((item) => Number(item[column.property]))
        if (
          !values.every((value) => isNaN(value)) &&
          (index === 6 || index === 5)
        ) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
          sums[index] += ' 元'
        } else {
          sums[index] = '-'
        }
      })
      return sums
    },
    async downloadInvoice (id) {
      this.$loading({
        lock: true,
        text: `下载请求中`
      })
      var data = await this.getDownloadInvoiceInfo(id)
      console.log(data)
      // var downInfo = data
      this.$loading().close()
      // var href =
      //   downInfo.url +
      //   '?sign=' +
      //   downInfo.sign +
      //   '&PolicyNo=' +
      //   downInfo.policyNo +
      //   '&corporation=' +
      //   downInfo.corporation
      // console.log(href)
      window.open(data, '_blank')
      // window.location.href = data
    },

    downloadLetter (id, type, key) {
      this.$http
        .get(
          `/letter/bgguaranteeletter/obtainDownloadLetter/` +
            id +
            '?type=' +
            type +
            '&key=' +
            key
        )
        .then(({ data: res }) => {
          this.updateLoading = false

          if (res.data) {
            window.open(res.data, '_blank')
          } else {
            this.$confirm('正在生成文件，请稍后再试！', '提示', {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'warning'
            }).then(() => {})
          }
        })
        .catch((rej) => {})
    },
    async pay (letterId, key) {
      this.$loading({
        lock: true,
        text: `请求支付中`
      })
      var data = await this.getPayMentInfo(letterId, key)
      var payInfo = data
      this.$loading().close()
      window.open(encodeURI(payInfo), '_blank')
    },
    seeStatus (id) {
      this.$router.push({
        name: 'awaitingAudit',
        query: {
          id: id
        }
      })
    },
    bhStatusChange (id) {
      this.statusVisible = true
      this.$nextTick(() => {
        this.$refs.bgguaranteeletterSqfStatus.id = id
        this.$refs.bgguaranteeletterSqfStatus.init()
      })
    },
    getPayMentInfo (letterId, key) {
      // 获取信息
      return new Promise((resolve, reject) => {
        this.$http
          .get(
            `/letter/bgguaranteeletter/getPayUrl/` + letterId + `?key=${key}`
          )
          .then(({ data: res }) => {
            this.updateLoading = false

            resolve(res.data)
          })
          .catch((rej) => {
            reject(rej.msg)
          })
      })
    },
    surrender (id, insuranceId) {
      this.$http
        .get(`/letter/bginsuranceinfo/` + insuranceId)
        .then(({ data: res }) => {
          if (!moment(res.data.startDate).isBefore(new Date())) {
            this.surrenderVisible = true
            this.$nextTick(() => {
              this.$refs.bgguaranteeletterSqfSurrender.dataForm.id = id
              this.$refs.bgguaranteeletterSqfSurrender.startDate =
                res.data.startDate
              this.$refs.bgguaranteeletterSqfSurrender.init()
            })
          } else {
            this.$confirm('此保单已生效，不能退保', '提示', {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'warning'
            })
              .then(() => {})
              .catch(() => {})
          }
        })
        .catch((rej) => {})
    },
    fomatMethod (value, i) {
      if (i === 1) {
        return this.guaranteeTypemaps.filter((a) => a.dictCode === value)[0]
          .dictName
      }
      if (i === 2) {
        return this.letterStatusmaps.filter((a) => a.dictCode === value)[0]
          .dictName
      }
    },
    seeInfo (id, type) {
      this.$router.push({
        name: 'bgguaranteeletterDetail',
        query: {
          seeLetterId: `${id}`,
          type: type,
          JumpName: 'project-IssuedBy-AuditGuarantee-index'
        }
      })
    },
    modifyJump (id, type, insuranceCode, insuranceName) {
      this.$router.push({
        name: 'bgguaranteeletterUpdate',
        params: {
          id: id,
          type: type,
          insuranceCode: insuranceCode,
          insuranceName: insuranceName
        }
      })
    },
    // 状态停用
    stopHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(
        this.$t('prompt.info', { handle: '停用' }),
        this.$t('prompt.title'),
        {
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          // 具体代码需微调格式去除空格
          this.$http
            .put(
              this.mixinViewModuleOptions.stopURL,
              id ? [id] : this.dataListSelections.map((item) => item.id)
            )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getDataList()
                }
              })
            })
            .catch(() => {})
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.AuditTab {
  .item {
    top: -2px !important;
    right: -4px;
  }
}
.box{
  .el-page-header{
  /deep/ .el-icon-back{
    margin-top: -15px;
  }
  /deep/ .el-page-header__title{
    margin-top: 8px;
  }
  /deep/ .el-page-header__left::after{
    top:40%;
  }
}
}

.line {
  display: block;
  height: 1px;
  width: 100%;
  margin: 8px 0;
  background-color: #dcdfe6;
}
</style>
