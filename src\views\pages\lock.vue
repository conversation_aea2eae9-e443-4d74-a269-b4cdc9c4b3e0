<template><div class="aui-wrapper aui-page__lock">
  <div class="aui-content__wrapper">
    <main class="aui-content">
      <div class="login-body">
          <input type="password" class="hide" id="loginPassword"/>
          <input type="text" class="hide" id="loginUserName"/>
        <el-form ref="dataForm" status-icon>
          <el-form-item prop="password">
            <el-input v-model="passwd" type="password" :placeholder="'锁屏密码'">
                <span slot="prefix" class="el-input__icon">
                  <svg class="icon-svg" aria-hidden="true"><use xlink:href="#icon-lock"></use></svg>
                </span>
            </el-input>

          </el-form-item>
          <el-form-item>
            <el-button type="success" @click="handleLogin()" class="w-percent-60">确认</el-button>
            <el-button type="danger" @click="handleLogout()" class="w-percent-60">退出</el-button>
          </el-form-item>
        </el-form>
      </div>
    </main>
  </div>
</div>
</template>
<script>
import { clearLoginInfo } from '@/utils'

export default {
  name: 'lock',
  data () {
    return {
      passwd2: '',
      passwd: '',
      lockPasswd: ''
    }
  },
  created () {

  },

  mounted () {
  },
  props: [],
  methods: {
    handleLogout () {
      this.$confirm(this.$t('prompt.info', { 'handle': this.$t('logout') }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.$http.post('/logout').then(({ data: res }) => {
          clearLoginInfo()
          this.$router.push({ name: 'login' })
        }).catch(() => {})
      }).catch(() => {})
    },
    handleLogin () {
      this.lockPasswd = this.$store.state.setLockPasswd

      if (this.passwd !== this.lockPasswd) {
        this.passwd = ''
        this.$message({
          message: '解锁密码错误,请重新输入',
          type: 'error'
        })
        this.passwdError = true
        setTimeout(() => {
          this.passwdError = false
        }, 1000)
        return
      }
      window.SITE_CONFIG['setLock'] = false
      this.pass = true
      setTimeout(() => {
        this.$router.push({ path: '/home' })
      }, 1000)
    }
  },
  components: {}
}
</script>

<style lang="scss" scoped>
  .aui-page__lock {
    &::before,
    &::after {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: -1;
      content: "";
    }
    &::before {
      background-image: url(~@/assets/img/lock.jpg);
      background-size: cover;
    }
    &::after {
      background-color: rgba(38, 50, 56, .4);
    }
    .aui-content {
      display: flex;
      flex-flow: column wrap;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      padding: 50px 20px 150px;
      text-align: center;
      &__wrapper {
        height: 100vh;
        background-color: transparent;
        overflow-x: hidden;
        overflow-y: auto;
      }
    }
    .login-header {
      padding: 20px;
      color: #fff;
      .login-brand {
        margin: 0 0 15px;
        font-size: 40px;
        font-weight: 400;
        letter-spacing: 2px;
        text-transform: uppercase;
      }
      .login-intro {
        padding: 0;
        margin: 0;
        list-style: none;
        > li {
          font-size: 16px;
          line-height: 1.5;
          color: rgba(255, 255, 255, .6);
          & + li {
            margin-top: 5px;
          }
        }
      }
    }
    .login-body,
    .login-footer {
      width: 460px;
    }
    .login-body {
      padding: 20px 30px;
      .login-title {
        font-size: 18px;
        font-weight: 400;
      }
      .el-input__prefix .el-input__icon {
        font-size: 16px;
      }
      .login-shortcut {
        margin-bottom: 20px;
        &__title {
          position: relative;
          margin: 0 0 15px;
          font-weight: 400;
          > span {
            position: relative;
            z-index: 2;
            padding: 0 20px;
            color: rgba(0, 0, 0, .3);
            background-color: #fff;
          }
        }
        &__list {
          padding: 0;
          margin: 0;
          list-style: none;
          font-size: 0;
          > li {
            display: inline-block;
            vertical-align: middle;
            margin: 0 10px;
            font-size: 28px;
          }
        }
      }
      .login-guide {
        color: rgba(0, 0, 0, .3);
      }
    }
    .login-footer {
      position: absolute;
      bottom: 0;
      padding: 20px;
      color: rgba(255, 255, 255, .6);
      p {
        margin: 10px 0;
      }
      a {
        padding: 0 5px;
        color: rgba(255, 255, 255, .6);
        &:focus,
        &:hover {
          color: #fff;
        }
      }
    }
    // 右侧垂直风格
    &--right-vertical {
      .aui-content {
        flex-flow: row nowrap;
        justify-content: flex-start;
        align-items: stretch;
        padding: 0;
      }
      .login-header {
        flex: 1;
        display: flex;
        flex-flow: column wrap;
        justify-content: center;
        padding: 30px 120px;
        text-align: left;
      }
      .login-body {
        position: relative;
        display: flex;
        flex-flow: column wrap;
        justify-content: center;
        padding: 120px 30px 150px;
        text-align: center;
        .login-guide {
          margin-top: 0;
        }
      }
    }
  }
</style>
