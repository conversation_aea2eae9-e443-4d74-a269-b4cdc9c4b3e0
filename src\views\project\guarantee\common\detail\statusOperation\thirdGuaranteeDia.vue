<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON>ang
 * @Date: 2022-01-18 09:08:45
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2022-01-19 14:26:22
-->
<template>
  <el-dialog :visible.sync="visible" title="反担保" :close-on-click-modal="false" :close-on-press-escape="false" width="800">
    <thirdGuarantee :dataForm='dataForm' ref="thirdGuarantee"></thirdGuarantee>
    <template slot="footer">
      <el-button type="primary"  @click="submita()">提交</el-button>
    </template>
  </el-dialog>
</template>

<script>
import thirdGuarantee from '@/views/project/guarantee/performance/form/thirdGuarantee'
export default {
  components: { thirdGuarantee },
  data () {
    return {
      dataForm: {},
      visible: false
    }
  },
  created () {},
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
      })
    },
    submita () {
      this.$nextTick(async () => {
        let a = await this.$refs.thirdGuarantee.push()
        if (a) {
          let { data } = await this.$http.post(
            'letter/guarantee/updateThirdGuarantee', this.dataForm
          )
          this.visible = false
          this.$message.success('添加成功！')
          this.$emit('refresh')
          console.log(data)
        }
      })
    }

  }
}
</script>
<style lang="scss" scoped>
.dialog {
  /deep/.el-dialog__body {
    max-height: none;
  }
}
</style>
