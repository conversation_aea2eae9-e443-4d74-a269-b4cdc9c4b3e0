<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2020-10-14 10:46:09
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-10-23 14:18:24
-->
<template>
  <div style="overflow:hidden;" v-loading='loading'>
    <el-row>
      <el-col :span="24">
        <el-card class="report">
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="月报表" name="month">
              <span class="item" slot='label'>
                <i class="el-icon-pie-chart"></i>&nbsp;月报表
              </span>
            </el-tab-pane>
            <el-tab-pane label="日报表" name="day">
              <span class="item" slot='label'>
                <i class="el-icon-tickets"></i>&nbsp;日报表
              </span>
            </el-tab-pane>
          </el-tabs>
          <div class="screen">
            <div class="block" v-if="activeName==='month'">
              <el-date-picker style="width:350px;" v-model="month" :clearable="false" value-format="yyyy-MM" size="small" type="monthrange" align="right" unlink-panels range-separator="至" start-placeholder="开始月份" end-placeholder="结束月份"
                :picker-options="pickerOptions">
              </el-date-picker>
            </div>
            <div class="block" v-if="activeName==='day'">
              <el-date-picker v-model="day" size="small" type="daterange" :clearable="false" value-format="yyyy-MM-dd" align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                :picker-options="pickerOptionsday">
              </el-date-picker>
            </div>
            <el-button type="primary" @click="getData" size="small" style="margin-left:20px;" icon="el-icon-search">查询</el-button>
          </div>
          <panel-group :panelData='this.chartData.backletterNum' />
          <div>

            <lineChartNew ref="lineChartNew" :lineChartData='this.chartData.lineData' :className="'lineData'"></lineChartNew>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :xs="12" :sm="12" :lg="12">
        <el-card class="report">
          <div style="height:25vw;">
            <mapChart :mapData='this.mapData' :timeType='activeName' :time="this.activeName === 'month' ? this.month : this.day" :max='max'></mapChart>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="12" v-if="isIssue!=='true'">
        <el-card class="report">
          <div style="height:25vw;">
            <barChart :barChartData='this.chartData.barChartData' :className="'barChartData'" height='25vw'></barChart>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="12" :lg="12" v-if="isIssue!=='true'">
        <el-card class="report">
          <div style="height:25vw;">
            <pieChart :pieChartData='this.chartData.pieChartData' :className="'backletterNum'" height='25vw'></pieChart>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="12" :lg="12">
         <el-card class="report">
          <div style="height:25vw;">
          <pieChart :pieChartData='this.chartData.letterStatusNum' :className="'letterStatusNum'" height='25vw'/>
       </div>
        </el-card>
      </el-col>
    </el-row>
  </div>

</template>
<script>
import PanelGroup from '@/components/project/chart/PanelGroup'
import lineChartNew from '@/components/project/chart/lineChartNew'
import mapChart from '@/components/project/chart/mapChart'
import barChart from '@/components/project/chart/BarChart'
import pieChart from '@/components/project/chart/PieChart'
import moment from 'moment'
export default {
  data () {
    return {
      activeName: 'month',
      pickerOptionsday: {
        shortcuts: [
          {
            text: '最近一周',
            onClick (picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick (picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick (picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      pickerOptions: {
        shortcuts: [
          {
            text: '本月',
            onClick (picker) {
              picker.$emit('pick', [new Date(), new Date()])
            }
          },
          {
            text: '今年至今',
            onClick (picker) {
              const end = new Date()
              const start = new Date(new Date().getFullYear(), 0)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近六个月',
            onClick (picker) {
              const end = new Date()
              const start = new Date()
              start.setMonth(start.getMonth() - 6)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      month: [],
      day: [],
      loading: false,
      chartData: {
        backletterNum: {},
        lineData: {},
        barChartData: {},
        pieChartData: {},
        letterStatusNum: {}
      },
      mapData: [],
      isIssue: 'false',
      max: null
    }
  },
  created () {
    this.$nextTick(() => {
      this.initDate()
      this.getData()
    })
  },
  components: {
    PanelGroup,
    lineChartNew,
    mapChart,
    pieChart,
    barChart
  },
  methods: {
    moment,
    handleClick () {
      this.getData()
      if (this.activeName === 'day') {
        this.$refs.lineChartNew.initChart()
      }
      if (this.activeName === 'month') {
        this.$refs.lineChartNew.initChart()
      }
    },
    initDate () {
      // 初始月份
      let start = new Date()
      start.setMonth(start.getMonth() - 12)
      this.month = [
        this.moment(start).format('YYYY-MM'),
        this.moment(new Date()).format('YYYY-MM')
      ]
      let dStart = new Date()
      dStart.setTime(dStart.getTime() - 3600 * 1000 * 24 * 30)
      this.day = [
        this.moment(dStart).format('YYYY-MM-DD'),
        this.moment(new Date()).format('YYYY-MM-DD')
      ]
    },
    getData (a) {
      this.loading = true
      this.$http
        .get('/letter/statistics/findStatisticsDate', {
          params: {
            startTime:
              this.activeName === 'month' ? this.month[0] : this.day[0],
            endTime: this.activeName === 'month' ? this.month[1] : this.day[1],
            timeType: this.activeName
          }
        })
        .then(({ data: res }) => {
          this.loading = false
          var obj = res.data
          this.isIssue = obj.isIssue
          this.chartData.backletterNum = obj.backletterNum
          // this.mapData = obj.projectNumMap
          this.mapData = []
          let arr = []
          obj.projectNumMap.map(a => {
            arr.push(Number(a.value))
          })
          var max = arr.reduce(function (a, b) {
            return b > a ? b : a
          })
          console.log(max)
          this.max = max
          obj.projectNumMap.map(a => {
            if (a.name.match('黑龙江') || a.name.match('内蒙古')) {
              this.mapData.push({
                name: a.name.substring(0, 3),
                value: a.value,
                id: a.parentCode
              })
            } else {
              this.mapData.push({
                name: a.name.substring(0, 2),
                value: a.value,
                id: a.parentCode
              })
            }
          })
          this.mapData.push({
            name: '南海诸岛',
            value: '0'
          })
          console.log(this.mapData)
          this.initLineChart(obj)
          this.initPieChart(obj)
          this.initBarChart(obj)
        })
    },
    initBarChart (obj) {
      this.chartData.barChartData = {
        title: '出具机构订单',
        seriesData: [
          {
            name: '金额',
            data: []
          },
          {
            name: '数量',
            data: []
          }
        ],
        xAxisNameData: []
      }

      if (obj.everyIssuePayNum) {
        obj.everyIssuePayNum.map((item) => {
          this.chartData.barChartData.xAxisNameData.push(item.issuer)
          this.chartData.barChartData.seriesData[0].data.push(item.monthAmount)
          this.chartData.barChartData.seriesData[1].data.push(item.num)
        })
      }
      console.log(this.chartData.everyMonthPayNum)
    },
    initLineChart (obj) {
      this.chartData.lineData = {
        title: '保函总览',
        seriesData: [
          {
            name: '数量',
            data: []
          },
          {
            name: '金额',
            data: []
          }
        ],
        legendData: ['数量', '金额'],
        xAxisNameData: []
      }
      if (obj.everyMonthPayNum) {
        obj.everyMonthPayNum.map((item) => {
          // this.chartData.lineData.legendData
          this.chartData.lineData.xAxisNameData.push(item.date)
          this.chartData.lineData.seriesData[0].data.push(item.num)
          this.chartData.lineData.seriesData[1].data.push(
            this.activeName === 'month' ? item.monthAmount : item.dayAmount
          )
        })
      }
    },
    initPieChart (obj) {
      let data = obj.everyIssuePayNum
      let legendData = []
      let seriesData = []
      data.map((a) => {
        console.log(a)
        legendData.push(a.issuer)
        seriesData.push({
          name: a.issuer,
          value: a.monthAmount
        })
      })
      console.log(legendData)
      this.chartData.pieChartData = {
        legendData: legendData,
        seriesName: '出具机构订单金额',
        seriesData: seriesData,
        unit: '元'
      }

      this.chartData.letterStatusNum = {
        legendData: ['已申请', '已支付', '已退款'],
        seriesName: '订单统计',
        seriesData: obj.letterStatusNum,
        unit: '笔'
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.report {
  .item {
    padding: 0 20px;
    height: 40px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    line-height: 40px;
    display: inline-block;
    list-style: none;
    font-size: 18px;
    font-weight: 600;
    // color: #303133;
    position: relative;
  }
  .el-tabs__active-bar {
    width: 130px !important;
  }
}
.screen {
  display: flex;
  padding: 15px 0;
}
.el-row {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
</style>
