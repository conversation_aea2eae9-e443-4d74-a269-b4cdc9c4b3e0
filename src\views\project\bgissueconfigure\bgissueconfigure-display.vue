<template>
    <el-dialog :visible.sync="visible" title="信息详情" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
        <el-form :model="dataForm" v-loading='loading' ref="dataForm" :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
        <el-row :gutter="20">
                <el-col :xs="24" :lg="colConfig">
                                                                                                                                                    <el-form-item label="编码" prop="code" label-width="190px">
                                      <span>{{dataForm.code}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="名称" prop="name" label-width="190px">
                                      <span>{{dataForm.name}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="出具方code" prop="issueCode" label-width="190px">
                                      <span>{{dataForm.issueCode}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="出具方别名" prop="alias" label-width="190px">
                                      <span>{{dataForm.alias}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="开标延后日期" prop="delayBidOpening" label-width="190px">
                                      <span>{{dataForm.delayBidOpening}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="是否需要邮寄信息，1是 0否" prop="isMail" label-width="190px">
                                      <span>{{dataForm.isMail}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="是否需要投标文件，1是 0否" prop="istenderdocument" label-width="190px">
                                      <span>{{dataForm.istenderdocument}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="是否需要营业执照，1是 0否" prop="isbusiness" label-width="190px">
                                      <span>{{dataForm.isbusiness}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="是否需要澄清答疑文件，1是 0否" prop="isclarify" label-width="190px">
                                      <span>{{dataForm.isclarify}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="是否需要委托书，1是 0否" prop="isentrust" label-width="190px">
                                      <span>{{dataForm.isentrust}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="是否需要委托合同，1是0否" prop="iscommissioncontract" label-width="190px">
                                      <span>{{dataForm.iscommissioncontract}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="是否需要法人身份证，1是0否" prop="islegalpersonid" label-width="190px">
                                      <span>{{dataForm.islegalpersonid}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="是否需要委托人身份证，1是0否" prop="isclientid" label-width="190px">
                                      <span>{{dataForm.isclientid}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="是否需要招标文件，1是0否" prop="isdocuments" label-width="190px">
                                      <span>{{dataForm.isdocuments}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="是否需要备注字段，1是 0否" prop="isRemarks" label-width="190px">
                                      <span>{{dataForm.isRemarks}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="是否需要申请书，1是 0否" prop="ispetition" label-width="190px">
                                      <span>{{dataForm.ispetition}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="可开具一般责任和连带责任保函，1一般责任，2连带责任，3全部" prop="commonlyOrRelated" label-width="190px">
                                      <span>{{dataForm.commonlyOrRelated}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="是否支持退保，1是 0否" prop="isSurrender" label-width="190px">
                                      <span>{{dataForm.isSurrender}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="是否支持批改，1是 0否" prop="isCorrection" label-width="190px">
                                      <span>{{dataForm.isCorrection}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="是否支持开具发票，1是 0否" prop="isApplyInvoice" label-width="190px">
                                      <span>{{dataForm.isApplyInvoice}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="是否支持批改截至日期，1是 0否" prop="isCorrectionDate" label-width="190px">
                                      <span>{{dataForm.isCorrectionDate}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="是否支持批改支付方式，1是 0否" prop="isCorrectionPayMethod" label-width="190px">
                                      <span>{{dataForm.isCorrectionPayMethod}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="是否支持批改项目名称，1是 0否" prop="isCorrectionProjectName" label-width="190px">
                                      <span>{{dataForm.isCorrectionProjectName}}</span>
                                </el-form-item>
                                                                                                                                                                                                                                                                                                                                            <el-form-item label="状态" prop="status" label-width="190px">
                                    <span v-if="dataForm.status == 0">停用</span>
                                    <span v-if="dataForm.status == 1">正常</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="备注" prop="remarks" label-width="190px">
                                      <span>{{dataForm.remarks}}</span>
                                </el-form-item>
                                                                                        </el-col>
            </el-row>
        </el-form>
        <template slot="footer">
            <el-button type="primary" @click="visible = false">确定</el-button>
        </template>
    </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      loading: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        issueCode: '',
        alias: '',
        delayBidOpening: '',
        isMail: '',
        istenderdocument: '',
        isbusiness: '',
        isclarify: '',
        isentrust: '',
        iscommissioncontract: '',
        islegalpersonid: '',
        isclientid: '',
        isdocuments: '',
        isRemarks: '',
        ispetition: '',
        commonlyOrRelated: '',
        isSurrender: '',
        isCorrection: '',
        isApplyInvoice: '',
        isCorrectionDate: '',
        isCorrectionPayMethod: '',
        isCorrectionProjectName: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        status: 1,
        remarks: ''
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.loading = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/letter/bgissueconfigure/${this.dataForm.id}`).then(({ data: res }) => {
        this.loading = false

        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    }
  }
}
</script>
