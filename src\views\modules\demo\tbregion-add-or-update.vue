<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="colConfig">
          <el-form-item label="区划编码" prop="code">
            <el-input v-model="dataForm.code" placeholder="区划编码"></el-input>
          </el-form-item>
          <el-form-item label="区划名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="项目任务名称"></el-input>
          </el-form-item>
          <el-form-item label="拼音" prop="name">
            <el-input v-model="dataForm.pinyin" placeholder="拼音"></el-input>
          </el-form-item>
          <el-form-item label="全名称" prop="name">
            <el-input v-model="dataForm.mergerName" placeholder="全名称"></el-input>
          </el-form-item>
          <el-form-item label="经度" prop="name">
            <el-input v-model="dataForm.lat" placeholder="经度"></el-input>
          </el-form-item>
          <el-form-item label="纬度" prop="name">
            <el-input v-model="dataForm.lng" placeholder="纬度"></el-input>
          </el-form-item>
          <el-form-item label="父节点编码" prop="parentCode">
            <el-input v-model="dataForm.parentCode" placeholder="父节点编码"></el-input>
          </el-form-item>
          <el-form-item label="层级" prop="nodeLevel">
            <el-input v-model="dataForm.nodeLevel" placeholder="层级"></el-input>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input v-model="dataForm.sort" placeholder="排序"></el-input>
          </el-form-item>
          <el-form-item label="节点类型" prop="nodeType">
            <el-radio-group v-model="dataForm.nodeType">
              <el-radio :label="'0'">父节点</el-radio>
              <el-radio :label="'1'">子节点</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="查询下一级" prop="leaf">
            <el-radio-group v-model="dataForm.leaf">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="状态" prop="status" size="mini">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="0">停用</el-radio>
              <el-radio :label="1">正常</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="父节点id" hidden prop="parentId">
        <el-input v-model="dataForm.parentId" placeholder="父节点id"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        mergerName: '',
        pinyin: '',
        lat: '',
        lng: '',
        parentId: '',
        parentCode: '',
        nodeLevel: '',
        sort: '',
        nodeType: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        leaf: null
      }
    }
  },
  computed: {
    dataRule () {
      return {
        code: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        parentCode: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        nodeLevel: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        sort: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        status: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http
        .get(`/demo/tbregion/${this.dataForm.id}`)
        .then(({ data: res }) => {
          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
        })
        .catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) {
            return false
          }
          this.$http[!this.dataForm.id ? 'post' : 'put'](
            '/demo/tbregion/',
            this.dataForm
          )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            })
            .catch(() => {})
        })
      },
      1000,
      { leading: true, trailing: false }
    )
  }
}
</script>
