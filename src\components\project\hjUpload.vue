<template>
  <el-dialog :visible.sync="visible" :title="title" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-button type="primary" @click="downloadExcel()">下载模板</el-button>
    <el-upload :action="url" :file-list="fileList" drag multiple :before-upload="beforeUploadHandle" :on-success="successHandle" class="text-center">
      <i class="el-icon-upload"></i>
      <div class="el-upload__text" v-html="$t('upload.text')"></div>
      <div class="el-upload__tip" slot="tip">{{ $t('upload.tip', { 'format': 'xls、xlsx' }) }}</div>
    </el-upload>
  </el-dialog>
</template>

<script>
import uploadMixin from '@/mixins/uploadMixin'
export default {
  mixins: [uploadMixin],
  data () {
    return {
    }
  },
  props: {
    uploadUrl: {
      type: String
    },
    title: {
      type: String,
      default: '上传'
    },
    downloadUrl: {
      type: String

    }
  }
}
</script>
