<template>
  <el-card shadow="never" class="aui-card--fill">
     <div class="listTab">
    <el-tabs class="AuditTab" v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane label="全部" name="10,20,30"></el-tab-pane>
        <el-tab-pane label="待审核" name="10"></el-tab-pane>
        <el-tab-pane label="审核通过" name="20"></el-tab-pane>
        <el-tab-pane label="审核不通过" name="30"></el-tab-pane>
      </el-tabs>
     </div>
    <div class="mod-letter__bgguaranteeletter}">
      <el-form :inline="true" :model="dataForm">
        <el-form-item>
          <el-input v-model="dataForm.applyName" clearable placeholder="申请方名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-date-picker v-model="daterange" @change="change" unlink-panels type="daterange" value-format="yyyy-MM-dd" :range-separator="$t('datePicker.range')" :start-placeholder="'开具开始日期'"
            :end-placeholder="'开具结束日期'">
          </el-date-picker>
        </el-form-item>
        <template v-if="searchShow">
          <el-form-item>
            <el-input v-model="dataForm.bbrName" clearable placeholder="被保人名称"></el-input>
          </el-form-item>
          <el-form-item>
            <el-input v-model="dataForm.issueName" clearable placeholder="出具方名称"></el-input>
          </el-form-item>
          <el-form-item>
            <el-input v-model="dataForm.platformName" clearable placeholder="使用方名称"></el-input>
          </el-form-item>
          <el-form-item>
            <el-select v-if="this.$route.name === 'letter-bgguaranteeletterCjf'" v-model="dataForm.letterStatus" clearable placeholder="请选择保函状态">
              <el-option v-for="item in letterStatusoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select @click="getDataList(1)" v-model="dataForm.guaranteeType" clearable placeholder="请选择保函类型">
              <el-option v-for="item in guaranteeTypeoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item>
        </template>
        <el-form-item>
          <el-button @click="getDataList(1)">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="danger" @click="resetHandle()">重置</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgguaranteeletter:export')" type="success" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
         <el-form-item>
          <el-button type="text" @click="searchShow = !searchShow">{{searchShow?'收起':'更多筛选'}}<i v-if="!searchShow" class="el-icon-arrow-down"></i><i v-else class="el-icon-arrow-up"></i></el-button>
        </el-form-item>
        <!-- <el-form-item>
           <el-button type="primary" @click="importHandle()">{{ $t('import') }}</el-button>
         </el-form-item> -->
      </el-form>
      <el-table v-loading="dataListLoading" show-summary :summary-method="getSummaries" :data="dataList" border @selection-change="dataListSelectionChangeHandle"
        @sort-change="dataListSortChangeHandle" style="width: 100%;">
          <el-table-column type="selection" fixed='left' header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="applyName" label="申请方名称" sortable="custom" header-align="center" align="left" min-width="240">
          <template slot-scope="scope">
            <div class="title1">{{scope.row.applyName}}</div>
            <div class="title2"><span class="t_span">出具方：</span>{{scope.row.guaranteeType=='tbbhyh'&&scope.row.bgIssueConfigureDTO?scope.row.bgIssueConfigureDTO.alias:scope.row.issueName}}</div>
            <div class="title2"><span class="t_span">保函类型：</span>
              <el-tag size="mini"><span>{{fomatMethod(scope.row.guaranteeType,1)}}</span>
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="项目名称" header-align="center" align="center" width="180">
          <el-table-column label="被保人名称" header-align="center" align="center" width="250">
            <template slot-scope="scope">
              <div>{{scope.row.projectName?scope.row.projectName:'-'}}</div>
              <div class="line"></div>
              <div>{{scope.row.bbrName?scope.row.bbrName:'-'}}</div>
            </template>
          </el-table-column>
        </el-table-column>
        <!-- <el-table-column prop="regoinName" label="所属区域" sortable="custom" header-align="center" align="center" width="180"></el-table-column> -->
        <!-- <el-table-column prop="issueName" label="出具方" sortable="custom" header-align="center" align="center" width="180"></el-table-column> -->
        <!-- <el-table-column prop="platformName" label="使用方名称" sortable="custom" header-align="center" align="center" width="180"></el-table-column> -->
        <el-table-column prop="letterStatus" label="当前状态" sortable="custom" header-align="center" align="center" width="125">
          <!-- eslint-disable-next-line vue/no-unused-vars -->
          <template slot-scope="scope">
            <el-tag size="mini" v-if="scope.row.offlineAuditStatus==='10'" :type="'warning'"><span>审核中</span>
            </el-tag>
             <el-tag size="mini" v-if="scope.row.offlineAuditStatus==='20'" :type="'success'"><span>审核通过</span>
            </el-tag>
             <el-tag size="mini" v-if="scope.row.offlineAuditStatus==='30'" :type="'danger'"><span>审核不通过</span>
            </el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="policyNo" label="保单号" sortable="custom" header-align="center" align="center" width="185"></el-table-column> -->
        <!-- <el-table-column prop="guaranteeType" label="保函类型" sortable="custom" header-align="center" align="center" width="170">
          <template slot-scope="scope">
            <el-tag size="mini" type="danger"><span>{{fomatMethod(scope.row.guaranteeType,1)}}</span>
            </el-tag>
          </template>
        </el-table-column> -->
        <el-table-column prop="guaranteeAmount" label="担保金额（元）" header-align="right" align="right" width="120">
          <template slot-scope="scope">
            <div>{{scope.row.guaranteeAmount}}</div>
          </template>
        </el-table-column>
        <el-table-column prop="guaranteePrice" label="保函费用（元）" header-align="right" align="right" width="120">
          <template slot-scope="scope">
            <div>{{scope.row.guaranteePrice}}</div>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="openDate" label="开具时间" sortable="custom" header-align="center" align="center" width="180"></el-table-column> -->
        <el-table-column prop="createDate" label="创建时间" sortable="custom" header-align="center" align="center" width="180"></el-table-column>
        <!-- <el-table-column prop="updateDate" label="更新时间" sortable="custom" header-align="center" align="center" width="180"></el-table-column> -->
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button type="text" size="small"  @click="seeInfo(scope.row.id)">{{scope.row.offlineAuditStatus==='10'?'审核':'查看'}}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
      <!-- 弹窗, 上传文件 -->
      <upload v-if="uploadVisible" ref="upload" @refreshDataList="getDataList"></upload>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import Cookies from 'js-cookie'
import qs from 'qs'
// import AddOrUpdate from './bgguaranteeletter-add-or-update'
// import Upload from './bgguaranteeletter-upload'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/guarantee/page',
        getDataListIsPage: true,
        exportURL: '/letter/bgguaranteeletter/export',
        deleteURL: '/letter/bgguaranteeletter',
        enableURL: '/letter/bgguaranteeletter/enable',
        stopURL: '/letter/bgguaranteeletter/stop',
        deleteIsBatch: true
      },
      guaranteeTypeoptions: [],
      guaranteeTypemaps: '',
      searchShow: false,
      letterStatusmaps: '',
      letterStatusoptions: [],
      activeName: '10,20,30',
      getDicListURL: '/sys/dict/type/',
      statusoptions: [
        {
          value: 1,
          label: '正常'
        },
        {
          value: 0,
          label: '停用'
        }
      ],
      dataForm: {
        name: '',
        bbrName: '',
        applyName: '',
        issueName: '',
        platformName: '',
        offlineAuditStatus: '10,20,30',
        insuranceName: '',
        guaranteeType: '',
        startDate: '',
        endDate: '',
        type: '3' // 类型1：查询申请方保函信息列表、2：查询出具方保函信息列表、3：查询使用方保函信息列表
      },
      daterange: null,
      orderField: 'update_date',
      order: 'desc',
      uploadVisible: false
    }
  },
  activated () {
    this.getGuaranteeTypeInfo()
    this.getLetterStatusInfo()

    this.getDataList()
  },
  watch: {
    dataForm: {
      handler (a) {
        if (!a.startDate && !a.endDate) {
          this.daterange = []
        }
        if (a.offlineAuditStatus === '10' || a.offlineAuditStatus === '20' || a.offlineAuditStatus === '30') {
          this.activeName = a.offlineAuditStatus
        } else {
          this.activeName = '10,20,30'
        }
      },
      deep: true
    }
  },
  components: {
    // AddOrUpdate,
    // Upload
  },
  methods: {
    exportHandle () {
      var params = qs.stringify({
        'token': Cookies.get('token'),
        ...this.dataForm,
        type: 2
      })
      window.location.href = `${window.SITE_CONFIG['apiURL']}${this.mixinViewModuleOptions.exportURL}?${params}`
    },
    handleClick () {
      this.dataForm.offlineAuditStatus = this.activeName
      this.getDataList()
    },
    statusColor (code) {
      let dcode = Number(code)
      if (dcode < 30) {
        return 'info'
      } else if (dcode < 50) {
        return 'warning'
      } else if (dcode === 50) {
        return 'success'
      } else if (dcode < 100) {
        return 'danger'
      }
    },
    change (val) {
      console.log(val)
      if (val) {
        this.$set(this.dataForm, 'startDate', val[0] + ' 00:00:00')
        this.$set(this.dataForm, 'endDate', val[1] + ' 23:59:59')
      } else {
        this.$set(this.dataForm, 'startDate', '')
        this.$set(this.dataForm, 'endDate', '')
      }
    },
    resetData () {
      if (this.$route.name !== 'letter-bgguaranteeletterCjf') {
        if (this.$route.name === 'bgguaranteeletterCjfReviewed') {
          // this.dataForm.letterStatus = '20'
          return {
            letterStatus: '20'
          }
        }
        if (this.$route.name === 'bgguaranteeletterCjfDefray') {
          return {
            letterStatus: '40'
          }
        }
        if (this.$route.name === 'bgguaranteeletterCjfPayed') {
          return {
            letterStatus: '50'
          }
        }
        if (this.$route.name === 'bgguaranteeletterCjfRefunded') {
          return {
            letterStatus: '90'
          }
        }
        if (this.$route.name === 'bgguaranteeletterCjfNotSubmitted') {
          return {
            letterStatus: '10'
          }
        }
      }
    },
    fomatMethod (value, i) {
      if (i === 1) {
        return this.guaranteeTypemaps[value]
      }
      if (i === 2) {
        return this.letterStatusmaps[value]
      }
    },
    // 获取当前状态信息
    getLetterStatusInfo () {
      this.$http
        .get(this.getDicListURL + 'letterStatus')
        .then(({ data: res }) => {
          this.letterStatusoptions = {
            ...this.letterStatusoptions,
            ...res.data.list
          }
          this.letterStatusmaps = {
            ...this.letterStatusmaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    },
    getGuaranteeTypeInfo () {
      this.$http
        .get(this.getDicListURL + 'guaranteeType')
        .then(({ data: res }) => {
          console.log(res.data)
          this.guaranteeTypeoptions = {
            ...this.guaranteeTypeoptions,
            ...res.data.list
          }
          this.guaranteeTypemaps = {
            ...this.guaranteeTypemaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    },
    // 导入
    importHandle () {
      this.uploadVisible = true
      this.$nextTick(() => {
        this.$refs.upload.init()
      })
    },
    seeInfo (id, type) {
      this.$router.push({
        name: 'bgguaranteeletterDetail',
        query: {
          seeLetterId: `${id}`,
          type: type,
          JumpName: this.$route.name
        }
      })
    },
    // 统计
    getSummaries (param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        const values = data.map((item) => Number(item[column.property]))
        if (
          !values.every((value) => isNaN(value)) &&
          (index === 4 || index === 5)
        ) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
          sums[index] += ' 元'
        } else {
          sums[index] = '-'
        }
      })
      return sums
    },
    // 状态启用具体代码需微调格式去除空格
    enableHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(
        this.$t('prompt.info', { handle: '启用' }),
        this.$t('prompt.title'),
        {
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          this.$http
            .put(
              this.mixinViewModuleOptions.enableURL,
              id ? [id] : this.dataListSelections.map((item) => item.id)
            )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getDataList()
                }
              })
            })
            .catch(() => {})
        })
        .catch(() => {})
    },
    // 状态停用
    stopHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(
        this.$t('prompt.info', { handle: '停用' }),
        this.$t('prompt.title'),
        {
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          // 具体代码需微调格式去除空格
          this.$http
            .put(
              this.mixinViewModuleOptions.stopURL,
              id ? [id] : this.dataListSelections.map((item) => item.id)
            )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getDataList()
                }
              })
            })
            .catch(() => {})
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
  .line{
  display: block;
  height: 1px;
  width: 100%;
  margin: 8px 0;
  background-color: #DCDFE6;
}
</style>
