<!--
 * @Descripttion:
 * @Author: k<PERSON>weiqiang
 * @Date: 2021-04-12 10:47:18
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-04-12 11:08:37
-->
<template>
 <el-dialog class="preview CAmodel" title="预览" append-to-body :visible.sync="imgVisible" >
      <div slot='title'>
        <h3>预览</h3>
      </div>
      <div v-loading='loading'><img style="width:100%;" :src="img" alt=""></div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="imgVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
</template>
<script>
export default {
  data () {
    return {
      imgVisible: false,
      img: '',
      loading: false
    }
  },
  methods: {
    init () {
      this.imgVisible = true
    }
  }
}
</script>
