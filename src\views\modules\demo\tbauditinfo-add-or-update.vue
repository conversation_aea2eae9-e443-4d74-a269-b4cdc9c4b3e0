<template>
    <div>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"  :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
        <el-form-item label="审核人" prop="contractName">
            <el-input v-model="dataForm.contractName" placeholder="审核人"></el-input>
        </el-form-item>
        <el-form-item label="审核时间" prop="auditDatetime">
            <el-input v-model="dataForm.auditDatetime" placeholder="审核时间"></el-input>
        </el-form-item>
        <el-form-item label="审核意见" prop="auditOpinion">
            <el-input v-model="dataForm.auditOpinion" placeholder="审核意见"></el-input>
        </el-form-item>
        <el-form-item label="审核状态" prop="auditStatus">
            <el-radio-group v-model="dataForm.auditStatus">
                <el-radio :label="'0'">审核中</el-radio>
                <el-radio :label="'1'">已审核</el-radio>
                <el-radio :label="'2'">已退回</el-radio>
            </el-radio-group>
        </el-form-item>

                    <el-form-item label="记录编码" prop="code">
     <el-input v-model="dataForm.code" placeholder="记录编码"></el-input>
</el-form-item>
                                   <el-form-item label="记录名称" prop="name">
     <el-input v-model="dataForm.name" placeholder="记录名称"></el-input>
</el-form-item>
                                   <el-form-item label="节点编码" prop="nodeCode">
     <el-input v-model="dataForm.nodeCode" placeholder="节点编码"></el-input>
</el-form-item>
                                   <el-form-item label="节点名称" prop="nodeName">
     <el-input v-model="dataForm.nodeName" placeholder="节点名称"></el-input>
</el-form-item>
                                   <el-form-item label="实例表编码" prop="projectCode">
     <el-input v-model="dataForm.projectCode" placeholder="实例表编码"></el-input>
</el-form-item>
                                                 <el-form-item label="实例表名称" prop="projectName">
                  <el-input v-model="dataForm.projectName" placeholder="实例表名称">
                  </el-input>
              </el-form-item>
        <el-form-item label="业务数据编码" prop="businessCode">
            <el-input v-model="dataForm.businessCode" placeholder="业务数据编码"></el-input>
        </el-form-item>
        <el-form-item label="业务数据名称" prop="businessName">
            <el-input v-model="dataForm.businessName" placeholder="业务数据名称"></el-input>
        </el-form-item>

                           <el-form-item label="状态" prop="status" size="mini">
   <el-radio-group v-model="dataForm.status">
       <el-radio :label="0">停用</el-radio>
       <el-radio :label="1">正常</el-radio>
   </el-radio-group>
</el-form-item>
                    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">确认</el-button>
    </template>
  </el-dialog>
     </div>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      selectVisible: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        nodeId: '',
        nodeCode: '',
        nodeName: '',
        projectId: '',
        projectCode: '',
        projectName: '',
        contractId: '',
        contractCode: '',
        contractName: '',
        businessId: '',
        businessCode: '',
        businessName: '',
        auditDatetime: '',
        auditOpinion: '',
        auditStatus: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        code: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        nodeCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        nodeName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        projectCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        projectName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        contractCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        contractName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        businessCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        businessName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        auditDatetime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        auditOpinion: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        auditStatus: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        status: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]

      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/demo/tbauditinfo/${this.dataForm.id}`).then(({ data: res }) => {
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    }
  }
}
</script>
