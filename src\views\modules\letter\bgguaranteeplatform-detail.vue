<template>
  <div >
    <el-dialog :visible.sync="visible" :title="`浏览`"  :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
      <el-form :model="dataForm" ref="dataForm" v-loading='loading' :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
        <el-row :gutter="20">
          <el-col :xs="24" :lg="colConfig">
            <el-form-item label="机构名称：" prop="name" label-width="190px">
              <span>{{dataForm.name}}</span>
            </el-form-item>

            <el-form-item label="机构类型：" prop="orgType" label-width="190px">
              <span v-if="dataForm.orgType == 1">招投标交易平台</span>
              <span v-if="dataForm.orgType == 2">其他</span>
            </el-form-item>

            <el-form-item label="加密方式：" prop="earnestMoney" label-width="190px">
              <span v-if="dataForm.orgType == 1">SHA256</span>
              <span v-if="dataForm.orgType == 2">RSA</span>
            </el-form-item>
            <el-form-item label="加密密钥：" prop="secretKey" label-width="190px">
              <span>{{dataForm.secretKey}}</span>
            </el-form-item>

            <el-form-item label="RSA私钥：" prop="rsaPrikey" label-width="190px">
              <span>{{dataForm.rsaPrikey}}</span>
            </el-form-item>
            <el-form-item label="RSA公钥：" prop="rsaPubkey" label-width="190px">
              <span>{{dataForm.rsaPubkey}}</span>
            </el-form-item>

            <el-form-item label="平台网站：" prop="website" label-width="190px">
              <span>{{dataForm.website}}</span>
            </el-form-item>

            <el-form-item label="法定代表人：" prop="corporation" label-width="190px">
              <span>{{dataForm.corporation}}</span>
            </el-form-item>

            <el-form-item label="邮箱：" prop="email" label-width="190px">
              <span>{{dataForm.email}}</span>
            </el-form-item>

            <el-form-item label="机构联系电话：" prop="phoneNumber" label-width="190px">
              <span>{{dataForm.phoneNumber}}</span>
            </el-form-item>

            <!-- <el-form-item label="LOGO："   label-width="190px">
              <span><img style="width:100%;" :src="url"><img></span>
            </el-form-item> -->

          </el-col>
          <el-col :xs="24" :lg="colConfig" label-width="190px">
            <el-form-item label="邮编：" prop="postcode" label-width="190px">
              <span>{{dataForm.postcode}}</span>
            </el-form-item>
            <el-form-item label="地址：" prop="address" label-width="190px">
              <span>{{dataForm.address}}</span>
            </el-form-item>
            <el-form-item label="联系人：" prop="biddingMoneyState" label-width="190px">
              <span>{{dataForm.linkman}}</span>
            </el-form-item>
            <el-form-item label="联系人电话：" prop="biddingGatherType" label-width="190px">
               <span>{{dataForm.linkmanTel}}</span>
            </el-form-item>
            <!-- <el-form-item label="担保金额限制(元)：" prop="guaranteeLimitAmount" label-width="190px">
              <span>{{dataForm.guaranteeLimitAmount}}</span>
            </el-form-item> -->
            <el-form-item label="开户行名称：" prop="bankAccountName" label-width="190px">
              <span>{{dataForm.bankAccountName}}</span>
            </el-form-item>
            <el-form-item label="银行账户号码：" prop="bankAccountNo" label-width="190px">
              <span>{{dataForm.bankAccountNo}}</span>
            </el-form-item>

            <el-form-item label="银行名称：" prop="bankName" label-width="190px">
              <span>{{dataForm.bankName}}</span>
            </el-form-item>
            <el-form-item label="是否收取平台使用费：" prop="isIncludePlatformFee" label-width="190px">
              <span v-if="dataForm.isIncludePlatformFee == 1">是</span>
              <span v-if="dataForm.isIncludePlatformFee == 0">否</span>
            </el-form-item>
            <!-- <el-form-item label="排序：" prop="orders" label-width="190px">
              <span>{{dataForm.orders}}</span>
            </el-form-item> -->
            <el-form-item label="状态：" prop="status" label-width="190px">
              <span v-if="dataForm.status == 0">未提交</span>
              <span v-if="dataForm.status == 1">待审核</span>
              <span v-if="dataForm.status == 2">已认证</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
             <el-form-item label="机构描述：" prop="description" label-width="190px">
              <span v-html="dataForm.description"></span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template slot="footer">
        <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data () {
    return {
      url: '',
      fileList: [],
      colConfig: 2, // 列配置 1 2列
      visible: false,
      loading: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        secretKey: '',
        rsaPrikey: '',
        rsaPubkey: '',
        secretType: '',
        website: '',
        orgType: '',
        corporation: '',
        email: '',
        phoneNumber: '',
        fax: '',
        logoUrl: '',
        postcode: '',
        address: '',
        linkman: '',
        linkmanTel: '',
        guaranteeLimitAmount: '',
        description: '',
        bankAccountName: '',
        bankAccountNo: '',
        bankName: '',
        bankNo: '',
        auditId: '',
        isIncludePlatformFee: '',
        orders: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      orgTypeOptions: '',
      orgTypemaps: '',
      getDicListURL: '/sys/dict/type/',
      secretTypeOptions: '',
      secretTypemaps: ''
    }
  },
  computed: {},
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.getInfo()
    },
    // 获取信息
    getInfo () {
      this.loading = true
      this.$http
        .get(`/letter/bgguaranteeplatform/${this.dataForm.id}`)
        .then(({ data: res }) => {
          this.loading = false

          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
          if (this.dataForm.logoUrl != null) {
            this.url = window.SITE_CONFIG['apiURL'] +
                '/sys/oss/localhostDownload/' +
                this.dataForm.logoUrl
          }
        })
        .catch(() => {})
    }
  }
}
</script>
