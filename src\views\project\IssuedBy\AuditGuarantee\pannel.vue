<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2021-09-28 14:40:16
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-09-28 16:15:46
-->
<template>
  <el-card class="box-card  maxcard proDia" shadow="never">
    <el-row :gutter="20">
      <h3 class="temp-subtitle">审核保函类型</h3>
      <div class="guaranteeList">
        <!-- {{list}} -->
        <div class="guaranteeList-item" :class="active==index?'active':''" v-for="(item,index) in list" @click="guaranteeClick(index,item.dictCode)" :key="index">
          <span class="icon"><img :src="iconL(item.dictCode)" alt=""></span>
          <span class="name">{{item.dictName}}</span>
          <i class="choice" v-if="active==index"><img src="@/assets/img/choice.png" alt=""></i>
        </div>
      </div>
    </el-row>
  </el-card>
</template>
<script>
import { getDict } from '@/utils/index'
export default {
  data () {
    return {
      list: [],
      guaranteeTypenamesOptions: getDict('电子保函类型'),
      iconList: [
        { name: 'tbdbbh', icon: require('@/assets/img/1-tbbh.png') },
        { name: 'lydbbh', icon: require('@/assets/img/1-lybh.png') },
        { name: 'other', icon: require('@/assets/img/1-tbbh.png') }
      ],
      active: null
    }
  },
  created () {
    this.getGuaranteeTypecodes()
  },
  methods: {
    iconL (code) {
      if (code) {
        // console.log
        if (this.iconList.filter(a => a.name === code).length > 0) {
          return this.iconList.filter(a => a.name === code)[0].icon
        } else {
          return this.iconList.filter(a => a.name === 'other')[0].icon
        }
      }
    },
    guaranteeClick (idx, code) {
      this.active = idx
      let params = {
        payStatus: ''
      }
      if (code === 'tbdbbh') {
        params.payStatus = '30'
      } else {
        params.payStatus = ''
      }
      this.$emit('getCode', code, params)
    },
    getGuaranteeTypecodes () {
      this.$http
        .get(`/letter/bgguaranteeissue/getGuaranteeTypecodes`)
        .then(({ data: res }) => {
          let op = this.guaranteeTypenamesOptions
          console.log(this.guaranteeTypecodes)
          let arr = res.data.split(',')
          console.log(arr)
          op.map((a) => {
            arr.map((b) => {
              if (b === a.dictCode) {
                this.list.push(a)
              }
            })
          })
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.guaranteeList {
  display: flex;
  flex-wrap: wrap;
  // margin: 20px auto;
  .guaranteeList-item {
    width: 200px;
    box-shadow: 0 0 30px #e7e7e7;
    padding: 15px;
    margin: 20px;
    text-align: center;
    transition: all 0.3s ease-in-out;
    border: 1px solid transparent;
    cursor: pointer;
    position: relative;
    span {
      display: block;
      margin-top: 12px;
    }
    .icon {
      transition: all 0.3s ease-in-out;
      display: inline-block;
      width: 40px;
      height: 40px;
      line-height: 40px;
      img {
        transition: all 0.3s ease-in-out;
        width: 40px;
      }
    }
  }
  .guaranteeList-item:hover {
    transform: scale(1.1);
    border: 1px solid #f18241;
    .icon {
      background-color: rgba(241, 130, 65, 0.1);
      transform: scale(1.4);
      border-radius: 50%;
      img {
        width: 25px;
      }
    }
  }
  .active {
    transform: scale(1.1);
    border: 1px solid rgb(241, 130, 65);
    color: #f18241;
    .icon {
      background-color: rgba(241, 130, 65, 0.1);
      transform: scale(1.4);
      border-radius: 50%;
      img {
        width: 25px;
      }
    }
  }
  .choice {
    position: absolute;
    top: -2px;
    right: -2px;
    img {
      width: 18px;
    }
  }
}
.maxcard {
  // margin-top: 20px;
  margin-bottom: 10px;
  // min-height: calc(100vh - 70px - 38px - 30px) !important;
  // height: calc(00vh - 70px - 38px - 30px) !important;
  background: rgb(253, 253, 253);
  // text-align: center;
  h4 {
    font-size: 22px;
  }
  .temp-subtitle {
    display: inline-block;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    font-size: 15px;
    border-left: 6px solid rgb(241, 130, 65);
    text-indent: 15px;
    font-weight: bold;
    margin: 0;
    // padding: 16px 0;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
