<template>
  <div class="home" >
    <el-row
      :gutter="20"
      class="head"
      v-if="show"
    >
      <el-col
        :md="8"
        :lg="6"
        :xl='4'
        v-for="item in headList"
        :key="item.name"

      >
        <el-card
          @click.native="goPath(item.path)"
          class="box-card"
          shadow="hover"
        >
          <div class="icon">
            <img
              :src='item.img'
              alt=""
            >
          </div>

          <div class="item">
            {{item.name}}
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row
      :gutter="20"
      class="con"
    >
      <el-col :md="24" :lg="16">
        <el-card
          class="box-card"
          shadow="hover"
        >
          <div
            slot="header"
            class="clearfix bim-header"
            shadow="hover"
          >
            <span>项目总览</span>

            <!-- <el-select
              v-model="value"
              placeholder="请选择"
              size="mini"
              class="year"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select> -->
          <!--             <el-button
                    style="float: right; padding: 3px 0"
                    type="text"
              >更多>></el-button>-->

          </div>
          <div class="item">
            <el-row>
              <el-col
                :span="12"
                class="alltable"
              >
                <el-table
                  :data="tableData"
                  style="width: 100%"
                  border
                >
                  <el-table-column prop="code1">
                  </el-table-column>
                  <el-table-column
                    prop="code2"
                    min-width="120"
                  >
                    <template slot-scope="scope">
                      <div>项目总数：{{scope.row.code2}}</div>
                      <div>总投资额：{{scope.row.code3}}万元</div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="code3"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <el-button
                        type="primary"
                        size='mini'
                        @click="toPath(scope.row.path)"
                        round
                      >
                        查看
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
              <el-col :span="12">
                <div
                  class="chart"
                  ref="myChart"
                ></div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
      <el-col :md="24" :lg="8">
        <el-card
          class="box-card"
          shadow="hover"
        >
          <div
            slot="header"
            class="clearfix bim-header"
            shadow="hover"
          >
            <span>项目进度</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="text" @click="goPath('basic-bimproject')" v-if="show"
            >更多>></el-button>
          </div>
          <div class="item">
            <div
              v-for="(item,index) in progressList"
              :key="index"
              style="padding: 10px 0;"
            >
              <div>项目{{index+1}} - {{item.name}}</div>
              <el-progress :percentage="item.projectProgress"></el-progress>
            </div>
          </div>
        </el-card>
      </el-col>

    </el-row>

    <el-row
      :gutter="20"
      class="con2"
    >
      <el-col :md="24" :lg="8">
        <el-card
          class="box-card"
          shadow="hover"
        >
          <div
            slot="header"
            class="clearfix bim-header"
            shadow="hover"
          >
            <span>新闻动态</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="text" @click="goPath('demo-news')" v-if="show"
            >更多>></el-button>
          </div>
          <div class="item news">
            <div
              class="newsCon"
              v-for="(item,index) in news"
              :key='index'
              @click="goNews(item.id,1)"
            >
              <div>{{item.title}} </div>
              <div>{{item.pubDate | fomatedate}}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :md="24" :lg="8">
        <el-card
          class="box-card"
          shadow="hover"
        >
          <div
            slot="header"
            class="clearfix bim-header"
            shadow="hover"
          >
            <span>通知公告</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="text" @click="goPath('demo-news')" v-if="show"
            >更多>></el-button>
          </div>
          <div class="item news">
            <div
              class="newsCon"
              v-for="(item,index) in Notice"
              :key='index'
               @click="goNews(item.id,2)"
            >
              <div>{{item.title}} </div>
              <div>{{item.pubDate | fomatedate}}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :md="24" :lg="8">
        <el-card
          class="box-card"
          shadow="hover"
        >
          <div
            slot="header"
            class="clearfix bim-header"
            shadow="hover"
          >
            <span>预警信息</span>
            <el-button
              style="float: right; padding: 3px 0" v-if="show"
              type="text"  @click="goPath('basic-bimwarninginfo')">更多>></el-button>
          </div>
          <div class="item news">
            <div
              class="newsCon"
              v-for="(item,index) in WarningList"
              :key='index'
              @click="getWarning(item.id)"
            >
              <div>{{item.name}} </div>
              <div>{{item.updateDate | fomatedate}}</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-dialog
        title="预警信息"
        :visible.sync="WarningDia"
        width="30%"
        center
        :before-close="handleClose">
        <div class="inner" v-loading='WarningLoadng'>
          <!-- <div><span style="font-weight:bold;">{{WarningData.projectName}}</span>  <span style="float: right;color: rgba(0, 0, 0, 0.3);">{{WarningData.updateDate}}</span> </div>
          <div style="margin-top:10px;" ref='warningCon'></div> -->

          <h2 class="title">{{WarningData.projectName}}</h2>
          <div class="desc"><a class="user">预警信息</a> <span class="data">{{WarningData.updateDate}}</span> </div>
          <div class="content" ref='warningCon'>

          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="WarningDia = false">确 定</el-button>
        </span>
      </el-dialog>
    </el-row>
  </div>
</template>
<script>
import mixinViewModule from '@/mixins/view-module'

const headList = [
  {
    name: '项目登记',
    img: require('../../assets/img/icon1.png'),
    path: '/basic-bimproject'
  },
  {
    name: '前期资料',
    img: require('../../assets/img/icon2.png'),
    path: '/basic-bimprepreliminary'
  },
  {
    name: '项目任务',
    img: require('../../assets/img/icon3.png'),
    path: '/progress-bucpphasetask'
  },
  {
    name: '合同申请',
    img: require('../../assets/img/icon4.png'),
    path: '/contract-bimcontractmanagement-apply'
  },
  {
    name: '变更登记',
    img: require('../../assets/img/icon5.png'),
    path: '/progress-bimbucprojectchange'
  },
  {
    name: '竣工结算',
    img: require('../../assets/img/icon6.png'),
    path: '/finance-bimfinancecompletion'
  }
]
var echarts = require('echarts')
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      // 获取预警信息 传入参数  条数限制 5
      getBimWarningLimitURL: '/demo/tbwarninginfo/dashboard',
      // 获取新闻公告信息  传入参数  类型 1公告 2新闻 和条数限制
      getNewsLimitURL: '/demo/news/dashboard',
      // 获取进度信息
      getProgressListURL: '/basic/bimproject/projectProcess',
      // 项目基本信息
      getProjectSpeedInteURL: '/basic/bimproject/getProjectSpeedInte',
      headList: headList,
      progressList: [],
      news: [], // 新闻列表
      Notice: [], // 通知列表
      WarningList: [], // 预警列表
      WarningDia: false,
      WarningLoadng: false,
      WarningData: {},
      tableData: [{
        code1: '项目前期',
        code2: '',
        code3: ''
      }, {
        code1: '在建项目',
        code2: '',
        code3: ''
      }, {
        code1: '竣工项目',
        code2: '',
        code3: ''
      }],
      options: [{
        value: '2019',
        label: '2019'
      }, {
        value: '2018',
        label: '2018'
      }],
      value: '2019',
      show: true
    }
  },
  created () {
    this.getHomeJurisdiction()
  },
  mounted () {
    //
  },
  activated () {
    this.charts()
    this.getProgressList()
    this.getNews()
    this.getNotice()
    this.Warning()
  },
  filters: {
    fomatedate (val) {
      //
      return val.substring(0, 10)
    }
  },
  methods: {
    getWarning (id) {
      this.WarningLoadng = true
      this.$http.get(`/demo/tbwarninginfo/${id}`).then(({ data: res }) => {
        this.WarningLoadng = false

        this.WarningData = {}
        this.WarningData = res.data
        this.$refs.warningCon.innerHTML = res.data.remars
      })
      this.WarningDia = true
    },
    handleClose (done) {
      done()
    },
    // eslint-disable-next-line camelcase
    IsURL (str_url) {
      // var strRegex = '((https|http|ftp|rtsp|mms)?://)' +
      // "(([0-9a-z_!~*'().&=+$%-]+: )?[0-9a-z_!~*'().&=+$%-]+@)?" + // ftp的user@
      //   '(([0-9]{1,3}\\.){3}[0-9]{1,3}' + // IP形式的URL- **************
      //   '|' + // 允许IP和DOMAIN（域名）
      //   "([0-9a-z_!~*'()-]+\\.)*" + // 域名- www.
      //   '([0-9a-z][0-9a-z-]{0,61})?[0-9a-z]\\.' + // 二级域名
      //   '[a-z]{2,6})' + // first level domain- .com or .museum
      //   '(:[0-9]{1,4})?' + // 端口- :80
      //   '((/?)|' + // a slash isn't required if there is no file name
      //   "(/[0-9a-z_!~*'().;?:@&=+$,%#-]+)+/?)"

      // eslint-disable-next-line no-useless-escape
      var re = /(http:\/\/)?([A-Za-z0-9]+\.[A-Za-z0-9]+[\/=\?%\-&_~`@[\]\':+!]*([^<>\"\"])*)/g
      let str = str_url.match(re)[0]
      return `http://${str}`
      // var re = new RegExp(strRegex)
      // // re.test()
      // if (re.test(str_url)) {
      //
      //   let url = str_url.replace(re, function (a, b, c) {
      //
      //     return c
      //   })
      //   let url2 = url.replace('</a>', '')
      //
      //   return url2
      // } else {
      //   return (false)
      // }
    },
    goNews (id, type) {
      //
      if (type === 1) {
        this.$http.get(`/demo/news/${id}`).then(({ data: res }) => {
          //
          this.data = res.data
          var str = res.data.content
          window.open(this.IsURL(str), '_blank')
        })
        return
      }

      let routeData = this.$router.resolve({
        name: 'news',
        params: { 'id': id }
      })
      window.open(routeData.href, '_blank')
    },
    // 获取首页权限
    getHomeJurisdiction () {
      this.$http.get('/sys/role/homeControl', {
        params: {
          homeControl: '1',
          userId: this.$store.state.user.id
        }
      }).then(({ data: res }) => {
      //

        if (res.data === true) {
          this.show = true
        } else {
          this.show = false
        }
      })
    },
    getProgressList () {
      this.$http.get(this.getProgressListURL).then(({ data: res }) => {
        //

        this.progressList = res.data
      })
    },
    charts () {
      this.$http.get(this.getProjectSpeedInteURL).then(({ data: res }) => {
        let data = res.data
        this.tableData[0].code2 = data.qqnumber
        this.tableData[0].code3 = data.qqactualInvestment
        this.tableData[1].code2 = data.sgnumber
        this.tableData[1].code3 = data.sgactualInvestment
        this.tableData[2].code2 = data.jgnumber
        this.tableData[2].code3 = data.jgactualInvestment

        var myChart = echarts.init(this.$refs.myChart)
        // 绘制图表
        myChart.setOption({
          color: function (params) {
            var colorList = [
              ['#6adf5b', '#96ee74'],
              ['#2b85ff', '#52b2ff'],
              ['#30c0fc', '#47d2f9']
            ]
            var index = params.dataIndex
            if (params.dataIndex >= colorList.length) {
              index = params.dataIndex - colorList.length
            }
            return new echarts.graphic.LinearGradient(0, 0, 0, 1,
              [{
                offset: 0,
                color: colorList[index][0]
              },
              {
                offset: 1,
                color: colorList[index][1]
              }
              ])
          },

          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b} : {c} ({d}%)'
          },
          legend: {
            x: 'center',
            y: 'top',
            data: ['项目前期', '在建项目', '竣工项目']
          },
          series: [
            {
              name: '项目总览',
              type: 'pie',
              radius: '60%',
              center: ['50%', '45%'],
              data: [
                { value: data.qqnumber, name: '项目前期' },
                { value: data.sgnumber, name: '在建项目' },
                { value: data.jgnumber, name: '竣工项目' }

              ],
              itemStyle: {

                normal: {
                  fontWeight: 'bold',
                  fontSize: 18,
                  label: {
                    position: 'inner',
                    formatter: function (params) { return (params.percent - 0) + '%' },
                    textStyle: {
                      color: '#fff'
                    },

                    rich: {
                      name: {
                        textBorderColor: '#fff'
                      }
                    }
                  },

                  labelLine: {
                    show: false
                  }
                }
              }
            }
          ]
        })
      })
    },
    // 获取新闻
    getNews () {
      let params = {
        newType: 2,
        limit: 6
      }
      // ?newType=${1}&limit=${6}
      this.$http.get(`${this.getNewsLimitURL}`, {
        params: params
      }).then(({ data: res }) => {
        this.news = res.data
      })
    },
    // 获取公告
    getNotice () {
      let params = {
        newType: 1,
        limit: 6
      }
      // ?newType=${1}&limit=${6}
      this.$http.get(`${this.getNewsLimitURL}`, {
        params: params
      }).then(({ data: res }) => {
        //

        this.Notice = res.data
      })
    },
    // 获取预警信息
    Warning () {
      this.$http.get(`${this.getBimWarningLimitURL}/${6}`).then(({ data: res }) => {
        this.WarningList = res.data
      })
    },
    goPath (path) {
      //
      this.$router.push({
        path: path
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.el-row {
  // margin-bottom: 20px;
}
.con {
  .el-col {
    height: 100%;
    // background: white;
    .el-card {
      height: 100%;
      .item {
        height: 100%;
        overflow: hidden;
        .el-col {
          height: 280px;
        }
      }
      .el-card__body {
        height: 300px !important;
      }
      .chart {
        width: 100%;
        height: 100%;
      }
    }
  }
}
.con2 {
  .el-col {
    height: 325px;
    // background: white;
    .el-card {
      height: 100%;
    }
  }
}
.head {
  .el-col {
    height: 100px;
    // background: white;
    .el-card {
      height: 100%;
      .el-card__body {
        padding: 20px;
        width: 100%;
        display: flex;
        cursor: pointer;
        .item {
          line-height: 58px;
          text-indent: 20px;
          font-size: 16px;
        }
      }
    }
    .box-card {
      display: flex;
    }
  }
}
.bim-header {
  position: relative;
  .year {
    width: 80px;
    position: absolute;
    right: 70px;
    top: -3px;
  }
}
.news {
  .newsCon {
    overflow: hidden;
    padding: 10px 0;
    border-bottom: 1px solid #ebeef5;
    cursor: pointer;
    div:first-child {
      float: left;
    }
    div:last-child {
      float: right;
    }
  }
  .newsCon:first-child {
    padding-top: 0;
  }
  .newsCon:last-child {
    border-bottom: none;
  }
}

 .inner {
    max-width: 677px;
    margin-left: auto;
    margin-right: auto;
}
 .inner .title {
      font-size: 22px;
      line-height: 1.4;
      margin-bottom: 14px;
}
 .inner .desc .user {
      color: #576b95;
      text-decoration: none;
      margin-right: 10px;
}
 .inner .desc .data {
      color: rgba(0, 0, 0, 0.3);
}
 .inner .desc .pageView {
      color: #f00;
      float: right;
}
 .inner .content {
      padding-top: 20px;
}
</style>
