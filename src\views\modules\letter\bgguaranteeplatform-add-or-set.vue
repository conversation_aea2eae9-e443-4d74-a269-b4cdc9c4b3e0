<template>
  <el-dialog :visible.sync="dialogVisible" :title="`建立关系`" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :inline="true" :model="dataForm" >
      <el-form-item>
          <el-input v-model="dataForm.name" clearable placeholder="机构名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="dataForm.corporation" clearable placeholder="法定代表人"></el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model="dataForm.orgType" clearable placeholder="请选择机构类型类型">
            <el-option v-for="item in orgTypeOptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
            </el-option>
          </el-select>
        </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="getDataList(1)">{{ $t('query') }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="selectHandle()">选择</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="dataListLoading" @row-dblclick="selectHandle" :data="dataList" border @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="name" label="机构名称" width="170px" sortable="custom" header-align="center" align="center"></el-table-column>
      <el-table-column prop="corporation" label="法定代表人" sortable="custom" header-align="center" align="center" width="150px"></el-table-column>
      <el-table-column prop="orgType" label="机构类型" width="120px" sortable="custom" header-align="center" align="center">
        <template slot-scope="scope">
          <el-tag size="mini" type="danger"><span>{{fomatMethod(scope.row.orgType)}}</span></el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="guaranteeLimitAmount" label="担保金额限制" width="150px" sortable="custom" header-align="center" align="center"></el-table-column>
      <el-table-column prop="auditingHour" label="保函审核时长" width="150px" sortable="custom" header-align="center" align="center"></el-table-column>
      <!-- <el-table-column prop="bankAccountName" label="开户行名称" width="150px" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="bankAccountNo" label="银行账户号码" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="bankName" label="银行名称" width="120px" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="bankNo" label="银行编码" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="email" label="邮箱" sortable="custom" header-align="center" align="center"></el-table-column> -->
      <el-table-column prop="phoneNumber" label="机构电话" sortable="custom" header-align="center" align="center" width="150"></el-table-column>
      <!-- <el-table-column prop="fax" label="传真" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="postcode" label="邮编" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="address" label="地址" sortable="custom" header-align="center" align="center"></el-table-column> -->
      <el-table-column prop="linkman" label="联系人" sortable="custom" header-align="center" align="center" width="150"></el-table-column>
      <el-table-column prop="linkmanTel" label="联系人电话" sortable="custom" header-align="center" align="center" width="170px"></el-table-column>
      <el-table-column prop="isIncludePlatformFee" label="是否收取平台使用费" sortable="custom" header-align="center" align="center" width="180px">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isIncludePlatformFee == 1" size="small">是</el-tag>
          <el-tag v-if="scope.row.isIncludePlatformFee == 0" size="small">否</el-tag>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle"
      @current-change="pageCurrentChangeHandle">
    </el-pagination>
  </el-dialog>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      platFormId: '',
      dialogVisible: false,
      guaranteeTypecodes: '',
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgguaranteeissue/page',
        getDataListIsPage: true,
        exportURL: '/letter/bgguaranteeissue/export',
        deleteURL: '/letter/bgguaranteeissue',
        enableURL: '/letter/bgguaranteeissue/enable',
        stopURL: '/letter/bgguaranteeissue/stop',
        deleteIsBatch: true
      },
      statusoptions: [
        {
          value: 0,
          label: '未提交'
        },
        {
          value: 1,
          label: '审核中'
        },
        {
          value: 2,
          label: '已审核'
        }
      ],
      orgTypeOptions: '',
      dataForm: {
        name: '',
        corporation: '',
        orgType: ''
      },
      orderField: 'code',
      order: 'asc',
      orgTypemaps: '',
      getDicListURL: '/sys/dict/type/'
    }
  },
  activated () {
    this.getOrgTypeInfo()
  },
  created () {
    console.log(111)
    this.platFormId = this.$route.params.platformId || ''
    console.log(this.platFormId)
  },
  methods: {
    fomatMethod (value) {
      return this.orgTypemaps[value]
    },
    selectHandle () {
      if (this.dataListSelections.length === 0) {
        return this.$message({
          message: '请选择要关联的选项',
          type: 'warning',
          duration: 500
        })
      }
      let params = {
        platFormId: this.$route.params.platformId,
        issue: this.dataListSelections.map(item => item.id)
      }
      this.$http
        .post('letter/bgissueplatform/relationship', params)
        .then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.dialogVisible = false
              this.$emit('refreshDataList')
            }
          })
        })
        .catch(() => {})
    },
    init () {
      this.dialogVisible = true
      this.getOrgTypeInfo()
      this.getDataList()
    },
    // 获取机构类型
    getOrgTypeInfo () {
      this.$http
        .get(this.getDicListURL + 'orgType')
        .then(({ data: res }) => {
          this.orgTypeOptions = {
            ...this.orgTypeOptions,
            ...res.data.list
          }
          this.orgTypemaps = {
            ...this.orgTypemaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    }
  }
}
</script>
