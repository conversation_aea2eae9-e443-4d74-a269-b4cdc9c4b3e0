<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-letter__bgguaranteeinvoice}">
      <el-form :inline="true" :model="dataForm"  >
        <el-form-item>
          <el-input v-model="dataForm.name" style="width:400px;" clearable placeholder="开票对象名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model="dataForm.invoiceType" clearable placeholder="发票类型">
            <el-option v-for="item in invoiceTypeoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="dataForm.invoiceObject" clearable placeholder="开票对象类型">
            <el-option v-for="item in invoiceObjectoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="dataForm.taxpayerType" clearable placeholder="纳税人类型">
            <el-option v-for="item in taxpayerTypeoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item>
          <el-select v-model="dataForm.status" clearable placeholder="请选择启用状态">
            <el-option v-for="item in statusoptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button type="danger" @click="getDataList(1)">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgguaranteeinvoice:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgguaranteeinvoice:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
          <el-form-item>
          <el-button type="danger" @click="resetHandle()">重置</el-button>
        </el-form-item>
        <!-- <el-form-item>
          <el-button v-if="$hasPermission('letter:bgguaranteeinvoice:update')" type="danger" @click="enableHandle()">{{ $t('enable') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgguaranteeinvoice:update')" type="danger" @click="stopHandle()">{{ $t('stop') }}</el-button>
        </el-form-item> -->
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="name" label="开票对象名称" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
        <el-table-column prop="invoiceType" label="发票类型" sortable="custom" header-align="center" align="center" width="200">
          <template slot-scope="scope">
            <el-tag size="mini" type="danger"><span>{{fomatMethod(scope.row.invoiceType)}}</span></el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="invoiceObject" label="开票对象类型" sortable="custom" header-align="center" align="center" width="200">
          <template slot-scope="scope">
            <el-tag size="mini" type="danger"><span>{{fomatMethod2(scope.row.invoiceObject)}}</span></el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="taxpayerType" label="纳税人类型" sortable="custom" header-align="center" align="center" width="200">
          <template slot-scope="scope">
            <el-tag size="mini" type="danger"><span>{{fomatMethod3(scope.row.taxpayerType)}}</span></el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="invoiceTitle" label="发票抬头" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
        <el-table-column prop="electronicInvoicePhone" label="电子发票接收手机号" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
        <el-table-column prop="electronicInvoiceEmail" label="电子发票接收邮箱" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
        <el-table-column prop="taxpayerNumber" label="纳税人识别号" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
        <el-table-column prop="phoneNumber" label="电话" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
        <el-table-column prop="address" label="地址" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
        <el-table-column prop="bankAccountName" label="开户行名称" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
        <el-table-column prop="bankAccountNo" label="银行账户号码" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
        <!-- <el-table-column prop="status" sortable="custom" label="状态" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == 0" size="small" type="danger">{{ $t('user.status0') }}</el-tag>
            <el-tag v-else size="small" type="success">{{ $t('user.status1') }}</el-tag>
          </template>
        </el-table-column> -->
        <el-table-column prop="createDate" label="创建时间" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
        <el-table-column prop="updateDate" label="更新时间" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="displayHandle(scope.row.id)">浏览</el-button>
            <el-dropdown>
              <span class="el-dropdown-link">
                <el-button type="text" size="mini">更多操作<i class="el-icon-arrow-down el-icon--right"></i></el-button>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('letter:bgguaranteeinvoice:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('letter:bgguaranteeinvoice:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('letter:bgguaranteeinvoice:update')&&scope.row.status==0" type="text" size="small" @click="enableHandle(scope.row.id)">{{ $t('enable') }}</el-button>
                </el-dropdown-item>
                <!-- <el-dropdown-item>
                  <el-button v-if="$hasPermission('letter:bgguaranteeinvoice:update')&&scope.row.status==1" type="text" size="small" @click="stopHandle(scope.row.id)">{{ $t('stop') }}</el-button>
                </el-dropdown-item> -->
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
      <!-- 浏览 -->
      <display v-if="displayVisible" ref="display" @refreshDataList="refreshList"></display>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './bgguaranteeinvoice-add-or-update'
import Display from './bgguaranteeinvoice-display'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgguaranteeinvoice/page',
        getDataListIsPage: true,
        exportURL: '/letter/bgguaranteeinvoice/export',
        deleteURL: '/letter/bgguaranteeinvoice',
        enableURL: '/letter/bgguaranteeinvoice/enable',
        stopURL: '/letter/bgguaranteeinvoice/stop',
        deleteIsBatch: true
      },
      statusoptions: [
        {
          value: 1,
          label: '正常'
        },
        {
          value: 0,
          label: '停用'
        }
      ],
      dataForm: {
        code: '',
        name: '',
        status: '',
        invoiceType: '',
        invoiceObject: '',
        taxpayerType: ''
      },
      getDicListURL: '/sys/dict/type/',
      invoiceTypemaps: '',
      invoiceTypeoptions: [],
      invoiceObjectmaps: '',
      invoiceObjectoptions: [],
      taxpayerTypemaps: '',
      taxpayerTypeoptions: [],
      orderField: 'code',
      order: 'asc'
    }
  },
  components: {
    AddOrUpdate,
    Display
  },
  activated () {
    this.getTaxpayerTypeInfo()
    this.getInvoiceObjectInfo()
    this.getInvoiceTypeInfo()
  },
  methods: {
    fomatMethod (value) {
      return this.invoiceTypemaps[value]
    },
    fomatMethod2 (value) {
      return this.invoiceObjectmaps[value]
    },
    fomatMethod3 (value) {
      return this.taxpayerTypemaps[value]
    },
    // 获取纳税人类型信息
    getTaxpayerTypeInfo () {
      this.$http
        .get(this.getDicListURL + 'taxpayerType')
        .then(({ data: res }) => {
          this.taxpayerTypeoptions = {
            ...this.taxpayerTypetoptions,
            ...res.data.list
          }
          this.taxpayerTypemaps = {
            ...this.taxpayerTypemaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    },
    // 获取开票对象信息
    getInvoiceObjectInfo () {
      this.$http
        .get(this.getDicListURL + 'invoiceObject')
        .then(({ data: res }) => {
          this.invoiceObjectoptions = {
            ...this.invoiceObjectoptions,
            ...res.data.list
          }
          this.invoiceObjectmaps = {
            ...this.invoiceObjectmaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    },
    // 获取发票类型信息
    getInvoiceTypeInfo () {
      this.$http
        .get(this.getDicListURL + 'invoiceType')
        .then(({ data: res }) => {
          this.invoiceTypeoptions = {
            ...this.invoiceTypeoptions,
            ...res.data.list
          }
          this.invoiceTypemaps = {
            ...this.invoiceTypemaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    },
    // 状态启用具体代码需微调格式去除空格
    enableHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(
        this.$t('prompt.info', { handle: '启用' }),
        this.$t('prompt.title'),
        {
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          this.$http
            .put(
              this.mixinViewModuleOptions.enableURL,
              id ? [id] : this.dataListSelections.map(item => item.id)
            )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getDataList()
                }
              })
            })
            .catch(() => {})
        })
        .catch(() => {})
    },
    // 状态停用
    stopHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(
        this.$t('prompt.info', { handle: '停用' }),
        this.$t('prompt.title'),
        {
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          // 具体代码需微调格式去除空格
          this.$http
            .put(
              this.mixinViewModuleOptions.stopURL,
              id ? [id] : this.dataListSelections.map(item => item.id)
            )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getDataList()
                }
              })
            })
            .catch(() => {})
        })
        .catch(() => {})
    }
  }
}
</script>
