<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" append-to-body :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="200px">
      <!-- {{dataForm}} -->

      <el-form-item prop="fileName" label="文件名称">
        <el-input v-model="dataForm.fileName" placeholder="文件名称"></el-input>
      </el-form-item>
      <el-form-item prop="pidName" label="上级菜单" class="menu-list ">
        <el-popover v-model="menuListVisible" ref="menuListPopover" placement="bottom-start" trigger="click">
          <el-input placeholder="输入关键字进行过滤" size="mini" v-model="filterText">
          </el-input>
          <el-scrollbar style="height:300px;">
            <el-tree :data="menuList" :props="{ label: 'fileName', children: 'children' }" node-key="id" ref="menuListTree" :highlight-current="true" :filter-node-method="filterNode"
              default-expand-all :expand-on-click-node="false" accordion @current-change="menuListTreeCurrentChangeHandle">
            </el-tree>
          </el-scrollbar>
        </el-popover>
        <el-input v-model="dataForm.pidName" v-popover:menuListPopover :readonly="true" placeholder="上级菜单">
          <i v-if="dataForm.pid !== '0'" slot="suffix" @click.stop="deptListTreeSetDefaultHandle()" class="el-icon-circle-close el-input__icon"></i>
        </el-input>
      </el-form-item>
      <el-form-item prop="isUploadSupported" label="是否支持上传" class="menu-list ">
        <el-switch v-model="dataForm.isUploadSupported" :active-value='1' :inactive-value='0'>
        </el-switch>
      </el-form-item>
      <div v-if="dataForm.isUploadSupported==1">

        <el-form-item prop="isRequired" label="文件必填" class="menu-list ">
          <el-switch v-model="dataForm.isRequired" :active-value='1' :inactive-value='0'>
          </el-switch>
        </el-form-item>
        <el-form-item prop="isMultipleFilesUpload" label="多文件上传" class="menu-list ">
          <el-switch v-model="dataForm.isMultipleFilesUpload" :active-value='1' :inactive-value='0'>
          </el-switch>
        </el-form-item>
        <el-form-item prop="fileSuffix" label="可上传格式" class="menu-list">
          <span>不校验格式：&nbsp;</span>
          <el-switch v-model="isfileSuffix" @change='fileSuffixChange' :active-value='true' :inactive-value='false'>
          </el-switch>
          <el-checkbox-group v-if="!isfileSuffix" v-model="fileSuffixForm" @change='change'>
            <el-checkbox v-for="(item,index) in fileSuffix" :key="index" :label="item"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item prop="isLegalSign" label="申请方法人签章" class="menu-list ">
          <el-switch v-model="dataForm.isLegalSign" :active-value='1' :inactive-value='0'>
          </el-switch>
        </el-form-item>
        <el-form-item prop="isOfficialSign" label="申请方公章签章" class="menu-list ">
          <el-switch v-model="dataForm.isOfficialSign" :active-value='1' :inactive-value='0'>
          </el-switch>
        </el-form-item>
        <el-form-item prop="isWriteSign" label="申请方手写章签章" class="menu-list ">
          <el-switch v-model="dataForm.isWriteSign" :active-value='1' :inactive-value='0'>
          </el-switch>
        </el-form-item>
        <el-form-item prop="isCheckLegalSign" label="审核方法人签章" class="menu-list ">
          <el-switch v-model="dataForm.isCheckLegalSign" :active-value='1' :inactive-value='0'>
          </el-switch>
        </el-form-item>
        <el-form-item prop="isCheckOfficialSign" label="审核方公章签章" class="menu-list ">
          <el-switch v-model="dataForm.isCheckOfficialSign" :active-value='1' :inactive-value='0'>
          </el-switch>
        </el-form-item>
        <el-form-item prop="isCheckWriteSign" label="审核方手写章签章" class="menu-list ">
          <el-switch v-model="dataForm.isCheckWriteSign" :active-value='1' :inactive-value='0'>
          </el-switch>
        </el-form-item>
        <el-form-item prop="remarks" label="备注" class="menu-list ">
          <el-input type="textarea" :rows="2" placeholder="请输入备注" v-model="dataForm.remarks">
          </el-input>
        </el-form-item>
      </div>

    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
// import { getIconList } from '@/utils'
export default {
  data () {
    return {
      filterText: '',
      visible: false,
      isfileSuffix: false,
      menuList: [],
      menuListVisible: false,
      iconList: [],
      fileSuffixForm: [],
      fileSuffix: ['jpg', 'jpeg', 'png', 'pdf', 'zip', '7z', 'txt', 'doc', 'docx'],
      iconListVisible: false,
      dataForm: {
        id: '',
        fileName: '',
        pid: '0',
        fileType: '',
        issueCode: '',
        guaranteeTypeCodes: '', // 保函类型
        pidName: '',
        status: 1,
        fileSuffix: '',
        isUploadSupported: '', // 是否支持上传
        isMultipleFilesUpload: '',
        isRequired: '', // 必填，1是 0否
        isLegalSign: '', //   申请方法人签章，1是 0否
        isOfficialSign: '', //  申请方公章签章，1是 0否
        isWriteSign: '', //  申请方手写章签章，1是 0否
        isCheckLegalSign: '', // 审核方法人签章，1是 0否
        isCheckOfficialSign: '', //  审核方公章签章，1是 0否
        isCheckWriteSign: '', // 审核方手写章签章，1是 0否
        remarks: '' // 备注
      }
    }
  },
  computed: {
    dataRule () {
      return {
        fileName: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        pidName: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        isMultipleFilesUpload: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        isRequired: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        fileType: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        fileSuffix: [
          {
            required: !this.isfileSuffix,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        isUploadSupported: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ]
      }
    }
  },
  mounted () {
    this.getPop()
  },
  watch: {
    filterText (val) {
      this.$refs.menuListTree.filter(val)
    }
  },
  methods: {
    getPop () {
      this.$nextTick(() => {
        // console.log(this.$refs['menuListPopover'])
      })
    },
    fileSuffixChange (val) {
      console.log(val)
      if (val) {
        this.fileSuffixForm = []
        this.dataForm.fileSuffix = ''
      }
    },
    change (a) {
      console.log(a)
      this.dataForm.fileSuffix = a.join(',')
      console.log(this.dataForm.fileSuffix)
    },
    filterNode (value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.fileSuffixForm = []
        this.$refs['dataForm'].resetFields()
        // this.dataForm.fileType = this.$t('menu.parentNameDefault')
        if (this.dataForm.id) {
          this.getInfo()
        } else {
          this.dataForm.fileType = ''
          this.dataForm.remarks = ''
          this.deptListTreeSetDefaultHandle()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http
        .get(`/letter/bgIssueDocument/${this.dataForm.id}`)
        .then(({ data: res }) => {
          this.dataForm = res.data
          console.log(this.dataForm.fileSuffix)
          if (this.dataForm.fileSuffix) {
            this.fileSuffixForm = this.dataForm.fileSuffix.split(',')
          } else {
            this.fileSuffixForm = []
          }
          if (this.dataForm.pid === '0') {
            return this.deptListTreeSetDefaultHandle()
          }

          this.$refs.menuListTree.setCurrentKey(this.dataForm.pid)
        })
        .catch(() => {})
    },
    // 上级菜单树, 设置默认值
    deptListTreeSetDefaultHandle () {
      this.dataForm.pid = '0'
      this.dataForm.pidName = '一级菜单'
      this.dataForm.level = '0'
    },
    // 上级菜单树, 选中
    menuListTreeCurrentChangeHandle (data) {
      console.log(data)
      this.dataForm.pid = data.id
      this.dataForm.pidName = data.fileName
      this.dataForm.level = `${Number(data.level) + 1}`
      this.menuListVisible = false
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) {
            return false
          }
          this.$http[!this.dataForm.id ? 'post' : 'put'](
            '/letter/bgIssueDocument',
            this.dataForm
          )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            })
            .catch(() => {})
        })
      },
      1000,
      { leading: true, trailing: false }
    )
  }
}
</script>

<style lang="scss" scoped>
.mod-sys__menu {
  .menu-list,
  .icon-list {
    .el-input__inner,
    .el-input__suffix {
      cursor: pointer;
    }
  }
  &-icon-popover {
    width: 458px;
    overflow: hidden;
  }
  &-icon-inner {
    width: 478px;
    max-height: 258px;
    overflow-x: hidden;
    overflow-y: auto;
  }
  &-icon-list {
    width: 458px;
    padding: 0;
    margin: -8px 0 0 -8px;
    > .el-button {
      padding: 8px;
      margin: 8px 0 0 8px;
      > span {
        display: inline-block;
        vertical-align: middle;
        width: 18px;
        height: 18px;
        font-size: 18px;
      }
    }
  }
}
</style>
