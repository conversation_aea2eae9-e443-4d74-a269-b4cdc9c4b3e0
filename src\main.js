/*
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019-09-18 14:03:54
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-10-09 11:14:56
 */
import '@babel/polyfill'
import Vue from 'vue'
import Element from 'element-ui'
import App from '@/App'
import i18n from '@/i18n'
import router from '@/router'
import store from '@/store'
import '@/icons'
import 'element-ui/lib/theme-chalk/index.css'
import '@/assets/scss/aui.scss'
import http from '@/utils/request'
import '@/utils/calculation'
import { hasPermission } from '@/utils'
import cloneDeep from 'lodash/cloneDeep'
import echarts from 'echarts'
import x2js from 'x2js' // xml数据处理插件
import VueClipboard from 'vue-clipboard2'
Vue.use(VueClipboard)

// eslint-disable-next-line new-cap
Vue.prototype.$x2js = new x2js() // 创建x2js对象，挂到vue原型上s
Vue.prototype.$echarts = echarts
Vue.config.productionTip = false
Vue.use(Element, {
  size: 'default',
  i18n: (key, value) => i18n.t(key, value)
})
// 挂载全局
Vue.prototype.$http = http
Vue.prototype.$hasPermission = hasPermission

// 保存整站vuex本地储存初始状态
window.SITE_CONFIG['storeState'] = cloneDeep(store.state)
new Vue({
  i18n,
  router,
  store,
  render: h => h(App)
}).$mount('#app')
