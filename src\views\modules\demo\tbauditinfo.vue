<template>
    <el-container>
        <el-aside width="200px" style="background-color: rgb(255, 255, 255)">
            <el-input
                    placeholder="输入关键字进行过滤"
                    v-model="filterText">
            </el-input>
            <el-tree
                    v-loading="treeLoading"
                    class="filter-tree"
                    :data="treeData"
                    :props="defaultProps"
                    default-expand-all
                    :highlight-current=true
                    :filter-node-method="filterNode"
                    @node-click="handleNodeClick"
                    ref="tree">
            </el-tree>
        </el-aside>
        <el-main style="padding-top: 0px">
            <el-card shadow="never" class="aui-card--fill">
                <div class="mod-contract__bimauditinfo}">
                    <el-form :inline="true" :model="dataForm" >
                        <el-form-item>
                            <el-input v-model="dataForm.code" clearable placeholder="编码"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-input v-model="dataForm.name" clearable placeholder="名称"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-input v-model="dataForm.projectId" clearable placeholder="项目ID"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-input v-model="dataForm.projectCode" clearable placeholder="项目编码"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-input v-model="dataForm.projectName" clearable placeholder="项目名称"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-select v-model="dataForm.status" clearable placeholder="请选择启用状态">
                                <el-option
                                        v-for="item in statusoptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="danger" @click="getDataList(1)">{{ $t('query') }}</el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="success" @click="exportHandle()">{{ $t('export') }}</el-button>
                        </el-form-item>
                    </el-form>
                    <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle" style="width: 100%;">
                        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
                            <el-table-column prop="name" label="记录名称" sortable="custom" header-align="center" align="center"></el-table-column>
                            <el-table-column prop="nodeCode" label="节点编码" sortable="custom" header-align="center" align="center"></el-table-column>
                            <el-table-column prop="nodeName" label="节点名称" sortable="custom" header-align="center" align="center"></el-table-column>
                            <el-table-column prop="projectCode" label="业务编码" sortable="custom" header-align="center" align="center"></el-table-column>
                            <el-table-column prop="projectName" label="业务名称" sortable="custom" header-align="center" align="center"></el-table-column>
                            <el-table-column prop="contractName" label="审核人员" sortable="custom" header-align="center" align="center"></el-table-column>
                            <el-table-column prop="auditDatetime" label="审核时间" sortable="custom" header-align="center" align="center"></el-table-column>
                            <el-table-column prop="auditOpinion" label="审核意见" sortable="custom" header-align="center" align="center"></el-table-column>
                            <el-table-column prop="auditStatus" label="节点审核状态" sortable="custom" header-align="center" align="center">
                               <template slot-scope="scope">
                                <el-tag v-if="scope.row.auditStatus == 0" size="mini" type="danger">未审核</el-tag>
                                <el-tag v-if="scope.row.auditStatus == 1" size="mini" type="danger">审核通过</el-tag>
                                <el-tag v-if="scope.row.auditStatus == 2" size="mini" type="danger">审核退回</el-tag>
                            </template>
                            </el-table-column>
                        <el-table-column prop="createDate" label="创建时间" sortable="custom" header-align="center" align="center"></el-table-column>
                        <el-table-column prop="updateDate" label="更新时间" sortable="custom" header-align="center" align="center"></el-table-column>
                        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
                            <template slot-scope="scope">
                                <el-button v-if="$hasPermission(Permission+':update')" type="text" size="mini" @click="addOrUpdateHandle(scope.row.id)">浏览</el-button>

                                <el-dropdown>
              <span class="el-dropdown-link">
                <el-button type="text" size="mini">更多操作<i class="el-icon-arrow-down el-icon--right"></i></el-button>
              </span>
                                    <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item>
                                            <el-button v-if="$hasPermission(Permission+':delete')" type="text" size="mini" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination
                            :current-page="page"
                            :page-sizes="[10, 20, 50, 100]"
                            :page-size="limit"
                            :total="total"
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="pageSizeChangeHandle"
                            @current-change="pageCurrentChangeHandle">
                    </el-pagination>
                    <!-- 弹窗, 新增 / 修改 -->
                    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="refreshList"></add-or-update>
                    <!-- 弹窗, 上传文件 -->
                    <upload v-if="uploadVisible" ref="upload" @refreshDataList="refreshList"></upload>
                </div>
            </el-card>
        </el-main>
    </el-container>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './tbauditinfo-add-or-update'
import Upload from './tbauditinfo-upload'
import { moduleRoutes } from '@/router'

export default {
  mixins: [mixinViewModule],
  data () {
    return {
      Permission: 'contract:bimauditinfo', // 权限名
      filterText: '',
      treeLoading: false,
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'projectName'
      },
      mixinViewModuleOptions: {
        getDataListURL: '/demo/tbauditinfo/page',
        getDataListIsPage: true,
        exportURL: '/demo/tbauditinfo/export',
        deleteURL: '/demo/tbauditinfo',
        enableURL: '/demo/tbauditinfo/enable',
        stopURL: '/demo/tbauditinfo/stop',
        getTreeDataURL: '/demo/tbauditinfo/tree',
        deleteIsBatch: true
      },
      statusoptions: [{
        value: 1,
        label: '正常'
      }, {
        value: 0,
        label: '停用'
      }],
      dataForm: {
        code: '',
        name: '',
        projectId: '',
        projectName: '',
        projectCode: '',
        auditStatus: '',
        status: 1
      },
      orderField: 'code',
      order: 'asc',
      uploadVisible: false
    }
  },
  components: {
    AddOrUpdate,
    Upload
  },
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val)
    }
  },
  activated () {
    // 通过路由参数pid, 控制列表请求操作
    this.getTreeDataList()
  },
  methods: {
    refreshList () {
      this.getTreeDataList()
      this.getDataList()
    },
    fileHandle (id, projectId, projectCode, projectName) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      // 组装路由名称, 并判断是否已添加, 如是: 则直接跳转
      var routeName = `$ {this.$route.name}/${id}`
      var route = window.SITE_CONFIG['dynamicRoutes'].filter(item => item.name === routeName)[0]
      if (route) {
        return this.$router.push({ name: routeName, params: { 'projectCode': projectCode, 'projectName': projectName, 'projectId': projectId, 'phase': 2, 'ywTableName': 'BimAuditInfo', 'ywTableId': id } })
      }// 否则: 添加并全局变量保存, 再跳转
      route = {
        path: routeName,
        component: () => import('@/views/modules/basic/businessannexUpload'),
        name: routeName,
        meta: {
          ...window.SITE_CONFIG['contentTabDefault'],
          menuId: this.$route.meta.menuId,
          title: '进行附件上传'
          // 这⾥可根据`⾃定义路由`中提到的window.SITE_CONFIG['contentTabDefault']对象属性进⾏配置
        }
      }
      this.$router.addRoutes([
        {
          ...moduleRoutes,
          name: `main-dynamic ${route.name}`,
          children: [route]
        }
      ])
      window.SITE_CONFIG['dynamicRoutes'].push(route)
      this.$router.push({ name: routeName, params: { 'projectCode': projectCode, 'projectName': projectName, 'projectId': projectId, 'phase': 2, 'ywTableName': 'BimAuditInfo', 'ywTableId': id } })
    },
    handleNodeClick (data) {
      this.dataForm.projectCode = data.projectCode
      this.dataForm.projectName = data.projectName
      this.dataForm.projectId = data.projectId
      this.getDataList()
    },
    // 树节点点击事件
    filterNode (value, data) {
      if (!value) return true
      return data.projectName.indexOf(value) !== -1
    },
    getTreeDataList: function () {
      this.treeLoading = true
      this.$http.get(
        this.mixinViewModuleOptions.getTreeDataURL,
        {
          params: {}
        }
      ).then(({ data: res }) => {
        this.treeLoading = false
        if (res.code !== 0) {
          this.treeData = []
          return
        }
        this.treeData = res.data
        // 默认显示第一节点的数据
        if (this.treeData && this.treeData.length > 0) {
          this.handleNodeClick({ projectCode: res.data[0].projectCode, projectName: res.data[0].projectName, projectId: res.data[0].projectId })
        }
      }).catch(() => {
        this.treeLoading = false
      })
    }
  }
}
</script>
