<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON>qiang
 * @Date: 2020-08-20 11:21:16
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-12-30 18:14:13
-->
<template>
  <el-row :gutter="20" style="margin:15px 20px 0;">
    <el-form label-position="left" ref="dataForm" :rules="dataRule" label-width="140px">
      <el-card class="box-card" shadow='never' v-if="filePer.wts===1">
        <div slot="header" class="clearfix">
          <span class="title">上传委托书</span>
          <el-button size="small" @click="banca" style="float:right;padding:0;" type="text">办理ca</el-button>
        </div>
        <div>
          <el-col :xs="24" :lg="24">
            <el-form-item label="上传方式">
              <el-radio-group v-model="radio">
                <el-radio :label="1">在线ca签章上传</el-radio>
                <el-radio :label="2">本地文件上传（请加盖公章）</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :lg="24" v-if="radio === 1">
            <el-form-item label="委托书CA上传" prop="wts">
              <template>
                <el-button size="small" @click="emitCA(1)" type="primary">委托书ca签章</el-button>
                <span class="el-upload__tip">&emsp;要求格式：pdf</span>
              </template>
              <ul class="el-upload-list el-upload-list--text" v-if="fileList.length>0">
                <li tabindex="0" class="el-upload-list__item is-success">
                  <a class="el-upload-list__item-name" @click="onPreview(fileList[0])"><i class="el-icon-document"></i>委托书
                  </a><label class="el-upload-list__item-status-label"><i class="el-icon-upload-success el-icon-circle-check"></i></label><i
                    @click="beforeRemove({status:'success',name:'委托书'},fileList)" class="el-icon-close"></i><i class="el-icon-close-tip">按
                    delete 键可删除</i>

                </li>
              </ul>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :lg="24" v-if="radio === 2">
            <el-form-item label="委托书上传" prop="wts">
              <template v-if="this.$route.name === 'warrantGuaranteeUpdate'&&sqsId">
                <div>
                  <i class="el-icon-success" style="color:#67C23A;"></i>&nbsp;已上传委托书
                </div>
              </template>
              <template v-else>
                <el-button size="small" @click="getModel(1)" type="text">模板下载</el-button>
                <el-upload class="upload-demo" ref="yyupload" :on-preview='onPreview' :action="uploadUrl" :on-remove='handleRemovewt' :data='upData' :headers="myHeaders" :before-remove="beforeRemove"
                  :on-success='SuccessHandle' :before-upload="beforeUpload" :limit="1" :on-exceed="handleExceed" :file-list="fileList">
                  <el-button size="small" type="primary">点击上传</el-button>
                  <span slot="tip" class="el-upload__tip">&emsp;要求格式：pdf</span>
                </el-upload>
              </template>

            </el-form-item>
          </el-col>
          <idcardUpload :myHeaders='myHeaders' @refresh='getFile' :wthtuploadUrl='wthtuploadUrl' :letterId='letterId' :active='active' :wtrfileList='wtrfileList' :frfileList='frfileList'
            :wtrfileList2='wtrfileList2' :frfileList2='frfileList2' @getFilewtr='getFilewtr' @getFilewfr='getFilewfr' @getFilewtr2='getFilewtr2' @getFilewfr2='getFilewfr2'></idcardUpload>
        </div>
      </el-card>
      <el-card class="box-card" shadow='never' v-if="filePer.wtht===1">
        <div slot="header" class="clearfix">
          <span class="title">上传委托合同</span>
        </div>
        <div>
          <el-col :xs="24" :lg="24">
            <el-form-item label="上传方式">
              <el-radio-group v-model="radioHT">
                <el-radio :label="1">在线ca签章上传</el-radio>
                <el-radio :label="2">本地文件上传（请加盖公章）</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :lg="24" v-if="radioHT === 1">
            <el-form-item label="委托合同CA上传" prop="wtht">
              <template>
                <el-button size="small" @click="emitCA(4)" type="primary">上传委托合同ca签章</el-button>
                <span class="el-upload__tip">&emsp;要求格式：pdf</span>
              </template>
              <ul class="el-upload-list el-upload-list--text" v-if="wtfileList.length>0">
                <li tabindex="0" class="el-upload-list__item is-success">
                  <a class="el-upload-list__item-name" @click="onPreview(wtfileList[0])"><i class="el-icon-document"></i>委托合同
                  </a><label class="el-upload-list__item-status-label"><i class="el-icon-upload-success el-icon-circle-check"></i></label><i
                    @click="beforeRemove({status:'success',name:'委托合同'},wtfileList)" class="el-icon-close"></i><i class="el-icon-close-tip">按
                    delete 键可删除</i>
                </li>
              </ul>
            </el-form-item>

          </el-col>
          <el-col :xs="24" :lg="24" v-if="radioHT === 2">
            <el-form-item label="委托合同上传" prop="wtht">

              <template>
                <el-button size="small" @click="getModel(4)" type="text">模板下载</el-button>
                <el-upload class="upload-demo" ref="yyupload" :on-preview='onPreview' :on-remove='handleRemovewt' :action="wthtuploadUrl" :data='wthtData' :headers="myHeaders"
                  :before-remove="beforeRemove" :on-success='wtSuccessHandle' :before-upload="beforeUpload" :limit="1" :on-exceed="handleExceed" :file-list="wtfileList">
                  <el-button size="small" type="primary">点击上传</el-button>
                  <span slot="tip" class="el-upload__tip">&emsp;要求格式：pdf</span>
                </el-upload>
              </template>
            </el-form-item>
          </el-col>
        </div>
      </el-card>
    </el-form>
    <preview v-if="visible" ref="preview"></preview>
    <caModel v-if="visible" ref="caModel" @reFresh='getFile'></caModel>

  </el-row>
</template>
<script>
import Cookies from 'js-cookie'
import preview from '@/views/project/guarantee/common/detail/preview'
import { banca } from '@/utils/index'
import caModel from '@/views/project/guarantee/components/caModel'
import idcardUpload from '@/views/project/guarantee/warrantGuarantee/form/idcardUpload'
export default {
  data () {
    return {
      radio: 1,
      radioHT: 1,
      visible: false,
      fileList: [],
      wtfileList: [],
      wtrfileList: [],
      frfileList: [],
      wtrfileList2: [],
      frfileList2: [],
      uploadUrl: '',
      upData: {},
      wthtData: { type: '4' },
      myHeaders: {
        token: Cookies.get('token') || ''
      }
    }
  },
  created () {
    this.uploadUrl = window.SITE_CONFIG['apiURL'] + '/letter/bank/wtsFileupload'
    this.wthtuploadUrl =
      window.SITE_CONFIG['apiURL'] + '/letter/guarantee/bhFileupload'
    this.upData = { letterId: this.letterId }
    this.wthtData = { ...this.wthtData, letterId: this.letterId }
  },
  watch: {
    letterId (a) {
      this.upData = { letterId: a }
      this.wthtData = { ...this.wthtData, letterId: a }
      this.wtrData = { ...this.wtrData, letterId: a }
      this.frData = { ...this.frData, letterId: a }
    },
    active (a) {
      if (a === 3) {
        this.getFile()
      }
    }
  },
  components: {
    preview,
    caModel,
    idcardUpload
  },
  props: {
    sqsId: String,
    letterId: String,
    dataForm: Object,
    serPriceId: String,
    active: Number,
    filePer: {
      type: Object
    }
  },
  computed: {
    dataRule () {
      return {
        wts: [
          {
            required: true,
            message: '请上传委托书'
          }
        ],
        wtht: [
          {
            required: true,
            message: '请上传委托合同'
          }
        ],
        wtr: [
          {
            required: true,
            message: '请上传委托人身份证'
          }
        ],
        fr: [
          {
            required: true,
            message: '请上传法人身份证'
          }
        ]
      }
    }
  },
  methods: {
    banca,
    getFile () {
      this.getBhFile(1)
      this.getBhFile(4)
      this.getBhFile(10)
      this.getBhFile(11)
      this.getBhFile(12)
      this.getBhFile(13)
    },
    getFilewtr (list) {
      this.wtrfileList = list
    },
    getFilewfr (list) {
      this.frfileList = list
    },
    getFilewtr2 (list) {
      this.wtrfileList2 = list
    },
    getFilewfr2 (list) {
      this.frfileList2 = list
    },
    async getBhFile (type) {
      let { data } = await this.$http.get(
        `letter/guarantee/getBhFile?letterId=${this.letterId}&type=${type}`
      )
      if (data.data && data.data.id) {
        // this.$emit('businessLicenseId', data.data.id)
        if (type === 1) {
          this.fileList = [
            { name: '委托书', id: data.data.id, url: data.data.url, type: 1 }
          ]
        }
        if (type === 4) {
          this.wtfileList = [
            { name: '委托合同', id: data.data.id, url: data.data.url, type: 4 }
          ]
        }
        if (type === 10) {
          this.wtrfileList = [
            {
              name: '委托人身份证',
              id: data.data.id,
              url: data.data.url,
              type: 10
            }
          ]
        }
        if (type === 11) {
          this.frfileList = [
            {
              name: '法人身份证',
              id: data.data.id,
              url: data.data.url,
              type: 11
            }
          ]
        }
        if (type === 12) {
          this.wtrfileList2 = [
            {
              name: '委托人身份证反面',
              id: data.data.id,
              url: data.data.url,
              type: 12
            }
          ]
        }
        if (type === 13) {
          this.frfileList2 = [
            {
              name: '法人身份证反面',
              id: data.data.id,
              url: data.data.url,
              type: 13
            }
          ]
        }
      } else {
        if (type === 1) {
          this.fileList = []
        }
        if (type === 4) {
          this.wtfileList = []
        }
        if (type === 10) {
          this.wtrfileList = []
        }
        if (type === 11) {
          this.frfileList = []
        }
        if (type === 12) {
          this.wtrfileList2 = []
        }
        if (type === 13) {
          this.frfileList2 = []
        }
      }
    },

    filterType (file) {
      if (file) {
        var extension = file.substring(file.lastIndexOf('.') + 1)
        return extension === 'pdf' || extension === 'PDF'
      }
    },
    emitCA (type) {
      // this.$emit('emitCA', type)
      this.showCaDia('caModel', this.dataForm, type)
    },
    showCaDia (name, data, type) {
      this.visible = true
      console.log(this.$route.params)
      this.$nextTick(() => {
        this.$refs[name].dataForm = {}
        var obj = {
          ...data,
          cjfChargeId: this.serPriceId,
          letterId: this.dataForm.letterId,
          guaranteeType: this.$route.params.type,
          key: this.$route.params.insuranceCode
          // biddingDocumentId: this.biddingDocumentId,
          // businessLicenseId: this.businessLicenseId
        }
        this.$refs[name].dataForm = obj
        this.$refs[name].type = type
        this.$refs[name].init()
      })
    },
    getModel (type) {
      this.$http
        .get(`/letter/guarantee/generateModel`, {
          params: {
            guaranteeDTO: JSON.stringify(this.dataForm),
            type: type
          }
        })
        .then(({ data: res }) => {
          window.open(
            `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${res.data.fileId.id}`
          )
          // this.zongingList = res.data
        })
        .catch(() => {})
    },
    yybeforeUpload (file) {
      let extension = file.name.substring(file.name.lastIndexOf('.') + 1)
      var lastName =
        extension !== 'jpg' &&
        extension !== 'JPG' &&
        extension !== 'jpeg' &&
        extension !== 'JPEG' &&
        extension !== 'png' &&
        extension !== 'PNG'
      if (lastName) {
        this.$message.warning('文件要求格式为：jpg,jpeg,png')
      }
      console.log(lastName)
      return !lastName
    },
    SuccessHandle (res, file, fileList) {
      if (res.data.flag !== true) {
        this.fileList = []
        return this.$message.error(res.data.msg)
      }
      this.$message.success('上传成功')
      this.fileList = [{ ...res.data, type: 1, name: '委托书' }]
      console.log(this.fileList)
    },
    beforeRemove (file, fileList) {
      console.log(file, fileList)
      let a = true
      if (file && file.status === 'success') {
        console.log(file)
        a = this.$confirm(`确定移除 ${file.name}？`)
          .then(() => {
            this.handleRemovewt(file, fileList)
          })
          .catch(() => {})
      }
      return a
    },
    beforeUpload (file) {
      var extension = file.name.substring(file.name.lastIndexOf('.') + 1)
      var lastName = extension !== 'pdf' && extension !== 'PDF'

      if (lastName) {
        this.$message.warning('文件要求格式为：.pdf')
      }

      return !lastName
    },
    onPreview (file) {
      console.log(file)
      if (file.ossId) {
        window.open(
          `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${file.ossId}`
        )
      } else {
        window.open(
          `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${file.id}`
        )
      }
    },
    handleExceed (files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件`
      )
    },
    wtSuccessHandle (res, file, fileList) {
      if (res.data.flag !== true) {
        this.wtfileList = []
        return this.$message.error(res.data.msg)
      }
      this.wtfileList = [{ ...res.data, type: 4, name: '委托合同' }]
      this.$message.success('上传成功')
    },
    wtbeforeUpload (file) {
      var extension = file.name.substring(file.name.lastIndexOf('.') + 1)
      var lastName = extension !== 'pdf' && extension !== 'PDF'
      if (lastName) {
        this.$message.warning('文件要求格式为：jpg,jpeg,png,pdf')
      }
      return !lastName
    },
    handleRemovewt (file, fileList) {
      console.log(fileList)
      if (file && file.status === 'success') {
        // 移除方法
        // var deleteId = file.id ? file.id : file.response.data.ossId
        return fileList.length > 0
          ? this.deleteFilewt(
            this.letterId,
            fileList[0].type
              ? fileList[0].type
              : fileList[0].response.data.type
          )
          : ''
      }
    },
    deleteFilewt (id, type) {
      return new Promise(async (resolve, reject) => {
        let { data } = await this.$http.get(
          'letter/guarantee/deleteBhFile?letterId=' + id + '&type=' + type
        )
        if (data.code !== 0) {
          this.$message.error(data.msg)
          resolve(false)
        }
        this.getFile()
        // type === 1 ? this.$emit('biddingDocumentId', '') : this.$emit('businessLicenseId', '')
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
        resolve(true)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.el-upload__tip {
  color: #f56c6c;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
</style>
