<!--
 * @Descripttion:
 * @Author: kong<PERSON><PERSON>ang
 * @Date: 2020-08-06 18:47:13
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-08-24 10:41:21
-->
<template>
  <el-dialog :visible.sync="visible" title="提示" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <div>
      <iframe :src="src+'ca.html'" style="width:100%;height:500px;"></iframe>
    </div>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="SelectStp('公章')">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>

export default {

  data () {
    return {
      visible: false,
      webPDFObj: '',
      businessLicenseId: '',
      biddingDocumentId: '',
      src: ''
    }
  },
  mounted () {
    this.src = process.env.BASE_URL
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {

      })
    }

  }
}
</script>
