<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON>qiang
 * @Date: 2021-03-31 15:09:58
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-05-10 09:58:16
-->
<template>
  <el-dialog title="选择出具机构" :close-on-click-modal="false" append-to-body :close-on-press-escape="false" :visible.sync="visible" width="800px">
    <div slot="title" >
      <span class="el-dialog__title">选择出具机构</span>&emsp;
      <span style="color:#909399;">{{title}}</span>
    </div>
    <div v-loading='loading'>
      <!-- {{this.cjList[this.active]}} -->
      <h3 class="temp-subtitle">担保公司</h3>
      <div class="guaranteeList">
        <div class="guaranteeList-item" :class="active==index?'active':''" v-for="(item,index) in cjList" @click="guaranteeClick(index)" :key="index">
          <span class="name">{{item.name}}</span>
          <i class="choice" v-if="active==index"><img src="@/assets/img/choice.png" alt=""></i>
        </div>
      </div>
      <h3 class="temp-subtitle">定价信息</h3>
      <div v-if="cjList.length>0">
        <!-- {{cjList[active].pricelist}} -->
        <el-table :data="cjList[active].pricelist" :show-header="false" stripe style="width: 100%">
          <el-table-column prop="name" label="日期">
          </el-table-column>
          <el-table-column prop="letterPrice" label="姓名">
            <template slot-scope="scope">
              <span v-if="scope.row.priceType == '1'">{{scope.row.letterPrice}}元</span>
              <span v-if="scope.row.priceType == '2'">千分之{{scope.row.percentValue*1000}}</span>
            </template>
          </el-table-column>

        </el-table>
      </div>
    </div>
      <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="submit()">申请</el-button>
    </template>
  </el-dialog>
</template>
<script>
export default {
  data () {
    return {
      visible: false,
      loading: false,
      cjList: [],
      active: 0,
      code: '',
      projectId: '',
      title: ''
    }
  },
  methods: {
    init () {
      // this.visible = true
    },
    guaranteeClick (idx) {
      this.active = idx
    },
    async submit () {
      let { data } = await this.$http.get(
        `letter/tbBlacklist/findByCertCodeAndName?issueCode=${this.cjList[this.active].code}`
      )
      if (data.data) {
        this.$emit('isSure', this.code, this.cjList[this.active], this.projectId)
      } else {
        this.$message.error('当前出具机构无法出具，请选择其他出具机构出具！')
      }
    },
    async goC (code, projectId, regionCode) {
      this.loading = true
      var { data } = await this.$http.get(
        `/letter/bgguaranteeissue/getIssue?guaranteeTypecode=${code}&pCode=1&projectId=${
          projectId || ''
        }&regionCode=${regionCode}`
      )
      this.loading = false
      this.code = code
      this.projectId = projectId
      if (data.data[0]) {
        this.cjList = data.data
        if (this.cjList.length === 1) {
          this.submit()
        } else {
          this.visible = true
        }
      } else {
        this.$message.error('暂无出具机构！')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.temp-subtitle {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  margin: 0;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.guaranteeList {
  display: flex;
  flex-wrap: wrap;
  // margin: 20px auto;
  .guaranteeList-item {
    width: 200px;
    box-shadow: 0 0 30px #e7e7e7;
    padding: 10px 15px 15px;
    margin: 20px;
    text-align: left;
    transition: all 0.3s ease-in-out;
    border: 1px solid transparent;
    cursor: pointer;
    position: relative;
    line-height: 22px;
    span {
      display: block;
      margin-top: 12px;
    }
    .icon {
      transition: all 0.3s ease-in-out;
      display: inline-block;
      width: 40px;
      height: 40px;
      line-height: 40px;
      img {
        transition: all 0.3s ease-in-out;
        width: 40px;
      }
    }
  }
  .guaranteeList-item:hover {
    transform: scale(1.1);
    border: 1px solid #f18241;
    .icon {
      background-color: rgba(241, 130, 65, 0.1);
      transform: scale(1.4);
      border-radius: 50%;
      img {
        width: 25px;
      }
    }
  }
  .active {
    transform: scale(1.1);
    border: 1px solid rgb(241, 130, 65);
    color: #f18241;
    .icon {
      background-color: rgba(241, 130, 65, 0.1);
      transform: scale(1.4);
      border-radius: 50%;
      img {
        width: 25px;
      }
    }
  }
  .choice {
    position: absolute;
    top: -5px;
    right: -2px;
    img {
      width: 18px;
    }
  }
}
</style>
