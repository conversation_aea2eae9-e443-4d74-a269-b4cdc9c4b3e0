<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"  :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="colConfig">
           <el-form-item label="机构编码" prop="code">
            <el-input v-model="dataForm.code" placeholder="机构编码"></el-input>
          </el-form-item>
          <el-form-item label="机构名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="机构名称"></el-input>
          </el-form-item>
          <el-form-item label="法定代表人" prop="corporation">
            <el-input v-model="dataForm.corporation" placeholder="法定代表人"></el-input>
          </el-form-item>
          <el-form-item label="机构类型" prop="orgType">
            <el-select v-model="dataForm.orgType" clearable placeholder="请选择机构类型">
              <el-option v-for="item in orgTypeOptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="机构联系电话" prop="phoneNumber">
            <el-input v-model="dataForm.phoneNumber" placeholder="机构联系电话"></el-input>
          </el-form-item>
          <el-form-item label="担保金额限制" prop="guaranteeLimitAmount">
            <el-input v-model="dataForm.guaranteeLimitAmount" placeholder="担保金额限制"></el-input>
          </el-form-item>
          <el-form-item label="机构网站" prop="website">
            <el-input v-model="dataForm.website" placeholder="机构网站"></el-input>
          </el-form-item>
          <!-- <el-form-item label="机构类型 1 保险 2银行 3 担保机构" prop="orgType">
     <el-input v-model="dataForm.orgType" placeholder="机构类型 1 保险 2银行 3 担保机构"></el-input>
</el-form-item> -->

          <el-form-item label="邮箱" prop="email">
            <el-input v-model="dataForm.email" placeholder="邮箱"></el-input>
          </el-form-item>

          <el-form-item label="传真" prop="fax">
            <el-input v-model="dataForm.fax" placeholder="传真"></el-input>
          </el-form-item>
          <el-form-item label="LOGO" prop="fileList">
            <el-input v-model="dataForm.logoUrl" placeholder="LOGO" readonly="readonly"> </el-input>
            <el-upload class="upload-demo" :action="url" :on-preview="handlePreview" :on-remove="handleRemove" :file-list="fileList" :on-success="successHandle" :limit='1' list-type="picture"
              :before-upload="beforeAvatarUpload">
              <el-button size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过2mb</div>
            </el-upload>
            <!-- <img src="http://************:9099/hzjt-admin/sys/oss/displayFile/1176059617161719809-e034a83707eb4ce68d6fa8c10c576f1a.jpg" alt=""> -->
          </el-form-item>
          <el-form-item label="邮编" prop="postcode">
            <el-input v-model="dataForm.postcode" placeholder="邮编"></el-input>
          </el-form-item>

        </el-col>
        <el-col :xs="24" :lg="colConfig">
          <el-form-item label="地址" prop="address">
            <el-input type="textarea" rows="3" v-model="dataForm.address" placeholder="地址"></el-input>
          </el-form-item>
          <el-form-item label="联系人" prop="linkman">
            <el-input v-model="dataForm.linkman" placeholder="联系人"></el-input>
          </el-form-item>
          <el-form-item label="联系人电话" prop="linkmanTel">
            <el-input v-model="dataForm.linkmanTel" placeholder="联系人电话"></el-input>
          </el-form-item>
          <el-form-item label="是否收取平台使用费" prop="isIncludePlatformFee">
            <!-- <el-input v-model="dataForm.isIncludePlatformFee" placeholder="是否收取平台使用费"></el-input> -->
            <el-radio-group v-model="dataForm.isIncludePlatformFee" placeholder="是否收取平台使用费">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="保函审核时长" prop="auditingHour">
            <el-input v-model="dataForm.auditingHour" placeholder="保函审核时长"></el-input>
          </el-form-item>
          <!-- <el-form-item label="可开具保函编码" prop="guaranteeTypecodes">
     <el-input v-model="dataForm.guaranteeTypecodes" placeholder="可开具保函编码"></el-input>
</el-form-item>
                <el-form-item label="可开具保函" prop="guaranteeTypenames">
     <el-input v-model="dataForm.guaranteeTypenames" placeholder="可开具保函"></el-input>
</el-form-item> -->
          <el-form-item label="开户行名称" prop="bankAccountName">
            <el-input v-model="dataForm.bankAccountName" placeholder="开户行名称"></el-input>
          </el-form-item>
          <el-form-item label="银行账户号码" prop="bankAccountNo">
            <el-input v-model="dataForm.bankAccountNo" placeholder="银行账户号码"></el-input>
          </el-form-item>
          <el-form-item label="银行名称" prop="bankName">
            <el-input v-model="dataForm.bankName" placeholder="银行名称"></el-input>
          </el-form-item>
          <el-form-item label="银行编码" prop="bankNo">
            <el-input v-model="dataForm.bankNo" placeholder="银行编码"></el-input>
          </el-form-item>
          <el-form-item label="排序" prop="orders">
            <el-input v-model="dataForm.orders" placeholder="排序"></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="dataForm.status" clearable placeholder="状态">
              <el-option v-for="item in statusoptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="权重" prop="bhsum" size="mini">
            <el-input v-model="dataForm.bhsum" placeholder="权重"></el-input>
          </el-form-item>

        </el-col>
      </el-row>
      <el-form-item label="可开具保函" prop="guaranteeTypenames">
        <!-- <el-input v-model="dataForm.guaranteeTypenames" placeholder="可开具保函"></el-input> -->
        <el-checkbox-group @change="change()" v-model="dataForm.guaranteeTypenamesArr">
          <el-checkbox v-for="item in guaranteeTypenamesOptions" :key="item.dictCode" :label="item.dictCode" :value="item.dictCode">
            {{item.dictName}}
          </el-checkbox>
        </el-checkbox-group>
        <!-- {{dataForm.guaranteeTypenamesArr}} -->
      </el-form-item>
      <el-form-item prop="description" label="机构描述">
        <!-- 富文本编辑器, 容器 -->
        <div id="J_quillEditor"></div>
        <!-- 自定义上传图片功能 (使用element upload组件) -->
      </el-form-item>
      <el-form-item label="机构描述" hidden prop="description">
        <el-input v-model="dataForm.description" placeholder="机构描述"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import 'quill/dist/quill.snow.css'
import Quill from 'quill'
export default {
  data () {
    return {
      quillEditor: null,
      quillEditorToolbarOptions: [
        ['bold', 'italic', 'underline', 'strike'],
        [{ header: 1 }, { header: 2 }],
        [{ list: 'ordered' }, { list: 'bullet' }],
        [{ script: 'sub' }, { script: 'super' }],
        [{ indent: '-1' }, { indent: '+1' }],
        [{ direction: 'rtl' }],
        [{ size: ['small', false, 'large', 'huge'] }],
        [{ header: [1, 2, 3, 4, 5, 6, false] }],
        [{ color: [] }, { background: [] }],
        [{ font: [] }],
        [{ align: [] }],
        ['clean']
      ],
      statusoptions: [
        {
          value: 0,
          label: '未提交'
        },
        {
          value: 1,
          label: '待审核'
        },
        {
          value: 2,
          label: '已认证'
        }
      ],
      url: '',
      fileList: [],
      colConfig: 2, // 列配置 1 2列
      visible: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        corporation: '',
        website: '',
        orgType: '',
        email: '',
        phoneNumber: '',
        fax: '',
        logoUrl: '',
        postcode: '',
        address: '',
        linkman: '',
        linkmanTel: '',
        guaranteeLimitAmount: '',
        description: '',
        auditingHour: '',
        guaranteeTypecodes: '',
        guaranteeTypenames: '',
        bankAccountName: '',
        bankAccountNo: '',
        bankName: '',
        bankNo: '',
        isIncludePlatformFee: '',
        orders: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        bhsum: '',
        guaranteeTypenamesArr: []
      },
      orgTypeOptions: '',
      orgTypemaps: '',
      getDicListURL: '/sys/dict/type/',
      guaranteeTypenamesOptions: []
    }
  },
  computed: {
    dataRule () {
      return {
        guaranteeLimitAmount: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        code: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        address: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        corporation: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        orgType: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        phoneNumber: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        // fax: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        // logoUrl: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        // postcode: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        // address: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        linkman: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        description: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        guaranteeTypecodes: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        guaranteeTypenames: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        isIncludePlatformFee: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.getOrgTypeInfo()
      this.fileList = []
      this.url = `${
        window.SITE_CONFIG['apiURL']
      }/sys/oss/fileupload?projectCode=issue&projectName=机构出具方信息&projectId=0&phase=0&ywTableName=bg_guarantee_issue`
      this.visible = true
      this.$nextTick(() => {
        if (this.quillEditor) {
          this.quillEditor.deleteText(0, this.quillEditor.getLength())
        } else {
          this.quillEditorHandle()
        }
        this.$refs['dataForm'].resetFields()
        this.guaranteeTypeInfo()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http
        .get(`/letter/bgguaranteeissue/${this.dataForm.id}`)
        .then(({ data: res }) => {
          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
          this.quillEditor.root.innerHTML = this.dataForm.description
          this.dataForm.guaranteeTypenamesArr = []
          if (this.dataForm.logoUrl != null) {
            this.fileList = []
            this.fileList.push({
              name: this.dataForm.logoUrl,
              url:
                window.SITE_CONFIG['apiURL'] +
                '/sys/oss/localhostDownload/' +
                this.dataForm.logoUrl,
              id: this.dataForm.id
            })
          }
          this.guaranteeTypenamesOptions = Array.from(
            this.guaranteeTypenamesOptions
          )
          this.dataForm.guaranteeTypecodes.split(',').map(item => {
            this.dataForm.guaranteeTypenamesArr.push(item)
          })
          // this.guaranteeTypeInfo()
          this.url = `${
            window.SITE_CONFIG['apiURL']
          }/sys/oss/fileupload?projectCode=issue&projectName=机构出具方信息&projectId=0&phase=0&ywTableName=bg_guarantee_issue`
        })
        .catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        let code = []
        let name = []
        this.dataForm.guaranteeTypenamesArr.map(item => {
          this.guaranteeTypenamesOptions.map(citem => {
            if (item === citem.dictCode) {
              name.push(citem.dictName)
            }
          })
        })
        code = this.dataForm.guaranteeTypenamesArr.join(',')
        name = name.join(',')
        this.dataForm.guaranteeTypecodes = code
        this.dataForm.guaranteeTypenames = name
        // eslint-disable-next-line no-unreachable
        this.$refs['dataForm'].validate(valid => {
          if (!valid) {
            return false
          }
          this.$http[!this.dataForm.id ? 'post' : 'put'](
            '/letter/bgguaranteeissue/',
            this.dataForm
          )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            })
            .catch(() => {})
        })
      },
      1000,
      { leading: true, trailing: false }
    ),
    handlePreview (file) {
      window.open(file.url)
    },
    handleRemove (file, fileList) {
      this.$http.get(`/sys/oss/delete/` + file.name).then(({ data: res }) => {
        this.fileList = []
        this.dataForm.logoUrl = ''
        this.$message.success('删除成功')
      })
    },
    successHandle (response, file, fileList) {
      if (response.code !== 0) {
        this.fileList = []
        return this.$message.error(response.msg)
      } else {
        this.fileList = fileList
        this.dataForm.logoUrl = response.data.id
        this.$message.success('上传成功！')
      }
    },
    beforeAvatarUpload (file) {
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG && !isPNG) {
        this.$message.error('上传头像图片只能是 JPG / PNG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!')
      }
      return (isJPG || isPNG) && isLt2M
    },
    // 富文本编辑器
    quillEditorHandle () {
      this.quillEditor = new Quill('#J_quillEditor', {
        modules: {
          toolbar: this.quillEditorToolbarOptions
        },
        theme: 'snow'
      })
      // 监听内容变化，动态赋值
      this.quillEditor.on('text-change', () => {
        this.dataForm.description = this.quillEditor.root.innerHTML
      })
    },
    // 获取机构类型
    getOrgTypeInfo () {
      this.$http
        .get(this.getDicListURL + 'orgType')
        .then(({ data: res }) => {
          this.orgTypeOptions = {
            ...this.orgTypeOptions,
            ...res.data.list
          }
          this.orgTypemaps = {
            ...this.orgTypemaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    },
    // 获取保函类型
    guaranteeTypeInfo () {
      this.guaranteeTypenamesOptions = []
      this.dataForm.guaranteeTypenamesArr = []
      this.$http
        .get(this.getDicListURL + 'guaranteeType')
        .then(({ data: res }) => {
          this.guaranteeTypenamesOptions = [
            ...this.guaranteeTypenamesOptions,
            ...res.data.list
          ]
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.el-card {
  margin-bottom: 53px;
}
.temp-content {
  // padding: 24px;
  .titlename {
    width: 100%;
    height: 60px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    font-weight: bold;
    border-bottom: 1px solid #e6e6e6;
    font-size: 20px;
    p {
      margin: 10px 0;
    }
  }
  .temp-line {
    padding-bottom: 32px;
  }
  .temp-line:after {
    position: absolute;
    right: 0;
    bottom: 0;
    content: '';
    display: block;
    clear: both;
    width: 100%;
    min-width: 100%;
    height: 1px;
    margin: 8px 0 24px;
    background: #e8e8e8;
  }
  .temp-form {
    position: relative;
    .temp-subtitle {
      font-size: 16px;
      color: #333;
      line-height: 29px;
      margin-bottom: 16px;
      font-weight: bold;
    }
    .el-date-editor.el-input {
      width: auto !important;
    }
    .el-date-editor {
      max-width: 280px;
    }
    .el-input {
      max-width: 280px;
    }
    .el-date-editor--daterange {
      max-width: 240px;
    }
  }
}
.foot {
  width: 100%;
  background: #fff;
  position: fixed;
  bottom: 0;
  padding: 10px 0;
  border-top: 1px solid #ddd;
  background: #f9f9f9;
  text-align: center;
  left: 80px;
  z-index: 999;
  span {
    text-align: center;
  }
}
.btn {
  .el-input-group__append {
    color: #fff;
    background-color: #409eff;
    border-color: #409eff;
    padding: 0 8px;
  }
}
.el-loading-mask {
  z-index: 998 !important;
}
</style>
<style lang="scss" scoped>
.el-card {
  margin-bottom: 53px;
}
.temp-content {
  // padding: 24px;
  .titlename {
    width: 100%;
    height: 60px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    font-weight: bold;
    border-bottom: 1px solid #e6e6e6;
    font-size: 20px;
    p {
      margin: 10px 0;
    }
  }
  .temp-line {
    padding-bottom: 32px;
  }
  .temp-line:after {
    position: absolute;
    right: 0;
    bottom: 0;
    content: '';
    display: block;
    clear: both;
    width: 100%;
    min-width: 100%;
    height: 1px;
    margin: 8px 0 24px;
    background: #e8e8e8;
  }
  .temp-form {
    position: relative;
    .temp-subtitle {
      font-size: 16px;
      color: #333;
      line-height: 29px;
      margin-bottom: 16px;
      font-weight: bold;
    }
    .el-date-editor.el-input {
      width: auto !important;
    }
    .el-date-editor {
      max-width: 280px;
    }
    .el-input {
      max-width: 280px;
    }
    .el-date-editor--daterange {
      max-width: 240px;
    }
  }
}
.foot {
  width: 100%;
  background: #fff;
  position: fixed;
  bottom: 0;
  padding: 10px 0;
  border-top: 1px solid #ddd;
  background: #f9f9f9;
  text-align: center;
  left: 80px;
  z-index: 999;
  span {
    text-align: center;
  }
}
.btn {
  .el-input-group__append {
    color: #fff;
    background-color: #409eff;
    border-color: #409eff;
    padding: 0 8px;
  }
}
.el-loading-mask {
  z-index: 998 !important;
}
</style>
