<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON>qiang
 * @Date: 2020-10-13 09:38:40
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-08-20 16:01:17
-->
<template>
  <el-col :span="20">
    <el-col :span="24" class="con-title">
      支付信息
    </el-col>
    <el-col :xs="24" :lg="12">
      <el-form-item label="打款账号" prop="bankAccount">
        {{dataForm.bankAccount}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="12">
      <el-form-item label="担保费金额" prop="moneyAmount">
        {{dataForm.moneyAmount}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="12">
      <el-form-item label="打款日期" prop="payTime">
        {{dataForm.payTime}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="12">
      <el-form-item label="联系人姓名" prop="contacts">
        {{dataForm.contacts}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="12">
      <el-form-item label="联系人电话" prop="mobile">
        {{dataForm.mobile}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="12">
      <el-form-item label="支付凭证" prop="mobile">
        <!-- {{img}} -->
        <el-button type="text" @click="miner(dataForm.ossId)">预览</el-button>
        <el-button type="text" @click="download(dataForm.ossId)">下载</el-button>
      </el-form-item>
    </el-col>
    <!-- <el-dialog class="preview CAmodel" title="预览" :visible.sync="imgVisible"  :close-on-click-modal="false">
      <div slot='title'>
        <h3>预览</h3>
      </div>
      <div v-loading='loading'><img style="width:100%;" :src="img" alt=""></div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="imgVisible = false">关 闭</el-button>
      </span>
    </el-dialog> -->
    <imgPreview v-if="imgVisible"  ref="imgPreview"></imgPreview>
  </el-col>

</template>
<script>
import imgPreview from '@/views/project/guarantee/components/imgPreview'
export default {
  props: {
    dataForm: Object
  },
  data () {
    return {
      imgVisible: false,
      img: '',
      loading: false
    }
  },
  components: {
    imgPreview
  },
  watch: {
    dataForm: {
      handler (a) {
        this.getImgUrl()
      },
      deep: true
    }
  },
  methods: {
    getImgUrl () {
      this.$http
        .get('letter/bgletterlitigation/getPayFileUrl?ossId=' + this.dataForm.ossId).then(({ data: res }) => {
          this.img = res.data
        })
    },
    miner (id) {
      this.imgVisible = true
      this.loading = true
      this.$nextTick(() => {
        this.$refs.imgPreview.init()
        this.$refs.imgPreview.img = this.img
      })
      // sys/oss/minioPreview?id=123
      setTimeout(() => {
        this.loading = false
      }, 300)
    },
    download (id, type) {
      window.open(
        this.img
      )
    }
  }
}
</script>
<style lang="scss" scoped>
.con-title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-bottom: 10px;
}
</style>
