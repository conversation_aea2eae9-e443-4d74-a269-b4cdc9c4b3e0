<template>
  <div class="card" ref="inCcard" v-loading='loading' element-loading-text="准备表单中..">
    <!-- {{$route.query}} -->
    <el-card class="box-card" shadow='never'>
      <steps :active='2'></steps>
       <!-- <button @click="goAwit()">111</button> -->
      <!-- <button @click="newWin('www.baidu.com')">111</button>
      <a href="www.baidu.com" target="_blank">11</a> -->
    </el-card>
    <!-- <el-card class="box-card" shadow="never"> -->
    <!-- {{dataForm}} -->
    <div style="padding-bottom:60px;padding-right:120px;" id="center">
      <bgGuaranteeApply class="anchor" data-name='投保人信息' v-if="dataForm.bgGuaranteeApply" :dataForm='dataForm.bgGuaranteeApply' :options='options' id="bgGuaranteeApply" ref="bgGuaranteeApply"></bgGuaranteeApply>
      <bgbiddingproject @clearProAndBBr='clearProAndBBr' class="anchor"  @getBbrId='getBbrId' data-name='项目信息' @setGuaranteeAmount='setGuaranteeAmount' v-if="dataForm.bgbiddingproject&&this.$route.params.insuranceCode!=='ddbx'" @gettbDate='gettbDate'
        :dataForm='dataForm.bgbiddingproject' :options='options' ref="bgbiddingproject"></bgbiddingproject>
      <bgbiddingprojectDd @clearProAndBBr='clearProAndBBr' class="anchor" @getBbrId='getBbrId' data-name='项目信息' v-else @setGuaranteeAmount='setGuaranteeAmount' :dataForm='dataForm.bgbiddingproject' :options='options' @gettbDate='gettbDate' ref="bgbiddingprojectDd"></bgbiddingprojectDd>
      <bginsurancebbr class="anchor" :bbrId='bbrId' data-name='被保人' v-if="dataForm.bginsurancebbr" :dataForm='dataForm.bginsurancebbr' :bgGuaranteeApply='dataForm.bgGuaranteeApply' :options='options' ref="bginsurancebbr" @show='showSyr'>
      </bginsurancebbr>
      <bginsurancesyr class="anchor" data-name='受益人' v-if="dataForm.bginsurancesyr&&showSyrCom" :dataForm='dataForm.bginsurancesyr' :options='options' ref="bginsurancesyr"></bginsurancesyr>
      <bginsuranceinfo data-name='保函信息' class="anchor" :tbDate='tbDate' :bidOpenDate='dataForm.bgbiddingproject.bidOpenDate' :tenderValid='dataForm.bgbiddingproject.tenderValid' v-if="dataForm.bginsuranceinfo"
        :dataForm='dataForm.bginsuranceinfo' :options='options' ref="bginsuranceinfo"></bginsuranceinfo>
      <bgguaranteeinvoice class="anchor" data-name='发票信息' v-if="dataForm.bgguaranteeinvoice&&this.$route.params.insuranceCode!=='ddbx'" :dataForm='dataForm.bgguaranteeinvoice' :options='options' ref="bgguaranteeinvoice"></bgguaranteeinvoice>
      <bgguaranteeinvoiceDD class="anchor" data-name='发票信息' v-if="dataForm.bgguaranteeinvoice&&this.$route.params.insuranceCode==='ddbx'" :dataForm='dataForm.bgguaranteeinvoice' :options='options' ref="bgguaranteeinvoice"></bgguaranteeinvoiceDD>
      <bgGuaranteeMailing class="anchor" data-name='收件信息' v-if="dataForm.bgGuaranteeMailing&&dataForm.bgguaranteeinvoice.invoiceType==='2'&&this.$route.params.insuranceCode!=='ddbx'" :dataForm='dataForm.bgGuaranteeMailing' :options='options' ref="bgGuaranteeMailing">
      </bgGuaranteeMailing>
    </div>
    <!-- </el-card> -->
    <anchor :isBottom='isBottom' ref="anchor"></anchor>
    <div class="foot">
      <span style="margin-bottom:10px;margin-right:20px;">
        <el-checkbox v-model="checked">我已阅读并同意 <el-button type="text" @click="showDia('xieyi')">服务协议</el-button>
        </el-checkbox>
      </span>
      <span>
        <el-button type="primary" :disabled="!checked" @click="backStep()" plain>上一步</el-button>
        <el-button type="primary" :disabled="!checked" :loading="nextLoading" @click="dataFormSubmitHandle(0,dataForm)">保存</el-button>
        <el-button type="primary" :disabled="!checked" :loading="nextLoading" @click="dataFormSubmitHandle(1,dataForm)" v-if="letterId !== ''">下一步</el-button>
        <el-button type="primary" :disabled="!checked" :loading="nextLoading" @click="dataFormSubmitHandle(1,dataForm)" v-if="wtsStatus == 1 && letterId !== ''">提交</el-button>
      </span>
    </div>

    <xieyi v-if="visible" ref="xieyi"></xieyi>
    <tip v-if="visible" ref="tip"></tip>
  </div>
</template>
<script>
import dictionaries from '@/views/project/guarantee/components/dictionaries'
import steps from '@/views/project/guarantee/components/step'
import debounce from 'lodash/debounce'
import bgGuaranteeApply from '@/views/project/guarantee/tenderGuarantee/form/bgGuaranteeApply' // 投保人信息（投保方）
import bginsurancebbr from '@/views/project/guarantee/tenderGuarantee/form/bginsurancebbr' // 被保人（招标方）
import bginsurancesyr from '@/views/project/guarantee/tenderGuarantee/form/bginsurancesyr' // 受益人信息
import bgbiddingproject from './form/bgbiddingproject' // 项目信息
import bgbiddingprojectDd from '@/views/project/guarantee/tenderGuarantee/form/bgbiddingprojectDd' // 项目信息
import bginsuranceinfo from '@/views/project/guarantee/tenderGuarantee/form/bginsuranceinfo' // 保函信息
import bgguaranteeinvoice from '@/views/project/guarantee/tenderGuarantee/form/bgguaranteeinvoice' // 发票信息
import bgguaranteeinvoiceDD from '@/views/project/guarantee/tenderGuarantee/form/bgguaranteeinvoiceDD' // 发票信息（大地）
import bgGuaranteeMailing from '@/views/project/guarantee/tenderGuarantee/form/mailing' // 收件信息
import anchor from '@/views/project/guarantee/components/Anchor'
import xieyi from '@/views/project/guarantee/components/xieyi'
import tip from '@/views/project/guarantee/components/tip'
import { newWin } from '@/utils'
export default {
  mixins: [dictionaries],
  data () {
    return {
      retJson: [],
      showSyrCom: false,
      checked: false,
      visible: false,
      nextLoading: false,
      loading: false,
      formArr: [],
      insuranceInfo: {},
      // eslint-disable-next-line standard/array-bracket-even-spacing
      formReset: [
        'bgGuaranteeApply',
        'bginsurancebbr',
        'bginsurancesyr',
        'bgbiddingproject',
        'bgbiddingprojectDd',
        'bginsuranceinfo',
        'bgguaranteeinvoice',
        'bgGuaranteeMailing'
      ],
      insuranfceIno: {},
      dataForm: {},
      tbDate: '',
      payAmount: '', // 金额
      projectId: '', // 项目id
      serPriceId: '', // 服务定价id
      letterId: '', // 保函id
      guaranteeType: '', // 保函类型
      wtsStatus: 0, // 委托书状态 0：需要 1：不需要
      bbrId: '',
      isBottom: false
    }
  },
  components: {
    steps,
    bgGuaranteeApply,
    bginsurancebbr,
    bginsurancesyr,
    bgbiddingproject,
    bginsuranceinfo,
    bgguaranteeinvoice,
    bgguaranteeinvoiceDD,
    bgbiddingprojectDd,
    bgGuaranteeMailing,
    // eslint-disable-next-line vue/no-unused-components
    anchor,
    xieyi,
    tip
  },
  created () {

  },
  mounted () {
    this.$nextTick(function () {
      window.addEventListener('scroll', this.linstener)
    })
  },
  activated () {
    this.insuranfceIno.name = this.$route.params.insuranceName
    this.insuranfceIno.code = this.$route.params.insuranceCode
    if (this.$route.params.id) {
      this.getDetail()
    } else {
      this.getInfo()
    }
  },
  methods: {
    getBbrId (val) {
      console.log(val)
      if (val) {
        this.$http
          .get(`/letter/bginsurancebbr/${val}`)
          .then(({ data: res }) => {
            if (res.code !== 0) {
              this.setObj()
              return
            }
            if (!res.data.certType) {
              res.data.certType = '2'
            }
            // console.log(res)
            this.dataForm.bginsurancebbr = res.data
          })
      } else {
        this.dataForm.bginsurancebbr = {
          certType: '2',
          certTermType: '0'
        }
      }
    },
    clearProAndBBr (val) {
      if (this.dataForm.bginsurancebbr.id) {
        this.dataForm.bgbiddingproject = {
          regionId: val,
          platformCode: 'zbt'
        }
        this.dataForm.bginsurancebbr = {
          certType: '2',
          certTermType: '0'
        }
      }
    },
    linstener () {
      this.$nextTick(() => {
        let dom = document.documentElement
        this.isBottom = dom.scrollHeight - dom.scrollTop === dom.clientHeight
      })
    },
    gettbDate (val) {
      console.log(val)
      this.tbDate = val
    },
    setGuaranteeAmount (val) {
      this.$nextTick(() => {
        this.$refs.bginsuranceinfo.guaranteeAmount = val / 10000
        this.$set(this.$refs.bginsuranceinfo.dataForm, 'guaranteeAmount', val)
      })
    },
    showDia (name) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs[name].init()
        this.$refs[name].letterId = this.letterId
      })
    },
    backStep () {
      this.$router.push({
        name: this.$route.name === 'applyBgGuaranteeApplyFromFLogin' ? 'project-guarantee-common-chooiseIn' : 'letter-bgguaranteeletterSqf'
      })
    },
    // 获取支付信息
    getPayMentInfo (letterId) {
      // 获取信息
      return new Promise((resolve, reject) => {
        this.$http
          .get(
            `/letter/bgguaranteeletter/getPayUrl/` +
              letterId +
              `?key=${this.$route.params.insuranceCode}`
          )
          .then(({ data: res }) => {
            this.updateLoading = false

            resolve(res.data)
          })
          .catch((rej) => {
            reject(rej.msg)
          })
      })
    },
    // 支付
    async pay (letterId) {
      this.$loading({
        lock: true,
        text: `请求支付中`
      })
      var data = await this.getPayMentInfo(letterId)
      var payInfo = data
      this.$loading().close()
      // var href = payInfo.payUrl + '?requestDoc={"serialNo":"' + payInfo.serialNo + '","requestHead":{"timestamp":"' + payInfo.timestamp + '","sign":"' + payInfo.sign + '","tradeDate":"' + this.moment(new Date()).format('YYYY-MM-DD HH:mm:ss') + '","tradeNo":"' + payInfo.tradeNo + '","nonce":"' + payInfo.nonce + '","cooperation":"' + payInfo.cooperation + '"},"payType":"' + payInfo.payType + '"}'
      // console.log(href)
      newWin(encodeURI(payInfo))
      // window.open(encodeURI(payInfo), '_blank')
    },
    showSyr (val) {
      if (val === 1) {
        this.showSyrCom = true
      } else {
        this.showSyrCom = false
      }
    },
    getDetail () {
      // letter/bgguaranteeletter/getInfoById/{id}
      this.loading = true
      this.$http
        .get(`/letter/bgguaranteeletter/getInfoById/${this.$route.params.id}`)
        .then(({ data: res }) => {
          this.loading = false
          if (res.code !== 0) {
            this.setObj()
            return
          }
          // console.log(res)
          this.dataForm = res.data
          this.setObj()
        })
    },
    // 获取界面信息
    getInfo () {
      // /letter/bgguaranteeletter/getAllByJkInfo/
      this.loading = true
      this.$http
        .get(`/letter/bgguaranteeapply/getInfo/`)
        .then(({ data: res }) => {
          this.loading = false
          if (res.code !== 0) {
            this.setObj()
            return
          }
          this.setObj()
          if (res.data) {
            this.dataForm.bgGuaranteeApply = {
              ...this.dataForm.bgGuaranteeApply,
              ...res.data
            }
          }
        })
    },
    setObj () {
      this.formReset.map((a) => {
        if (!this.dataForm[a]) this.$set(this.dataForm, a, {})
      })
      if (this.dataForm.letterId) this.letterId = this.dataForm.letterId
      this.$nextTick(() => {
        this.$refs.anchor.setJump()
      })
    },
    Verification () {
      return new Promise((resolve, reject) => {
        var arr = []
        this.formReset.map((a) => {
          if (this.$refs[`${a}`] !== undefined) {
            this.$refs[`${a}`].push().then(res => {
              arr.push(res)
            })
          }
        })
        setTimeout(() => {
          resolve(arr)
        }, 300)
      })
    },
    // faren,sqdw,wtr,xmmc
    // applyPDF () {
    //   this.dataFormSubmitHandle(1, this.dataForm)
    // },

    goAwit () {
      this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
        (item) => item.name !== 'applyBgGuaranteeApplyFromFLogin'
      )
      this.$router.push({
        name: 'bgguaranteeletterSqf'
      })

      const audit = this.$router.resolve({
        name: 'awaitingAudit',
        query: {
          id: this.letterId,
          insuranceCode: this.$route.params.insuranceCode
        }
      })
      console.log(audit)
      // var form = document.createElement('form')
      // form.action = audit.href
      // form.target = '_blank'
      // form.method = 'POST'
      // document.body.appendChild(form)
      // form.submit()
      // window.open(audit.href, '_blank')
      // encodeURI(audit.href)
      newWin(audit.href)
      // setTimeout(window.open(audit.href, '_blank'), 500)
    },
    openWin (url) {
      try {
        var el1 = document.createElement('a')
        el1.setAttribute('target', '_blank')
        el1.setAttribute('id', 'openWin')
        el1.setAttribute('href', url)
        document.body.appendChild(el1)
        document.getElementById('openWin').click()// 点击事件
        document.body.removeChild(el1)
      } catch (e) {
        window.open(url)
      }
    },
    dataFormSubmitHandle: debounce(
      async function (status, data) {
        this.serPriceId = this.$refs['bginsuranceinfo'].serPriceId
        console.log(data)
        var obj = {
          ...data,
          cjfChargeId: this.serPriceId,
          status: status,
          letterId: this.letterId,
          guaranteeType: this.guaranteeType,
          key: this.$route.params.insuranceCode
        }

        this.nextLoading = true
        this.Verification().then((res) => {
          console.log('验证：', res)
          if (!res.includes(false)) {
            if (status === 1) {
              const h = this.$createElement
              //  `该保函由${this.insuranfceIno.name}提供，保费为${this.dataForm.bginsuranceinfo.guaranteePrice}，是否继续？`,
              this.$confirm(
                h('p', null, [
                  h('span', null, '该保函由 '),
                  h('span', { style: 'color: teal' }, `${this.insuranfceIno.name}`),
                  h('span', null, ' 提供，'),
                  h('span', null, '保费为 '),
                  h('span', { style: 'color: rgb(230, 162, 60)' }, `${this.dataForm.bginsuranceinfo.guaranteePrice}`),
                  h('span', null, '元，是否继续？')
                ]),
                '提示',
                {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }
              )
                .then(() => {
                  // 验证通过，走提交
                  this.submitData(status, obj)
                })
                .catch(() => {
                  this.nextLoading = false
                })
            } else {
              this.submitData(status, obj)
            }
          } else {
            this.nextLoading = false
            this.$message.error('表单提交有误，请检查提交信息')
          }
        })
      },
      1000,
      { leading: true, trailing: false }
    ),
    submitData (status, obj) {
      this.$loading().close()
      this.$http
        .post('/letter/bgguaranteeapply/saveAllLogin', obj)
        .then(({ data: res }) => {
          this.nextLoading = false

          if (status !== 0) {
            if (this.$route.params.pdfType === '0') {
              if (res.data.signature) {
                this.$message({
                  message: this.$t('prompt.success'),
                  type: 'success',
                  duration: 500,
                  onClose: () => {
                    this.letterId = res.data.letterId
                    this.dataForm = res.data
                    if (this.dataForm.status === 1) {
                      this.goAwit()
                    }
                  }
                })
              } else {
                if (window.ActiveXObject !== undefined) {
                  newWin(window.SITE_CONFIG['bhtURL'] +
                    `#/pdfIe?letterId=${this.letterId}`)
                } else {
                  this.showDia('tip')
                }
              }
            } else {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.letterId = res.data.letterId
                  this.dataForm = res.data
                  if (this.dataForm.status === 1) {
                    this.goAwit()
                  }
                }
              })
            }
          } else {
            this.$message.success('保存成功！')
            this.letterId = res.data.letterId
            this.dataForm = res.data
          }
        })
        .catch(() => {
          this.$loading().close()
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.card {
  min-width: 900px;
  // max-width: 1200px;
  min-height: calc(100vh - 200px);
  // margin: 15px auto;
  // margin-bottom: 85px;
}
.foot {
  width: 100%;
  background: #fff;
  position: fixed;
  bottom: 0;
  padding: 10px 0;
  border-top: 1px solid #ddd;
  background: #f9f9f9;
  text-align: center;
  left: 0px !important;
  z-index: 999;
  padding-left: 210px;
  span {
    text-align: center;
  }
}
</style>
