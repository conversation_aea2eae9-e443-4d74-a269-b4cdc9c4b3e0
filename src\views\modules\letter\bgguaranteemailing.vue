<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-letter__bgguaranteemailing}">
      <el-form :inline="true" :model="dataForm" >

        <el-form-item>
          <el-input v-model="dataForm.mailingUser" clearable   placeholder="收件人"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="dataForm.mailingTel" clearable  style="width:400px;"  placeholder="收件电话"></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="danger"   @click="getDataList(1)">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgguaranteemailing:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgguaranteemailing:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>

         <el-form-item>
          <el-button type="danger" @click="resetHandle()">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle"   style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <!--<el-table-column prop="code" label="编码" sortable="custom" header-align="center" align="center"></el-table-column>-->
            <el-table-column prop="mailingUser" label="收件人" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="mailingTel" label="收件手机" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="mailingAddress" label="收件地址" sortable="custom" header-align="center" align="center"></el-table-column>

              <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="displayHandle(scope.row.id)">浏览</el-button>
            <el-dropdown>
              <span class="el-dropdown-link">
                <el-button type="text" size="mini">更多操作<i class="el-icon-arrow-down el-icon--right"></i></el-button>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item><el-button v-if="$hasPermission('letter:bgguaranteemailing:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button></el-dropdown-item>
                <el-dropdown-item> <el-button v-if="$hasPermission('letter:bgguaranteemailing:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button></el-dropdown-item>
                  <el-dropdown-item> <el-button v-if="$hasPermission('letter:bgguaranteemailing:update')&&scope.row.status==0" type="text" size="small" @click="enableHandle(scope.row.id)">{{ $t('enable') }}</el-button></el-dropdown-item>
                    <el-dropdown-item> <el-button v-if="$hasPermission('letter:bgguaranteemailing:update')&&scope.row.status==1" type="text" size="small" @click="stopHandle(scope.row.id)">{{ $t('stop') }}</el-button></el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
      <!-- 浏览 -->
      <display v-if="displayVisible" ref="display" @refreshDataList="refreshList"></display>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './bgguaranteemailing-add-or-update'
import Display from './bgguaranteemailing-display'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgguaranteemailing/page',
        getDataListIsPage: true,
        exportURL: '/letter/bgguaranteemailing/export',
        deleteURL: '/letter/bgguaranteemailing',
        enableURL: '/letter/bgguaranteemailing/enable',
        stopURL: '/letter/bgguaranteemailing/stop',
        deleteIsBatch: true
      },
      statusoptions: [{
        value: 1,
        label: '正常'
      }, {
        value: 0,
        label: '停用'
      }],
      dataForm: {
        code: '',
        mailingUser: '',
        mailingTel: '',
        status: ''
      },
      orderField: 'code',
      order: 'asc',
      uploadVisible: false
    }
  },
  components: {
    AddOrUpdate,
    Display
  },
  methods: {
    // 状态启用具体代码需微调格式去除空格
    enableHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(this.$t('prompt.info', { 'handle': '启用' }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.$http.put(this.mixinViewModuleOptions.enableURL, id ? [id] : this.dataListSelections.map(item => item.id)).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.getDataList()
            }
          })
        }).catch(() => {})
      }).catch(() => {})
    },
    // 状态停用
    stopHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(this.$t('prompt.info', { 'handle': '停用' }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        // 具体代码需微调格式去除空格
        this.$http.put(this.mixinViewModuleOptions.stopURL, id ? [id] : this.dataListSelections.map(item => item.id)).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.getDataList()
            }
          })
        }).catch(() => {})
      }).catch(() => {})
    }
  }
}

</script>
