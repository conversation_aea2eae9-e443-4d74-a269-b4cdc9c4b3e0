<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON>qiang
 * @Date: 2020-08-06 18:47:13
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-06-22 16:08:36
-->
<template>
  <el-dialog :visible.sync="visible" title="修改" id="diaUp" :close-on-click-modal="false" :close-on-press-escape="false" width="1200px">
    <div v-if="JSON.stringify(form) !='{}'">
      <!-- {{dataForm.auditSort}} -->
      <!-- <codeInfo  :dataForm='form.codeInfo' examine ref="codeInfo"></codeInfo> -->
        <!-- <respondent v-if="dataForm.auditSort=='1'"  :dataForm='dataForm.scUserListBsqr'  ref="respondent"></respondent> -->
        <litigationGuarantee  isCompoent  ref="litigationGuarantee"></litigationGuarantee>
    </div>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="allsubmitData()">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import respondent from '@/views/project/guarantee/litigationGuarantee/form/respondent'
import litigationGuarantee from '@/views/project/guarantee/litigationGuarantee/index'

// eslint-disable-next-line no-unused-vars
import codeInfo from '@/views/project/guarantee/litigationGuarantee/form/codeInfo'
export default {
  data () {
    return {
      visible: false,
      type: 'bh', //
      form: {},
      dataForm: {}
    }
  },
  components: {
    // eslint-disable-next-line vue/no-unused-components
    respondent,
    litigationGuarantee
    // eslint-disable-next-line no-undef
    // codeInfo
  },
  // props: {
  //   dataForm: {
  //     type: Object
  //   }
  // },
  watch: {
    dataForm: {
      handler (a) {
        if (a) {
          this.form = JSON.parse(JSON.stringify(a))
          console.log(a.auditSort)

          this.$nextTick(() => {
            // console.log(' this.form', this.form)
            this.$refs.litigationGuarantee.dataForm = this.form
          })
        }
      },
      deep: true
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.getDetail()
      })
    },
    // 表单提交
    getDetail () {
      this.loading = true
      this.$http
        .get(
          `/letter/bgletterlitigation/getInfo/?letterId=` +
            this.$route.query.seeLetterId
        )
        .then(({ data: res }) => {
          this.loading = false
          if (res.code !== 0) {
            return false
          }
          this.dataForm = res.data
        })
    },
    allsubmitData () {
      this.nextLoading = true
      this.$nextTick(() => {
        this.$refs.litigationGuarantee.dataFormSubmitHandle(0, this.form)
      })
      setTimeout(() => {
        this.nextLoading = false
        this.visible = false
        this.$emit('refresh')
      }, 1000)
    },
    async submitData (status, obj) {
      this.nextLoading = true
      this.form.type = 2
      this.$refs.respondent.push().then(a => {
        if (a) {
          this.$http
            .post(`/letter/bgletterlitigation/saveBh`, this.form)
            .then(async ({ data: res }) => {
              this.nextLoading = false
              this.$emit('refresh')
              this.$message.success('修改成功！')
              this.visible = false
            })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
#diaUp /deep/ .el-form-item {
    margin-bottom: 18px !important;
}
</style>
