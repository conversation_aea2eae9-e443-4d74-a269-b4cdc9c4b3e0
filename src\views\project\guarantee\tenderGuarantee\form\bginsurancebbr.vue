<template>
  <el-row :gutter="20" style="margin:15px 20px 0;">
    <el-form :model="dataForm" label-position="left" :rules="dataRule" ref="dataForm" label-width="140px" v-loading="loading">
      <el-card class="box-card" shadow='never'>
        <div slot="header" class="clearfix">
          <span class="title">被保人信息（招标方）</span>
          <!-- <el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button> -->
        </div>
        <div>
          <el-col :xs="24" :xl="24">
            <div class="formCon">
              <el-row :gutter="40">
                <el-col :xs="24" :lg='24' :xl="16" class="certCode">
                  <el-form-item label="证件类型/号码" prop="certCode">

                    <el-input v-model.trim="dataForm.certCode" size="small" @blur="codeChange" class="wd180" style="width:100%;" placeholder="证件号码">
                      <el-tooltip slot="append" content="输入证件号码自动带入系统已有被保人信息。" placement="bottom">
                        <i class="el-icon-question" style="font-size:16px;color:#409EFF;"></i>
                      </el-tooltip>
                      <el-select v-model="dataForm.certType" slot="prepend" size="small" class="wd180" style="width:100%;" placeholder="证件类型">
                        <el-option v-for="item in options.certTypeoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
                        </el-option>
                      </el-select>
                    </el-input>
                  </el-form-item>
                </el-col>

              </el-row>
              <el-row :gutter="40">
                <el-col :xs="24" :lg='24' :xl="16">
                  <el-form-item label="名称" prop="name">
                    <el-input v-model.trim="dataForm.name" @change="bbrChange" size="small" class="wd180" style="width:100%;" placeholder="被保人（招标方）名称">
                      <el-button slot="append" icon="el-icon-search" type="success" @click="bbrcx">企查查</el-button>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- <el-row :gutter="40">
                <el-col :xs="24" :lg='12' :xl="8">

                  <el-form-item label="联系人" prop="linkman">
                    <el-input v-model="dataForm.linkman" size="small" class="wd180" style="width:100%;" placeholder="联系人"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg='12' :xl="8">
                  <el-form-item label="联系人电话" prop="linkmanTel">
                    <el-input v-model="dataForm.linkmanTel" size="small" class="wd180" style="width:100%;" placeholder="联系人电话"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg='12' :xl="8">
                  <el-form-item label="公司电话" prop="phoneNumber">
                    <el-input v-model="dataForm.phoneNumber" size="small" class="wd180" style="width:100%;" placeholder="公司电话"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg='12' :xl="8">
                  <el-form-item label="证件有效期类型" prop="certTermType">
                    <el-select v-model="dataForm.certTermType" size="small" class="wd180" style="width:100%;" placeholder="证件有效期类型">
                      <el-option v-for="item in options.certTermTypeoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg='12' :xl="8">
                  <el-form-item label="证件有效期" prop="certTerm">
                    <el-date-picker v-model="dataForm.certTerm" type="date" value-format="yyyy-MM-dd" size="small" class="wd180" style="width:100%;" placeholder="证件有效期"></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg='12' :xl="8">
                  <el-form-item label="是否有受益人" hidden>
                    <el-radio-group v-model="radio" @change="radioChange">
                      <el-radio :label="0">否</el-radio>
                      <el-radio :label="1">是</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg='16' :xl="16">
                  <el-form-item label="地址" prop="address">
                    <el-input v-model="dataForm.address" type="textarea" :rows="2" size="small" class="wd180" style="width:100%;" placeholder="地址"></el-input>
                  </el-form-item>
                </el-col>
              </el-row> -->
            </div>
          </el-col>
        </div>
      </el-card>

    </el-form>
  </el-row>
</template>
<script>
export default {
  data () {
    return {
      radio: 0,
      showBbr: false,
      loading: false
    }
  },
  props: {
    dataForm: {
      type: Object
    },
    options: {
      type: Object
    },
    bgGuaranteeApply: {
      type: Object
    }
  },

  computed: {
    dataRule () {
      var checkPhone = (rule, value, callback) => {
        if (!value) {
          return callback(new Error('手机号不能为空'))
        } else {
          const reg = /^1[3|4|5|7|8|9][0-9]\d{8}$/
          console.log(reg.test(value))
          if (reg.test(value)) {
            callback()
          } else {
            return callback(new Error('请输入正确的手机号'))
          }
        }
      }
      var checkZuoPhone = (rule, value, callback) => {
        if (!value) {
          return callback(new Error('请输入正确的电话号'))
        } else {
          // eslint-disable-next-line no-useless-escape
          const reg = /(^[0-9]{3,4}\-[0-9]{3,8}$)|(^[0-9]{3,8}$)|(^\([0-9]{3,4}\)[0-9]{3,8}$)|(^0{0,1}13[0-9]{9}$)/
          console.log(reg.test(value))
          if (reg.test(value)) {
            callback()
          } else {
            return callback(new Error('请输入正确的电话号'))
          }
        }
      }
      var certCodeRule = (rule, value, callback) => {
        var reg = /^[A-Za-z0-9]*-?[A-Za-z0-9]+$/
        if (this.dataForm.certType === '2') {
          if (value.length !== 18) {
            return callback(new Error('统一社会信用代码必须是18位'))
          } else {
            console.log()
            if (reg.test(value)) {
              callback()
            } else {
              return callback(new Error('统一社会信用代码格式不正确'))
            }
          }
        } else {
          if (value.length < 7) {
            return callback(new Error('证件号码长度最小7位'))
          } else {
            if (reg.test(value)) {
              callback()
            } else {
              return callback(new Error('证件号码格式不正确'))
            }
          }
        }
      }
      return {
        name: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        certType: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        certCode: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          },
          { validator: certCodeRule, trigger: 'blur' }
        ],
        certTerm: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        certTermType: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        linkman: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        linkmanTel: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          },
          { validator: checkPhone, trigger: 'change' }
        ],
        phoneNumber: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          },
          { validator: checkZuoPhone, trigger: 'change' }
        ],
        address: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ]
      }
    }
  },
  beforeMount () {
    if (this.dataForm.certCode) {
      this.showBbr = true
    }
    if (!this.dataForm.certType) {
      this.$set(this.dataForm, 'certType', '2')
    }
    if (!this.dataForm.certTermType) {
      this.$set(this.dataForm, 'certTermType', '0')
    }
  },
  methods: {
    bbrChange (val) {
      if (val === this.bgGuaranteeApply.name) {
        this.dataForm.name = ''
        this.$confirm('被保人名称不能和投保人名称相同', '提示', {
          confirmButtonText: '确定',
          type: 'warning',
          showCancelButton: false
        }).then(() => {})
      }
    },
    bbrcodeChange (val) {},
    bbrcx () {
      var url = 'https://www.qcc.com/search?key='
      window.open(url + this.dataForm.name)
    },
    codeChange (e) {
      // console.log(this.dataForm.certCode)
      var reg = /^[A-Za-z0-9]*-?[A-Za-z0-9]+$/
      if (reg.test(this.dataForm.certCode)) {
        if (this.dataForm.certType === '2') {
          if (this.dataForm.certCode.length !== 18) {
            return false
          }
        } else {
          if (this.dataForm.certCode.length < 7) {
            return false
          }
        }
      } else {
        return false
      }

      if (this.dataForm.certCode === this.bgGuaranteeApply.certCode) {
        this.dataForm.certCode = ''
        this.$confirm('被保人证件号码不能和投保人证件号码相同', '提示', {
          confirmButtonText: '确定',
          type: 'warning',
          showCancelButton: false
        }).then(() => {})
        return false
      }
      let nameList = [
        'certType',
        'name',
        'certTermType',
        'certTerm',
        'address',
        'linkman',
        'linkmanTel',
        'phoneNumber'
      ]

      if (this.dataForm.certCode) {
        this.showBbr = true
        this.loading = true
        nameList.map((a) => {
          if (a !== 'certType' && a !== 'certTermType') {
            this.$set(this.dataForm, a, '')
          }
        })

        this.$http
          .get(`/letter/bginsurancebbr/getByCertCode/${this.dataForm.certCode}`)
          .then(({ data: res }) => {
            this.loading = false

            if (res.data) {
              nameList.map((a) => {
                console.log(a, res.data[a])

                if (a === 'certType') {
                  if (res.data[a]) {
                    this.$set(this.dataForm, a, res.data[a])
                  }
                } else {
                  this.$set(this.dataForm, a, res.data[a])
                }
              })
            }
          })
      } else {
        this.showBbr = false
        nameList.map((a) => {
          this.$set(this.dataForm, a, '')
        })
      }
    },
    radioChange (val) {
      this.$emit('show', val)
      // this.syvisible = true
    },
    push () {
      return new Promise((resolve, reject) => {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
</style>
