<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-03-10 18:54:25
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-02-20 15:54:51
-->
<template>
    <el-card shadow="never" class="aui-card--fill">
      <el-table size='mini' stripe :cell-style="TableCellStyle"  v-loading="dataListLoading" :data="dataList" border style="width: 100%;zoom:0.9">
        <el-table-column
        type="index"
        width="50">
        <template slot-scope="scope">
          <span v-show="dataList.length !=scope.$index+1">{{scope.$index+1}}</span>
        </template>
      </el-table-column>
        <el-table-column prop="projectName" label="项目名称" header-align="center" align="center" width="200">
        <template slot-scope="scope">
          <span v-if="scope.$index==dataList.length-1" >合计</span>
          <span v-else>{{scope.row.projectName}}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="name" label="保函名称" header-align="center" align="center" width="150"></el-table-column> -->
      <el-table-column label="保函类型" header-align="center" align="center" width="70">
        <template slot-scope="scope">
          <span >{{dict('电子保函类型',scope.row.guaranteeType)}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="applyName" label="申请方名称" header-align="center" align="center" ></el-table-column>
      <el-table-column prop="bbrName" label="被保人名称" header-align="center" align="center" ></el-table-column>
      <el-table-column prop="issueName" label="出具方名称" header-align="center" align="center" ></el-table-column>
      <el-table-column prop="tendereeAgent" label="招标代理" header-align="center" align="center" ></el-table-column>
      <el-table-column prop="regoinName" label="所属区域" header-align="center" align="center" ></el-table-column>
      <el-table-column prop="guaranteeAmount" label="担保金额（元）" header-align="center" align="center" width="70"></el-table-column>
      <el-table-column prop="guaranteePrice" label="保函金额（元）" header-align="center" align="center" width="70"></el-table-column>
      <el-table-column prop="policyNo" label="保单号" header-align="center" align="center" ></el-table-column>
      <el-table-column prop="letterStatus" label="保函状态" header-align="center" align="center" width="70">
        <template slot-scope="scope">
          <span>{{dict('保函状态',scope.row.letterStatus)}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="openDate" label="开具日期" header-align="center" align="center" width="90"></el-table-column>
      <el-table-column prop="invoiceStatus" label="是否开具发票" header-align="center" align="center"  width="95">
        <template slot-scope="scope">
          <span>{{scope.row.invoiceStatus=='1'?'是':'否'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="paymentMethod" label="支付方式" header-align="center" align="center" width="70"></el-table-column>
      <el-table-column  prop="invoicingRevenue" label="宏筑收入（开票）（元）" header-align="center" align="center" ></el-table-column>
    </el-table>
    </el-card>
</template>
<script>
import mixinViewModule from '@/mixins/view-module'
import moment from 'moment'
import { getDict } from '@/utils/index'

export default {
  mixins: [ mixinViewModule ],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgSettlement/settlementDetails',
        getDataListIsPage: false,
        activatedIsNeed: false,
        exportURL: '/letter/bgguaranteeletter/accountDataExport',
        deleteIsBatch: true
      },
      dataForm: {
        issueCode: this.$route.query.issueCode || '',
        startDate: moment().subtract(1, 'month').format('YYYY-MM'),
        endDate: moment().subtract(1, 'month').format('YYYY-MM')
      }
    }
  },
  computed: {
    fomate () {
      var str = ''
      if (this.guaranteeType === 'tbdbbh') {
        str = '投标有效期'
      }
      if (this.guaranteeType === 'lydbbh') {
        str = '担保期间'
      }
      if (this.guaranteeType === 'yfkdbbh') {
        str = '有效期'
      }
      return str
    }
  },
  mounted () {
    if (this.$route.query.startDate) {
      this.dataForm.startDate = this.$route.query.startDate
      this.dataForm.endDate = this.$route.query.endDate
    }
    this.getDataList()
  },
  methods: {
    TableCellStyle ({ rowIndex }) {
      if (rowIndex === this.dataList.length - 1) {
        return 'background-color:#f1824126'
      }
    },
    dict (name, val) {
      var aa = getDict(name).filter((a) => a.dictCode === val)
      return aa.length > 0 ? aa[0].dictName : '-'
    },
    go (issueCode) {
      this.$router.push({
        path: '/reconciliation',
        query: {
          issueCode
        }
      })
    }
  }
}
</script>
