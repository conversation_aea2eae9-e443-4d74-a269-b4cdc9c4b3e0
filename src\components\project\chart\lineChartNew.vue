<template>
  <div :class="'chart'+className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts' // echarts theme
import resize from './mixins/resize'
require('echarts/theme/macarons')

export default {
  mixins: [resize],
  props: {
    className: {
      type: String
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    lineChartData: {
      type: Object
    }
  },
  data () {
    return {
      chart: null
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.initChart()
    })
    window.addEventListener('resize', () => {
      if (this.chart) {
        this.chart.resize()
      }
    })
  },
  destroyed () {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  watch: {
    lineChartData: {
      deep: true,
      handler (val) {
        this.setOptions(val)
      }
    }
  },
  methods: {
    initChart () {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.lineChartData)
      // console.log(JSON.stringify(this.lineChartData))
    },
    setOptions ({ title, seriesData, xAxisNameData, legendData } = {}) {
      // console.log(seriesData, xAxisNameData)
      var arr = []
      if (seriesData && seriesData.length > 0) {
        seriesData.map(item => {
          arr.push(
            {
              name: item.name,
              type: 'line',
              stack: 'vistors',
              data: item.data,
              animationDuration: 2800,
              animationEasing: 'quadraticOut'
            }
          )
        })
      }
      this.chart.setOption({
        title: {
          text: title
          // textStyle: {
          //   color: '#409EFF'
          // },
          // padding: [0, 0, 10, 100]
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'cross',
            crossStyle: {
              color: '#999'
            }// 默认为直线，可选为：'line' | 'shadow'
          }
        },
        legend: {
          data: legendData
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            // dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ['line', 'bar'] },
            restore: { show: true },
            saveAsImage: { show: true }
          }
        },
        xAxis: [{
          type: 'category',
          axisLabel: {
            rotate: 25
          },
          data: xAxisNameData,
          axisTick: {
            alignWithLabel: true
          }
        }],
        yAxis: [{
          type: 'value',
          axisTick: {
            show: false
          }
        }],
        series: arr
      })
    }
  }

}
</script>
