<template>
  <main :class="['aui-content', { 'aui-content--tabs': $route.meta.isTab }]">
    <!-- tab展示内容 -->
    <template v-if="$route.meta.isTab">
      <el-dropdown class="aui-content--tabs-tools">
        <i class="el-icon-arrow-down"></i>
        <el-dropdown-menu slot="dropdown" :show-timeout="0">

          <el-dropdown-item @click.native="tabRemoveHandle($store.state.contentTabsActiveName)">{{ $t('contentTabs.closeCurrent') }}</el-dropdown-item>
          <el-dropdown-item @click.native="tabsCloseOtherHandle()">{{ $t('contentTabs.closeOther') }}</el-dropdown-item>
          <el-dropdown-item @click.native="tabsCloseAllHandle()">{{ $t('contentTabs.closeAll') }}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-tabs v-model="$store.state.contentTabsActiveName" @tab-click="tabSelectedHandle" @tab-remove="tabRemoveHandle" @contextmenu.prevent.native="openTag">
        <el-tab-pane v-for="item in $store.state.contentTabs" :key="item.name" :name="item.name" :label="item.title" :closable="item.name !== 'home'"
          :class="{ 'is-iframe': tabIsIframe(item.iframeURL) }">
          <template v-if="item.name === 'home'">
            <svg slot="label" class="icon-svg aui-content--tabs-icon-nav" aria-hidden="true">
              <use xlink:href="#icon-home"></use>
            </svg>
            <ul v-show="visible" :style="{left:left+'px',top:top+'px'}" class="contextmenu">
              <li @click="tabRefreshHandle($store.state.contentTabsActiveName)">{{ $t('contentTabs.refreshCurrent') }}</li>
            </ul>
          </template>
          <template v-if="item.name !== 'home'">
            <ul v-show="visible" :style="{left:left+'px',top:top+'px'}" class="contextmenu">
              <li @click="tabRefreshHandle($store.state.contentTabsActiveName)">{{ $t('contentTabs.refreshCurrent') }}</li>
              <li @click="tabRemoveHandle($store.state.contentTabsActiveName)">{{ $t('contentTabs.closeCurrent') }}</li>
              <li @click="tabsCloseOtherHandle()">{{ $t('contentTabs.closeOther') }}</li>
              <li @click="tabsCloseAllHandle()">{{ $t('contentTabs.closeAll') }}</li>
            </ul>
          </template>
          <iframe v-if="tabIsIframe(item.iframeURL)" :src="item.iframeURL" width="100%" height="100%" frameborder="0" scrolling="yes"></iframe>
          <el-scrollbar style="height: 100%" v-else>
            <keep-alive>
              <router-view v-if="item.name === $store.state.contentTabsActiveName" />
            </keep-alive>
          </el-scrollbar>

        </el-tab-pane>
      </el-tabs>
    </template>
    <!-- <el-scrollbar style="height: 100%">
      </el-scrollbar> -->
    <!-- 其他方式, 展示内容 -->
    <el-scrollbar style="height: 100%" v-else>
      <template>
        <keep-alive>

          <router-view />

        </keep-alive>
      </template>
    </el-scrollbar>

  </main>
</template>

<script>
import { isURL } from '@/utils/validate'
export default {
  data () {
    return {
      visible: false,
      top: 0,
      left: 0
    }
  },
  methods: {
    // tabs, 是否通过iframe展示
    openTag (e) {
      const menuMinWidth = 105
      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left
      const offsetWidth = this.$el.offsetWidth // container width
      const maxLeft = offsetWidth - menuMinWidth // left boundary
      const left = e.clientX - offsetLeft + 15 // 15: margin right
      if (left > maxLeft) {
        this.left = maxLeft
      } else {
        this.left = left
      }
      if (e.clientY < 90) {
        this.visible = true
      } else {
        this.visible = false
      }
    },
    abv (e) {
      alert(1)
    },
    tabIsIframe (url) {
      return isURL(url)
    },
    // tabs, 选中tab
    tabSelectedHandle (tab) {
      console.log(tab)
      tab = this.$store.state.contentTabs.filter(
        item => item.name === tab.name
      )[0]
      if (tab.name !== 'home') {
        if (!this.$store.state.sidebarMode) {
          this.$store.state.navbarActive = tab.navActive
          this.$store.state.sidebarMenuList = window.SITE_CONFIG['menuList']
          if (this.$store.state.sidebarMenuList[tab.navActive]) {
            // eslint-disable-next-line standard/computed-property-even-spacing
            this.$store.state.sidebarMenuList = this.$store.state.sidebarMenuList[
              tab.navActive
            ].children
          }
        }
      }
      // // // console.log(this.$store.state.contentTabs)
      if (tab) {
        this.$router.push({
          name: tab.name,
          params: { ...tab.params },
          query: { ...tab.query }
        })
      }
      this.visible = false
    },
    // tabs, 删除tab
    tabRemoveHandle (tabName) {
      if (tabName === 'home') {
        return false
      }
      this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
        item => item.name !== tabName
      )
      if (this.$store.state.contentTabs.length <= 0) {
        this.$store.state.sidebarMenuActiveName = this.$store.state.contentTabsActiveName =
          'home'
        return false
      }
      // 当前选中tab被删除
      if (tabName === this.$store.state.contentTabsActiveName) {
        // eslint-disable-next-line standard/computed-property-even-spacing
        var tab = this.$store.state.contentTabs[
          this.$store.state.contentTabs.length - 1
        ]
        this.$router.push({
          name: tab.name,
          params: { ...tab.params },
          query: { ...tab.query }
        })
      }
      this.visible = false
    },
    tabRefreshHandle (tabName) {
      this.$store.state.contentIsNeedRefresh = true
      this.$nextTick(() => {
        this.$store.state.contentIsNeedRefresh = false
      })
      this.visible = false
    },
    // tabs, 关闭其它
    tabsCloseOtherHandle () {
      this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
        item => {
          return (
            item.name === 'home' ||
            item.name === this.$store.state.contentTabsActiveName
          )
        }
      )
      this.visible = false
    },
    // tabs, 关闭全部
    tabsCloseAllHandle () {
      this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
        item => item.name === 'home'
      )
      this.$router.push({ name: 'home' })
      this.visible = false
    }
  }
}
</script>
<style lang="scss" scoped>
.contextmenu {
  margin: 0;
  background: #fff;
  z-index: 3000;
  position: absolute;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
  li {
    margin: 0;
    padding: 7px 16px;
    cursor: pointer;
    &:hover {
      background: #eee;
    }
  }
}
</style>
