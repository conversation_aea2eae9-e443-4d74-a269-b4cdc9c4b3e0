<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-12 10:47:18
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2022-01-20 10:41:55
-->
<template>
 <el-dialog class="preview CAmodel" title="委托合同上传" append-to-body :visible.sync="visible" >
      <uploadList v-if="JSON.stringify(dataForm) != '{}'" sh :type='0' :letterId='dataForm.letterId' :active='3' :dataForm='dataForm'
              :issueCode='dataForm.issueCode' :guaranteeTypeCodes='dataForm.guaranteeType' ></uploadList>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="visible = false">关 闭</el-button>
      </span>
    </el-dialog>
</template>
<script>
import uploadList from '@/views/project/guarantee/warrantGuarantee/compoents/uploadList'
export default {
  components: {
    uploadList
  },
  data () {
    return {
      visible: false,
      dataForm: {}
    }
  },
  methods: {
    init () {
      this.visible = true
    }
  }
}
</script>
<style lang="scss" scoped>
  /deep/.CAmodel{
    .el-dialog__body{
      max-height: none;
      min-height: auto;
    }
  }
</style>
