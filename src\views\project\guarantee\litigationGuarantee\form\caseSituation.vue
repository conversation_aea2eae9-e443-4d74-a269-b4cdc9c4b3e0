<template>
  <el-row :gutter="20" style="margin:15px 20px 0;">
    <el-card class="box-card" shadow='never'>
      <div slot="header" class="clearfix">
        <span class="title">案件信息</span>
      </div>
      <div>
        <el-col :xs="24" :lg="24">
          <div class="formCon">
            <template>
              <!-- {{paramsForm.paramsRules}} -->
              <el-form :model="dataForm" ref="dataForm" label-position="left" style="margin-bottom:15px;" :rules="dataRule" label-width="140px">
                <el-col :xs="24" :lg="12">
                  <el-form-item prop="court" label="受理法院">
                    <el-input v-model="dataForm.court" class="wd180" size="mini"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg="24">
                  <el-form-item prop="name" label="案件简述">
                    <el-input type="textarea" :rows="3" v-model="dataForm.name" class="wd350" size="mini"></el-input>
                    <!-- <el-input type="textarea" :rows="3" v-model="dataForm.causeOfAction" class="wd350" size="mini"></el-input> -->
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg="12">
                  <el-form-item prop="propertyPreservationType" label="诉前/诉中">
                    <el-select v-model="dataForm.propertyPreservationType" @change="change" placeholder="请选择" class="wd180" size="mini">
                      <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg="12">
                  <el-form-item prop="code" label="案号" v-if="dataForm.propertyPreservationType=='2'">
                    <el-input v-model="dataForm.code" class="wd180" size="mini"></el-input>
                  </el-form-item>
                </el-col>

                 <el-col :xs="24" :lg="24">
                  <el-form-item prop="preservationGoods" label="查封标的物">
                    <el-input type="textarea" :rows="3" v-model="dataForm.preservationGoods" class="wd350" size="mini"></el-input>
                    <span style="margin-left:25px;color:rgba(0,0,0,.4);"
                      class="el-upload__tip"><span style="color:#F56C6C;">注：</span>结尾以。（中文字符句号）结尾</span>
                  </el-form-item>
                </el-col>
                   <el-col :xs="24" :lg="24">
                  <el-form-item prop="otherObjects" label="其他标的物">
                    <el-input type="textarea" :rows="3" v-model="dataForm.otherObjects" class="wd350" size="mini"></el-input>
                    <span style="margin-left:25px;color:rgba(0,0,0,.4);"
                      class="el-upload__tip"><span style="color:#F56C6C;">注：</span>结尾以。（中文字符句号）结尾</span>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg="24">
                  <el-form-item prop="causeOfAction" label="案由">
                    <el-input type="textarea" :rows="3" v-model="dataForm.causeOfAction" class="wd350" size="mini"></el-input>
                  </el-form-item>
                </el-col>
              </el-form>
            </template>
          </div>
        </el-col>
      </div>
    </el-card>
    <!-- propertyPreservationType //财产保全类型 1诉前，2诉中
    preservationAmount //保全金额
    charge //收费
    causeOfAction //案由
    code //编号
    court //受理法院 -->
  </el-row>
</template>
<script>
export default {
  data () {
    return {
      options: [
        {
          value: '1',
          label: '诉前'
        },
        {
          value: '2',
          label: '诉中'
        }
      ]
    }
  },
  computed: {
    dataRule () {
      return {
        propertyPreservationType: [
          {
            required: false,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        name: [
          {
            required: false,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        guaranteePrice: [
          {
            required: false,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        preservationGoods: [
          {
            required: false,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        causeOfAction: [
          {
            required: false,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        code: [
          {
            required: false,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        otherObjects: [
          {
            required: false,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        court: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  props: ['dataForm'],
  methods: {
    change (val) {
      if (val === '1') {
        this.$set(this.dataForm, 'code', '')
      }
    },
    push () {
      return new Promise((resolve, reject) => {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.wd180 {
  width: 260px !important;
}
.wd350 {
  width: 350px !important;
}
.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
</style>
