<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2020-10-13 09:38:40
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-06-23 17:04:17
-->
<template>
  <el-col :span="20">
    <el-col :span="24" class="con-title">
      案件基本信息
    </el-col>
    <el-col :xs="24" :lg="24">
      <el-form-item prop="name" label="案件简述">
        {{dataForm.name}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="12">
      <el-form-item prop="propertyPreservationType" label="诉前/诉中">
        {{dataForm.propertyPreservationType=='1'?'诉前':'诉中'}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="12">
      <el-form-item prop="code" label="案号">
        {{dataForm.code}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="12">
      <el-form-item prop="court" label="受理法院">
        {{dataForm.court}}
      </el-form-item>
    </el-col>
      <el-col :xs="24" :lg="24">
      <el-form-item prop="causeOfAction" label="案由">
        {{dataForm.causeOfAction}}
      </el-form-item>
    </el-col>
     <el-col :xs="24" :lg="24">
      <el-form-item prop="preservationGoods" label="查封标的物">
        {{dataForm.preservationGoods}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="24">
      <el-form-item prop="preservationGoods" label="其他标的物">
        {{dataForm.otherObjects}}
      </el-form-item>
    </el-col>
  </el-col>

</template>
<script>
export default {
  props: {
    dataForm: Object
  },
  data () {
    return {
      options: [
        {
          value: '1',
          label: '诉前'
        }, {
          value: '2',
          label: '诉中'
        }
      ]
    }
  },
  methods: {}
}
</script>
<style lang="scss" scoped>
.con-title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
border-left: 6px solid rgb(241, 130, 65);  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-bottom: 10px;
}
</style>
