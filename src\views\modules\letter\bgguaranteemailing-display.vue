<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-08-14 10:58:10
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-08-17 16:40:36
-->
<template>
  <el-dialog :visible.sync="visible" append-to-body title="信息详情" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" label-position="left" v-loading='loading' ref="dataForm" :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="colConfig">
          <!-- <el-form-item label="名称" prop="name" label-width="190px">
            <span>{{dataForm.name}}</span>
          </el-form-item>
          <el-form-item label="邮编" prop="mailingCode" label-width="190px">
            <span>{{dataForm.mailingCode}}</span>
          </el-form-item> -->
          <el-form-item label="收件人" prop="mailingUser" label-width="190px">
            <span>{{dataForm.mailingUser}}</span>
          </el-form-item>
          <el-form-item label="收件电话" prop="mailingTel" label-width="190px">
            <span>{{dataForm.mailingTel}}</span>
          </el-form-item>
          <el-form-item label="收件地址" prop="mailingAddress" label-width="190px">
            <span>{{dataForm.mailingAddress}}</span>
          </el-form-item>
          <!-- <el-form-item label="状态" prop="status" label-width="190px">
            <span v-if="dataForm.status == 0">停用</span>
            <span v-if="dataForm.status == 1">正常</span>
          </el-form-item> -->
        </el-col>
      </el-row>
    </el-form>
    <template slot="footer">
      <el-button type="primary" @click="visible = false">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      loading: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        mailingCode: '',
        mailingUser: '',
        mailingTel: '',
        mailingAddress: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.loading = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http
        .get(`/letter/bgguaranteemailing/${this.dataForm.id}`)
        .then(({ data: res }) => {
          this.loading = false

          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
        })
        .catch(() => {})
    }
  }
}
</script>
