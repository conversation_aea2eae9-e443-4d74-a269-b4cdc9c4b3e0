<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-basic__businessannex}">
      <el-form :inline="true" :model="dataForm" >
          <el-form-item >
          <el-input v-model="dataForm.ywTableId"  hidden="true" ></el-input>
          <el-input v-model="dataForm.projectId"  hidden="true" ></el-input>
          <el-input v-model="dataForm.projectCode"  hidden="true"  ></el-input>

          <el-input v-model="dataForm.projectName"  hidden="true" ></el-input>

          <el-input v-model="dataForm.phase"  hidden="true"  ></el-input>

          <el-input v-model="dataForm.ywTableName" hidden="true" ></el-input>
          </el-form-item>
               <el-form-item >
                  <el-input v-model="dataForm.code"  placeholder="附件编码"></el-input>
              </el-form-item>
              <el-form-item >
                  <el-input v-model="dataForm.name"  placeholder="附件名称"></el-input>
              </el-form-item>
              <el-form-item >
                  <el-input v-model="dataForm.annexName"  clearable  placeholder="云附件名称"></el-input>
              </el-form-item>
          <el-form-item >
              <el-select v-model="dataForm.status" clearable     placeholder="请选择启用状态">
                  <el-option
                          v-for="item in statusoptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value">
                  </el-option>
              </el-select>
          </el-form-item>
        <el-form-item>
          <el-button type="danger"   @click="getDataList(1)">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
           <el-button type="primary" @click="importHandle()">附件上传</el-button>
         </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle"   style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="code" label="附件编码" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="name" label="附件名称" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="projectCode" label="项目编码" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="projectName" label="项目名称" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="phase" label="项目阶段" sortable="custom" header-align="center" align="center">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.phase == 1" size="mini" type="danger">项目立项</el-tag>
                    <el-tag v-if="scope.row.phase == 2" size="mini" type="success">前期准备</el-tag>
                    <el-tag v-if="scope.row.phase == 3" size="mini" type="info">设计阶段</el-tag>
                    <el-tag v-if="scope.row.phase == 4" size="mini" type="danger">招采阶段</el-tag>
                    <el-tag v-if="scope.row.phase == 5" size="mini" type="danger">合同阶段</el-tag>
                    <el-tag v-if="scope.row.phase == 6" size="mini" type="success">施工阶段</el-tag>
                    <el-tag v-if="scope.row.phase == 7" size="mini" type="info">竣工阶段</el-tag>
                    <el-tag v-if="scope.row.phase == 8" size="mini" type="danger">后期维护</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="ywTableName" label="业务类型" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="ywTableId" label="业务表id" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="annexId" label="云附件表id" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="annexName" label="云附件名称" sortable="custom" header-align="center" align="center"></el-table-column>
              <el-table-column prop="status" sortable="custom" label="状态" header-align="center" align="center">
   <template slot-scope="scope">
       <el-tag v-if="scope.row.status == 0" size="mini" type="danger">{{ $t('user.status0') }}</el-tag>
       <el-tag v-else size="mini" type="success">{{ $t('user.status1') }}</el-tag>
   </template>
</el-table-column>
              <el-table-column prop="createDate" label="创建时间" sortable="custom" header-align="center" align="center"></el-table-column>
              <el-table-column prop="updateDate" label="更新时间" sortable="custom" header-align="center" align="center"></el-table-column>
              <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="180">
          <template slot-scope="scope">
              <el-button type="text" size="mini" @click="displayHandle(scope.row.annexId,scope.row.annexName)">预览</el-button>
            <el-button v-if="$hasPermission('basic:businessannex:delete')" type="text" size="mini" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
              <el-button type="text" size="mini" @click="downloadHandle(scope.row.annexId,scope.row.name)">{{ $t('download') }}</el-button>
            <el-button v-if="$hasPermission('basic:businessannex:update')&& scope.row.status == 0" type="success" size="mini" @click="enableHandle(scope.row.id)">{{ $t('enable') }}</el-button>
            <el-button v-if="$hasPermission('basic:businessannex:update')&& scope.row.status == 1" type="danger" size="mini" @click="stopHandle(scope.row.id)">{{ $t('stop') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
        <!-- 弹窗, 上传文件 -->
      <upload v-if="uploadVisible" ref="upload" @refreshDataList="getDataList"></upload>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './businessannex-add-or-update'
import Upload from './businessannex-upload'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/basic/businessannex/page',
        getDataListIsPage: true,
        exportURL: '/basic/businessannex/export',
        deleteURL: '/basic/businessannex',
        enableURL: '/basic/businessannex/enable',
        stopURL: '/basic/businessannex/stop',
        deleteIsBatch: true
      },
      statusoptions: [{
        value: 1,
        label: '正常'
      }, {
        value: 0,
        label: '停用'
      }],
      phaseoptions: [{
        value: 1,
        label: '项目立项'
      }, {
        value: 2,
        label: '前期准备'
      }, {
        value: 3,
        label: '设计阶段'
      }, {
        value: 4,
        label: '招采阶段'
      }, {
        value: 5,
        label: '合同阶段'
      }, {
        value: 6,
        label: '施工阶段'
      }, {
        value: 7,
        label: '竣工阶段'
      }, {
        value: 8,
        label: '后期维护'
      }],
      dataForm: {
        id: '',
        code: '',
        name: '',
        projectCode: '',
        projectName: '',
        projectId: '',
        phase: '',
        ywTableName: '',
        ywTableId: '',
        annexName: '',
        status: 1
      },
      orderField: 'code',
      order: 'asc',
      uploadVisible: false
    }
  },
  components: {
    AddOrUpdate,
    Upload
  },
  created () {
    //
    this.dataForm.projectCode = this.$route.params.projectCode || ''
    this.dataForm.projectName = this.$route.params.projectName || ''
    this.dataForm.ywTableName = this.$route.params.ywTableName || ''
    this.dataForm.ywTableId = this.$route.params.ywTableId || ''
    this.dataForm.phase = this.$route.params.phase
    this.dataForm.projectId = this.$route.params.projectId || ''
    this.getDataList()
    //
  },
  methods: {
    // 导入
    importHandle () {
      this.uploadVisible = true
      var params = { 'projectCode': this.dataForm.projectCode, 'projectName': this.dataForm.projectName, 'projectId': this.dataForm.projectId, 'phase': this.dataForm.phase, 'ywTableName': this.dataForm.ywTableName, 'ywTableId': this.dataForm.ywTableId }
      this.$nextTick(() => {
        this.$refs.upload.init(params)
      })
    },
    // 状态启用具体代码需微调格式去除空格
    enableHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(this.$t('prompt.info', { 'handle': '启用' }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.$http.put(this.mixinViewModuleOptions.enableURL, id ? [id] : this.dataListSelections.map(item => item.id)).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.getDataList()
            }
          })
        }).catch(() => {})
      }).catch(() => {})
    },
    downloadHandle (id, name) {
      var url = `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${id}`
      window.location.href = url
    },
    displayHandle (id, path) {
      var previewUrl = ''
      var url = `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${id}`
      previewUrl = url + '?fullfilename=' + path
      window.open(`${window.SITE_CONFIG['fileView']}` + encodeURIComponent(previewUrl))
    },
    // 状态停用
    stopHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(this.$t('prompt.info', { 'handle': '停用' }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        // 具体代码需微调格式去除空格
        this.$http.put(this.mixinViewModuleOptions.stopURL, id ? [id] : this.dataListSelections.map(item => item.id)).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.getDataList()
            }
          })
        }).catch(() => {})
      }).catch(() => {})
    }
  }
}

</script>
