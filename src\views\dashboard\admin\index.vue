<template>
  <el-card shadow="hover" class="aui-card--fill">
  <div class="dashboard-editor-container">
    <panel-group :panelData='this.chartData.statisticsTotal'  />
    <el-row style="background:#fff;padding:16px 16px 0;margin-bottom:32px;">
      <!-- <line-chart :chart-data="lineChartData" /> -->
    </el-row>
     <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="24">
        <div class="chart-wrapper" >
         <bar-chart :barChartData='this.chartData.everyMonthPayNum' :className="'everyMonthPayNum'"/>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="7">
        <div class="chart-wrapper">
          <pie-chart :PieChartData='this.chartData.backletterNum' :className="'backletterNum'"/>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="7">
        <div class="chart-wrapper">
          <pie-chart :PieChartData=' this.chartData.backletterNumrate' :className="'backletterNumrate'"/>
        </div>
      </el-col>
        <el-col :xs="24" :sm="24" :lg="10">
        <div class="chart-wrapper">
          <bar-chart  :barChartData='this.chartData.guaranteeTypeNum' :className="'guaranteeTypeNum'"/>
        </div>
      </el-col>

    </el-row>

  </div>
  </el-card>
</template>

<script>
import PanelGroup from '@/components/project/chart/PanelGroup'
// import LineChart from '@/components/project/chart/LineChart'
import BarChart from '@/components/project/chart/BarChart'
import PieChart from '@/components/project/chart/PieChart'

export default {
  name: 'DashboardAdmin',
  components: {
    PanelGroup,
    // LineChart,
    PieChart,
    BarChart
  },
  data () {
    return {

      url: '',
      chartData: {
        statisticsTotal: {
          totalNum: '', // 保函总数
          guaranteePriceTotalAmount: '', // 保函总金额
          guaranteeAmountTotalAmount: '', // 总
          intradayGuaranteePriceTotal: ''// 当日
        },
        everyMonthPayNum: {},
        backletterNum: {},
        backletterNumrate: {},
        guaranteeTypeNum: {}
      }
    }
  },
  created () {
    this.$nextTick(() => {
      // this.initline()
      this.getData()
    })
  },
  mounted () {
    this.url = window.SITE_CONFIG['apiURL']
  },
  methods: {
    // 折线图数据
    getData () {
      this.$http.get('/letter/statistics/getStatisticsDate').then(({ data: res }) => {
        var obj = res.data
        this.chartData.statisticsTotal = {
          totalNum: obj.backletterNum.totalNum, // 保函总数
          guaranteePriceTotalAmount: obj.backletterNum.guaranteePriceTotalAmount, // 保函总金额
          guaranteeAmountTotalAmount: obj.backletterNum.guaranteeAmountTotalAmount, // 总
          intradayGuaranteePriceTotal: obj.backletterNum.intradayGuaranteePriceTotal// 当日
        }
        console.log(this.chartData.statisticsTotal)
        // 统计服务费收取方式比例
        this.chartData.backletterNum = {
          legendData: ['已支付', '已拒绝', '未支付'],
          seriesName: '订单统计',
          seriesData: [
            { value: obj.backletterNum.totalNum, name: '已支付' },
            { value: obj.backletterNum.auditRfusedNum, name: '已拒绝' },
            { value: obj.backletterNum.unpaidNum, name: '未支付' }
          ]
        }
        this.chartData.backletterNumrate = {
          legendData: ['平台使用费500以下', '平台使用费500-1000', '平台使用费1000以上'],
          seriesName: '订单统计',
          seriesData: [
            { value: obj.backletterNum.lessNum, name: '平台使用费500以下' },
            { value: obj.backletterNum.betweenSthNum, name: '平台使用费500-1000' },
            { value: obj.backletterNum.greaterNum, name: '平台使用费1000以上' }
          ]
        }
        this.chartData.everyMonthPayNum = {
          title: '保函申请数量（已支付）',
          seriesData: [
            {
              name: '数量',
              data: []
            }

          ],
          xAxisNameData: []
        }
        if (obj.everyMonthPayNum) {
          obj.everyMonthPayNum.map(item => {
            this.chartData.everyMonthPayNum.xAxisNameData.push(item.date)
            this.chartData.everyMonthPayNum.seriesData[0].data.push(item.num)
          })
        }
        this.chartData.guaranteeTypeNum = {
          title: '保函类型（已支付）',
          seriesData: [
            {
              name: '数量',
              data: []
            }

          ],
          xAxisNameData: []
        }
        if (obj.guaranteeTypeNum) {
          obj.guaranteeTypeNum.map(item => {
            this.chartData.guaranteeTypeNum.xAxisNameData.push(item.name)
            this.chartData.guaranteeTypeNum.seriesData[0].data.push(item.num)
          })
        }
      }).catch(() => {})
    },
    // getData2 () {
    //   this.$http.get('/finance/statistics/statisticsFee').then(({ data: res }) => {
    //     if (res.code !== 0) {
    //       return
    //     }
    //     // 头部整体数据
    //     this.chartData.statisticsTotal = res.data.statisticsTotal
    //     // 统计服务费收取方式比例
    //     this.chartData.statisticsServiceFeeRate = {
    //       legendData: ['收取招标人费用总额', '收取投标企业费用总额'],
    //       seriesName: '统计服务费收取方式比例',
    //       seriesData: [
    //         { value: res.data.statisticsServiceFeeRate.zbrServiceFee, name: '收取招标人费用总额' },
    //         { value: res.data.statisticsServiceFeeRate.unitServiceFee, name: '收取投标企业费用总额' }
    //       ]
    //     }
    //     // 统计标书费收取方式比例
    //     this.chartData.statisticsBiddingFeeRate = {
    //       legendData: ['办事处代收总金额', '公司收取总金额', '中金支付代转总金额'],
    //       seriesName: '统计标书费收取方式比例',
    //       seriesData: [
    //         { value: res.data.statisticsBiddingFeeRate.officeCollectionFee, name: '办事处代收总金额' },
    //         { value: res.data.statisticsBiddingFeeRate.companyCollectionFee, name: '公司收取总金额' },
    //         { value: res.data.statisticsBiddingFeeRate.ciccCollectionFee, name: '中金支付代转总金额' }
    //       ]
    //     }
    //     // 保证金收取和退费金额比例
    //     this.chartData.statisticsEarnestMoneyRate = {
    //       legendData: ['收取金额', '退费金额'],
    //       seriesName: '保证金收取和退费金额比例',
    //       seriesData: [
    //         { value: res.data.statisticsEarnestMoneyRate.money, name: '收取金额' },
    //         { value: res.data.statisticsEarnestMoneyRate.refundMoney, name: '退费金额' }
    //       ]
    //     }

    //     // 收取服务费
    //     this.chartData.statisticsServiceFee = {
    //       title: '收取服务费',
    //       seriesData: [
    //         {
    //           name: '从招标人收取服务费',
    //           data: []
    //         },
    //         {
    //           name: '从投标企业收取服务费',
    //           data: []
    //         }
    //       ],
    //       xAxisNameData: []
    //     }
    //     res.data.statisticsServiceFee.map(item => {
    //       this.chartData.statisticsServiceFee.xAxisNameData.push(item.deptName)
    //       this.chartData.statisticsServiceFee.seriesData[0].data.push(item.statistics.zbrServiceFee)
    //       this.chartData.statisticsServiceFee.seriesData[1].data.push(item.statistics.unitServiceFee)
    //     })
    //     // 统计各办事处
    //     this.chartData.statisticsOfficeTotal = {
    //       title: '统计各办事处',
    //       seriesData: [
    //         {
    //           name: '项目总数',
    //           data: []
    //         },
    //         {
    //           name: '投标企业总数',
    //           data: []
    //         },
    //         {
    //           name: '标书费收取总额',
    //           data: []
    //         },
    //         {
    //           name: '服务费总数',
    //           data: []
    //         },
    //         {
    //           name: '应收服务费',
    //           data: []
    //         },
    //         {
    //           name: '未收服务费',
    //           data: []
    //         }
    //       ],
    //       xAxisNameData: []
    //     }
    //     res.data.statisticsOfficeTotal.map(item => {
    //       this.chartData.statisticsOfficeTotal.xAxisNameData.push(item.deptName)
    //       this.chartData.statisticsOfficeTotal.seriesData[0].data.push(item.statistics.projectNum)
    //       this.chartData.statisticsOfficeTotal.seriesData[1].data.push(item.statistics.unitNum)
    //       this.chartData.statisticsOfficeTotal.seriesData[2].data.push(item.statistics.biddingFee)
    //       this.chartData.statisticsOfficeTotal.seriesData[3].data.push(item.statistics.receivedServiceFee)
    //       this.chartData.statisticsOfficeTotal.seriesData[4].data.push(item.statistics.receivableFee)
    //       this.chartData.statisticsOfficeTotal.seriesData[5].data.push(item.statistics.uncollectedServiceFee)
    //     })
    //   }).catch(() => {})
    // },
    handleSetLineChartData () {
      // this.lineChartData = lineChartData
    }
  }

}
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  padding: 32px;
  background-color: rgb(240, 242, 245);
  position: relative;

  .github-corner {
    position: absolute;
    top: 0px;
    border: 0;
    right: 0;
  }

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}

@media (max-width:1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
</style>
