<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2020-10-29 16:09:16
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2022-05-10 10:29:32
-->
<template>
  <el-card v-if="draw" class="fileWin previewFile" :style="draw?'width:calc((100% - 210px) - 45%);':'width:0;'">
    <el-tabs tab-position="right" style="height: 200px;" @tab-click='tabclick'>
      <el-tab-pane v-for="(item,index) in fileData" :key="index" :label="item.name"> </el-tab-pane>
      <div class="fileCon" style="width:100%;height:calc(100vh - 40px);border:none;">
        <iframe v-if="filterType(fileData[activeIdx].url)==1" :src="url + fomateUrl(fileData[activeIdx].id)" id="iframe" style="width:100%;height:100%;border:none;"></iframe>
        <div style="height:100%;"  v-else-if="filterType(fileData[activeIdx].url)==2">
           <div class="imgBar" >
              <i class="el-icon-refresh-left" @click="rotateChange(90)"></i>
              <i class="el-icon-refresh-right" @click="rotateChange(-90)"></i>
              <i class="el-icon-circle-plus-outline" @click="zoomChange(10,'add')"></i>
              <i class="el-icon-remove-outline" @click="zoomChange(-10,'de')"></i>
              <i class="el-icon-download" @click="download(fileData[activeIdx].id)"></i>
              <!-- <i class="el-icon-full-screen" @click="miner(choseData.id)"></i> -->
            </div>
              <div style="height:100%;width:100%;overflow:auto;" >
            <img style="width:100%;margin-top:35px;" :style="`transform:rotate(${rotate}deg);width:${zoom}%`" :src="`${aipUrl}/sys/oss/minioPreview?id=${fileData[activeIdx].id}`" alt="">
        </div>
        </div>
         <div style="text-align:center;" v-else>
          暂不支持预览该类型文件
        </div>
      </div>
    </el-tabs>
    <el-button class="close" @click="close" type="danger" icon="el-icon-close" circle></el-button>
  </el-card>
</template>
<script>
import { getUUID } from '@/utils'
export default {
  props: {
    draw: Boolean,
    fileData: Array
  },
  data () {
    return {
      url: '',
      activeIdx: '',
      aipUrl: '',
      rotate: 0,
      zoom: 100
    }
  },
  created () {
    this.url = window.SITE_CONFIG['fileView']
    this.aipUrl = window.SITE_CONFIG['apiURL']
  },
  watch: {
    fileData (a) {
      this.activeIdx = 0
    }
  },
  methods: {
    close () {
      this.$emit('close')
    },
    rotateChange (reg) {
      this.rotate = this.rotate + reg
    },
    zoomChange (zoom, type) {
      if (this.zoom === 150 && type === 'add') {
        this.$message.warning('已放大至最大')
        return
      }
      if (this.zoom === 70 && type === 'de') {
        this.$message.warning('已缩小至最小')
        return
      }
      this.zoom = this.zoom + zoom
    },
    download (file) {
      window.open(
        `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${file}`
      )
    },
    tabclick (a, b) {
      this.activeIdx = a.index
      this.rotate = 0
      this.zoom = 100
    },
    fomateUrl (id) {
      let apiURL = window.SITE_CONFIG['apiURL']
      return encodeURIComponent(
        apiURL +
          '/sys/oss/minioPreview?id=' +
          id +
          '&uuid=' +
          getUUID() +
          `&fullfilename=file${new Date().getTime()}.pdf` +
          '&_t=' +
          new Date().getTime()
      )
    },
    filterType (file) {
      var extension = file ? file.substring(file.lastIndexOf('.') + 1) : ''
      if (extension === 'pdf' || extension === 'PDF') {
        return 1
      } else if (
        extension === 'png' ||
          extension === 'jpg' ||
          extension === 'jpeg'
      ) {
        return 2
      } else {
        return 0
      }
    },
    flieLabel (type) {
      if (type === 1) {
        return '申请书'
      }
      if (type === 2) {
        return '招标文件'
      }
      if (type === 0 || type === 3) {
        return '营业执照'
      }
      if (type === 4) {
        return '担保合同'
      }
      if (type === 7) {
        return '线下支付凭证'
      }
      if (type === 5) {
        return '电子保函'
      }
      if (type === 6) {
        return '发票'
      }
      if (type === 8) {
        return '澄清文件'
      }
      if (type === 9) {
        return '说明文件'
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.fileWin {
  width: 0;
  height: 100vh;
  z-index: 2001;
  margin: 0;
  position: fixed;
  top: 0;
  transition: width 0.3s ease-in-out;
  right: 0;
  .close {
    padding: 5px;
    position: absolute;
    right: 15px;
    top: 15px;
  }
}
.previewFile /deep/ .is-current .el-tree-node__content{
  background: #ddebfc;
}
.imgBar{
  background-color: rgba(71, 71, 71, 1);
  height:32px;
  text-align: center;
  color: white;
  line-height: 32px;
  i{
    display: inline-block;
    font-size: 16px;
  margin: 0 5px;
  cursor: pointer;
  }
}
</style>
