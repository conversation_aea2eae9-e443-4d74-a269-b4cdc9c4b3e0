<template>
  <el-row :gutter="20" style="margin:15px 20px 0;">
  <el-form :model="dataForm" label-position="left" v-loading='loading' :rules="dataRule" ref="dataForm" label-width="140px">
      <el-card class="box-card" shadow='never'>
        <div slot="header" class="clearfix">
          <span class="title">发票信息</span>
          <!-- <el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button> -->
        </div>
        <div>
            <el-col :xs="12" :lg="24">
        <div class="formCon">
          <el-row :gutter="40">
            <!-- <el-col :xs="24" :lg="12">
              <el-form-item label="开票对象" prop="invoiceObject">
                <el-select v-model="dataForm.invoiceObject" size="small" class="wd180" style="width:100%;" placeholder="开票对象">
                  <el-option v-for="item in options.invoiceObjectoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="发票类型" prop="invoiceType">
                <el-select v-model="dataForm.invoiceType" size="small" class="wd180" style="width:100%;" placeholder="发票类型">
                  <el-option v-for="item in options.invoiceTypeoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
                  </el-option>
                </el-select>
              </el-form-item>

            </el-col> -->

            <el-col :xs="24" :lg="12">
              <!-- <el-form-item label="纳税人类型" prop="taxpayerType">
                <el-select v-model="dataForm.taxpayerType" size="small" class="wd180" style="width:100%;" placeholder="纳税人类型">
                  <el-option v-for="item in options.taxpayerTypeoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
                  </el-option>
                </el-select>
              </el-form-item> -->
               <el-form-item label="纳税人识别号" prop="taxpayerNumber">
                <el-input  v-model.trim="dataForm.taxpayerNumber" size="small" @blur="taxpayerNumberChange" class="wd180" style="width:100%;" placeholder="纳税人识别号">
                  <el-tooltip slot="append" content="输入纳税人识别号自动带入已有发票信息。" placement="bottom">
                <i class="el-icon-question" style="font-size:16px;color:#409EFF;"></i>
              </el-tooltip>
                </el-input>
                <!-- <div style="color:#F56C6C;font-size:12px;">注：输入纳税人识别号自动带入已有发票信息。</div> -->
              </el-form-item>
            </el-col>
          </el-row>
          <el-row   :gutter="40">
             <el-col :xs="24" :lg="24">
              <el-form-item label="发票抬头" prop="invoiceTitle">
                <el-input v-model.trim="dataForm.invoiceTitle"  size="small" class="wd180" style="width:100%;" placeholder="发票抬头">
                  <el-button slot="append"  icon="el-icon-search" @click="hqqyfpxx">企查查发票</el-button>
                </el-input>
              </el-form-item>
              <el-form-item label="开户行名称" prop="bankAccountName">
                <el-input v-model.trim="dataForm.bankAccountName"  size="small" class="wd180" style="width:100%;" placeholder="开户行名称"></el-input>
              </el-form-item>

            </el-col>
            <el-col :xs="24" :lg="12">
                <el-form-item label="银行账户号码" prop="bankAccountNo">
                <el-input v-model.trim="dataForm.bankAccountNo"  size="small" class="wd180" style="width:100%;" placeholder="银行账户号码"></el-input>
              </el-form-item>
              <el-form-item label="税务登记电话" prop="phoneNumber">
                <el-input v-model="dataForm.phoneNumber" size="small" class="wd180" style="width:100%;" placeholder="电话"></el-input>
              </el-form-item>

            </el-col>
             <el-col :xs="24" :lg="12">
                 <el-form-item label="发票接收邮箱" prop="electronicInvoiceEmail">
                  <el-input v-model.trim="dataForm.electronicInvoiceEmail"  size="small" class="wd180" style="width:100%;" placeholder="发票接收邮箱"></el-input>
                </el-form-item>
                  <el-form-item label="发票接收手机号" prop="electronicInvoicePhone">
                <el-input v-model="dataForm.electronicInvoicePhone" size="small" class="wd180" style="width:100%;" placeholder="发票接收手机号"></el-input>
              </el-form-item>
             </el-col>
            <el-col :xs="24" :lg="24">
             <el-form-item label="税务登记地址" prop="address">
                <el-input v-model="dataForm.address" type="textarea" :rows="2" size="small" class="wd180" style="width:100%;" placeholder="地址"></el-input>
              </el-form-item>
            </el-col>

          </el-row>

        </div>
      </el-col>
        </div>
      </el-card>

  </el-form>
    </el-row>

</template>
<script>
export default {
  data () {
    return {
      radio: 0,
      loading: false
    }
  },
  props: {
    dataForm: {
      type: Object
    },
    options: {
      type: Object
    }
  },
  computed: {
    dataRule () {
      var checkPhone = (rule, value, callback) => {
        if (!value) {
          return callback(new Error('手机号不能为空'))
        } else {
          const reg = /^1[3|4|5|7|8|9][0-9]\d{8}$/
          console.log(reg.test(value))
          if (reg.test(value)) {
            callback()
          } else {
            return callback(new Error('请输入正确的手机号'))
          }
        }
      }
      var checkEmail = (rule, value, callback) => {
        if (!value) {
          return callback(new Error('发票接收邮箱不能为空'))
        } else {
          const reg = /^([a-zA-Z0-9._-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/
          if (reg.test(value)) {
            callback()
          } else {
            return callback(new Error('请输入正确的发票接收邮箱格式'))
          }
        }
      }
      // eslint-disable-next-line no-unused-vars
      var checkZuoPhone = (rule, value, callback) => {
        if (!value) {
          return callback(new Error('请输入正确的电话号'))
        } else {
          // eslint-disable-next-line no-useless-escape
          const reg = /^(0[0-9]{2,3}\-)([2-9][0-9]{6,7})+(\-[0-9]{1,4})?$/
          console.log(reg.test(value))
          if (reg.test(value)) {
            callback()
          } else {
            return callback(new Error('请输入正确的电话号'))
          }
        }
      }
      return {
        invoiceType: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        invoiceObject: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        taxpayerType: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        taxpayerNumber: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        phoneNumber: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
          // { validator: checkZuoPhone, trigger: 'change' }
        ],
        address: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        bankAccountName: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        bankAccountNo: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        invoiceTitle: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        electronicInvoicePhone: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          },
          { validator: checkPhone, trigger: 'change' }
        ],
        electronicInvoiceEmail: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          },
          { validator: checkEmail, trigger: 'change' }
        ]
      }
    }
  },
  beforeMount () {
    if (!this.dataForm.invoiceObject) {
      this.$set(this.dataForm, 'invoiceObject', '1')
    }
    if (!this.dataForm.invoiceType) {
      this.$set(this.dataForm, 'invoiceType', '1')
    }
    if (!this.dataForm.taxpayerType) {
      this.$set(this.dataForm, 'taxpayerType', '1')
    }
  },
  methods: {
    hqqyfpxx () {
      var url = 'https://www.qcc.com/tax_search?key='
      window.open(url + this.dataForm.invoiceTitle)
    },
    taxpayerNumberChange (e) {
      console.log(e, this.dataForm)
      let nameList = [
        'bankAccountName',
        'invoiceTitle',
        'bankAccountNo',
        'address',
        'phoneNumber'
      ]
      nameList.map(a => {
        this.$set(this.dataForm, a, '')
      })
      if (this.dataForm.taxpayerNumber) {
        this.loading = true
        this.$http
          .post(
            `/letter/bgguaranteeinvoice/getInfoByTaxpayerNumber/${this.dataForm.taxpayerNumber}`
          )
          .then(({ data: res }) => {
            this.loading = false

            this.$set(
              this.dataForm,
              'bankAccountName',
              res.data.bankAccountName
            )
            nameList.map(a => {
              this.$set(this.dataForm, a, res.data[a])
            })
          })
      }
    },
    push () {
      return new Promise((resolve, reject) => {
        this.$refs['dataForm'].validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
</style>
