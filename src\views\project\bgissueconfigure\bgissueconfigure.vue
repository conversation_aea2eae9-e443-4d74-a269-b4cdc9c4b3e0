<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-letter__bgissueconfigure}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-input v-model="dataForm.name" clearable   placeholder="名称"></el-input>
        </el-form-item>
        <el-form-item >
          <el-select v-model="dataForm.status" clearable  placeholder="请选择启用状态">
            <el-option
                    v-for="item in statusoptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getDataList(1)">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="danger" @click="resetHandle()">重置</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
           <el-button type="primary" @click="importHandle()">{{ $t('import') }}</el-button>
         </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgissueconfigure:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgissueconfigure:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgissueconfigure:update')" type="danger" @click="enableHandle()">{{ $t('enable') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgissueconfigure:update')" type="danger" @click="stopHandle()">{{ $t('stop') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle"   style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="code" label="编码" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="name" label="名称" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="issueCode" label="出具方code" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="alias" label="出具方别名" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="delayBidOpening" label="开标延后日期" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="isMail" label="是否需要邮寄信息，1是 0否" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="istenderdocument" label="是否需要投标文件，1是 0否" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="isbusiness" label="是否需要营业执照，1是 0否" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="isclarify" label="是否需要澄清答疑文件，1是 0否" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="isentrust" label="是否需要委托书，1是 0否" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="iscommissioncontract" label="是否需要委托合同，1是0否" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="islegalpersonid" label="是否需要法人身份证，1是0否" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="isclientid" label="是否需要委托人身份证，1是0否" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="isdocuments" label="是否需要招标文件，1是0否" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="isRemarks" label="是否需要备注字段，1是 0否" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="ispetition" label="是否需要申请书，1是 0否" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="commonlyOrRelated" label="可开具一般责任和连带责任保函，1一般责任，2连带责任，3全部" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="isSurrender" label="是否支持退保，1是 0否" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="isCorrection" label="是否支持批改，1是 0否" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="isApplyInvoice" label="是否支持开具发票，1是 0否" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="isCorrectionDate" label="是否支持批改截至日期，1是 0否" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="isCorrectionPayMethod" label="是否支持批改支付方式，1是 0否" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="isCorrectionProjectName" label="是否支持批改项目名称，1是 0否" sortable="custom" header-align="center" align="center"></el-table-column>
              <el-table-column prop="createDate" label="创建时间" sortable="custom" header-align="center" align="center"></el-table-column>
              <el-table-column prop="updateDate" label="更新时间" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="status" sortable="custom" label="状态" header-align="center" align="center">
   <template slot-scope="scope">
       <el-tag v-if="scope.row.status == 0" size="small" type="danger">{{ $t('user.status0') }}</el-tag>
       <el-tag v-else size="small" type="success">{{ $t('user.status1') }}</el-tag>
   </template>
</el-table-column>
            <el-table-column prop="remarks" label="备注" sortable="custom" header-align="center" align="center"></el-table-column>
              <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="displayHandle(scope.row.id)">浏览</el-button>
            <el-button v-if="$hasPermission('letter:bgissueconfigure:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
            <el-dropdown>
              <span class="el-dropdown-link">
                <el-button type="text" size="mini">更多操作<i class="el-icon-arrow-down el-icon--right"></i></el-button>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item> <el-button v-if="$hasPermission('letter:bgissueconfigure:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button></el-dropdown-item>
                  <el-dropdown-item> <el-button v-if="$hasPermission('letter:bgissueconfigure:update')&&scope.row.status==0" type="text" size="small" @click="enableHandle(scope.row.id)">{{ $t('enable') }}</el-button></el-dropdown-item>
                    <el-dropdown-item> <el-button v-if="$hasPermission('letter:bgissueconfigure:update')&&scope.row.status==1" type="text" size="small" @click="stopHandle(scope.row.id)">{{ $t('stop') }}</el-button></el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
        <!-- 弹窗, 上传文件 -->
      <upload v-if="uploadVisible" ref="upload" :uploadUrl=uploadUrl :downloadUrl=downloadUrl @refreshDataList="refreshList"></upload>
      <!-- 浏览 -->
      <display v-if="displayVisible" ref="display" @refreshDataList="refreshList"></display>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './bgissueconfigure-add-or-update'
import Upload from '@/components/project/hjUpload'
import Display from './bgissueconfigure-display'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      uploadUrl: '/letter/bgissueconfigure/upload', // 上传链接
      downloadUrl: '/letter/bgissueconfigure/downloadExcel', // 下载链接
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgissueconfigure/page',
        getDataListIsPage: true,
        exportURL: '/letter/bgissueconfigure/export',
        deleteURL: '/letter/bgissueconfigure',
        enableURL: '/letter/bgissueconfigure/enable',
        stopURL: '/letter/bgissueconfigure/stop',
        deleteIsBatch: true
      },
      statusoptions: [{
        value: 1,
        label: '正常'
      }, {
        value: 0,
        label: '停用'
      }],
      dataForm: {
        name: '',
        status: 1
      },
      uploadVisible: false
    }
  },
  components: {
    AddOrUpdate,
    Display,
    Upload
  },
  methods: {
    // 导入
    importHandle () {
      this.uploadVisible = true
      this.$nextTick(() => {
        this.$refs.upload.init()
      })
    },
    // 状态启用具体代码需微调格式去除空格
    enableHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(this.$t('prompt.info', { 'handle': '启用' }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.$http.put(this.mixinViewModuleOptions.enableURL, id ? [id] : this.dataListSelections.map(item => item.id)).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.getDataList()
            }
          })
        }).catch(() => {})
      }).catch(() => {})
    },
    // 状态停用
    stopHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(this.$t('prompt.info', { 'handle': '停用' }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        // 具体代码需微调格式去除空格
        this.$http.put(this.mixinViewModuleOptions.stopURL, id ? [id] : this.dataListSelections.map(item => item.id)).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.getDataList()
            }
          })
        }).catch(() => {})
      }).catch(() => {})
    }
  }
}

</script>
