<template>
  <el-dialog :visible.sync="visible" title="浏览" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"  :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
          <el-form-item label="附件编码" prop="code">
     <el-input v-model="dataForm.code"  readonly="true"  disabled="disabled"  placeholder="附件编码"></el-input>
</el-form-item>
                <el-form-item label="附件名称" prop="name">
     <el-input v-model="dataForm.name" disabled="disabled"  placeholder="附件名称"></el-input>
</el-form-item>
                <el-form-item label="文件夹编码" prop="projectCode">
     <el-input v-model="dataForm.projectCode" disabled="disabled" readonly="true"    placeholder="文件夹编码"></el-input>
</el-form-item>
                <el-form-item label="文件夹名称" prop="projectName">
     <el-input v-model="dataForm.projectName"  disabled="disabled" readonly="true"  placeholder="文件夹名称"></el-input>
</el-form-item>
                <el-form-item label="文件类型" prop="phase" disabled="disabled">
                    <el-radio-group v-model="dataForm.phase">
                        <el-radio :label="0">系统资料</el-radio>
                        <el-radio :label="1">其他类型</el-radio>
                    </el-radio-group>
</el-form-item>
        <el-form-item label="业务表ID"  prop="ywTableId">
            <el-input v-model="dataForm.ywTableId" disabled="disabled" readonly="true"   placeholder="业务表ID"></el-input>
        </el-form-item>
                <el-form-item label="业务表名"  prop="ywTableName">
     <el-input v-model="dataForm.ywTableName" disabled="disabled" readonly="true"   placeholder="业务表名"></el-input>
</el-form-item>
        <el-form-item label="附件ID" prop="annexId">
            <el-input v-model="dataForm.annexId" readonly="true"  disabled="disabled"  placeholder="附件Id"></el-input>
        </el-form-item>
                <el-form-item label="附件名称" prop="annexName">
     <el-input v-model="dataForm.annexName" readonly="true" disabled="disabled"   placeholder="附件名称"></el-input>
</el-form-item>
                  <el-form-item label="状态" prop="status" disabled="disabled" size="mini">
   <el-radio-group v-model="dataForm.status">
       <el-radio :label="0">停用</el-radio>
       <el-radio :label="1">正常</el-radio>
   </el-radio-group>
</el-form-item>
                    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        projectId: '',
        projectCode: '',
        projectName: '',
        phase: '',
        ywTableName: '',
        ywTableId: '',
        annexId: '',
        annexName: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        code: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        projectCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        projectName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        phase: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        ywTableName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        annexName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        status: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]

      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/basic/businessannex/${this.dataForm.id}`).then(({ data: res }) => {
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/basic/businessannex/', this.dataForm).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
