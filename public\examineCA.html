<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-08-26 17:24:45
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2022-01-21 14:25:07
-->
<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <title></title>
  <!-- 引入样式 -->
  <link rel="stylesheet" href="element-theme/default/index.css">
  <!-- 引入组件库 -->
  <link rel="stylesheet" href="css/ca.css">
  </link>
  <script src="js/polyfill.js"></script>
  <!-- <script src="js/browser.min.js"></script> -->
  <script type="text/javascript" src="js/hebcaWebPDF.js"></script>
</head>
<style>
  #wsHebcaWebPDFPlugin {
    visibility: hidden;
  }
</style>

<body>
  <div id="app">
    <div class="left">
      <div class="left_tip">
        <div style="color: #F56C6C;
        font-weight: bold;
        margin-bottom: 20px;">注意事项：</div>
        <div style="font-size: 14px;">1、请确保安装最新河北CA驱动版本，

          <el-button type="text">
            <a onclick="javascript:window.location.href='http://helper.pasiatec.com/client/HEBCA-helper-ztb.zip'"
              target="_blank">点击此处下载CA驱动</a>
          </el-button>
        </div>
        <div style="font-size: 14px;">2、若安装最新驱动仍不能签章，请
          <el-button type="text"><a
              onclick="javascript:window.location.href='http://www.hebztb.com/resource/site_1//zbt/tools/河北腾翔电子签章.exe'"
              target="_blank">点击此处下载签章插件</a>
          </el-button>
        </div>
        <div style="font-size: 14px;">3、签章时请关闭杀毒软件，否则会出现签章不成功等错误，

        </div>
      </div>
      <!-- {{active}} -->
      <el-steps direction="vertical" :active="active" :space='100'>
        <el-step v-for="(activity, index) in activities" :key="index" v-if="includesType(activity.type)">
          <el-button size='small' @click='operationsCilck(activity.fun,activity.params)' slot="title" type="primary">
            {{activity.content}}
          </el-button>
        </el-step>
      </el-steps>
      <!-- <el-timeline :reverse="false">
        <el-timeline-item v-for="(activity, index) in activities" :key="index" :timestamp="activity.timestamp">
          <el-button size='small' :type="activity.type" type="primary"></el-button>
        </el-timeline-item>
      </el-timeline> -->
    </div>
    <div class="right" v-loading="loading" v-show="showFIle">
      <iframe id='iframe' :src="url" width="100%" height="100%" style="border:none;"></iframe>
    </div>
  </div>
</body>

<script src="js/vue.js"></script>
<script src="js/jquery.js"></script>
<script src="js/eleIndex.js"></script>
<script>
  const apiURL = JSON.parse(window.sessionStorage.getItem("store")).apiURL
  const bhtURL = JSON.parse(window.sessionStorage.getItem("store")).bhtURL
  const fileView = JSON.parse(window.sessionStorage.getItem("store")).fileView
  var app = new Vue({
    el: '#app',
    letterId: '',
    form: {},
    data: {
      url: '',
      bhid: '',
      sqsId: '',
      type: '',
      issueCode: '',
      guaranteeType: '',
      isCheckLegalSign: '',
      isCheckOfficialSign: '',
      wtbhId: '',
      showFIle: false,
      btnDis: false,
      loading: false,
      active: 1,
      name: '',
      retype: '',
      activities: [
        {
          content: '盖公章',
          fun: 'SelectStampTest',
          params: ['5'],
          type: '5',
          primary: 'primary',
          type: ['0', '4', '14000']
        },
        {
          content: '盖法人章',
          fun: 'SelectStampTest2',
          params: ['4'],
          type: '4',
          primary: 'primary',
          type: ['0', '14000']
        },
      ]
    },
    watch: {
      type(a) {

      }
    },
    created: function () {
      this.getModel()
    },
    mounted: function () {
      console.log(0000)
      this.url = fileView
      // this.url = 'http://47.92.94.225:8012/onlinePreview?url='
    },
    destroyed: function () {
      console.log(1111)
      // this.url = 'http://47.92.94.225:8012/onlinePreview?url='
    },
    methods: {
      includesType(type) {
        return type.includes(this.type)
      },
      getCookie: function (name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
          let cookies = document.cookie.split(';');
          for (let i = 0; i < cookies.length; i++) {
            let cookie = cookies[i].trim();
            // 判断这个cookie的参数名是不是我们想要的
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
              cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
              break;
            }
          }
        }
        return cookieValue;
      },
      getUUID: function () {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
          return (c === 'x' ? (Math.random() * 16 | 0) : ('r&0x3' | '0x8')).toString(16)
        })
      },
      operationsCilck: function (name, params) {
        this[name].apply({}, params)
      },
      getModel: function () {
        var that = this
        this.loading = true
        this.type = window.parent.document.getElementById('type-examineCAChrome').dataset.type.replace("\"", "").replace("\"", "")
        this.letterId = window.parent.document.getElementById('type-examineCAChrome').dataset.letterid.replace("\"", "").replace("\"", "")
        this.issueCode = window.parent.document.getElementById('type-examineCAChrome').dataset.issuecode.replace("\"", "").replace("\"", "")
        console.log(window.parent.document.getElementById('type-examineCAChrome').dataset)
        this.guaranteeType = window.parent.document.getElementById('type-examineCAChrome').dataset.guaranteetype.replace("\"", "").replace("\"", "")
        // if (this.guaranteeType == 'lydbbh') {
        //   this.retype =  window.parent.document.getElementById('type-examineCAChrome').dataset.retype.replace("\"", "").replace("\"", "")
        // }
        console.log(this.issueCode, this.type, this.guaranteeType, this.letterId)
        if (this.issueCode == 'XTDB') {
          this.activities[1].type = []
        }
        if (this.type == '0') {
          this.bhid = JSON.parse(window.parent.document.getElementById(this.fomateType(this.type)).dataset.form).bhid
        } else  if(this.type == '4'){
          this.bhid = JSON.parse(window.parent.document.getElementById(this.fomateType(this.type)).dataset.form).wtht
        }else{
          this.bhid = JSON.parse(window.parent.document.getElementById(this.fomateType(this.type)).dataset.form).dbht
        }
        this.isCheckLegalSign = window.parent.document.getElementById(this.fomateType(this.type)).dataset.isCheckLegalSign
          this.isCheckOfficialSign = window.parent.document.getElementById(this.fomateType(this.type)).dataset.isCheckOfficialSign
        console.log(this.bhid)
        // that.url = 'http://47.92.94.225:8012/onlinePreview?url=' +`http://47.92.94.225:9099/hzjt-dzbh/commission/123.pdf`
        $.ajax({
          url: apiURL + '/letter/guarantee/getOssById',
          type: 'get',
          // 设置的是请求参数
          data: { ossId: this.bhid, _t: new Date().getTime() },
          // 用于设置响应体的类型
          headers: { token: this.getCookie('token') },
          dataType: 'json',
          success: function (res) {
            console.log(res)
            if (res.code !== 0) {
              that.loading = false
              return that.$message.error(res.msg);
            }

            console.log(res.data)
            // console.log(that.sqsId)
            that.bhid = res.data.id
            // if(res.data.type ===6){
            //   that.active =2
            // }
            // 一旦设置的 dataType 选项，就不再关心 服务端 响应的 Content-Type 了
            // 客户端会主观认为服务端返回的就是 JSON 格式的字符串
            // 'http://131.2.101.103:8012/onlinePreview?url=' //测试站
            // 'http://47.92.94.225:8012/onlinePreview?url=' //开评测试站
            // var viewUrl = encodeURIComponent(apiURL+'/letter/guarantee/getFileByte/'+ that.bhid +'/'+that.getUUID()+'?fullfilename='+new Date().getTime()+'.pdf')
            var viewUrl = encodeURIComponent(apiURL + '/sys/oss/minioPreview?id=' + that.bhid + '&uuid=' + that.getUUID() + '&fullfilename=' + new Date().getTime() + '.pdf')
            setTimeout(function () {
              that.loading = false
              that.showFIle = true
              that.url = fileView + viewUrl
            }, 300)
            // that.url = fileView + apiURL + res.data.sqsId.url+'?_t:'+new Date().getTime()
            // that.$message.success('生成模板成功');
          }
        })
      },
      fomateType(val){
        if( val == '0'){
            return 'examineCAChromebh'
          }
          if(val == '4'){
            return 'examineCAChromewt'
          }
          if(val == '14000'){
            return 'examineCAChromedb'
          }
      },
      //调用SelectStamp接口，用户选择印模，返回印模相关信息:
      // boolean success: true表示操作执行成功
      // string error: 操作执行失败时的错误信息
      // string stamp: 已经选择的印模Base64字符串
      // int isSM2: 表示印模对应的证书为SM2证书，1=是; 0=否
      SelectStampcallback: function (success, error, stamp, isSM2) {
        console.log(success, error, stamp, isSM2)
        //在此处添加处理代码...
        if (success) {
          var form = JSON.parse(window.parent.document.getElementById(this.fomateType(this.type)).dataset.form)

          // this.issueCode = JSON.parse(window.parent.document.getElementById('examineCAChrome').dataset.form).issuecode
          var that = this
          let params = {
            isSM2Cert: isSM2,
            sealImg: stamp,
            ossId: this.bhid,
            fileType: this.type,
            issueCode: this.issueCode,
            guaranteeType: this.guaranteeType,
            signMark: this.name
          }
          $.ajax({
            url: apiURL + '/web/ca/hashPreSealPDF',
            type: 'post',
            // 设置的是请求参数
            data: JSON.stringify(params),
            processData: false,
            // 用于设置响应体的类型
            contentType: "application/json; charset=UTF-8",
            headers: { token: this.getCookie('token') },
            dataType: 'json',
            success: function (res) {
              if (res.code != 0) {
                return that.$message.error(res.msg);
              }
              var fileHash = res.data
              SealHash(fileHash, that.SealHashcallback)
            }
          })
        }
        else {
          this.$message.error("Error:\n" + error);
          return;
        }
      },
      SealHashcallback: function (success, error, result) {
        console.log(success, error, result)
        if (success) {
          console.log(result)
          //合并pdf签章
          this.MergePdfSeal(result);
        } else {
          alert(error);
        }
      },
      MergePdfSeal: function (result) {
        var form = JSON.parse(window.parent.document.getElementById(this.fomateType(this.type)).dataset.form)
        var params = {
          "signature": result,
          ossId: this.bhid,
          fileType: this.type,
          issueCode: this.issueCode,
          guaranteeType: this.guaranteeType,
          signMark: this.name,
          _t: new Date().getTime()
        };
        this.loading = true;
        var that = this
        $.ajax({
          url: apiURL + '/web/ca/pdfSign',
          type: 'post',
          // 设置的是请求参数
          data: JSON.stringify(params),
          processData: false,
          // 用于设置响应体的类型
          contentType: "application/json; charset=UTF-8",
          headers: { token: this.getCookie('token') },
          dataType: 'json',
          success: function (res) {
            console.log(res, that.issueCode == 'XTDB', that.issueCode)
            if (that.issueCode == 'XTDB' && that.guaranteeType == 'tbbhyh') {
              console.log(1)
              that.updateXTLetterStatus()
            }
            if (res.code != 0) {
              that.loading = false
              return that.$message.error(res.msg);
            }
            var viewUrl = encodeURIComponent(apiURL + '/sys/oss/minioPreview?id=' + res.data.id + '&uuid=' + that.getUUID() + '&fullfilename=' + new Date().getTime() + '.pdf')
            setTimeout(function () {
              that.loading = false
              that.url = fileView + viewUrl
            }, 300)

            // setTimeout(()=>{ that.url = fileView + viewUrl},500)
            // that.url = fileView + apiURL + res.data.url+'?_t:'+new Date().getTime()
            this.btnDis = true
            that.$message.success('签章成功');
          }
        })
      },
      // letter/bgguaranteeletter/updateXTLetterStatus
      updateXTLetterStatus: function () {
        $.ajax({
          url: apiURL + `/letter/bgguaranteeletter/updateXTLetterStatus?id=${this.letterId}`,
          type: 'get',
          // 设置的是请求参数
          processData: false,
          // 用于设置响应体的类型
          dataType: 'json',
          headers: { token: this.getCookie('token') },
          success: function (res) {
            cosnole.log(2)
          }
        })
      },
      SelectStampTest: function (name) {
        console.log(name)
        this.name = name
        SelectStamp(this.SelectStampcallback);
      },
      SelectStampTest2: function (name) {
        console.log(name)
        this.name = name
        SelectStamp(this.SelectStampcallback);
      },
      OpenNetFileExample: function (url) {
        var filepath = url
        OpenNetFile(filepath);
      }
    },
  })
</script>

</html>
