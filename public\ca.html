<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-08-26 17:24:45
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-04-25 09:26:27
-->
<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <title></title>
  <!-- 引入样式 -->
  <link rel="stylesheet" href="element-theme/default/index.css">
  <!-- 引入组件库 -->
  <link rel="stylesheet" href="css/ca.css">
  </link>
  <script src="js/polyfill.js"></script>
  <script type="text/javascript" src="js/hebcaWebPDF.js"></script>
  <!-- <script src="js/browser.min.js"></script> -->

</head>
<style>
  #wsHebcaWebPDFPlugin {
    visibility: hidden;
  }
</style>

<body>
  <div id="app">
    <div class="left">
      <div class="left_tip">
        <div style="color: #F56C6C;
        font-weight: bold;
        margin-bottom: 20px;">注意事项：</div>
        <div style="font-size: 14px;">1、请确保安装最新河北CA驱动版本，

          <el-button type="text">
            <a onclick="javascript:window.location.href='http://helper.pasiatec.com/client/HEBCA-helper-ztb.zip'"
              target="_blank">点击此处下载CA驱动</a>
          </el-button>
        </div>
        <div style="font-size: 14px;">2、若安装最新驱动仍不能签章，请
          <el-button type="text"><a
              onclick="javascript:window.location.href='http://www.hebztb.com/resource/site_1//zbt/tools/河北腾翔电子签章.exe'"
              target="_blank">点击此处下载签章插件</a>
          </el-button>
        </div>
        <div style="font-size: 14px;">3、签章时请关闭杀毒软件，否则会出现签章不成功等错误，

        </div>
      </div>
      <!-- {{active}} -->
      <el-steps direction="vertical" :active="active" :space='100'>
        <!-- {{this.type}} -->
        <el-step v-for="(activity, index) in activities" :key="index" v-if="includesType(activity.type)">
          <el-button size='small' :disabled='activity.btnDis!==active'
            @click='operationsCilck(activity.fun,activity.params)' slot="title" type="primary">{{activity.content}}
          </el-button>
        </el-step>
      </el-steps>
      <!-- <el-timeline :reverse="false">
        <el-timeline-item v-for="(activity, index) in activities" :key="index" :timestamp="activity.timestamp">
          <el-button size='small' :type="activity.type" type="primary"></el-button>
        </el-timeline-item>
      </el-timeline> -->
    </div>
    <div class="right" v-loading="loading" v-show="showFIle">
      <iframe id='iframe' :src="url" width="100%" height="100%" style="border:none;"></iframe>
    </div>
  </div>
</body>

<script src="js/vue.js"></script>
<script src="js/jquery.js"></script>
<script src="js/eleIndex.js"></script>
<script>
  const apiURL = JSON.parse(window.sessionStorage.getItem("store")).apiURL
  const bhtURL = JSON.parse(window.sessionStorage.getItem("store")).bhtURL
  const fileView = JSON.parse(window.sessionStorage.getItem("store")).fileView

  var app = new Vue({
    el: '#app',
    form: {},
    data: {
      url: '',
      fileId: '',
      wtbhId: '',
      guaranteeApi: '/letter/guarantee/generateModel',
      type: '',
      issueCode: '',
      guaranteeType: '',
      showFIle: false,
      btnDis: false,
      loading: false,
      active: 1,
      name: '',
      activities: [
        {
          content: '生成模板',
          fun: 'getModel',
          primary: 'primary',
          btnDis: 1,
          type: ['1', '14000','4','1100','1500','1200','1700']
        },
        {
          content: '盖公章',
          fun: 'SelectStampTest',
          params: ['2'],
          primary: 'primary',
          btnDis: 2,
          type: ['1', '14000','4','1100','1500','1200','1700']
        },
        {
          content: '盖手写章',
          fun: 'SelectStampTest2',
          params: ['1'],
          primary: 'primary',
          btnDis: 3,
          type: ['14000','1100']
        },
      ]
    },
    mounted: function () {
      this.url = fileView
      this.letterId = window.parent.document.getElementById('caModel').dataset.letterid.replace("\"","").replace("\"","")
      this.type = window.parent.document.getElementById('caModel').dataset.type.replace("\"","").replace("\"","")
      this.issueCode = window.parent.document.getElementById('caModel').dataset.issuecode.replace("\"","").replace("\"","")
      this.guaranteeType = window.parent.document.getElementById('caModel').dataset.guaranteetype.replace("\"","").replace("\"","")
      console.log(this.letterId, this.type)
      this.getApi()
      // this.url = 'http://47.92.94.225:8012/onlinePreview?url='
    },
    destroyed: function () {
      console.log(1111)
      // this.url = 'http://47.92.94.225:8012/onlinePreview?url='
    },
    methods: {
      includesType: function (type) {
        console.log(type.includes(this.type) ,type)
        return type.includes(this.type)
      },
      getCookie: function (name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
          let cookies = document.cookie.split(';');
          for (let i = 0; i < cookies.length; i++) {
            let cookie = cookies[i].trim();
            // 判断这个cookie的参数名是不是我们想要的
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
              cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
              break;
            }
          }
        }
        return cookieValue;
      },
      getApi: function () {
        // var form = JSON.parse(this.form)
        this.guaranteeApi = '/letter/guarantee/generateModel'
      },
      getUUID: function () {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
          return (c === 'x' ? (Math.random() * 16 | 0) : ('r&0x3' | '0x8')).toString(16)
        })
      },
      operationsCilck: function (name, params) {
        console.log(name, params)
        this[name].apply({}, params)
      },
      getModel: function () {
        var that = this
        var params = {
          letterId: this.letterId,
          fileType: this.type
        }
        params['_t'] = +new Date().getTime()
        this.loading = true
        // '/letter/guarantee/generateGuaranteeByTable'
        // that.url = 'http://47.92.94.225:8012/onlinePreview?url=' +`http://47.92.94.225:9099/hzjt-dzbh/commission/123.pdf`
        $.ajax({
          url: apiURL + this.guaranteeApi,
          type: 'get',
          // 设置的是请求参数
          data: params,
          // 用于设置响应体的类型
          headers: { token: this.getCookie('token') },
          dataType: 'json',
          success: function (res) {
            console.log(res)
            that.loading = false
            if (res.code !== 0) {
              return that.$message.error(res.msg);
            }
            that.btnDis = false
            that.active++
            console.log(that.active)
            if(that.type == '14000'|| that.type =='4'){
              if(that.active > 3){
                this.active = 1
              }
            }else{
              if(that.active > 2){
                this.active = 1
              }
            }
            that.fileId = res.data.fileId.id

            console.log(that.fileId)
            // that.wtbhId = res.data.wthtId.id
            // 一旦设置的 dataType 选项，就不再关心 服务端 响应的 Content-Type 了
            // 客户端会主观认为服务端返回的就是 JSON 格式的字符串
            // 'http://131.2.101.103:8012/onlinePreview?url=' //测试站
            // 'http://47.92.94.225:8012/onlinePreview?url=' //开评测试站
            var viewUrl = encodeURIComponent(apiURL + '/sys/oss/minioPreview?id=' + res.data.fileId.id + '&uuid=' + that.getUUID() + '&fullfilename=委托申请书' + new Date().getTime() + '.pdf' + '&_t=' + new Date().getTime())
            // setTimeout(()=>{ that.url = fileView + viewUrl},500)
            console.log(viewUrl)
            that.url = fileView + viewUrl
            that.showFIle = true
            // that.url = fileView + apiURL + res.data.fileId.url+'?_t:'+new Date().getTime()
            console.log(that.url)
            that.$message.success('生成模板成功');
          }
        })
      },
      //调用SelectStamp接口，用户选择印模，返回印模相关信息:
      // boolean success: true表示操作执行成功
      // string error: 操作执行失败时的错误信息
      // string stamp: 已经选择的印模Base64字符串
      // int isSM2: 表示印模对应的证书为SM2证书，1=是; 0=否
      SelectStampcallback: function (success, error, stamp, isSM2) {
        //在此处添加处理代码...
        console.log(success, error, stamp, isSM2)
        if (success) {
          var that = this
          let params = {
            isSM2Cert: isSM2,
            sealImg: stamp,
            ossId: this.fileId,
            fileType:  this.type,
            issueCode:this.issueCode,
            guaranteeType:this.guaranteeType,
            signMark:this.name
          }
          $.ajax({
            url: apiURL + '/web/ca/hashPreSealPDF',
            type: 'post',
            // 设置的是请求参数
            data: JSON.stringify(params),
            processData: false,
            // 用于设置响应体的类型
            contentType: "application/json; charset=UTF-8",
            headers: { token: this.getCookie('token') },
            dataType: 'json',
            success: function (res) {
              if (res.code != 0) {
                return that.$message.error(res.msg);
              }
              var fileHash = res.data
              SealHash(fileHash, that.SealHashcallback)
            }
          })
        }
        else {
          this.$message.error("Error:\n" + error);
          return;
        }
      },
      SealHashcallback: function (success, error, result) {
        console.log(success, error, result)
        if (success) {
          console.log(result)
          //合并pdf签章
          this.MergePdfSeal(result);
        } else {
          alert(error);
        }
      },
      MergePdfSeal: function (result) {
        var params = {
          "signature": result,
          ossId: this.fileId,
          issueCode:this.issueCode,
          guaranteeType:this.guaranteeType,
          signMark:this.name
        };
        this.loading = true;
        var that = this
        $.ajax({
          url: apiURL + '/web/ca/pdfSign',
          type: 'post',
          // 设置的是请求参数
          data: JSON.stringify(params),
          processData: false,
          // 用于设置响应体的类型
          contentType: "application/json; charset=UTF-8",
          headers: { token: this.getCookie('token') },
          dataType: 'json',
          success: function (res) {
            that.loading = false
            if (res.code != 0) {
              return that.$message.error(res.msg);
            }
            var viewUrl = encodeURIComponent(apiURL + '/sys/oss/minioPreview?id=' + res.data.id + '&uuid' + that.getUUID() + '&fullfilename=sign委托申请书' + new Date().getTime() + '.pdf' + '&_t=' + new Date().getTime())
            that.url = fileView + viewUrl
            // that.active = 1
            that.active++
            if(that.type == '14000'|| that.type == '4'){
              if(that.active > 3){
                this.active = 1
              }
            }else{
              if(that.active > 2){
                this.active = 1
              }
            }
            // setTimeout(()=>{ that.url = fileView + viewUrl},500)
            // that.url = fileView + apiURL + res.data.url+'?_t:'+new Date().getTime()
            // setTimeout(()=>{ that.url = fileView + viewUrl},500)
            // that.url = fileView + apiURL + res.data.url+'?_t:'+new Date().getTime()
            // that.url = fileView + apiURL + res.data+'?_t:'+new Date().getTime()
            // console.log(window.parent.document)
            that.btnDis = true
            that.$message.success('签章成功');
            console.log(that.name == '1'&&(that.guaranteeType=='lydbbh'||that.guaranteeType=='yfkdbbh'||that.guaranteeType=='zfdbbh'),that.guaranteeType,that.name)
            if(that.name == '1'&&(that.guaranteeType=='lydbbh'||that.guaranteeType=='yfkdbbh'||that.guaranteeType=='zfdbbh')){
              that.changeConfim()
            }
          }
        })
      },
      changeConfim:function(){
        $.ajax({
          url: apiURL + '/letter/bgLetterPerformance/updateContractConfirm',
          type: 'get',
          // 设置的是请求参数
          data: {
            id:this.letterId
          },
          // 用于设置响应体的类型
          headers: { token: this.getCookie('token') },
          dataType: 'json',
          success: function (res) {

            console.log(res)

          }
        })
      },
      SelectStampTest: function (name) {
        console.log(name)
        this.name = name
        SelectStamp(this.SelectStampcallback);
      },
      SelectStampTest2: function (name) {
        console.log(name)
        this.name = name
        SelectStamp(this.SelectStampcallback);
      },
    },
  })
</script>

</html>
