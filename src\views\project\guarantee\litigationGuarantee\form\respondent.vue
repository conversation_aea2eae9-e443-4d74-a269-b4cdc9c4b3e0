<template>
  <el-row :gutter="20" style="margin:15px 20px 0;">
    <el-card class="box-card" shadow='never'>
      <div slot="header" class="clearfix">
        <span class="title">被申请人信息</span>
      </div>
      <div>
        <el-col :xs="24" :lg="24">
          <div class="formCon">
            <el-button size="small" style="margin-bottom:15px;" @click="add" type="primary">添加被申请人</el-button>
            <el-divider direction="vertical"></el-divider>
            <span>当前被申请人数：{{paramsForm.data.length}}人</span>
            <template>
              <!-- {{paramsForm.paramsRules}} -->
              <el-form :model="paramsForm" ref="paramsForm" style="margin-bottom:15px;" :rules="paramsForm.paramsRules">
                <el-table :data="paramsForm.data" height="280"  style="width: 100%">
                  <el-table-column type="index" width="50">
                  </el-table-column>
                  <el-table-column label="类型" width="110">
                    <template slot-scope="scope">
                      {{scope.row.groupOrIndividual==1?'公司':'自然人'}}
                    </template>
                  </el-table-column>
                  <el-table-column label="名称">
                    <template slot-scope="scope">
                      {{scope.row.userName}}

                      <!-- <el-form-item :prop="'data.' + scope.$index + '.userName'" :rules="paramsForm.paramsRules.userName">
                        <el-input v-model="scope.row.userName" size="mini"></el-input>
                      </el-form-item> -->
                    </template>
                  </el-table-column>
                  <el-table-column label="证件类型 / 证件号" width="200">
                    <template slot-scope="scope">
                      <div> {{scope.row.documentType=='1'?'身份证':'社会统一信用代码'}}</div>
                      <div class="line"></div>
                      <div>{{scope.row.cardNumber}}</div>
                      <!-- <el-form-item :prop="'data.' + scope.$index + '.documentType'" :rules="paramsForm.paramsRules.documentType">
                        <el-select v-model="scope.row.documentType" size="mini" disabled placeholder="请选择">
                          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                          </el-option>
                        </el-select>
                      </el-form-item> -->
                    </template>
                  </el-table-column>
                  <!-- <el-table-column label="证件号">
                    <template slot-scope="scope">
                      <el-form-item :prop="'data.' + scope.$index + '.cardNumber'" :rules="paramsForm.paramsRules.cardNumber">
                        <el-input v-model="scope.row.cardNumber" size="mini"></el-input>
                      </el-form-item>
                    </template>
                  </el-table-column> -->
                  <el-table-column label="联系方式" width="130">
                    <template slot-scope="scope">
                      {{scope.row.userTel}}
                      <!-- <el-form-item :prop="'data.' + scope.$index + '.userTel'" :rules="paramsForm.paramsRules.userTel">
                        <el-input v-model="scope.row.userTel" size="mini"></el-input>
                      </el-form-item> -->
                    </template>
                  </el-table-column>
                  <el-table-column label="法定代表人" width="110">
                    <template slot-scope="scope">
                      {{scope.row.legalPerson?scope.row.legalPerson:'-'}}

                      <!-- <el-form-item :prop="'data.' + scope.$index + '.legalPerson'" :rules="scope.row.groupOrIndividual!='1'?[]:paramsForm.paramsRules.legalPerson">
                        <el-input v-model="scope.row.legalPerson" :disabled="scope.row.groupOrIndividual!='1'" size="mini"></el-input>
                      </el-form-item> -->
                    </template>
                  </el-table-column>
                  <el-table-column label="住址" width="200">
                    <template slot-scope="scope">
                      {{scope.row.address}}
                      <!-- <el-form-item :prop="'data.' + scope.$index + '.address'" :rules="paramsForm.paramsRules.address">
                        <el-input v-model="scope.row.address" size="mini"></el-input>
                      </el-form-item> -->
                    </template>
                  </el-table-column>
                  <el-table-column width="100" label="操作" align='center' fixed="right">
                    <!-- eslint-disable-next-line vue/no-unused-vars -->
                    <template slot-scope="scope">
                      <el-button type="text" @click="edit(scope.$index)">修改</el-button>
                      <el-popconfirm title="确认删除此条数据？" @confirm="deleteCol(scope.$index)">
                         <el-button class="del" type="text" slot="reference">删除</el-button>
                      </el-popconfirm>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form>
            </template>
          </div>
        </el-col>
      </div>
    </el-card>
    <el-dialog :title="chooseData.type==='add'?'新增':'修改'+'被申请人'"  :before-close="handleClose" width="300px" append-to-body :visible.sync="dialogVisible" :close-on-click-modal='false' :close-on-press-escape='false'>
     <div>
        <diaForm ref="diaForm" :data='chooseData.data' type='2'></diaForm>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button @click="deleteData">取 消</el-button>
        <el-button type="primary" @click="queren">{{chooseData.type==='add'?'新 增':'修 改'}}</el-button>
      </span>
    </el-dialog>
  </el-row>
</template>
<script>
import { rule2 } from './rule'
import diaForm from './diaForm'
export default {
  components: { diaForm },
  data () {
    return {
      dialogVisible: false,
      options: [
        {
          value: '1',
          label: '身份证'
        },
        {
          value: '2',
          label: '社会统一信用代码'
        }
      ],
      paramsForm: {
        data: [],
        paramsRules: rule2
      },
      chooseData: {
        type: '',
        data: {}
      }
    }
  },
  watch: {
    dataForm (a) {
      this.paramsForm.data = a
    }
  },
  props: ['dataForm'],
  methods: {
    add () {
      this.$nextTick(() => {
        // this.$refs['diaForm'].resetFields()
        this.paramsForm.data.push({
          groupOrIndividual: '',
          userName: '',
          documentType: '',
          cardNumber: '',
          userTel: '',
          userType: '1',
          address: '',
          legalPerson: '',
          litigationAgent: '',
          litigationAgentTel: ''
        })
        this.chooseData = {
          type: 'add',
          data: this.paramsForm.data[this.paramsForm.data.length - 1]
        }
        this.dialogVisible = true
      })
    },
    edit (index) {
      this.chooseData = {
        type: 'edit',
        data: this.paramsForm.data[index]
      }
      this.dialogVisible = true
    },
    queren () {
      this.$nextTick(() => {
        this.$refs.diaForm.push().then((res) => {
          if (res) {
            this.dialogVisible = false
          }
        })
      })
    },
    handleClose (done) {
      done()
    },
    deleteCol (index) {
      this.paramsForm.data.splice(index, 1)
    },
    deleteData (index) {
      if (this.chooseData.type === 'add') {
        this.paramsForm.data.splice(this.paramsForm.data.length - 1)
      }
      this.dialogVisible = false
    },
    push () {
      return new Promise((resolve, reject) => {
        this.$refs['paramsForm'].validate((valid) => {
          this.$emit('getData', 'scUserListBsqr', this.paramsForm.data)
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.line {
  display: block;
  height: 1px;
  width: 100%;
  margin: 5px 0;
  background-color: #dcdfe6;
}
.del {
  margin-left: 10px;
  color: #f56c6c;
}
.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
.el-icon-close {
  cursor: pointer;
  color: #f56c6c;
  transition: all 0.3s ease-in-out;
}
.el-icon-close:hover {
  font-size: 18px;
  font-weight: bold;
}
</style>
