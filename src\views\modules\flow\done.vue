<!--
 * @Descripttion:
 * @Author: kong<PERSON>qiang
 * @Date: 2021-11-04 14:12:52
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-12-18 11:20:37
-->
<template>
  <el-card shadow="never" class="aui-card--fill">
    <el-form :inline="true" :model="dataForm" @keyup.enter="getDataList()">
       <el-form-item>
        <el-input v-model="dataForm.applyName" size="small" clearable placeholder="申请人名称"></el-input>
      </el-form-item>
      <el-form-item>
        <el-date-picker v-model="daterange" size="small" @change="change" unlink-panels type="daterange" value-format="yyyy-MM-dd" :range-separator="$t('datePicker.range')" :start-placeholder="'开始日期'"
          :end-placeholder="'结束日期'">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.bbrName" size="small" clearable placeholder="被保人名称"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button size="small" @click="getDataList()">{{ $t("query") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle" style="width: 100%">
          <el-table-column label="项目名称" header-align="center" align="center" min-width="180">
        <template slot-scope="scope">
          <div>{{scope.row.projectName?scope.row.projectName:'-'}}</div>
        </template>
      </el-table-column>
      <el-table-column prop="applyName" label="申请方名称" header-align="center" align="left" width="240">
        <template slot-scope="scope">
          <div class="title1">{{scope.row.applyName}}</div>
          <!-- <div class="title2"><span class="t_span">出具方：</span>{{scope.row.guaranteeType=='tbbhyh'?scope.row.bgIssueConfigureDTO.alias:scope.row.issueName}}</div> -->
        </template>
      </el-table-column>
      <el-table-column label="保函类型" header-align="center" align="center" width="180">
        <template slot-scope="scope">
          <el-tag size="mini"><span>{{dict(scope.row.guaranteeType)}}</span>
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="被保人名称" header-align="center" align="center" width="250">
        <template slot-scope="scope">
          <div>{{scope.row.bbrName?scope.row.bbrName:'-'}}</div>
        </template>
      </el-table-column>
      <el-table-column prop="createDate" label="申请时间" header-align="center" align="center" width="180">
      </el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button type="text" size="small" @click="taskHandle(scope.row, 'detail')">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle"> </el-pagination>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import { getDict } from '@/utils/index'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/demo/tbYHSH/auditDonePage',
        getDataListIsPage: true
      },
      daterange: [],
      dataForm: {
        applyName: '',
        bbrName: '',
        startDate: '',
        endDate: ''
      }
    }
  },
  methods: {
    dict (val) {
      var aa = getDict('电子保函类型').filter((a) => a.dictCode === val)
      return aa.length > 0 ? aa[0].dictName : '-'
    },
    change (val) {
      if (val) {
        this.$set(this.dataForm, 'startDate', val[0] + ' 00:00:00')
        this.$set(this.dataForm, 'endDate', val[1] + ' 23:59:59')
      } else {
        this.$set(this.dataForm, 'startDate', '')
        this.$set(this.dataForm, 'endDate', '')
      }
    },
    // 处理
    taskHandle (row, showType) {
      // if (!row.businessKey) {
      //   return this.$message.error(this.$t('task.businessKeyError'))
      // }
      this.handleFlowRoute(row, showType)
    }
  }
}
</script>
