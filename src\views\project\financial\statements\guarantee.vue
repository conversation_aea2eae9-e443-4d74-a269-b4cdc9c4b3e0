<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2021-03-10 18:54:25
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2024-03-27 08:48:18
-->
<template>
  <div>
    <el-table size='mini' v-loading="dataListLoading" :data="dataList" border style="width: 100%;zoom:0.9">
      <el-table-column prop="projectName" label="项目名称" header-align="center" align="center" width="200"></el-table-column>
      <!-- <el-table-column prop="name" label="保函名称" header-align="center" align="center" width="150"></el-table-column> -->
      <el-table-column label="保函类型" header-align="center" align="center" width="70">
        <template slot-scope="scope">
          <span>{{dict('电子保函类型',scope.row.guaranteeType)}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="applyName" label="申请方名称" header-align="center" align="center" ></el-table-column>
      <el-table-column prop="bbrName" label="被保人名称" header-align="center" align="center" ></el-table-column>
      <el-table-column prop="issueName" label="出具方名称" header-align="center" align="center" ></el-table-column>
      <el-table-column prop="tendereeAgent" label="招标代理" header-align="center" align="center" ></el-table-column>
      <el-table-column prop="regoinName" label="所属区域" header-align="center" align="center" ></el-table-column>
      <el-table-column prop="guaranteeAmount" label="担保金额（元）" header-align="center" align="center" width="70"></el-table-column>
      <el-table-column prop="guaranteePrice" label="保函金额（元）" header-align="center" align="center" width="70"></el-table-column>
      <el-table-column prop="policyNo" label="保单号" header-align="center" align="center" ></el-table-column>
      <el-table-column prop="letterStatus" label="保函状态" header-align="center" align="center" width="70">
        <template slot-scope="scope">
          <span>{{dict('保函状态',scope.row.letterStatus)}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="openDate" label="开具日期" header-align="center" align="center" width="90"></el-table-column>
      <el-table-column prop="invoiceStatus" label="是否开具发票" header-align="center" align="center"  width="95">
        <template slot-scope="scope">
          <span>{{scope.row.invoiceStatus=='1'?'是':'否'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="paymentMethod" label="支付方式" header-align="center" align="center" width="70">
        <template slot-scope="scope">
          <span>{{dict('支付方式',scope.row.paymentMethod)}}</span>
        </template>
      </el-table-column>
      <el-table-column  prop="invoicingRevenue" label="宏筑收入（开票）（元）" header-align="center" align="center" ></el-table-column>
      <el-table-column  prop="revenue" label="宏筑收入（元）" header-align="center" align="center" ></el-table-column>
      <!-- <el-table-column  prop="backAgentInvoicing" label="代理开票（元）" header-align="center" align="center" ></el-table-column> -->
      <el-table-column  prop="backAgent" label="代理结算金额（元）" header-align="center" align="center" ></el-table-column>
      <el-table-column  prop="regionalBonus" label="区域奖金（元）" header-align="center" align="center" ></el-table-column>
    </el-table>
    <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle"
      @current-change="pageCurrentChangeHandle">
    </el-pagination>
  </div>
</template>
<script>
import { getDict } from '@/utils/index'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins: [ mixinViewModule ],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgSettlement/cwMxPage',
        getDataListIsPage: true,
        exportURL: '/letter/bgguaranteeletter/accountDataExport',
        deleteIsBatch: true
      }
    }
  },
  // props: {
  //   dataList: Array,
  //   guaranteeType: String
  // },
  computed: {
    fomate () {
      var str = ''
      if (this.guaranteeType === 'tbdbbh') {
        str = '投标有效期'
      }
      if (this.guaranteeType === 'lydbbh') {
        str = '担保期间'
      }
      if (this.guaranteeType === 'yfkdbbh') {
        str = '有效期'
      }
      return str
    }
  },
  methods: {
    dict (name, val) {
      var aa = getDict(name).filter((a) => a.dictCode === val)
      return aa.length > 0 ? aa[0].dictName : '-'
    }
  }
}
</script>
