{"name": "hzjt-bim-ui", "version": "2.0.0", "private": true, "openGzip": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:sit": "vue-cli-service build --mode production.sit", "build:uat": "vue-cli-service build --mode production.uat", "build:prod": "vue-cli-service build --mode production.prod", "lint": "vue-cli-service lint", "et": "node_modules/.bin/et", "et:init": "node_modules/.bin/et -i", "et:list": "gulp themes"}, "dependencies": {"@babel/polyfill": "^7.8.7", "axios": "^0.18.0", "babel-plugin-component": "^1.1.1", "echarts": "^4.2.1", "element-theme": "^2.0.1", "element-theme-chalk": "^2.4.7", "element-ui": "^2.15.6", "gulp-autoprefixer": "^6.0.0", "gulp-clean-css": "^3.10.0", "gulp-load-plugins": "^1.5.0", "gulp-rename": "^1.4.0", "gulp-sass": "^4.0.2", "js-cookie": "^2.2.0", "less": "^3.12.2", "less-loader": "^6.2.0", "lodash": "^4.17.11", "moment": "^2.24.0", "qrcode": "^1.5.3", "qs": "^6.5.2", "quill": "^1.3.7", "screenfull": "^3.3.3", "svg-sprite-loader": "^4.1.1", "vue": "^2.5.17", "vue-clipboard2": "^0.3.1", "vue-count-to": "^1.0.13", "vue-i18n": "^8.1.0", "vue-quill-editor": "^3.0.6", "vue-router": "^3.0.1", "vuex": "^3.0.1", "wangeditor": "^3.1.1", "x2js": "^3.4.0"}, "devDependencies": {"@types/js-cookie": "^3.0.1", "@vue/cli-plugin-babel": "^3.10.0", "@vue/cli-plugin-eslint": "^3.10.0", "@vue/cli-service": "^3.10.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "8.0.1", "compression-webpack-plugin": "^6.0.3", "echarts": "^4.2.1", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.0.0", "node-sass": "^4.14.1", "sass": "^1.89.2", "sass-loader": "^7.0.3", "uglifyjs-webpack-plugin": "^2.2.0", "vue-template-compiler": "^2.5.17"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "@vue/standard"], "rules": {}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "engines": {"node": ">= 8.11.1", "npm": ">= 5.6.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "element-theme": {"config": "./src/element-ui/theme-variables.scss", "out": "./src/element-ui/theme", "minimize": true, "browsers": ["> 1%", "last 2 versions", "not ie <= 10"]}}