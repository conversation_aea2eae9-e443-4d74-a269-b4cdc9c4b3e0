/*
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-09-02 16:36:38
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-10-26 09:59:51
 */
import Vue from 'vue'
// import http from '@/utils/request'

Vue.component('bh', {
  props: {
    html: String,
    dataForm: Object
  },
  render (h) {
    const com = Vue.extend({
      template: this.html,
      props: {
        dataForm: Object
      }
    })

    return h(com, {
      props: {
        dataForm: this.dataForm
      }
    })
  }
})
