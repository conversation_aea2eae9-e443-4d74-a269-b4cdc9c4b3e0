<!--
 * @Descripttion:
 * @Author: kong<PERSON>qiang
 * @Date: 2021-02-01 14:53:27
 * @LastEditors: kong<PERSON>qiang
 * @LastEditTime: 2021-02-03 15:12:43
-->
<!--
 * @Descripttion:
 * @Author: kong<PERSON>qiang
 * @Date: 2020-08-06 18:47:13
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-02-01 14:39:40
-->
<template>
  <el-drawer :visible.sync="visible"  :close-on-click-modal="false" :close-on-press-escape="false" size="500px">
     <div class="litE" >
    <auditRecords :dataForm='dataForm' ref="auditRecords"></auditRecords>
     </div>
    <div style="text-align:center; margin-top:15px;">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
    </div>
  </el-drawer>
</template>

<script>
import auditRecords from './auditRecords'
export default {
  data () {
    return {
      visible: false,
      dataForm: {}
    }
  },
  components: {
    auditRecords
  },
  props: {},

  mounted () {},
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.auditRecords.getList()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 22px !important;
}
.litE{
  height: calc(100vh - 140px);
  overflow-y: scroll;
  padding:0 20px;
}
</style>
