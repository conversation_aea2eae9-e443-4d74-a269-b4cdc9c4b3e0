<template>
  <el-dialog :visible.sync="visible" title="信息详情" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" v-loading='loading' ref="dataForm" :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="colConfig">
          <el-form-item label="机构名称" prop="name" label-width="190px">
            <span>{{dataForm.name}}</span>
          </el-form-item>
          <el-form-item label="机构联系电话" prop="phoneNumber" label-width="190px">
            <span>{{dataForm.phoneNumber}}</span>
          </el-form-item>
          <el-form-item label="机构网站" prop="website" label-width="190px">
            <span>{{dataForm.website}}</span>
          </el-form-item>
          <el-form-item label="机构类型" prop="orgType" label-width="190px">
            <span v-if="dataForm.orgType == 1">保险</span>
            <span v-if="dataForm.orgType == 2">2银行</span>
            <span v-if="dataForm.orgType == 3">担保机构</span>
          </el-form-item>
          <el-form-item label="法定代表人" prop="corporation" label-width="190px">
            <span>{{dataForm.corporation}}</span>
          </el-form-item>
          <el-form-item label="邮箱" prop="email" label-width="190px">
            <span>{{dataForm.email}}</span>
          </el-form-item>
          <el-form-item label="传真" prop="fax" label-width="190px">
            <span>{{dataForm.fax}}</span>
          </el-form-item>
          <el-form-item label="LOGO" prop="logoUrl" label-width="190px">
            <span><img style="width:100%;" :src="url"><img></span>
          </el-form-item>
          <el-form-item label="邮编" prop="postcode" label-width="190px">
            <span>{{dataForm.postcode}}</span>
          </el-form-item>
          <el-form-item label="地址" prop="address" label-width="190px">
            <span>{{dataForm.address}}</span>
          </el-form-item>
          <el-form-item label="联系人" prop="linkman" label-width="190px">
            <span>{{dataForm.linkman}}</span>
          </el-form-item>

        </el-col>
        <el-col :xs="24" :lg="colConfig">
          <el-form-item label="联系人电话" prop="linkmanTel" label-width="190px">
            <span>{{dataForm.linkmanTel}}</span>
          </el-form-item>
          <el-form-item label="担保金额限制(元)" prop="guaranteeLimitAmount" label-width="190px">
            <span>{{dataForm.guaranteeLimitAmount}}</span>
          </el-form-item>
          <el-form-item label="保函审批时长" prop="auditingHour" label-width="190px">
            <span>{{dataForm.auditingHour}}</span>
          </el-form-item>
          <!-- <el-form-item label="可开具保函编码" prop="guaranteeTypecodes" label-width="190px">
            <span>{{dataForm.guaranteeTypecodes}}</span>
          </el-form-item> -->
          <el-form-item label="可开具保函" prop="guaranteeTypenames" label-width="190px">
            <span>{{dataForm.guaranteeTypenames}}</span>
          </el-form-item>
          <el-form-item label="开户行名称" prop="bankAccountName" label-width="190px">
            <span>{{dataForm.bankAccountName}}</span>
          </el-form-item>
          <el-form-item label="银行账户号码" prop="bankAccountNo" label-width="190px">
            <span>{{dataForm.bankAccountNo}}</span>
          </el-form-item>
          <el-form-item label="银行名称" prop="bankName" label-width="190px">
            <span>{{dataForm.bankName}}</span>
          </el-form-item>
          <el-form-item label="银行编码" prop="bankNo" label-width="190px">
            <span>{{dataForm.bankNo}}</span>
          </el-form-item>
          <el-form-item label="是否收取平台使用费" prop="isIncludePlatformFee" label-width="190px">
            <span v-if="dataForm.isIncludePlatformFee == 1">是</span>
            <span v-if="dataForm.isIncludePlatformFee == 0">否</span>
          </el-form-item>
          <el-form-item label="排序" prop="orders" label-width="190px">
            <span>{{dataForm.orders}}</span>
          </el-form-item>
          <el-form-item label="状态" prop="status" label-width="190px">
            <span v-if="dataForm.status == 0">未提交</span>
            <span v-if="dataForm.status == 1">待审核</span>
            <span v-if="dataForm.status == 2">已认证</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="机构描述：" prop="description" label-width="190px">
            <span v-html="dataForm.description"></span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template slot="footer">
      <el-button type="primary" @click="visible = false">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      colConfig: 2, // 列配置 1 2列
      visible: false,
      loading: false,
      utl: '',
      dataForm: {
        id: '',
        code: '',
        name: '',
        corporation: '',
        website: '',
        orgType: '',
        email: '',
        phoneNumber: '',
        fax: '',
        logoUrl: '',
        postcode: '',
        address: '',
        linkman: '',
        linkmanTel: '',
        guaranteeLimitAmount: '',
        description: '',
        auditingHour: '',
        guaranteeTypecodes: '',
        guaranteeTypenames: '',
        bankAccountName: '',
        bankAccountNo: '',
        bankName: '',
        bankNo: '',
        isIncludePlatformFee: '',
        orders: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        auditId: ''
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.loading = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http
        .get(`/letter/bgguaranteeissue/${this.dataForm.id}`)
        .then(({ data: res }) => {
          this.loading = false

          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
          if (this.dataForm.logoUrl != null) {
            this.url = window.SITE_CONFIG['apiURL'] +
                '/sys/oss/localhostDownload/' +
                this.dataForm.logoUrl
          }
        })
        .catch(() => {})
    }
  }
}
</script>
