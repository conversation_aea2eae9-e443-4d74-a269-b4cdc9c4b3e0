<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts' // echarts theme
import resize from './mixins/resize'
require('echarts/theme/macarons')

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler (val) {
        // console.log(val)
        this.setOptions(val)
      }
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy () {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart () {
      this.chart = echarts.init(this.$el, 'macarons')
      // // console.log(this.chartData)
      this.setOptions(this.chartData)
    },
    setOptions ({ receivedServiceFee, biddingFee, unitNum, projectNum, nameList, receivableFee, uncollectedServiceFee } = {}) {
      this.chart.setOption({
        title: {
          text: '各办事处当天情况',
          textStyle: {
            color: '#a3c1ff'
          },
          padding: [0, 0, 10, 100]
        },
        xAxis: {
          data: nameList,
          boundaryGap: false,
          axisTick: {
            show: false
          }
        },
        grid: {
          left: 10,
          right: 10,
          bottom: 20,
          top: 30,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        yAxis: {
          axisTick: {
            show: false
          }
        },
        legend: {
          data: ['项目总数', '投标企业总数', '标书费收取总额', '已收服务费', '应收服务费', '未收服务费']
        },
        series: [{
          name: '项目总数',
          itemStyle: {
            normal: {
              color: '#FF005A',
              lineStyle: {
                color: '#FF005A',
                width: 2
              }
            }
          },
          smooth: true,
          type: 'line',
          data: projectNum,
          animationDuration: 2800,
          animationEasing: 'cubicInOut'
        },
        {
          name: '投标企业总数',
          smooth: true,
          type: 'line',
          itemStyle: {
            normal: {
              color: '#3888fa',
              lineStyle: {
                color: '#3888fa',
                width: 2
              },
              areaStyle: {
                color: '#f3f8ff'
              }
            }
          },
          data: unitNum,
          animationDuration: 2800,
          animationEasing: 'quadraticOut'
        },

        {
          name: '标书费收取总额',
          smooth: true,
          type: 'line',
          itemStyle: {
            normal: {
              color: '#E6A23C',
              lineStyle: {
                color: '#E6A23C',
                width: 2
              },
              areaStyle: {
                color: '#f3f8ff'
              }
            }
          },
          data: biddingFee,
          animationDuration: 2800,
          animationEasing: 'quadraticOut'
        }, {
          name: '已收服务费',
          smooth: true,
          type: 'line',
          itemStyle: {
            normal: {
              color: '#F56C6C',
              lineStyle: {
                color: '#F56C6C',
                width: 2
              },
              areaStyle: {
                color: '#f3f8ff'
              }
            }
          },
          data: receivedServiceFee,
          animationDuration: 2800,
          animationEasing: 'quadraticOut'
        }, {
          name: '应收服务费',
          smooth: true,
          type: 'line',
          itemStyle: {
            normal: {
              color: '#ffde33',
              lineStyle: {
                color: '#ffde33',
                width: 2
              },
              areaStyle: {
                color: '#f3f8ff'
              }
            }
          },
          data: receivableFee,
          animationDuration: 2800,
          animationEasing: 'quadraticOut'
        }, {
          name: '未收服务费',
          smooth: true,
          type: 'line',
          itemStyle: {
            normal: {
              color: '#ff9933',
              lineStyle: {
                color: '#ff9933',
                width: 2
              },
              areaStyle: {
                color: '#f3f8ff'
              }
            }
          },
          data: uncollectedServiceFee,
          animationDuration: 2800,
          animationEasing: 'quadraticOut'
        }]
      })
    }
  }
}
</script>
