<template>
  <div class="card">
    <el-card class="box-card">
      <steps :active='3'></steps>
      <el-card class="box-card" shadow="never">
        <div style="padding: 3px;">
          <div style="padding: 3px; border-color: grey">
            <object ref="webPDFObj" classid="CLSID:6EE0E832-A96C-480C-AE0A-EB35A9BB9651" width="100%" style="height:500px;float: left">
            </object>
          </div>
        </div>
      </el-card>
    </el-card>
    <div class="foot">
      <span>
        <el-button type="primary" @click="backStep()" plain>上一步</el-button>
        <el-button type="primary" @click="submitHandle()" v-if="letterId !== ''">下一步</el-button>
      </span>
    </div>
  </div>
</template>
<script>
import steps from '@/views/project/guarantee/components/step'
export default {
  data () {
    return {
      id: '',
      webPDFObj: '',
      src:
        `${window.SITE_CONFIG['apiURL']}` +
        '/letter/bgguaranteetemplate/applyPDF2/',
      savesrc:
        `${window.SITE_CONFIG['apiURL']}` +
        '/letter/bgguaranteetemplate/savePDF2/',
      height: '900px'
    }
  },
  created () {
    this.id = this.$route.params.tid
  },
  mounted () {
    this.init()
    console.log(this.src)
  },
  components: { steps },
  methods: {
    init () {
      try {
        this.$nextTick(() => {
          this.webPDFObj = this.$refs['webPDFObj']
          this.webPDFObj.OpenNetFile(this.src + this.id + '?wtsCode=entrustment_contract')
          this.webPDFObj.ShowToolbar(true)
        })
      } catch (e) {
        alert(e)
        // console.log(e)
      }
    },
    backStep () {
      this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
        item => item.name !== 'pdf'
      )
      this.$router.push({
        name: this.$route.params.backName,
        params: {
          type: this.$route.params.type,
          id: this.$route.params.tid
        }
      })
    },
    ShowExceptionError (e) {
      if (e.name === 'TypeError') {
        alert('JS错误：' + e.message)
      } else {
        try {
          alert('WebPDF错误：' + this.webPDFObj.GetErrMsg())
        } catch (ee) {
          alert('JS内部错误：' + ee.message)
        }
      }
    },
    submitHandle () {
      try {
        this.webPDFObj.SealKeyWord('【授权企业盖章位置】', 1)
        var result = this.webPDFObj.SaveNetFile(this.savesrc + this.id)
        if (result === null || result === '') {
          this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
            item => item.name !== 'pdf2'
          )
          this.$http
            .get(`/letter/bgguaranteeapply/submitById/${this.id}`)
            .then(({ data: res }) => {
              this.$loading().close()

              const audit = this.$router.resolve({
                name: 'awaitingAudit',
                query: { id: this.id }
              })
              this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
                item => item.name !== 'pdf'
              )
              this.$router.push({
                name: 'letter-bgguaranteeletterSqf'
              })
              window.open(audit.href, '_blank')
            })
        } else {
          alert(result)
        }
      } catch (e) {
        this.ShowExceptionError(e)
        return false
      }
      return true
    },
    changePdfPage (val) {
      console.log(val)
      if (val === 0 && this.currentPage > 1) {
        this.currentPage--
        console.log(this.currentPage)
      }
      if (val === 1 && this.currentPage < this.pageCount) {
        this.currentPage++
        console.log(this.currentPage)
      }
    },
    loadPdfHandler (e) {
      this.currentPage = 1
    }
  }
}
</script>
<style lang="scss" scoped>
.card {
  min-width: 900px;
  max-width: 1200px;
  margin: 15px auto;
  margin-bottom: 65px;
}
.foot {
  width: 100%;
  background: #fff;
  position: fixed;
  bottom: 0;
  padding: 10px 0;
  border-top: 1px solid #ddd;
  background: #f9f9f9;
  text-align: center;
  left: 0px !important;
  z-index: 999;
  span {
    text-align: center;
  }
}
</style>
