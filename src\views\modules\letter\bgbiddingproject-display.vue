<template>
  <el-dialog :visible.sync="visible" title="信息详情" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" v-loading='loading' ref="dataForm" :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="colConfig">
          <!-- <el-form-item label="标段编码" prop="code" label-width="190px">
            <span>{{dataForm.code}}</span>
          </el-form-item> -->
          <el-form-item label="标段名称" prop="name" label-width="190px">
            <span>{{dataForm.name}}</span>
          </el-form-item>
          <!-- <el-form-item label="开工日期"   prop="bidOpenDate" label-width="190px">
            <span>{{dataForm.bidOpenDate}}</span>
          </el-form-item> -->
          <el-form-item label="招标人" prop="tenderee" label-width="190px">
            <span>{{dataForm.tenderee}}</span>
          </el-form-item>
          <el-form-item label="投标工程名" prop="biddingName" label-width="190px">
            <span>{{dataForm.biddingName}}</span>
          </el-form-item>
          <el-form-item label="标段开始日期" prop="bidStartDate" label-width="190px">
            <span>{{dataForm.bidStartDate}}</span>
          </el-form-item>
          <el-form-item label="状态" prop="status" label-width="190px">
            <span v-if="dataForm.status == 0">停用</span>
            <span v-if="dataForm.status == 1">正常</span>
          </el-form-item>
          <el-form-item label="备注信息" prop="remars" label-width="190px">
            <span>{{dataForm.remars}}</span>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :lg="colConfig">
<!--          <el-form-item label="标段ID" prop="biddingid" label-width="190px">-->
<!--            <span>{{dataForm.biddingid}}</span>-->
<!--          </el-form-item>-->
          <el-form-item label="担保金额" prop="guaranteeAmount" label-width="190px">
            <span>{{dataForm.guaranteeAmount}}</span>
          </el-form-item>
          <el-form-item label="招标代理公司" prop="tendereeAgent" label-width="190px">
            <span>{{dataForm.tendereeAgent}}</span>
          </el-form-item>
          <el-form-item label="所属项目部" prop="biddingToOffces" label-width="190px">
            <span>{{dataForm.biddingToOffces}}</span>
          </el-form-item>
          <el-form-item label="标段结束日期" prop="bidEndDate" label-width="190px">
            <span>{{dataForm.bidEndDate}}</span>
          </el-form-item>
          <el-form-item label="交易平台编码" prop="platformCode" label-width="190px">
            <span>{{dataForm.platformCode}}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template slot="footer">
      <el-button type="primary" @click="visible = false">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      colConfig: 2, // 列配置 1 2列
      visible: false,
      loading: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        bidOpenDate: '',
        biddingid: '',
        guaranteeAmount: '',
        tenderee: '',
        tendereeAgent: '',
        biddingName: '',
        biddingToOffces: '',
        remars: '',
        bidStartDate: '',
        bidEndDate: '',
        platformCode: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.loading = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http
        .get(`/letter/bgbiddingproject/${this.dataForm.id}`)
        .then(({ data: res }) => {
          this.loading = false

          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
        })
        .catch(() => {})
    }
  }
}
</script>
