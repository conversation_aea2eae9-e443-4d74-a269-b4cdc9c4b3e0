<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-08-19 15:14:36
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-09-02 18:13:35
-->
<template>
  <el-card class="box-card box" shadow='never'>
    <!-- <el-button size="mini" @click="zoom+0.1" round>放大</el-button>
    <el-button size="mini" @click="zoom-0.1" round>缩小</el-button> -->
    <el-scrollbar class="height" >
      <div class="model" id="tenModel" :style="`zoom: ${zoom};`">
        <bh></bh>
      </div>
    </el-scrollbar>
  </el-card>
</template>
<script>
import './bh'
export default {
  data () {
    return {
      model: '',
      zoom: 0.9,
      insurancecode: '',
      src: ''
    }
  },
  components: {
  },
  created () {
    this.src = process.env.BASE_URL
    this.getModel()
    this.insurancecode = this.$route.params.insuranceCode
  },
  methods: {
    async getModel () {
      let { data } = await this.$http.get('letter/bgguaranteetemplate/getInfoByIssueCodeAndType/' + this.$route.params.insuranceCode + '/0')
      this.model = data.data.content
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  margin-bottom: 15px;
}
.box {
  height: calc(100vh - 205px);
}
.height {
  height: 100%;
}
.model {
  margin: 2px;
  padding: 1.5em;

  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.4);
}
</style>
