<!--
 * @Descripttion:
 * @Author: kong<PERSON>qiang
 * @Date: 2020-10-13 09:38:40
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-02-01 16:10:33
-->
<template>
  <el-col :span="20">
    <el-col :span="24" class="con-title">
      编号信息
    </el-col>

    <el-col :xs="24" :lg="12">
      <el-form-item prop="contractNo" label="合同编号">
        {{dataForm.contractNo}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="12">
      <el-form-item prop="guaranteeNo" label="保函编号">
        {{dataForm.guaranteeNo}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="12">
      <el-form-item prop="approvalNo" label="审批表编号">
        {{dataForm.approvalNo}}
      </el-form-item>
    </el-col>
  </el-col>

</template>
<script>
export default {
  props: {
    dataForm: Object
  },
  data () {
    return {}
  },
  methods: {}
}
</script>
<style lang="scss" scoped>
.con-title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-bottom: 10px;
}
</style>
