<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"  :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="colConfig">
          <el-form-item label="编码" prop="code">
            <el-input v-model="dataForm.code" placeholder="编码"></el-input>
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="名称"></el-input>
          </el-form-item>
          <el-form-item label="保函类型" prop="guaranteeType">
            <el-input v-model="dataForm.guaranteeType" placeholder="保函类型"></el-input>
          </el-form-item>
          <el-form-item label="保函结束日期" prop="validEndDate">
            <el-input v-model="dataForm.validEndDate" placeholder="保函结束日期"></el-input>
          </el-form-item>
          <el-form-item label="保函开始日期" prop="validStartDate">
            <el-input v-model="dataForm.validStartDate" placeholder="保函开始日期"></el-input>
          </el-form-item>
          <el-form-item label="保函开具日期" prop="openDate">
            <el-input v-model="dataForm.openDate" placeholder="保函开具日期"></el-input>
          </el-form-item>
          <el-form-item label="担保金额" prop="guaranteeAmount">
            <el-input-number placeholder="请输入担保金额" class="wd240" v-model="dataForm.guaranteeAmount" controls-position="right" :precision="0" :min="0" :max="10000000"></el-input-number>
          </el-form-item>
          <el-form-item label="保函费用" prop="guaranteePrice">
            <el-input v-model="dataForm.guaranteePrice" placeholder="保函费用"></el-input>
          </el-form-item>
         <!-- <el-form-item label="保函金额" prop="letterPrice">
            <el-input v-model="dataForm.letterPrice" placeholder="保函金额"></el-input>
          </el-form-item>
          <el-form-item label="平台服务费" prop="platformFee">
            <el-input v-model="dataForm.platformFee" placeholder="平台服务费"></el-input>
          </el-form-item>-->
          <el-form-item label="保函下载地址" prop="letterUrl">
            <el-input v-model="dataForm.letterUrl" placeholder="保函下载地址"></el-input>
          </el-form-item>
          <el-form-item label="申请方id" prop="applyId">
            <el-input v-model="dataForm.applyId" placeholder="申请方id"></el-input>
          </el-form-item>
          <el-form-item label="申请方编码" prop="applyCode">
            <el-input v-model="dataForm.applyCode" placeholder="申请方编码"></el-input>
          </el-form-item>
          <el-form-item label="申请方名称" prop="applyName">
            <el-input v-model="dataForm.applyName" placeholder="申请方名称"></el-input>
          </el-form-item>
          <el-form-item label="被保人id" prop="bbrId">
            <el-input v-model="dataForm.bbrId" placeholder="被保人id"></el-input>
          </el-form-item>
          <el-form-item label="被保人编码" prop="bbrCode">
            <el-input v-model="dataForm.bbrCode" placeholder="被保人编码"></el-input>
          </el-form-item>
          <el-form-item label="被保人名称" prop="bbrName">
            <el-input v-model="dataForm.bbrName" placeholder="被保人名称"></el-input>
          </el-form-item>
          <el-form-item label="出具方id" prop="issueId">
            <el-input v-model="dataForm.issueId" placeholder="出具方id"></el-input>
          </el-form-item>
          <el-form-item label="出具方编码" prop="issueCode">
            <el-input v-model="dataForm.issueCode" placeholder="出具方编码"></el-input>
          </el-form-item>
          <el-form-item label="出具方名称" prop="issueName">
            <el-input v-model="dataForm.issueName" placeholder="出具方名称"></el-input>
          </el-form-item>
          <el-form-item label="使用方id" prop="platformId">
            <el-input v-model="dataForm.platformId" placeholder="使用方id"></el-input>
          </el-form-item>
          <el-form-item label="使用方编码" prop="platformCode">
            <el-input v-model="dataForm.platformCode" placeholder="使用方编码"></el-input>
          </el-form-item>
          <el-form-item label="使用方名称" prop="platformName">
            <el-input v-model="dataForm.platformName" placeholder="使用方名称"></el-input>
          </el-form-item>
          <el-form-item label="保险id" prop="insuranceId">
            <el-input v-model="dataForm.insuranceId" placeholder="保险id"></el-input>
          </el-form-item>
          <el-form-item label="保险编码" prop="insuranceCode">
            <el-input v-model="dataForm.insuranceCode" placeholder="保险编码"></el-input>
          </el-form-item>
          <el-form-item label="保险名称" prop="insuranceName">
            <el-input v-model="dataForm.insuranceName" placeholder="保险名称"></el-input>
          </el-form-item>
          <el-form-item label="发票信息" prop="invoiceId">
            <el-input v-model="dataForm.invoiceId" placeholder="发票信息"></el-input>
          </el-form-item>
          <el-form-item label="收件信息" prop="mailingId">
            <el-input v-model="dataForm.mailingId" placeholder="收件信息"></el-input>
          </el-form-item>
          <el-form-item label="支付信息" prop="payId">
            <el-input v-model="dataForm.payId" placeholder="支付信息"></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="status" size="mini">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="0">停用</el-radio>
              <el-radio :label="1">正常</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        guaranteeType: '',
        validEndDate: '',
        validStartDate: '',
        openDate: '',
        guaranteeAmount: '',
        guaranteePrice: '',
        letterPrice: '',
        platformFee: '',
        letterUrl: '',
        applyId: '',
        applyCode: '',
        applyName: '',
        bbrId: '',
        bbrCode: '',
        bbrName: '',
        issueId: '',
        issueCode: '',
        issueName: '',
        platformId: '',
        platformCode: '',
        platformName: '',
        insuranceId: '',
        insuranceCode: '',
        insuranceName: '',
        invoiceId: '',
        mailingId: '',
        payId: '',
        serpriceId: '',
        changeId: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        code: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        guaranteeType: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        validEndDate: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        validStartDate: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        openDate: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        guaranteeAmount: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        guaranteePrice: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        letterPrice: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        platformFee: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        letterUrl: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        applyId: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        applyCode: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        applyName: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        bbrId: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        bbrCode: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        bbrName: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        issueId: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        issueCode: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        issueName: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        platformId: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        platformCode: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        platformName: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        insuranceId: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        insuranceCode: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        insuranceName: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        invoiceId: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        mailingId: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        payId: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        status: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http
        .get(`/letter/bgguaranteeletter/${this.dataForm.id}`)
        .then(({ data: res }) => {
          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
        })
        .catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate(valid => {
          if (!valid) {
            return false
          }
          this.$http[!this.dataForm.id ? 'post' : 'put'](
            '/letter/bgguaranteeletter/',
            this.dataForm
          )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            })
            .catch(() => {})
        })
      },
      1000,
      { leading: true, trailing: false }
    )
  }
}
</script>
