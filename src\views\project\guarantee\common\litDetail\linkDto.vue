<!--
 * @Descripttion:
 * @Author: kong<PERSON>qiang
 * @Date: 2020-10-13 09:38:40
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-08-19 16:33:40
-->
<template>
  <el-col :span="20">
    <el-col :span="24" class="con-title">
      用户信息
    </el-col>
    <el-col :xs="24" :lg="12">
      <el-form-item prop="linkman" label="联系人">
        {{dataForm.linkDto.linkman}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="12">
      <el-form-item prop="linkmanTel" label="联系方式">
        {{dataForm.linkDto.linkmanTel}}
      </el-form-item>
    </el-col>
      <el-col :xs="24" :lg="12">
      <el-form-item prop="dataSource" label="来源">
        {{dataForm.dataSource}}
        {{dataForm.dataSource=='hreb'?'宏融e保':'亿丰达'}}
      </el-form-item>
    </el-col>
  </el-col>

</template>
<script>
export default {
  props: {
    dataForm: Object
  },
  data () {
    return {}
  },
  methods: {}
}
</script>
<style lang="scss" scoped>
.con-title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-bottom: 10px;
}
</style>
