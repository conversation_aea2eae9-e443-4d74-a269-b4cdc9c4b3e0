<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"  :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="colConfig">
          <el-form-item label="名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="名称"></el-input>
          </el-form-item>
          <el-form-item label="证件类型" prop="certType">
            <el-select v-model="dataForm.certType" clearable placeholder="请选择证件类型">
              <el-option v-for="item in certTypeOptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="证件号码" prop="certCode">
            <el-input v-model="dataForm.certCode" placeholder="证件号码"></el-input>
          </el-form-item>
          <el-form-item label="证件有效期" prop="certTerm">
            <el-date-picker v-model="dataForm.certTerm" type="datetime" value-format="yyyy-MM-dd" placeholder="请选择证件有效期"></el-date-picker>
          </el-form-item>
          <el-form-item label="证件有效期类型" prop="certTermType">
            <el-select v-model="dataForm.certTermType" clearable placeholder="请选择证件有效期类型">
              <el-option v-for="item in certTermTypeOptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="联系人" prop="linkman">
            <el-input v-model="dataForm.linkman" placeholder="联系人"></el-input>
          </el-form-item>
          <el-form-item label="联系人电话" prop="linkmanTel">
            <el-input v-model="dataForm.linkmanTel" placeholder="联系人电话"></el-input>
          </el-form-item>

          <el-form-item label="法定代表人" prop="corporation">
            <el-input v-model="dataForm.corporation" placeholder="法定代表人"></el-input>
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="dataForm.email" placeholder="邮箱"></el-input>
          </el-form-item>
          <el-form-item label="是否VIP" prop="isVip">
            <el-radio-group v-model="dataForm.isVip">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :lg="colConfig">
          <el-form-item label="公司电话" prop="phoneNumber">
            <el-input v-model="dataForm.phoneNumber" placeholder="公司电话"></el-input>
          </el-form-item>
          <el-form-item label="传真" prop="fax">
            <el-input v-model="dataForm.fax" placeholder="传真"></el-input>
          </el-form-item>
          <el-form-item label="邮编" prop="postcode">
            <el-input v-model="dataForm.postcode" placeholder="邮编"></el-input>
          </el-form-item>
          <el-form-item label="所属平台编码" prop="platformCode">
            <el-input v-model="dataForm.platformCode" placeholder="所属平台编码"></el-input>
          </el-form-item>
          <el-form-item label="地址" prop="address">
            <el-input type="textarea" rows="3" v-model="dataForm.address" placeholder="地址"></el-input>
          </el-form-item>
          <!-- <el-form-item label="描述" prop="description">
            <el-input type="textarea" rows="3" v-model="dataForm.description" placeholder="描述"></el-input>
          </el-form-item> -->
          <el-form-item label="注册地址" prop="registerAddress">
            <el-input type="textarea" rows="3" v-model="dataForm.registerAddress" placeholder="注册地址"></el-input>
          </el-form-item>
          <el-form-item label="注册资金" prop="registerCapital">
            <el-input v-model="dataForm.registerCapital" placeholder="注册资金"></el-input>
          </el-form-item>
          <el-form-item label="注册时间" prop="registerTime">
            <el-date-picker v-model="dataForm.registerTime" type="datetime" value-format="yyyy-MM-dd" placeholder="请选择注册时间"></el-date-picker>
          </el-form-item>

          <!-- <el-form-item label="状态" prop="status" size="mini">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="0">未提交</el-radio>
              <el-radio :label="1">待审核</el-radio>
              <el-radio :label="2">已认证</el-radio>
            </el-radio-group>
          </el-form-item> -->
        </el-col>
      </el-row>
      <el-form-item label="描述" prop="description">
            <!-- 富文本编辑器, 容器 -->
            <div id="J_quillEditor"></div>
            <!-- 自定义上传图片功能 (使用element upload组件) -->
            <el-form-item label="描述" hidden prop="description">
              <!--<textarea  rows="10" cols="210" placeholder="请输入" maxlength="1000" @input="descInput" v-model="dataForm.description" />-->
              <span class="numberV"></span>
            </el-form-item>
          </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确认</el-button>
      <!-- <el-button type="primary" v-if="dataForm.id" @click="dataSubmitHandle()">提交</el-button> -->
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import 'quill/dist/quill.snow.css'
import Quill from 'quill'
export default {
  data () {
    return {
      colConfig: 2, // 列配置 1 2列
      visible: false,
      getDicListURL: '/sys/dict/type/',
      quillEditor: null,
      quillEditorToolbarOptions: [
        ['bold', 'italic', 'underline', 'strike'],
        [{ header: 1 }, { header: 2 }],
        [{ list: 'ordered' }, { list: 'bullet' }],
        [{ script: 'sub' }, { script: 'super' }],
        [{ indent: '-1' }, { indent: '+1' }],
        [{ direction: 'rtl' }],
        [{ size: ['small', false, 'large', 'huge'] }],
        [{ header: [1, 2, 3, 4, 5, 6, false] }],
        [{ color: [] }, { background: [] }],
        [{ font: [] }],
        [{ align: [] }],
        ['clean']
      ],
      dataForm: {
        id: '',
        code: '',
        name: '',
        certType: '',
        certCode: '',
        certTerm: '',
        certTermType: '',
        linkman: '',
        linkmanTel: '',
        corporation: '',
        email: '',
        phoneNumber: '',
        fax: '',
        postcode: '',
        platformCode: '',
        address: '',
        description: '',
        registerAddress: '',
        registerCapital: '',
        registerTime: '',
        isVip: '',
        deptId: '',
        status: '',
        creator: '',
        createDate: '',
        updater: '',
        auditId: '',
        updateDate: ''
      },
      certTypeOptions: [],
      certTermTypeOptions: []
    }
  },
  computed: {
    dataRule () {
      return {
        code: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        certType: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        certCode: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        certTerm: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        certTermType: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        linkman: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        linkmanTel: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        status: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.getCertTypeInfo()
      this.getCertTermTypeInfo()
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
        if (this.quillEditor) {
          this.quillEditor.deleteText(0, this.quillEditor.getLength())
        } else {
          this.quillEditorHandle()
        }
      })
    },
    // 获取证件类型
    getCertTypeInfo () {
      this.$http
        .get(this.getDicListURL + 'certTypeTT')
        .then(({ data: res }) => {
          this.certTypeOptions = {
            ...this.certTypeOptions,
            ...res.data.list
          }
        })
        .catch(() => {})
    },
    // 获取证件有效期类型
    getCertTermTypeInfo () {
      this.$http
        .get(this.getDicListURL + 'certTermType')
        .then(({ data: res }) => {
          this.certTermTypeOptions = {
            ...this.certTermTypeOptions,
            ...res.data.list
          }
        })
        .catch(() => {})
    },
    // 获取信息
    getInfo () {
      this.$http
        .get(`/letter/bgguaranteeapply/${this.dataForm.id}`)
        .then(({ data: res }) => {
          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
          this.quillEditor.root.innerHTML = this.dataForm.description
        })
        .catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate(valid => {
          if (!valid) {
            return false
          }
          this.$http[!this.dataForm.id ? 'post' : 'put'](
            '/letter/bgguaranteeapply/',
            this.dataForm
          )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            })
            .catch(() => {})
        })
      },
      1000,
      { leading: true, trailing: false }
    ),
    // 富文本编辑器
    quillEditorHandle () {
      this.quillEditor = new Quill('#J_quillEditor', {
        modules: {
          toolbar: this.quillEditorToolbarOptions
        },
        theme: 'snow'
      })
      // 监听内容变化，动态赋值
      this.quillEditor.on('text-change', () => {
        this.dataForm.description = this.quillEditor.root.innerHTML
      })
    },
    // 表单提交进入流程审核环节
    dataSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate(valid => {
          if (!valid) {
            return false
          }
          this.$http['put']('/letter/bgguaranteeapply/submit/', this.dataForm)
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            })
            .catch(() => {})
        })
      },
      1000,
      { leading: true, trailing: false }
    )
  }
}
</script>
