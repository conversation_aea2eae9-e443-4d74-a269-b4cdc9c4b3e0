<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2020-08-19 14:50:36
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2022-06-09 14:31:43
-->
<template >
  <div class="card" ref="inCcard" v-loading='loading' element-loading-text="准备表单中..">

    <!-- {{businessLicenseId}} -->
    <el-row>
      <el-col :span="10">
        <template-model :dataForm='{...dataForm.bgGuaranteeApply,...dataForm.projectInfo,name:dataForm.bgGuaranteeApply.name}' />
      </el-col>
      <el-col :span="14" v-show="active === 1">
        <el-card class="box-card box" shadow='never'>
          <!-- {{this.dataForm}} -->
          <el-card class="box-card" style="margin-bottom:5px;">
            <steps :active='active'></steps>
          </el-card>
          <el-scrollbar class="height nav">
            <enterpriseForm v-if="dataForm.bgGuaranteeApply" ref="bgGuaranteeApply" :pakeage='pakeage' :dataForm='dataForm.bgGuaranteeApply' />
            <projectInfo v-if="dataForm.projectInfo" ref="projectInfo" :pakeage='pakeage' :dataForm='dataForm.projectInfo' :bgGuaranteeApplyName='dataForm.bgGuaranteeApply.name' />
            <remark v-if="pakeage.isRemarks=='1'" :dataForm='dataForm' ></remark>
            <bgGuaranteeMailing v-if="dataForm.bgGuaranteeMailing&&pakeage.isMail=='1'" :span='24' :dataForm='dataForm.bgGuaranteeMailing' ref="bgGuaranteeMailing" />
            <bgguaranteeinvoice v-if="dataForm.bgguaranteeinvoice&&pakeage.isInvoiceFrom=='1'" :dataForm='dataForm.bgguaranteeinvoice' :options='options' ref="bgguaranteeinvoice" />
            <div class="foot">

              <span>
                <el-button type="primary" @click="backStep()" plain>上一步</el-button>
                <el-button type="primary" :loading="nextLoading" @click="dataFormSubmitHandle(0,dataForm)">下一步</el-button>
                <!-- <el-button type="primary" :disabled="!checked" :loading="nextLoading" @click="dataFormSubmitHandle(0,dataForm)">保存</el-button> -->
                <!-- <el-button type="primary" :disabled="!checked" :loading="nextLoading" v-if="this.dataForm.letterId !== ''" @click="dataFormSubmitHandle(1,dataForm)">下一步</el-button> -->
              </span>
            </div>
          </el-scrollbar>

        </el-card>
      </el-col>

      <el-col :span="14" v-show="active === 2&&dataForm.letterId">
        <el-card class="box-card box" shadow='never'>
          <el-scrollbar class="height">
            <!-- <uploadFile ref="uploadFile" :filePer='filePer' :active='active' :letterId='dataForm.letterId' :projectId="dataForm.projectId" @biddingDocumentId='getBiddingDocumentId'
              @businessLicenseId='getBusinessLicenseId' />
            <caIpload ref="caIpload" @emitCA="emitCA"  :active='active' :filePer='filePer' :serPriceId="$refs['projectInfo']?$refs['projectInfo'].serPriceId:''"  :sqsId='dataForm.sqsId' :letterId='dataForm.letterId' :dataForm='dataForm' @biddingDocumentId='getBiddingDocumentId'
              @businessLicenseId='getBusinessLicenseId' /> -->
            <uploadList :type='0' :letterId='dataForm.letterId' :active='active + 1' :dataForm='dataForm' :serPriceId="$refs['projectInfo']?$refs['projectInfo'].serPriceId:''"
              :issueCode='$route.params.insuranceCode' :guaranteeTypeCodes='$route.params.type' @getFile='getFile'></uploadList>
            <div class="foot">
              <span style="margin-bottom:10px;margin-right:20px;">
                <div class="el-upload__tip" >
                  注：如有疑问，请联系 400-0311-616&nbsp;客服电话
                </div>
                <el-checkbox v-model="checked">我已阅读并同意 <el-button type="text" @click="showDia('xieyi')">服务协议</el-button>
                </el-checkbox>
                <el-checkbox v-if="$route.params.insuranceCode === 'HZDB' " v-model="tzchecked">我已阅读并同意 <el-button type="text" @click="showDia('tzxieyi')">云保函承诺书</el-button>
                </el-checkbox>
              </span>
              <span>
                <el-button type="primary" @click="active=1" plain>上一步</el-button>
                <el-button type="primary" v-if="$route.params.insuranceCode === 'HZDB'  " :disabled="!tzchecked||!checked" :loading="nextLoading" @click="dataFormSubmitHandle(1,dataForm)">下一步</el-button>
                <el-button type="primary" v-else :disabled="!checked" :loading="nextLoading" @click="dataFormSubmitHandle(1,dataForm)">下一步</el-button>

                <!-- <el-button type="primary" :disabled="!checked" :loading="nextLoading" @click="dataFormSubmitHandle(0,dataForm)">保存</el-button> -->
                <!-- <el-button type="primary" :disabled="!checked" :loading="nextLoading" v-if="this.dataForm.letterId !== ''" @click="dataFormSubmitHandle(1,dataForm)">下一步</el-button> -->
              </span>
            </div>
          </el-scrollbar>
        </el-card>
      </el-col>
    </el-row>
    <!-- <el-button  @click="showCaDia('caModel',dataForm)">11</el-button> -->
    <model v-if="visible" ref="model"></model>
    <caModel v-if="visible" ref="caModel"></caModel>
    <xieyi v-if="visible" ref="xieyi"></xieyi>
    <tzxieyi v-if="visible" ref="tzxieyi"></tzxieyi>
    <tip v-if="visible" ref="tip"></tip>
    <errorDia v-if="visible" ref="errorDia"></errorDia>
  </div>
</template>
<script>
import steps from '@/views/project/guarantee/components/stepA'
import debounce from 'lodash/debounce'
import bgguaranteeinvoice from '@/views/project/guarantee/common/InvoiceAndMail/form'
import templateModel from '@/views/project/guarantee/components/templateModel'
import enterpriseForm from '@/views/project/guarantee/warrantGuarantee/form/enterpriseForm'
import remark from '@/views/project/guarantee/warrantGuarantee/form/remark'
import projectInfo from '@/views/project/guarantee/warrantGuarantee/form/projectInfo'
import uploadFile from '@/views/project/guarantee/warrantGuarantee/form/uploadFile'
import bgGuaranteeMailing from '@/views/project/guarantee/tenderGuarantee/form/mailing'
import caIpload from '@/views/project/guarantee/warrantGuarantee/form/caIpload'
import tip from '@/views/project/guarantee/warrantGuarantee/compoents/tip'
import xieyi from '@/views/project/guarantee/components/xieyi'
import tzxieyi from '@/views/project/guarantee/components/tzxieyi'
import model from '@/views/project/guarantee/warrantGuarantee/compoents/model'
import caModel from '@/views/project/guarantee/components/caModel'
import { newWin } from '@/utils'
import uploadList from '@/views/project/guarantee/warrantGuarantee/compoents/uploadList'
import { VerificationFIle } from './VerificationFIle'
import errorDia from '@/views/project/guarantee/warrantGuarantee/compoents/errorDia'
export default {
  data () {
    return {
      loading: false,
      dataForm: {
        letterId: '',
        projectId: '',
        remark: ''
      },
      active: 1,
      formReset: ['bgGuaranteeApply', 'projectInfo', 'bgGuaranteeMailing', 'bgguaranteeinvoice'],
      checked: false,
      tzchecked: false,
      visible: false,
      nextLoading: false,
      businessLicenseId: '', // 营业执照Id
      biddingDocumentId: '', // 招标文件Id
      fileList: [],
      pakeage: {},
      filePer: {
        zbwj: 1,
        wts: 1,
        cqdy: 1,
        wtht: 1,
        wtrfront: 0,
        wtrbehind: 0,
        frfront: 0,
        frbehind: 0
      }
    }
  },
  components: {
    tip,
    xieyi,
    tzxieyi,
    model,
    // eslint-disable-next-line vue/no-unused-components
    caIpload,
    caModel,
    steps,
    remark,
    uploadList,
    templateModel,
    enterpriseForm,
    // eslint-disable-next-line vue/no-unused-components
    bgGuaranteeMailing,
    bgguaranteeinvoice,
    projectInfo,
    // eslint-disable-next-line vue/no-unused-components
    uploadFile,
    errorDia
  },
  created () {
    this.setObj()
    this.filePermission()
    this.getpakeage()
    if (this.$route.params.id) {
      this.getDetail()
    } else {
      this.getInfo()
    }
    if (this.$route.params.projectId) {
      this.getProject()
    }
  },
  methods: {
    setObj () {
      this.formReset.map((a) => {
        if (!this.dataForm[a]) this.$set(this.dataForm, a, {})
      })
      this.$set(this.dataForm, 'insuranceType', this.$route.params.type)
    },
    getProject () {
      this.$http
        .get(`letter/bgbiddingproject/${this.$route.params.projectId}`)
        .then(({ data: res }) => {
          console.log(res)
          this.dataForm.projectInfo = Object.assign({}, this.dataForm.projectInfo, res.data, {
            regionName: res.data.region,
            projectName: res.data.name,
            startDate: res.data.bidOpenDate,
            tendereeAgent: res.data.tendereeAgent || ''
          })
        })
    },
    getBiddingDocumentId (val) {
      this.biddingDocumentId = val
    },
    filePermission () {
      this.$http
        .get(`/sys/params/getValue/${this.$route.params.insuranceCode}`)
        .then(({ data: res }) => {
          this.filePer = {
            ...this.filePer,
            ...JSON.parse(res.data)
          }
          console.log(this.filePer)
        })
    },
    getFile (val) {
      this.fileList = val
    },
    getBusinessLicenseId (val) {
      console.log(val)
      this.businessLicenseId = val
    },
    getpakeage () {
      this.$http
        .get(`/letter/bgissueconfigure/getInfoByIssueCode?issueCode=${this.$route.params.insuranceCode}&guaranteeTypeCode=${this.$route.params.type}`)
        .then(async ({ data: res }) => {
          console.log('配置', res)
          this.pakeage = res.data
        })
    },
    getDetail () {
      this.loading = true
      this.$http
        .get(
          `/letter/guarantee/getDetailByLetterId/?letterId=` +
            this.$route.params.id
        )
        .then(({ data: res }) => {
          this.loading = false
          if (res.code !== 0) {
            this.setObj()
            return
          }
          this.dataForm = res.data
          this.biddingDocumentId = this.dataForm.biddingDocumentId
          this.businessLicenseId = this.dataForm.businessLicenseId
          this.setObj()
        })
    },
    getInfo () {
      // /letter/bgguaranteeletter/getAllByJkInfo/
      this.loading = true
      this.$http
        .get(`/letter/bgguaranteeapply/getInfo/`)
        .then(({ data: res }) => {
          this.loading = false
          if (res.code !== 0) {
            this.setObj()
            return
          }
          this.setObj()
          if (res.data) {
            this.dataForm.bgGuaranteeApply = {
              ...this.dataForm.bgGuaranteeApply,
              ...res.data
            }
          }
        })
    },
    Verification () {
      return new Promise((resolve, reject) => {
        var arr = []
        this.formReset.map((a) => {
          if (this.$refs[`${a}`] !== undefined) {
            this.$refs[`${a}`].push().then((res) => {
              arr.push(res)
            })
          }
        })
        setTimeout(() => {
          console.log(arr.length, this.formReset.length)
          resolve(arr)
        }, 300)
      })
    },
    backStep () {
      this.$router.push({
        name:
          this.$route.name === 'warrantGuarantee'
            ? 'project-guarantee-common-chooiseIn'
            : 'letter-bgguaranteeletterSqf'
      })
    },
    emitCA (type) {
      console.log(type)
      this.showCaDia('caModel', this.dataForm, type)
    },
    showCaDia (name, data, type) {
      this.visible = true
      console.log(this.$route.params)
      this.$nextTick(() => {
        this.$refs[name].dataForm = {}
        this.serPriceId = this.$refs['projectInfo'].serPriceId
        var obj = {
          ...data,
          cjfChargeId: this.serPriceId,
          status: status,
          letterId: this.dataForm.letterId,
          guaranteeType: this.$route.params.type,
          key: this.$route.params.insuranceCode,
          biddingDocumentId: this.biddingDocumentId,
          businessLicenseId: this.businessLicenseId
        }
        this.$refs[name].dataForm = obj
        this.$refs[name].type = type
        this.$refs[name].init()
      })
    },
    showDia (name, sqs, wthtId) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs[name].init()
        this.$refs[name].letterId = this.dataForm.letterId
        if (name === 'tip' || name === 'model') {
          this.$refs[name].sqs = sqs
          this.$refs[name].wthtId = wthtId
        }
      })
    },

    dataFormSubmitHandle: debounce(
      async function (status, data) {
        this.serPriceId = this.$refs['projectInfo'].serPriceId
        var obj = {
          ...data,
          cjfChargeId: this.serPriceId,
          status: status,
          letterId: this.dataForm.letterId,
          guaranteeType: this.$route.params.type,
          key: this.$route.params.insuranceCode,
          biddingDocumentId: this.biddingDocumentId,
          businessLicenseId: this.businessLicenseId
        }

        this.Verification().then((res) => {
          console.log(res)
          if (!res.includes(false)) {
            if (status === 1) {
              VerificationFIle(this.fileList).then((res) => {
                if (res.length === 0) {
                  const h = this.$createElement
                  //  `该保函由${this.insuranfceIno.name}提供，保费为${this.dataForm.bginsuranceinfo.guaranteePrice}，是否继续？`,
                  this.$confirm(
                    h('p', null, [
                      h('span', null, '该保函由 '),
                      h(
                        'span',
                        { style: 'color: teal' },
                        `${this.$route.params.insuranceName}`
                      ),
                      h('span', null, ' 提供，'),
                      h('span', null, '保费为 '),
                      h(
                        'span',
                        { style: 'color: rgb(230, 162, 60)' },
                        `${this.dataForm.projectInfo.guaranteePrice}`
                      ),
                      h('span', null, '元，是否继续？')
                    ]),
                    '提示',
                    {
                      confirmButtonText: '确定',
                      cancelButtonText: '取消',
                      type: 'warning'
                    }
                  )
                    .then(async () => {
                      // 验证通过，走提交
                      console.log(this.dataForm.payStatus !== '30' && this.$route.params.insuranceCode !== 'YFDDB', this.$route.params.insuranceCode)
                      // eslint-disable-next-line no-unreachable
                      if (this.dataForm.payStatus === '30') {
                        this.submitData(status, obj)
                      } else {
                        if (this.$route.params.insuranceCode === 'YFDDB' || this.$route.params.insuranceCode === 'GS') {
                          this.submitData(status, obj)
                        } else {
                          this.pay(this.dataForm.letterId, this.$route.params.insuranceCode)
                          this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
                            (item) => item.name !== this.$router.name
                          )
                          this.$router.push({
                            name: 'bgguaranteeletterSqf'
                          })
                        }
                      }
                    })
                    .catch(() => {})
                } else {
                  this.visible = true
                  this.$nextTick(() => {
                    this.$refs['errorDia'].init()
                    this.$refs['errorDia'].errorList = res
                  })
                }
              })
            } else {
              this.submitData(status, obj)
            }
            // this.submitData(status, obj)
          } else {
            this.$message.error('表单提交有误，请检查提交信息')
          }
        })
      },
      1000,
      { leading: true, trailing: false }
    ),
    async pay (letterId, key) {
      this.$loading({
        lock: true,
        text: `请求支付中`
      })
      var data = await this.getPayMentInfo(letterId, key)
      var payInfo = data
      this.$loading().close()
      window.open(encodeURI(payInfo), '_blank')
    },
    getPayMentInfo (letterId, key) {
      // 获取信息
      return new Promise((resolve, reject) => {
        this.$http
          .get(
            `/letter/bgguaranteeletter/getPayUrl/` + letterId + `?key=${key}`
          )
          .then(({ data: res }) => {
            this.updateLoading = false

            resolve(res.data)
          })
          .catch((rej) => {
            reject(rej.msg)
          })
      })
    },
    async submitData (status, obj) {
      this.nextLoading = true
      this.$http
        .post(`/letter/guarantee/save`, obj)
        .then(async ({ data: res }) => {
          this.nextLoading = false

          if (status !== 0) {
            this.$message.success('提交成功')
            console.log(this.$route.params.insuranceCode, this.$route.params.insuranceCode === 'YFDDB')
            if (this.$route.params.insuranceCode === 'YFDDB' || this.$route.params.insuranceCode === 'GS') {
              this.pay(this.dataForm.letterId, this.$route.params.insuranceCode)
              this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
                (item) => item.name !== this.$router.name
              )
              this.$router.push({
                name: 'bgguaranteeletterSqf'
              })
            } else {
              this.goAwit()
            }
          } else {
            this.active = 2
            this.dataForm.letterId = res.data.letter.id
            this.dataForm.projectId = res.data.BgBiddingProjectDTO.id
            this.$message.success('保存成功！')
          }
        })
    },
    VerFileSign2 (letterId, obj) {
      return new Promise((resolve, reject) => {
        let _that = this
        _that.$http
          .get(`letter/guarantee/isSign`, {
            params: {
              letterId: letterId
            }
          })
          .then(({ data: res }) => {
            if (res.code !== 0) {
              return _that.$message.error(res.msg)
            }
            if (JSON.stringify(res.data) !== '{}') {
              if (res.data.sqs.isSign !== 1) {
                // this.getModel(obj)
                _that.$message.error('委托书未上传！')
                resolve(false)
              }
              if (_that.filePer.wtht === 2 && res.data.wtht.isSign !== 1) {
                // this.getModel(obj)
                _that.$message.error('委托合同未上传！')
                resolve(false)
              }
              resolve(true)
            } else {
              // this.getModel(obj)
              _that.$message.error('委托书、委托合同未上传！')
              resolve(false)
            }
          })
          .catch((e) => {
            resolve(false)
          })
      })
    },

    getModel (obj) {
      this.$http
        .post(`/letter/guarantee/generateModel`, obj)
        .then(async ({ data: res }) => {
          this.$set(this.dataForm, 'sqsId', res.data.sqsId.id)
          this.$set(this.dataForm, 'wthtId', res.data.wthtId.id)
          if (window.ActiveXObject !== undefined) {
            newWin(
              window.SITE_CONFIG['bhtURL'] +
                `#/signatureIe?sqs=${this.dataForm.sqsId}&wthtId=${this.dataForm.wthtId}`
            )
            // window.open(
            //   window.SITE_CONFIG['bhtURL'] +
            //           `#/signatureIe?sqs=${this.dataForm.sqsId}&wthtId=${this.dataForm.wthtId}`,
            //   '_blank'
            // )
          } else {
            this.showDia('tip', this.dataForm.sqsId, this.dataForm.wthtId)
          }
        })
    },
    VerFileSign (ossId) {
      return new Promise((resolve, reject) => {
        this.$http
          .get(`letter/guarantee/getOssById`, {
            params: {
              ossId: ossId
            }
          })
          .then(({ data: res }) => {
            if (res.code !== 0) {
              resolve(false)
              return
            }
            if (res.data.isSign === 0) {
              resolve(false)
            } else {
              resolve(true)
            }
          })
          .catch((e) => {
            resolve(false)
            console.log(e)
          })
      })
      // letter/guarantee/getOssById  根据ossId
    },
    goAwit () {
      this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
        (item) => item.name !== this.$router.name
      )
      this.$router.push({
        name: 'bgguaranteeletterSqf'
      })

      const audit = this.$router.resolve({
        name: 'awaitingAudit',
        query: {
          id: this.dataForm.letterId,
          insuranceCode: this.$route.params.insuranceCode
        }
      })

      // this.$router.push({
      //   name: 'letter-bgguaranteeletterSqf'
      // })
      newWin(audit.href)
      // window.open(audit.href, '_blank')
    }
  }
}
</script>
<style lang="scss" scoped>
.el-upload__tip {
  color: #f56c6c;
}
.box {
  height: calc(100vh - 100px);
}
.height {
  height: 100%;
}
.nav {
  height: calc(100% - 100px);
}
.foot {
  margin: 30px 0;
  text-align: center;
  span {
    display: block;
  }
}
.el-upload__tip {
  color: #f56c6c;
}
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
</style>
