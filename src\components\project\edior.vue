<template>
  <div>
    <quill-editor ref="text" v-model="quill" class="myQuillEditor" :options="editorOption"  @change="onEditorChange($event)"/>
  </div>
</template>
<script>
import { quillEditor } from 'vue-quill-editor'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
const toolbarOptions = [
  ['bold', 'italic', 'underline', 'strike'], // 加粗 斜体 下划线 删除线
  ['blockquote', 'code-block'], // 引用  代码块
  [{ header: 1 }, { header: 2 }], // 1、2 级标题
  [{ list: 'ordered' }, { list: 'bullet' }], // 有序、无序列表
  [{ script: 'sub' }, { script: 'super' }], // 上标/下标
  [{ indent: '-1' }, { indent: '+1' }], // 缩进
  // [{'direction': 'rtl'}],                         // 文本方向
  [{ size: ['small', false, 'large', 'huge'] }], // 字体大小
  [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题
  [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
  [{ font: [] }], // 字体种类
  [{ align: [] }], // 对齐方式
  ['clean'] // 清除文本格式

]

export default {
  data () {
    return {
      quill: '',
      editorOption: {
        placeholder: '请输入...',
        modules: {
          toolbar: {
            container: toolbarOptions
            // container: "#toolbar",

          }
        }
      }
    }
  },
  props: {
    content: String
  },
  watch: {
    content (a, b) {
      this.quill = a
    }
  },
  components: {
    quillEditor
  },
  methods: {
    onEditorChange (e) {
      // console.log(e)

      this.$emit('onEditorChange', e.html)
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
