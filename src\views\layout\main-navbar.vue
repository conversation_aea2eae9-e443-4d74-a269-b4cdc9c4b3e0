<template>
  <nav class="aui-navbar" :class="`aui-navbar--${$store.state.navbarLayoutType}`">
    <div class="aui-navbar__header">
      <h1 class="aui-navbar__brand" @click="$router.push({ name: 'home' })">
        <a class="aui-navbar__brand-lg" href="javascript:;"><img style="width:100px;" src="@/assets/img/logo3.png"></a>
        <a class="aui-navbar__brand-mini" href="javascript:;">{{ $t('brand.mini') }}</a>
      </h1>
    </div>
    <div class="aui-navbar__body">
      <el-menu class="aui-navbar__menu mr-auto" mode="horizontal">
        <el-menu-item index="1" @click="$store.state.sidebarFold = !$store.state.sidebarFold">
          <svg class="icon-svg aui-navbar__icon-menu aui-navbar__icon-menu--switch" aria-hidden="true">
            <use xlink:href="#icon-outdent"></use>
          </svg>
        </el-menu-item>
        <el-menu-item index="2" @click="refresh()">
          <svg class="icon-svg aui-navbar__icon-menu aui-navbar__icon-menu--refresh" aria-hidden="true">
            <use xlink:href="#icon-sync"></use>
          </svg>
        </el-menu-item>
        <template v-if="!$store.state.sidebarMode">
          <el-menu-item v-for="(item,index) in navRoute" :key="item.name" @click="getList(item,index)" :style="$store.state.navbarActive == index ?'color:#409EFF !important;':''">
            <svg class="icon-svg aui-navbar__icon-menu aui-navbar__icon-menu--refresh" aria-hidden="true" :style="$store.state.navbarActive == index ?'color:#409EFF !important;':'#303133 !important'">
              <use :xlink:href="'#'+item.icon"></use>
            </svg>
            {{item.name}}
          </el-menu-item>
        </template>
      </el-menu>
      <el-menu class="aui-navbar__menu" mode="horizontal">
        <!--   <el-menu-item index="1">
          <el-dropdown placement="bottom" :show-timeout="0">
            <el-button size="mini">{{ $t('_lang') }}</el-button>
            <el-dropdown-menu slot="dropdown" disabled="true">
              <el-dropdown-item v-for="(val, key) in i18nMessages" :key="key" @click.native="$i18n.locale = key">{{ val._lang }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-menu-item>-->
        <!-- <el-menu-item index="12" @click="handleSetLock()">
          <svg class="icon-svg aui-navbar__icon-menu" aria-hidden="true">
            <use xlink:href="#icon-lock"></use>
          </svg>
          <div class="top-bar__item">
            <top-lock></top-lock>
          </div>
        </el-menu-item> -->
        <el-menu-item index="5">
          <el-popover placement="bottom" width="300" trigger="hover">
            <div v-if="num!==0">
              <div class="tip">
                <h3>当前待处理保函</h3>
                <p>{{num}}</p>
              </div>
              <el-button style="float:right;" @click="goPath('project-IssuedBy-AuditGuarantee-index')" type="text">处理</el-button>
            </div>
            <div style="text-align:center;" v-else>
              暂无消息
            </div>
            <div slot="reference">
              <!-- <div class="point point-flicker" v-if="num===0">
              </div> -->
              <!-- <el-badge :value="num" :hidden='num===0' :max="99" class="item">
                <i class="el-icon-message-solid icon-svg aui-navbar__icon-menu"></i>
              </el-badge> -->
            </div>
          </el-popover>
        </el-menu-item>
        <el-menu-item index="2">
          <a href="http://www.hebztb.com" target="_blank">
            <svg class="icon-svg aui-navbar__icon-menu" aria-hidden="true">
              <use xlink:href="#icon-earth"></use>
            </svg>
          </a>
        </el-menu-item>
        <el-menu-item index="3" @click="fullscreenHandle()">
          <svg class="icon-svg aui-navbar__icon-menu" aria-hidden="true">
            <use xlink:href="#icon-fullscreen"></use>
          </svg>
        </el-menu-item>
        <el-menu-item index="4" class="aui-navbar__avatar">
          <el-dropdown placement="bottom" :show-timeout="0">
            <span class="el-dropdown-link">
              <img src="~@/assets/img/avatar.png">
              <span>{{ $store.state.user.name }}</span>
              <i class="el-icon-arrow-down"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="updatePasswordHandle()">{{ $t('updatePassword.title') }}</el-dropdown-item>
              <el-dropdown-item @click.native="isIE?bindCA():msg()">绑定CA</el-dropdown-item>
              <el-dropdown-item @click.native="deBindCA()">解绑CA</el-dropdown-item>
              <el-dropdown-item @click.native="logoutHandle()">{{ $t('logout') }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-menu-item>
      </el-menu>
    </div>
    <!-- 弹窗, 修改密码 -->
    <update-password v-if="updatePassowrdVisible" ref="updatePassowrd"></update-password>
    <!-- 绑定CA -->
    <mian-bindCA v-if="bindCAVisible" ref="mianBindCA"></mian-bindCA>
    <!-- 弹窗, 设定锁屏密码-->
    <top-lock v-if="lockVisible" ref="lock"></top-lock>
  </nav>
</template>

<script>
import { messages } from '@/i18n'
import screenfull from 'screenfull'
import TopLock from './main-navbar-lock'
import UpdatePassword from './main-navbar-update-password'
import mianBindCA from './mian-bindCA'
import { clearLoginInfo } from '@/utils'
import socketMixin from '@/mixins/socketMixin'
export default {
  inject: ['refresh'],
  mixins: [socketMixin],
  data () {
    return {
      i18nMessages: messages,
      updatePassowrdVisible: false,
      lockVisible: false,
      bindCAVisible: false,
      isIE: window.ActiveXObject !== undefined,
      navRoute: [],
      socketOptions: {
        isActive: true, // 此页面是否在激活（进入）时，调用查询数据列表接口？
        isMsg: true
      },
      dataForm: {
        isBinding: 0
      },
      num: 0,
      tip: null
    }
  },
  components: {
    UpdatePassword,
    TopLock,
    mianBindCA
  },
  created () {
    this.navRoute = window.SITE_CONFIG['menuList']
  },
  mounted () {
    if (!this.$store.state.sidebarMode) {
      this.getList(
        this.navRoute[this.$store.state.navbarActive],
        this.$store.state.navbarActive
      )
    } else {
      this.$store.state.sidebarMenuList = this.navRoute
    }
    // this.getCount()
  },
  watch: {
    num (a, b) {
      if (a) {
        this.$store.state.navNum = a
      }
    },
    '$store.state.navNum' (a, b) {
      if (a) {
        this.num = a
      }
    }
  },

  methods: {
    msg () {
      this.$message.error('绑定CA需重新登录CA，请打开ie重新登录CA')
    },
    open () {
      if (this.tip) {
        this.tip.close()
      }
      this.tip = this.$notify({
        // position: 'bottom-right',
        title: '消息通知',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        message: `<div>当前有新的待审核保函 <span style='color:red;'>${this.num}</span> 条</div>`,
        duration: 10000
      })
    },
    goPath (path) {
      this.$router.push({
        name: path,
        query: {
          letterStatus: '20'
        }
      })
    },
    getCount () {
      this.$http
        .get(
          `/letter/guarantee/getTotalPendingReview`
        )
        .then(({ data: res }) => {
          this.num = res.data
        })
        .catch((rej) => {
        })
    },

    websocketonmessage (e) {
      // 数据接收
      // '{msg:cjf}'
      const redata = JSON.parse(e.data)
      if (redata.type === 'cjf') {
        this.num = redata.sum
        if (this.socketOptions.isMsg) {
          this.open()
        }
      }
      // console.log(e.data)
    },
    // 绑定CA
    bindCA () {
      this.bindCAVisible = true
      this.$nextTick(() => {
        this.$refs.mianBindCA.init()
      })
    },
    deBindCA () {
      this.$confirm('此操作将接触此账号与CA的绑定, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.dataForm.isBinding = 0
          this.$http
            .post('/caBinding', this.dataForm)
            .then(({ data: res }) => {
              this.$message({
                message: 'CA解绑成功',
                type: 'success',
                duration: 500
              })
            })
            .catch(() => {})
        })
        .catch(() => {})
    },
    // 获取路由
    getList (item, index) {
      this.$store.state.navbarActive = index
      this.$store.state.sidebarMenuList = item.children
    },
    // 全屏
    fullscreenHandle () {
      if (!screenfull.enabled) {
        return this.$message({
          message: this.$t('fullscreen.prompt'),
          type: 'warning',
          duration: 500
        })
      }
      screenfull.toggle()
    },
    // 锁屏
    handleSetLock () {
      this.lockVisible = true
      this.$nextTick(() => {
        this.$refs.lock.init()
      })
    },
    // 修改密码
    updatePasswordHandle () {
      this.updatePassowrdVisible = true
      this.$nextTick(() => {
        this.$refs.updatePassowrd.init()
      })
    },
    // 退出
    logoutHandle () {
      if (this.$route.name === 'pdf') {
        this.$message.error('签章页面不支持注销登录，请完成操作后在进行注销！')
        return
      }
      this.$store.state.isObj = true
      this.$confirm(
        this.$t('prompt.info', { handle: this.$t('logout') }),
        this.$t('prompt.title'),
        {
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel'),
          closeOnClickModal: false,
          type: 'warning'
        }
      )
        .then(() => {
          this.$store.state.isObj = false
          this.$http
            .post('/logout')
            .then(({ data: res }) => {
              clearLoginInfo()
              this.$router.push({ name: 'login' })
            })
            .catch(() => {})
        })
        .catch(() => {
          this.$store.state.isObj = false
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.point {
  width: 6px;
  height: 6px;
  background-color: rgb(255, 0, 0);
  position: absolute;
  border-radius: 50%;
  top: 25px;
  right: 25px;
  border-radius: 50%;
}
@keyframes warn {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }
  40% {
    opacity: 0.5;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

/* 设置动画前颜色 */
.point-flicker:after {
  background-color: rgba(255, 0, 0, 0.3);
}

/* 设置动画后颜色 */
.point-flicker:before {
  background-color: rgba(255, 0, 0, 0.3);
}

/* 设置动画 */
.point-flicker:before,
.point-flicker:after {
  content: '';
  width: 12px;
  height: 12px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -6px;
  margin-top: -6px;
  border-radius: 50%;
  /* CSS3 animation 属性 网址 */
  /* https://www.w3school.com.cn/cssref/pr_animation.asp */
  animation: warn 1.5s ease-out 0s infinite;
}
</style>
