<!--
 * @Descripttion:
 * @Author: kong<PERSON><PERSON>ang
 * @Date: 2021-02-01 17:43:14
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-05-09 10:53:48
-->
<template>
  <el-dialog :visible.sync="visible" title="上传文件" :close-on-click-modal="false" :show-close='false' :close-on-press-escape="false" width="850">
    <div>
      <!-- {{$store.state.user.auditProcedures}} -->
      <el-form ref="form" :model="form" label-width="140px">
        <template v-if="$store.state.user.auditProcedures==0||dataForm.guaranteeType==='lybhyh'">
           <el-form-item label="上传保函">
              <el-radio-group v-model="value">
                <!-- <el-radio :label="1">生成保函</el-radio> -->
                <el-radio :label="1">手动上传</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item prop="wts" v-if="value==1">
              <!-- <el-button size="small" @click="getModel(5)" type="text">模板下载</el-button> -->
              <el-upload :action="url" style="text-align:left !important;" :headers="myHeaders" :file-list="fileList" drag multiple :before-upload="beforeUploadHandle" :data='upData'
                :on-success="successHandle" class="text-center">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text" v-html="$t('upload.text')"></div>
                <div class="el-upload__tip" slot="tip">{{ $t('upload.tip', { 'format': ' pdf '}) }}</div>
              </el-upload>
            </el-form-item>
        </template>
       <template v-if="$store.state.user.auditProcedures==5">
        <el-form-item label="上传委托合同" v-if="dataForm.guaranteeTypeCode!='tbdbbh'">
          <el-radio-group v-model="value2">
            <el-radio :label="1">生成合同</el-radio>
            <el-radio :label="2">手动上传</el-radio>
          </el-radio-group>
        </el-form-item>
         <el-form-item prop="wts" v-if="dataForm.guaranteeTypeCode!='tbdbbh'&&value2==1">
          <el-button size="small" @click="getGenerateGuaranteeModelHT(dataForm.guaranteeTypeCode)" :loading='btnLoading' type="primary">生成合同</el-button>
            <ul class="el-upload-list el-upload-list--text" v-if="fileList2.length>0">
                <li tabindex="0" class="el-upload-list__item is-success">
                  <a class="el-upload-list__item-name" @click="onPreview(fileList2[0])"><i class="el-icon-document"></i>委托合同
                  </a><label class="el-upload-list__item-status-label"><i class="el-icon-upload-success el-icon-circle-check"></i></label><i
                    @click="fileList2=[]" class="el-icon-close"></i>
                </li>
              </ul>
        </el-form-item>
        <el-form-item prop="wts" v-if="dataForm.guaranteeTypeCode!='tbdbbh'&&value2==2">
          <el-button size="small" @click="getModel(dataForm.guaranteeTypeCode=='ssdbbh'? 322 : 4)" type="text">模板下载</el-button>
          <el-upload :action="url" style="text-align:left !important;" :headers="myHeaders" :file-list="fileList2" drag multiple :before-upload="beforeUploadHandle" :data='fileData'
            :on-success="successHandle" class="text-center">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text" v-html="$t('upload.text')"></div>
            <div class="el-upload__tip" slot="tip">{{ $t('upload.tip', { 'format': ' pdf '}) }}</div>
          </el-upload>
        </el-form-item>
        </template>

      </el-form>

    </div>
    <template slot="footer">
      <el-button v-if="$store.state.user.auditProcedures==0||dataForm.guaranteeType==='lybhyh'" @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" v-if="$store.state.user.auditProcedures==0||dataForm.guaranteeType==='lybhyh'" @click="submitData()">确认出函</el-button>
      <el-button type="primary" v-if="$store.state.user.auditProcedures==5" @click="close">完成签章</el-button>
    </template>
    <examineCAChrome v-if="visibleBH" ref="examineCAChrome" @refresh='getFile'></examineCAChrome>
  </el-dialog>
</template>
<script>
import examineCAChrome from '@/views/project/guarantee/common/detail/examineCAChrome'
import Cookies from 'js-cookie'

export default {
  data () {
    return {
      visible: false,
      visibleBH: false,
      btnLoading: false,
      value: 1,
      value2: 1,
      tempId: '',
      form: {},
      ossId: '',
      options: [],
      type: '',
      dataForm: {},
      url: '',
      fileList: [],
      fileList2: [],
      myHeaders: {
        token: Cookies.get('token') || ''
      },
      upData: {},
      fileData: {}
    }
  },
  components: { examineCAChrome },
  watch: {
    dataForm: {
      handler (a) {
        if (a.guaranteeTypeCode !== 'tbdbbh' && a.guaranteeTypeCode !== 'ssdbbh') {
          this.value2 = 1
        }
      },
      deep: true
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.url =
          window.SITE_CONFIG['apiURL'] + '/letter/guarantee/bhFileupload'
        this.fileList = []
        this.fileList2 = []
        this.tempId = ''
        this.upData = {
          letterId: this.dataForm.letterId,
          fileType: 5
        }
        this.fileData = {
          letterId: this.dataForm.letterId,
          fileType: this.dataForm.guaranteeTypeCode === 'ssdbbh' ? 322 : 4
        }
      })
    },
    close () {
      this.visible = false
      this.$emit('refresh')
    },
    getFile (id, type) {
      this.$http
        .get(`letter/guarantee/getOssById`, {
          params: {
            ossId: id
          }
        })
        .then(({ data: res }) => {
          // console.log(res.data)
          // this.fileList[0] = { ...res.data, filename: '电子保函' }
          if (type === '0') {
            this.$set(this.fileList, 0, { ...res.data, filename: '电子保函', name: '电子保函' })
          } else {
            this.$set(this.fileList2, 0, { ...res.data, filename: '委托合同', name: '委托合同' })
          }
          console.log(this.fileList)
        })
        .catch((e) => {})
    },
    onPreview (file) {
      if (file.ossId) {
        window.open(
          `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${file.ossId}`
        )
      } else {
        window.open(
          `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${file.id}`
        )
      }
    },
    // change () {
    //   this.$http
    //     .get(
    //       `/letter/bgletterlitigation/updateTempId?letterId=${this.dataForm.letterId}&tempId=${this.tempId}`
    //     )
    //     .then(({ data: res }) => {
    //       if (res.code !== 0) {
    //         return false
    //       }
    //       // this.$message.success('选择模板成功')
    //     })
    // },
    // getModelList () {
    //   this.$http
    //     .get(
    //       `/letter/bgguaranteetemplate/getListByIssueCodeAndType?issueCode=${this.dataForm.issueCode}&type=0&guaranteeType=${this.dataForm.guaranteeTypeCode}`
    //     )
    //     .then(({ data: res }) => {
    //       this.options = res.data
    //       this.tempId = this.options[0].id
    //       this.change()
    //     })
    // },
    beforeUploadHandle (file) {
      let extension = file.name.substring(file.name.lastIndexOf('.') + 1)
      if (extension !== 'pdf') {
        this.$message.error(this.$t('upload.tip', ' pdf '))
        return false
      }
    },
    // 上传成功需要微调格式去除空格
    successHandle (res, file, fileList) {
      if (res.code !== 0) {
        return
      }
      if (res.data.type === 5) {
        this.fileList = fileList
        console.log(fileList)
        this.ossId = res.data.ossId
      } else {
        this.fileList2 = fileList
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          // this.$emit('refreshDataList')
        }
      })
    },
    showDia (name, bhid, wtht, issueCode, type) {
      this.visibleBH = true
      this.$nextTick(() => {
        if (name === 'tip' || name === 'examineCAChrome') {
          this.$refs[name].dataForm = this.dataForm
          this.$refs[name].form.bhid = bhid
          this.$refs[name].form.wtht = wtht
          this.$refs[name].type = type
          this.$refs[name].issueCode = issueCode
        }
        this.$refs[name].init()
      })
    },
    submitData () {
      if (this.fileList.length === 0) {
        return this.$message.error('请上传电子保函')
      }
      this.$http
        .get(
          'letter/bgguaranteeletter/confirmToIssue?letterId=' +
            this.dataForm.letterId +
            '&ossId=' +
             this.ossId + '&isSign=' + this.value
        )
        .then(({ data: res }) => {
          this.btnLoading = false

          this.visible = false
          this.$message.success('出具成功！')
          if (this.dataForm.correctStatus === '20') {
            // this.pgPush()
          }
          this.$emit('refresh')
        })
    },
    pgPush () {
      this.$http
        .get(
          'letter/guarantee/correctAuditHREB?letterId=' + this.dataForm.letterId
        )
        .then(({ data: res }) => {})
    },
    getGenerateGuaranteeModelHT (type) {
      if (type === 'ssdbbh') {
        this.$http
          .get(
            'letter/bgletterlitigation/guaranteeContract?letterId=' +
              this.dataForm.letterId +
              '&fileType=322'
          )
          .then(({ data: res }) => {
            this.btnLoading = false

            this.showDia(
              'examineCAChrome',
              '',
              res.data,
              this.dataForm.issueCode,
              this.dataForm.guaranteeTypeCode
            )
          })
      } else {
        this.$http
          .get(
            'letter/guarantee/generateGuaranteeModel?letterId=' +
              this.dataForm.letterId
          )
          .then(({ data: res }) => {
            this.btnLoading = false

            this.showDia(
              'examineCAChrome',
              '',
              res.data.wtht,
              this.dataForm.key,
              this.dataForm.guaranteeTypeCode
            )
          })
      }
    },
    getGenerateGuaranteeModel () {
      this.btnLoading = true
      console.log(this.dataForm.guaranteeTypeCode)
      if (this.dataForm.guaranteeTypeCode === 'ssdbbh') {
        this.$http
          .get(
            '/letter/bgletterlitigation/generationGuarantee?letterId=' +
              this.dataForm.letterId +
              '&tempId='
          )
          .then(({ data: res }) => {
            this.btnLoading = false

            this.showDia(
              'examineCAChrome',
              res.data.id,
              '',
              this.dataForm.issueCode,
              this.dataForm.guaranteeTypeCode
            )
          })
      } else {
        this.$http
          .get(
            'letter/guarantee/generateGuaranteeModel?letterId=' +
              this.dataForm.letterId
          )
          .then(({ data: res }) => {
            this.btnLoading = false

            this.showDia(
              'examineCAChrome',
              res.data.dzbh,
              '',
              this.dataForm.key,
              this.dataForm.guaranteeTypeCode
            )
          })
      }
    },
    getModel (type) {
      this.$http
        .get(`/letter/guarantee/generateModel`, {
          params: {
            letterId: this.dataForm.letterId,
            fileType: type
          }
        })
        .then(({ data: res }) => {
          window.open(
            `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${res.data.fileId.id}`
          )
        })
        .catch(() => {})
    }
  }
}
</script>
