<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"  :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="colConfig">
          <el-form-item label="编码" prop="code">
            <el-input v-model="dataForm.code" placeholder="编码"></el-input>
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="名称"></el-input>
          </el-form-item>
          <el-form-item label="保险类型 1 投标保证" prop="insuranceType">
            <el-input v-model="dataForm.insuranceType" placeholder="保险类型 1 投标保证"></el-input>
          </el-form-item>
          <el-form-item label="保险开始时间" prop="startDate">
            <el-input v-model="dataForm.startDate" placeholder="保险开始时间"></el-input>
          </el-form-item>
          <el-form-item label="保险截止时间" prop="endDate">
            <el-input v-model="dataForm.endDate" placeholder="保险截止时间"></el-input>
          </el-form-item>
          <el-form-item label="保险机构ID" prop="insuranceOrgid">
            <el-input v-model="dataForm.insuranceOrgid" placeholder="保险机构ID"></el-input>
          </el-form-item>
          <el-form-item label="保险机构" prop="insuranceOrg">
            <el-input v-model="dataForm.insuranceOrg" placeholder="保险机构"></el-input>
          </el-form-item>
          <el-form-item label="保函单号" prop="insuranceCode">
            <el-input v-model="dataForm.insuranceCode" placeholder="保函单号"></el-input>
          </el-form-item>
          <el-form-item label="保函下载url" prop="insuranceUrl">
            <el-input v-model="dataForm.insuranceUrl" placeholder="保函下载url"></el-input>
          </el-form-item>
          <el-form-item label="保险本地路径" prop="insuranceAddr">
            <el-input v-model="dataForm.insuranceAddr" placeholder="保险本地路径"></el-input>
          </el-form-item>
          <el-form-item label="保险变更记录" prop="insuranceChange">
            <el-input v-model="dataForm.insuranceChange" placeholder="保险变更记录"></el-input>
          </el-form-item>
          <el-form-item label="担保金额" prop="guaranteeAmount">
             <el-input-number placeholder="请输入担保金额" class="wd240" v-model="dataForm.guaranteeAmount" controls-position="right" :precision="0" :min="0" :max="10000000"></el-input-number>

          </el-form-item>
          <el-form-item label="保险金额" prop="guaranteePrice">
            <el-input v-model="dataForm.guaranteePrice" placeholder="保险金额"></el-input>
          </el-form-item>
          <el-form-item label="所属平台编码" prop="platformCode">
            <el-input v-model="dataForm.platformCode" placeholder="所属平台编码"></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="status" size="mini">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="0">停用</el-radio>
              <el-radio :label="1">正常</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        insuranceType: '',
        startDate: '',
        endDate: '',
        insuranceOrgid: '',
        insuranceOrg: '',
        insuranceCode: '',
        insuranceUrl: '',
        insuranceAddr: '',
        insuranceChange: '',
        guaranteeAmount: '',
        guaranteePrice: '',
        platformCode: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        code: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        insuranceType: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        startDate: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        endDate: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        insuranceOrgid: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        insuranceOrg: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        insuranceCode: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        insuranceUrl: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        insuranceAddr: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        insuranceChange: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        guaranteeAmount: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        guaranteePrice: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        platformCode: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        status: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http
        .get(`/letter/bginsuranceinfo/${this.dataForm.id}`)
        .then(({ data: res }) => {
          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
        })
        .catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate(valid => {
          if (!valid) {
            return false
          }
          this.$http[!this.dataForm.id ? 'post' : 'put'](
            '/letter/bginsuranceinfo/',
            this.dataForm
          )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            })
            .catch(() => {})
        })
      },
      1000,
      { leading: true, trailing: false }
    )
  }
}
</script>
