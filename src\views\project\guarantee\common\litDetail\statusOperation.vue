<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2020-10-13 09:38:40
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-12-17 08:58:10
-->
<template>
  <el-col class="status statusOp" style="width:300px;">
    <span style="text-align:right;">
        <div>保函状态</div>
      <!-- {{dataForm}} -->
      <div class="s_s" :style="'color:'+statusColor(dataForm.letterStatus)" > {{certType(dataForm.letterStatus,'letterStatusoptions')}}</div>
    </span>
    <span style="float:right;text-align:right;" v-if="this.$route.query.JumpName=='project-IssuedBy-liGuaranteeMaintenance-index'">
      <div>线下支付状态</div>
      <div class="s_s Warning" v-if="dataForm.offlineAuditStatus=='10'">审核中</div>
      <div class="s_s Success" v-if="dataForm.offlineAuditStatus=='20'">审核通过</div>
      <div class="s_s Danger" v-if="dataForm.offlineAuditStatus=='30'">审核不通过</div>
      <el-divider></el-divider>
      <el-button size="small" :loading="btnLoading" type="warning" v-if="dataForm.offlineAuditStatus=='10'" icon="el-icon-s-check" @click="showDiaSH('examine','cw')">审核
      </el-button>
      <div v-if="dataForm.offlineAuditStatus=='20'">
        <span style="color:#67C23A;"><i class="el-icon-success"></i>&nbsp;线下支付已审核</span>
      </div>
    </span>
    <!-- 发票 -->
    <span style="float:right" v-if="this.$route.query.JumpName=='project-IssuedBy-liFinancialInvoice-index'">
      <el-button size="small" :loading="btnLoading" type="warning" v-if="!dataForm.invoiceStatus||dataForm.invoiceStatus=='0'" icon="el-icon-s-check" @click="showDia('issueInvoice')">出具发票
      </el-button>
      <div v-else>
        <span style="color:#67C23A;"><i class="el-icon-success"></i>&nbsp;已出具发票</span>
        <div>
          <el-button :loading="btnLoading" @click="showDia('issueInvoice')" type="text">重新出具发票</el-button>
        </div>
      </div>

    </span>
    <examine v-if="visible" ref="examine" @refresh='refresh'></examine>
    <issueInvoice v-if="visible" ref="issueInvoice" @refresh='refresh' :options='options' :dataForm='dataForm'></issueInvoice>
  </el-col>
</template>
<script>
import { getDict } from '@/utils/index'
import examine from '@/views/project/guarantee/common/detail/examine'
import issueInvoice from '@/views/project/guarantee/common/detail/issueInvoice'
import dictionaries from '@/views/project/guarantee/components/dictionaries'
export default {
  props: {
    dataForm: Object
  },
  mixins: [dictionaries],

  components: { examine, issueInvoice },
  data () {
    return {
      btnLoading: false,
      visible: false
    }
  },
  created () {},

  methods: {
    certType (val, name) {
      var aa = getDict('保函状态').filter((a) => a.dictCode === val)
      return aa.length > 0 ? aa[0].dictName : '-'
    },
    refresh () {
      this.$emit('refresh')
    },
    showDiaSH (name, type) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs[name].init()
        this.$refs[name].type = type
        this.$refs[name].remark = this.dataForm.remark
      })
    },
    showDia (name) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs[name].init()
      })
    },
    statusColor (code) {
      let dcode = Number(code)
      if (dcode < 30) {
        return '#909399'
      } else if (dcode < 50) {
        return '#E6A23C'
      } else if (dcode < 60) {
        return '#67C23A'
      } else if (dcode < 100) {
        return '#F56C6C'
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.statusOp{
  text-align: right !important;
}
.status {
  position: absolute;
  right: 0;
  top: 100px;
  text-align: left  !important;
  padding-right: 30px !important;
  div {
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 10px;
  }
  .s_s {
    color: rgba(0, 0, 0, 0.85);
    font-size: 20px;
    margin-bottom:15px;
  }
}
</style>
