<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="colConfig">
          <el-form-item label="模板编码" prop="code">
            <el-input v-model="dataForm.code" placeholder="编码"></el-input>
          </el-form-item>
          <el-form-item label="模板名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="模板名称"></el-input>
          </el-form-item>
          <el-form-item label="申请人盖章关键字" prop="acSignKeyword">
            <el-input v-model="dataForm.acSignKeyword" placeholder="申请人盖章关键字"></el-input>
          </el-form-item>
          <!--<el-form-item label="申请人企业ID" prop="companyId">
     <el-input v-model="dataForm.companyId" placeholder="申请人企业ID"></el-input>
</el-form-item>-->
          <!-- <el-form-item disabled prop="content" label="模板正文">

            <div id="J_quillEditor"></div>
          </el-form-item> -->
          <el-form-item  prop="content" label="模板正文">
            <!-- 富文本编辑器, 容器 -->
            <edior :content='dataForm.content' @onEditorChange='onEditorChange'></edior>
          </el-form-item>

          <el-form-item label="模板参数" prop="params">
            <el-input type="textarea" :rows="3" class="wd360" v-model="dataForm.params" size="small" placeholder="模板参数"></el-input>
          </el-form-item>
          <el-form-item label="出具机构盖章关键字" prop="gcSignKeyword">
            <el-input v-model="dataForm.gcSignKeyword" placeholder="出具机构盖章关键字"></el-input>
          </el-form-item>
          <el-form-item label="保函类型" prop="guaranteeType">
            <el-select v-model="dataForm.guaranteeType"  size="small" class="wd180" placeholder="保函类型">
              <el-option v-for="item in guaranteeTypeoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否默认" prop="isDefault">
            <el-radio-group v-model="dataForm.isDefault">
              <el-radio :label="0">否</el-radio>
              <el-radio :label="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="状态" prop="status" size="mini">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="0">停用</el-radio>
              <el-radio :label="1">正常</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import edior from '@/components/project/edior2'
import 'quill/dist/quill.snow.css'

export default {
  data () {
    return {

      colConfig: 1, // 列配置 1 2列
      visible: false,
      getDicListURL: '/sys/dict/type/',
      guaranteeTypeoptions: [],

      dataForm: {
        id: '',
        acSignKeyword: '',
        companyId: '',
        content: '',
        gcSignKeyword: '',
        guaranteeType: '',
        isDefault: 1,
        name: '',
        code: '',
        params: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  components: { edior },
  computed: {
    dataRule () {
      // var validateContent = (rule, value, callback) => {
      //   if (this.quillEditor.getLength() <= 1) {
      //     return callback(new Error(this.$t('validate.required')))
      //   }
      //   callback()
      // }
      return {
        content: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
          // { validator: validateContent, trigger: 'blur' }
        ],
        isDefault: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        code: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        status: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.getInsuranceTypeInfo()
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    onEditorChange (val) {
      console.log(val)
      this.dataForm.content = val
    },
    // 保函类型信息
    getInsuranceTypeInfo () {
      this.$http
        .get(this.getDicListURL + 'guaranteeType')
        .then(({ data: res }) => {
          this.guaranteeTypeoptions = {
            ...this.guaranteeTypeoptions,
            ...res.data.list
          }
        })
        .catch(() => {})
    },
    // 富文本编辑器
    // quillEditorHandle () {
    //   this.quillEditor = new Quill('#J_quillEditor', {
    //     modules: {
    //       toolbar: this.quillEditorToolbarOptions
    //     },
    //     theme: 'snow'
    //   })
    //   // 监听内容变化，动态赋值
    //   this.quillEditor.on('text-change', () => {
    //     this.dataForm.content = this.quillEditor.root.innerHTML
    //   })
    // },
    // 获取信息
    getInfo () {
      this.$http
        .get(`/letter/bgguaranteetemplate/${this.dataForm.id}`)
        .then(({ data: res }) => {
          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
          this.quillEditor.root.innerHTML = this.dataForm.content
          // console.log(this.dataForm.content)
        })
        .catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate(valid => {
          if (!valid) {
            return false
          }
          this.$http[!this.dataForm.id ? 'post' : 'put'](
            '/letter/bgguaranteetemplate/',
            this.dataForm
          )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            })
            .catch(() => {})
        })
      },
      1000,
      { leading: true, trailing: false }
    )
  }
}
</script>
