<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON>qiang
 * @Date: 2020-09-21 15:44:32
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-10-15 17:17:57
-->
<template>
  <el-dialog :title="title" :visible.sync="visible" width="30%" :before-close="handleClose">
    <!-- <span>这是一段信息</span> -->
    <el-form :model="form" ref="form" status-icon>
      <div v-if="type=='2'">
        <el-form-item prop="mobile" :rules="[
                    {
                      required: this.type == '2',
                      message: this.$t('validate.required'),
                      trigger: 'blur'
                    }
                  ]">
          <el-input v-model.trim="form.mobile" placeholder="用户手机号">
            <span slot="prefix" class="el-input__icon">
              <svg class="icon-svg" aria-hidden="true">
                <use xlink:href="#icon-user"></use>
              </svg>
            </span>
          </el-input>
        </el-form-item>
        <el-form-item prop="smsyzm" :rules="[
                    {
                      required: this.type == '2',
                      message: this.$t('validate.required'),
                      trigger: 'blur'
                    }
                  ]">
          <el-input v-model.trim="form.smsyzm" style="width: 325px;margin-right:15px;" placeholder="手机验证码">
            <span slot="prefix" class="el-input__icon">
              <svg class="icon-svg" aria-hidden="true">
                <use xlink:href="#icon-code"></use>
              </svg>
            </span>
          </el-input>
          <el-button plain v-if="btnShow" style="padding: 9px 34px;" type="primary" @click="sendMsg">发送短信</el-button>
          <el-button plain v-else type="primary" style="width:80px;" disabled>{{count}} s</el-button>
        </el-form-item>
      </div>
      <div v-if="type==3">
        <el-form-item prop="username" :rules="[
                    {
                      required: this.type === '3',
                      message: this.$t('validate.required'),
                      trigger: 'blur'
                    }
                  ]">
          <el-input v-model.trim="form.username" placeholder="用户名或手机号">
            <span slot="prefix" class="el-input__icon">
              <svg class="icon-svg" aria-hidden="true">
                <use xlink:href="#icon-user"></use>
              </svg>
            </span>
          </el-input>
        </el-form-item>
        <el-form-item prop="password" :rules="[
                    {
                      required: this.type === '3',
                      message: this.$t('validate.required'),
                      trigger: 'blur'
                    }
                  ]">
          <el-input v-model.trim="form.password" type="password" :placeholder="$t('login.password')">
            <span slot="prefix" class="el-input__icon">
              <svg class="icon-svg" aria-hidden="true">
                <use xlink:href="#icon-lock"></use>
              </svg>
            </span>
          </el-input>
        </el-form-item>
        <el-form-item prop="captcha" :rules="[
                    {
                      required: this.type === '3',
                      message: this.$t('validate.required'),
                      trigger: 'blur'
                    }
                  ]">
          <el-row :gutter="20">
            <el-col :span="14">
              <el-input v-model.trim="form.captcha" :placeholder="$t('login.captcha')">
                <span slot="prefix" class="el-input__icon">
                  <svg class="icon-svg" aria-hidden="true">
                    <use xlink:href="#icon-safetycertificate"></use>
                  </svg>
                </span>
              </el-input>
            </el-col>
            <el-col :span="10" class="login-captcha">
              <img :src="captchaPath" @click="getCaptcha()">
            </el-col>
          </el-row>
        </el-form-item>

      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">返回普通登录</el-button>
      <el-button type="primary" :loading='btnLoading' @click="dataFormSubmitHandle">登 录</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { getUUID } from '@/utils'
import Cookies from 'js-cookie'
import debounce from 'lodash/debounce'

export default {
  data () {
    return {
      visible: false,
      title: '',
      captchaPath: '',
      form: {},
      btnShow: true,
      btnLoading: false,
      count: '',
      timer: null,
      type: '2' //  2：手机登录 3：第三方登录
    }
  },
  destroyed () {
    this.timer = null
  },
  // props: {
  //   title: String
  // },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['form'].resetFields()
        this.form = JSON.parse(JSON.stringify(this.form))
        this.form.captcha = ''
        this.form.password = ''
        this.form.username = ''

        this.getCaptcha()
      })
    },
    getCaptcha () {
      this.form.uuid = getUUID()
      this.captchaPath = `${window.SITE_CONFIG['apiURL']}/captcha?uuid=${this.form.uuid}`
    },
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false
          }
          this.btnLoading = true
          this.$http
            .post('/login', this.form)
            .then(({ data: res }) => {
              this.btnLoading = false
              if (res.code !== 0) {
                this.getCaptcha()
                return
              }
              this.getPdfInfo(res.data.token)
            })
            .catch(() => {})
        })
      },
      1000,
      { leading: true, trailing: false }
    ),
    getPdfInfo (token) {
      this.$http
        .get(`/sys/params/getValue/${'isSign'}`)
        .then(({ data: res }) => {
          this.loading = false

          this.$store.state.pdfType = res.data
          sessionStorage.setItem('store', JSON.stringify(this.$store.state))
          Cookies.set('token', token)
          this.getAllDict()
          this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
            (item) => item.name === 'home'
          )
          this.$router.replace({ name: 'home' })
        })
        .catch(() => {})
    },
    getAllDict () {
      this.$http
        .get(`sys/dict/type/getAllDict`)
        .then(({ data: res }) => {
          sessionStorage.setItem('dict', JSON.stringify(res.data))
        })
        .catch(() => {})
    },
    sendMsg () {
      if (!this.form.mobile) {
        this.$message.error('请先输入手机号')
        return false
      }

      this.form.uuid = getUUID()
      this.$http
        .get(
          `${window.SITE_CONFIG['apiURL']}/smsyzm?uuid=${this.form.uuid}&tel=${this.form.mobile}`
        )
        .then(({ data: res }) => {
          console.log(res)

          this.getCode()
          this.$message.success('发送短信成功')
        })
        .catch(() => {})
    },
    getCode () {
      const TIME_COUNT = 60
      if (!this.timer) {
        this.count = TIME_COUNT
        this.btnShow = false
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--
          } else {
            this.btnShow = true
            clearInterval(this.timer)
            this.timer = null
          }
        }, 1000)
      }
    },
    handleClose () {
      this.timer = null
      this.count = ''
      this.visible = false
      this.$emit('closeDia')
    }
  }
}
</script>
<style lang="scss" scoped>
.login-captcha {
  overflow: hidden;
  > img {
    width: 100%;
    cursor: pointer;
  }
}
</style>
