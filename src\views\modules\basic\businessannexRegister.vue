<template>
  <el-container>
    <el-main style="padding-top: 0px">
      <el-card shadow="never" class="aui-card--fill">
        <div class="mod-basic__businessannex}">
          <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle" style="width: 100%;">
            <!--<el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>-->
            <el-table-column prop="name" label="电子件名称" sortable="custom" header-align="center" align="center">
              <!--<template slot-scope="scope">-->
              <!--<el-tag  size="mini" type="text"><span>{{fomatMethod(scope.row.ywTableName)}}</span></el-tag>-->
              <!--</template>-->
            </el-table-column>
            <el-table-column prop="annexName" label="附件名称" sortable="custom" header-align="center" align="center">
              <template slot-scope="scope">
                <a style="cursor:pointer;" size="mini" type="text" @click="clickSee(scope.row.annexId)"
                  v-if="scope.row.annexName != null && scope.row.annexName != undefined && scope.row.annexName != ''"><span>{{scope.row.name}}.{{filterName(scope.row.annexName)}}</span></a>
                <el-tag size="mini" type="danger" v-if="scope.row.annexName === null || scope.row.annexName === undefined  || scope.row.annexName === ''"><span>未上传</span></el-tag>
              </template>
            </el-table-column>
            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="180">
              <template slot-scope="scope">
                <el-dropdown>
                  <span class="el-dropdown-link">
                    <el-button type="text" v-if="scope.row.annexName === null || scope.row.annexName === undefined  || scope.row.annexName === ''" @click="importHandle(scope.row.id,scope.row.name)">附件上传</el-button>
                  </span>
                  <span class="el-dropdown-link">
                    <el-button v-if="scope.row.annexName != null && scope.row.annexName != undefined && scope.row.annexName != ''" type="text" size="mini" @click="deleteRegister(scope.row.id)">
                      {{ $t('delete') }}</el-button>
                  </span>
                </el-dropdown>

              </template>
            </el-table-column>
          </el-table>
          <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle"
            @current-change="pageCurrentChangeHandle">
          </el-pagination>
          <!-- 弹窗, 新增 / 修改 -->
          <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
          <!-- 弹窗, 上传文件 -->
          <upload v-if="uploadVisible" ref="upload" @refreshDataList="getDataList"></upload>
        </div>
      </el-card>
    </el-main>
  </el-container>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './businessannex-add-or-update'
import Upload from './businessannexRegister-upload'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      Permission: 'basic:businessannex', // 权限名
      filterText: '',
      treeLoading: false,
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'projectName'
      },
      mixinViewModuleOptions: {
        getDataListURL: '/basic/businessannex/page',
        getDataListIsPage: true,
        exportURL: '/basic/businessannex/export',
        deleteURL: '/basic/businessannex',
        enableURL: '/basic/businessannex/enable',
        stopURL: '/basic/businessannex/stop',
        getTreeDataURL: '/basic/bimproject/tree',
        deleteIsBatch: true
      },
      statusoptions: [
        {
          value: 1,
          label: '正常'
        },
        {
          value: 0,
          label: '停用'
        }
      ],
      phaseoptions: [
        {
          value: 0,
          label: '前期'
        },
        {
          value: 1,
          label: '中期'
        },
        {
          value: 2,
          label: '后期'
        }
      ],
      dataForm: {
        // id: '',
        // code: '',
        // name: '',
        // projectId: ''
        // projectName: '',
        // projectCode: '',
        // phase: '',
        ywTableId: '',
        phase: '0'
        // annexName: '',
        // status: 1
      },
      getDicListURL: '/sys/dict/type/',
      ywTableNamemaps: '',
      ywTableNameoptions: [],
      orderField: 'phase',
      order: 'asc',
      uploadVisible: false
    }
  },
  components: {
    AddOrUpdate,
    Upload
  },
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val)
    }
  },
  activated () {
    // 通过路由参数pid, 控制列表请求操作
    // this.getTreeDataList()
    // this.getYwTableNameInfo()
  },
  methods: {
    fomatMethod (value) {
      return this.ywTableNamemaps[value]
    },
    filterName (name) {
      return name.substring(name.lastIndexOf('.') + 1)
    },
    // 获取保函类型信息
    getYwTableNameInfo () {
      this.$http
        .get(this.getDicListURL + 'ywTableName')
        .then(({ data: res }) => {
          this.ywTableNameoptions = {
            ...this.ywTableNameoptions,
            ...res.data.list
          }
          this.ywTableNamemaps = {
            ...this.ywTableNamemaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    },
    refreshList () {
      this.getDataList()
    },
    // 导入
    importHandle (id, name) {
      this.uploadVisible = true
      var params = { id: id, name: name }
      this.$nextTick(() => {
        this.$refs.upload.init(params)
      })
    },
    // 点击查看
    clickSee (id) {
      var fileUrl = `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${id}`
      window.open(fileUrl)
    },
    // 状态启用具体代码需微调格式去除空格
    enableHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(
        this.$t('prompt.info', { handle: '启用' }),
        this.$t('prompt.title'),
        {
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          this.$http
            .put(
              this.mixinViewModuleOptions.enableURL,
              id ? [id] : this.dataListSelections.map((item) => item.id)
            )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getDataList()
                }
              })
            })
            .catch(() => {})
        })
        .catch(() => {})
    },
    downloadHandle (id, name) {
      var url = `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${id}`
      window.location.href = url
    },
    displayHandle (id, path) {
      var previewUrl = ''
      var url = `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${id}`
      previewUrl = url + '?fullfilename=' + path
      console.log(
        `${window.SITE_CONFIG['fileView']}` + encodeURIComponent(previewUrl)
      )
      window.open(
        `${window.SITE_CONFIG['fileView']}` + encodeURIComponent(previewUrl)
      )
    },
    // 删除
    deleteRegister (id) {
      console.log(id)
      this.$http
        .get(`/letter/bgguaranteeapply/registerDelete?id=${id}`)
        .then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.refreshList()
            }
          })
        })
        .catch(() => {})
    },
    // 状态停用
    stopHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(
        this.$t('prompt.info', { handle: '停用' }),
        this.$t('prompt.title'),
        {
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          // 具体代码需微调格式去除空格
          this.$http
            .put(
              this.mixinViewModuleOptions.stopURL,
              id ? [id] : this.dataListSelections.map((item) => item.id)
            )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getDataList()
                }
              })
            })
            .catch(() => {})
        })
        .catch(() => {})
    },
    handleNodeClick (data) {
      this.dataForm.projectCode = data.projectCode
      this.dataForm.projectName = data.projectName
      this.dataForm.projectId = data.projectId
      this.getDataList()
    },
    // 树节点点击事件
    filterNode (value, data) {
      if (!value) return true
      return data.projectName.indexOf(value) !== -1
    },
    getTreeDataList: function () {
      this.treeLoading = true
      this.$http
        .get(this.mixinViewModuleOptions.getTreeDataURL, {
          params: {}
        })
        .then(({ data: res }) => {
          this.treeLoading = false
          if (res.code !== 0) {
            this.treeData = []
            return
          }
          this.treeData = res.data
          // 默认显示第一节点的数据
          if (this.treeData && this.treeData.length > 0) {
            this.handleNodeClick({
              projectCode: res.data[0].projectCode,
              projectName: res.data[0].projectName,
              projectId: res.data[0].projectId
            })
          }
        })
        .catch(() => {
          this.treeLoading = false
        })
    }
  }
}
</script>
