<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-03-31 15:09:58
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-07-06 09:07:32
-->
<template>
  <el-dialog title="确认信息" :close-on-click-modal="false" append-to-body :close-on-press-escape="false" :visible.sync="visible" width="200px">
    <div v-loading='loading'>
      <el-alert style="margin-bottom:20px;" title="请再次确认信息是否正确后点击申请即可申请保函！"   :closable="false" type="warning" show-icon>
      </el-alert>
      <!-- {{dataForm}} -->
      <h3 class="temp-subtitle">保函类型</h3>
      <div class="con">
        {{dataForm.title}}
      </div>
      <h3 class="temp-subtitle">项目所属区域</h3>
      <div class="con">{{dataForm.regionName}}</div>
      <h3 class="temp-subtitle">项目标段名称</h3>
      <div class="con">{{dataForm.name}}</div>
      <h3 class="temp-subtitle" v-if="dataForm.guaranteeAmount">担保金额</h3>
      <div class="con" v-if="dataForm.guaranteeAmount">{{dataForm.guaranteeAmount/10000}}万元</div>
    </div>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" :disabled='this.count > 0' @click="$emit('submit')">{{con}}</el-button>
    </template>
  </el-dialog>
</template>
<script>
export default {
  data () {
    return {
      visible: false,
      loading: false,
      dataForm: {},
      con: '',
      count: '',
      timer: null
    }
  },
  beforeDestroy () {
    clearInterval(this.timer)
    this.timer = null
  },
  methods: {
    init () {
      this.visible = true
      this.getCode()
    },

    getCode () {
      const TIME_COUNT = 5
      this.con = `仔细核对 ${TIME_COUNT} s秒可点击申请`
      if (!this.timer) {
        this.count = TIME_COUNT
        this.timer = setInterval(() => {
          if (this.count > 1 && this.count <= TIME_COUNT) {
            this.count--
            this.con = `仔细核对 ${this.count} s秒可点击申请`
          } else {
            this.count = 0
            this.con = `申请`
            clearInterval(this.timer)
            this.timer = null
          }
        }, 1000)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.temp-subtitle {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  margin: 0;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.con {
  padding: 20px;
  font-size: 16px;
}
</style>
