<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2020-08-24 18:13:40
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-03-24 16:16:20
-->
<template>
  <el-card class="box-card" shadow="never">
    <div slot="header" class="clearfix">
      <span>附件信息</span>
    </div>
    <div>
      <span class="cen" v-if="!fileData[0]">暂无数据</span>
      <el-col :span="24" v-else v-for="(item,index) in fileData" :key="index">
        <!-- {{item.type===5&&letterStatus==='50'}}{{item.type}}{{letterStatus}} -->
        <!-- {{item}} -->
        <el-form-item :label="item.name">
          <div style="width:32px;height:32px;">
            <img style="width:100%;height:100%;" src="@/assets/img/file.png" alt="">
          </div>
           <!-- {{filterType(item.url)&&(item.type !== 0 && item.type !== 3)}} -->
          <!-- {{filterType(item.url)}} -->
          <el-button type="text" v-if="filterType(item.url)&&(item.type !== 0 && item.type !== 3)" @click="showDia(item.id,item.name)">预览</el-button>
          <el-button type="text" v-else @click="filterType(item.url)?showDia(item.id,item.name):miner(item.id)">预览</el-button>
          <!-- <img :src="miner(item.id)" alt=""> -->
          <el-button type="text" @click="download(item.id,item.type)">下载</el-button>
        </el-form-item>
      </el-col>
      <!-- {{fileData}} -->
    </div>
    <el-dialog class="preview CAmodel" title="预览" :visible.sync="imgVisible" :fullscreen="fullscreen" :close-on-click-modal="false">
      <div slot='title'>
        <h3>预览</h3>
        <!-- <el-button @click="fullscreen=!fullscreen" class="el-dialog__headerbtn diaFull" type="text"><i class="el-icon-full-screen"></i></el-button> -->
      </div>
      <div v-loading='loading'><img style="width:100%;" :src="img" alt=""></div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="imgVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
    <preview v-if="visible" @refresh='visible=false' ref="preview"></preview>
  </el-card>
</template>
<script>
// import Cookies from 'js-cookie'
import preview from '@/views/project/guarantee/common/detail/preview'
export default {
  data () {
    return {
      visible: false,
      imgVisible: false,
      img: '',
      fullscreen: false,
      loading: false
    }
  },
  components: {
    preview
  },
  props: {
    fileData: Array
  },

  created () {},
  methods: {
    filterType (file) {
      if (file) {
        var extension = file.substring(file.lastIndexOf('.') + 1)
        return extension === 'pdf' || extension === 'PDF'
      }
    },
    showDia (id, name) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['preview'].name = name
        this.$refs['preview'].id = id
        this.$refs['preview'].init()
      })
    },
    miner (id) {
      this.imgVisible = true
      this.loading = true
      // sys/oss/minioPreview?id=123
      this.img = `${window.SITE_CONFIG['apiURL']}/sys/oss/minioPreview?id=${id}`
      setTimeout(() => {
        this.loading = false
      }, 300)
      // this.img = res.data
    },
    flieLabel (type) {
      if (type === 1) {
        return '申请书'
      }
      if (type === 2) {
        return '招标文件'
      }
      if (type === 0 || type === 3) {
        return '营业执照'
      }
      if (type === 4) {
        return '担保合同'
      }
      if (type === 7) {
        return '线下支付凭证'
      }
      if (type === 5) {
        return '电子保函'
      }
      if (type === 6) {
        return '发票'
      }
      if (type === 8) {
        return '澄清文件'
      }
      if (type === 9) {
        return '说明文件'
      }
      if (type === 10) {
        return '委托人身份证正面'
      }
      if (type === 11) {
        return '法人身份证正面'
      }
      if (type === 12) {
        return '委托人身份证反面'
      }
      if (type === 13) {
        return '法人身份证反面'
      }
    },
    download (id, type) {
      window.open(
        `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${id}`
      )
    }
  }
}
</script>
<style lang="scss" scoped>
.cen {
  width: 100%;
  text-align: center;
  color: #bbb;
  display: inline-block;
  margin: 10px 0;
}
.CAmodel .el-dialog__body {
  max-height: none !important;
  padding: 5px 20px;
  min-height: 550px;
  height: calc(100% - 81px);
}
.min550 {
  min-height: 550px;
  height: 100%;
}
</style>
