<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019-10-08 09:54:14
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-09-22 09:13:28
 -->
<template>
    <div>
      <!-- <Detailed style="margin:20px 0;"  v-if="level == 3 || level == 0 "></Detailed>-->
        <hello style="margin:20px 0;" ></hello>
        <chars style="margin:20px 0;"  v-if="level == 0 "></chars>
        <Announcement style="margin:20px 0;"  ></Announcement>
        <!-- 弹窗, 修改密码 -->
        <update-password v-if="updatePassowrdVisible" ref="updatePassowrd"></update-password>
    </div>
</template>
<script>
import hello from '@/views/modules/homeControl/hello' // 欢迎模块
import chars from '@/views/modules/homeControl/chars' // 图表模块
/* import Detailed from '@/views/modules/homeControl/Detailed' // 详细信息模块 */
import Announcement from '@/views/modules/homeControl/Announcement' // 新闻公告模块
import UpdatePassword from '@/views/layout/main-navbar-change-password'
export default {
  components: { hello, chars, Announcement, UpdatePassword },
  data () {
    return {
      updatePassowrdVisible: false,
      passwdflag: true, //  flase  密码需要修改后才能使用功能
      level: null //  0:全部权限 1:欢迎 2:审核通过 3:详细信息
    }
  },
  created () {
    this.getHomeJurisdiction()
    this.changePsw()
  },
  activated () {

  },
  methods: {
    getHomeJurisdiction () {
      this.$http.get('/sys/role/homeControl', {
        params: {
          homeControl: '1',
          userId: this.$store.state.user.id
        }
      }).then(({ data: res }) => {
        this.level = res
        res.data.map((item, index, arr) => {
          if (Number(item) === 0) {
            this.level = 0
          } else {
            this.level = Number(arr[arr.length - 1])
          }
        })
      })
    },
    getCheckPassWord () {
      const h = this.$createElement
      this.$notify.error({
        title: '警告',
        message: h('div', { }, [
          h('div', { 'text-align': 'left' }, `当前用户为:${this.$store.state.user.name}`),
          h('div', { 'text-align': 'left' }, `初始密码为：qwer1234`),
          h('div', { 'text-align': 'left' }, `为了您的账户安全,请尽快修改您的密码`),
          h('div', { }, [
            h('a', { style: { 'float': 'right', 'cursor': 'pointer' },
              on: {
                click: this.diaOpen
              } }, '修改密码')
          ])
        ]),
        duration: 0
      })
    },
    diaOpen () {
      this.updatePassowrdVisible = true
      this.$nextTick(() => {
        this.$notify.close()
        this.$refs.updatePassowrd.init()
      })
    },
    changePsw () {
      this.$http.get('/sys/user/checkPassWord').then(({ data: res }) => {
        if (res.code === 0) {
          if (!res.data) {
            this.getCheckPassWord()
          }
        }
      }
      )
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
