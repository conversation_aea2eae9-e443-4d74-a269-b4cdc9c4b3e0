<template>
  <el-table :data="list" style="width: 100%;padding-top: 15px;">
    <el-table-column label="用户编码" min-width="170">
      <template slot-scope="scope">
        {{ scope.row.userCode | orderNoFilter }}
      </template>
    </el-table-column>
    <el-table-column label="用户姓名" width="175" align="center">
      <template slot-scope="scope">
        {{ scope.row.userName | orderNoFilter }}
      </template>
    </el-table-column>
    <el-table-column label="用户状态" width="170" align="center">
      <template slot-scope="scope">
        {{ scope.row.status | statusFilter }}
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import { transactionList } from '@/api/remote-search'

export default {
  filters: {
    statusFilter (status) {
      const statusMap = {
        0: '停用',
        1: '正常'
      }
      return statusMap[status]
    },
    orderNoFilter (str) {
      return str.substring(0, 30)
    }
  },
  data () {
    return {
      list: null
    }
  },
  created () {
    this.fetchData()
  },
  methods: {
    fetchData () {
      transactionList().then(response => {
        this.list = response.data.data
      })
    }
  }
}
</script>
