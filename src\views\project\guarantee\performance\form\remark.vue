<template>
  <el-row :gutter="20" style="margin:15px 20px 0;">
    <el-form label-position="left" ref="dataForm" label-width="140px">
      <el-card class="box-card" shadow='never'>
        <div slot="header" class="clearfix">
          <span class="title">其他</span>
        </div>
        <div>
          <el-col :xs="24" :lg="24">
            <el-form-item label="备注">
              <span slot='label'>备注 <el-tooltip content="如有疑问可此添加信息联系审核人员。" placement="bottom">
                  <i class="el-icon-question" style="font-size:16px;color:#409EFF;"></i>
                </el-tooltip></span>
              <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="dataForm.remark">
              </el-input>
            </el-form-item>
          </el-col>
        </div>
      </el-card>
    </el-form>
  </el-row>
</template>
<script>
export default {
  data () {
    return {}
  },
  props: {
    dataForm: {
      type: Object
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
