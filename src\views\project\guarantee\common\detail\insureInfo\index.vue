<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2020-10-13 09:38:40
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-10-29 15:06:38
-->
<template>
  <el-col :span="20">
    <el-col :span="24" class="con-title">
      投保人信息
    </el-col>
    <el-col :span="24">
      <el-form-item label="机构名称">
        {{fomate('bgGuaranteeApply','name')}}
      </el-form-item>
    </el-col>
    <el-col :span="24">
      <el-form-item label="法定代表人">
        {{fomate('bgGuaranteeApply','corporation')}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="draw?24:12">
      <el-form-item label="机构证件类型">
        {{certType(dataForm.bgGuaranteeApply.certType,'certTypeoptions')}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="draw?24:12">
      <el-form-item label="机构证件号码">
        {{fomate('bgGuaranteeApply','certCode')}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="draw?24:12">
      <el-form-item label="联系人">
        {{fomate('bgGuaranteeApply','linkman')}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="draw?24:12">
      <el-form-item label="联系人电话">
        {{fomate('bgGuaranteeApply','linkmanTel')}}
      </el-form-item>
    </el-col>
  </el-col>

</template>
<script>
export default {
  props: {
    dataForm: Object,
    options: Object,
    draw: Boolean
  },
  methods: {
    fomate (key, val) {
      return this.dataForm[key][val] ? this.dataForm[key][val] : '-'
    },
    certType (val, name) {
      // console.log(this.options)
      var aa = this.options[name].filter((a) => a.dictCode === val)
      if (name === 'letterStatusoptions') {
        console.log(aa)
      }
      return aa.length > 0 ? aa[0].dictName : '-'
    }
  }
}
</script>
<style lang="scss" scoped>

.con-title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  // border-left: 6px solid rgb(241, 130, 65);
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-bottom: 10px;
}
</style>
