<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"  :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
        <el-row :gutter="20">
            <el-col :xs="24" :lg="colConfig">
          <el-form-item label="编码" hidden prop="code">
     <el-input v-model="dataForm.code" placeholder="编码"></el-input>
</el-form-item>
                <el-form-item label="名称" prop="name">
     <el-input v-model="dataForm.name" placeholder="名称"></el-input>
</el-form-item>
                <el-form-item label="发票类型" prop="invoiceType">
     <el-select v-model="dataForm.invoiceType" placeholder="发票类型">
         <el-option
                 v-for="item in invoiceTypeoptions"
                 :key="item.dictCode"
                 :label="item.dictName"
                 :value="item.dictCode"
         >
         </el-option>
     </el-select>
</el-form-item>
                <el-form-item label="开票对象" prop="invoiceObject">
     <el-select v-model="dataForm.invoiceObject" placeholder="开票对象">
         <el-option
                 v-for="item in invoiceObjectoptions"
                 :key="item.dictCode"
                 :label="item.dictName"
                 :value="item.dictCode"
         >
         </el-option>
     </el-select>
</el-form-item>
                <el-form-item label="纳税人类型" prop="taxpayerType">
     <el-select v-model="dataForm.taxpayerType" placeholder="纳税人类型">
         <el-option
                 v-for="item in taxpayerTypeoptions"
                 :key="item.dictCode"
                 :label="item.dictName"
                 :value="item.dictCode"
         >
         </el-option>
     </el-select>
</el-form-item>
                <el-form-item label="发票抬头" prop="invoiceTitle">
                    <el-input v-model="dataForm.invoiceTitle" placeholder="发票抬头"></el-input>
                </el-form-item>
                <el-form-item label="接收手机号" prop="electronicInvoicePhone">
                    <el-input v-model="dataForm.electronicInvoicePhone" placeholder="电子发票接收手机号"></el-input>
                </el-form-item>
                <el-form-item label="接收邮箱" prop="electronicInvoiceEmail">
                    <el-input v-model="dataForm.electronicInvoiceEmail" placeholder="电子发票接收邮箱"></el-input>
                </el-form-item>
                <el-form-item label="纳税人识别号" prop="taxpayerNumber">
     <el-input v-model="dataForm.taxpayerNumber" placeholder="纳税人识别号"></el-input>
</el-form-item>
                <el-form-item label="电话" prop="phoneNumber">
     <el-input v-model="dataForm.phoneNumber" placeholder="电话"></el-input>
</el-form-item>
                <el-form-item label="地址" prop="address">
     <el-input v-model="dataForm.address" placeholder="地址"></el-input>
</el-form-item>
                <el-form-item label="开户行名称" prop="bankAccountName">
     <el-input v-model="dataForm.bankAccountName" placeholder="开户行名称"></el-input>
</el-form-item>
                <el-form-item label="银行账户号码" prop="bankAccountNo">
     <el-input v-model="dataForm.bankAccountNo" placeholder="银行账户号码"></el-input>
</el-form-item>
                <el-form-item label="收件编码" prop="mailingCode">
     <el-input v-model="dataForm.mailingCode" placeholder="收件编码"></el-input>
</el-form-item>
                <el-form-item label="收件人" prop="mailingUser">
     <el-input v-model="dataForm.mailingUser" placeholder="收件人"></el-input>
</el-form-item>
                <el-form-item label="收件电话" prop="mailingTel">
     <el-input v-model="dataForm.mailingTel" placeholder="收件电话"></el-input>
</el-form-item>
                <el-form-item label="收件地址" prop="mailingAddress">
     <el-input v-model="dataForm.mailingAddress" placeholder="收件地址"></el-input>
</el-form-item>
                  <el-form-item label="状态" prop="status" size="mini">
   <el-radio-group v-model="dataForm.status">
       <el-radio :label="0">停用</el-radio>
       <el-radio :label="1">正常</el-radio>
   </el-radio-group>
</el-form-item>
                            </el-col>
        </el-row>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" :loading="btnLoading" @click="dataFormSubmitHandle()">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      getDicListURL: '/sys/dict/type/',
      invoiceTypeoptions: [],
      invoiceObjectoptions: [],
      taxpayerTypeoptions: [],
      btnLoading: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        invoiceType: '',
        invoiceObject: '',
        taxpayerType: '',
        invoiceTitle: '',
        taxpayerNumber: '',
        phoneNumber: '',
        address: '',
        electronicInvoicePhone: '',
        electronicInvoiceEmail: '',
        bankAccountName: '',
        bankAccountNo: '',
        mailingCode: '',
        mailingUser: '',
        mailingTel: '',
        mailingAddress: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        // code: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        invoiceType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        invoiceObject: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        taxpayerType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        taxpayerNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        phoneNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        address: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        bankAccountName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        bankAccountNo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        mailingCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        mailingUser: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        mailingTel: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        mailingAddress: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        status: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]

      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.getTaxpayerTypeInfo()
      this.getInvoiceObjectInfo()
      this.getInvoiceTypeInfo()
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取纳税人类型信息
    getTaxpayerTypeInfo () {
      this.$http.get(this.getDicListURL + 'taxpayerType').then(({ data: res }) => {
        this.taxpayerTypeoptions = {
          ...this.taxpayerTypetoptions,
          ...res.data.list
        }
      }).catch(() => {})
    },
    // 获取开票对象信息
    getInvoiceObjectInfo () {
      this.$http.get(this.getDicListURL + 'invoiceObject').then(({ data: res }) => {
        this.invoiceObjectoptions = {
          ...this.invoiceObjectoptions,
          ...res.data.list
        }
      }).catch(() => {})
    },
    // 获取发票类型信息
    getInvoiceTypeInfo () {
      this.$http.get(this.getDicListURL + 'invoiceType').then(({ data: res }) => {
        this.invoiceTypeoptions = {
          ...this.invoiceTypeoptions,
          ...res.data.list
        }
      }).catch(() => {})
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/letter/bgguaranteeinvoice/${this.dataForm.id}`).then(({ data: res }) => {
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.btnLoading = true
        this.$http[!this.dataForm.id ? 'post' : 'put']('/letter/bgguaranteeinvoice/', this.dataForm).then(({ data: res }) => {
          this.btnLoading = false

          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
