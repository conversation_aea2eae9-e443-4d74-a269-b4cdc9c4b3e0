<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false"
    :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"
      :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="colConfig">
          <el-form-item label="出具方名称" prop="issueCode">
            <el-select v-model="dataForm.issueCode" @change="issueChange" size="small" class="wd180" placeholder="保函类型">
              <el-option v-for="item in issueNameList" :key="item.code" :label="item.name" :value="item.code">
              </el-option>
            </el-select>
          </el-form-item>
          <!-- {{ bgTypes }} -->
          <el-form-item label="保函类型" prop="guaranteeType">
            <el-select v-model="dataForm.guaranteeType" size="small" class="wd180" placeholder="保函类型">
              <el-option v-for="item in bgTypes" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
              </el-option>
            </el-select>
            <!-- <el-input v-model="dataForm.guaranteeType" placeholder="保函类型"></el-input> -->
          </el-form-item>
          <!-- <el-form-item label="出具方编码" prop="issueCode">
            <el-input v-model="dataForm.issueCode" placeholder="出具方编码"></el-input>
          </el-form-item> -->
          <el-form-item label="文件名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="文件名称"></el-input>
          </el-form-item>
          <el-form-item label="文件数量" prop="documentNum">
            <el-input v-model="dataForm.documentNum" placeholder="文件数量"></el-input>
          </el-form-item>
          <el-form-item label="文件要求" prop="documentDemand">
            <el-input v-model="dataForm.documentDemand" placeholder="文件要求"></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
          </el-form-item>
          <el-form-item label="模板文件" prop="template">
            <!-- <el-input v-model="dataForm.template" placeholder="模板文件"></el-input> -->
            <el-upload :action="url" :data="{ type: '51' }" :on-remove="(a,b)=>remove(1,a,b)" :headers="myHeaders" :file-list="fileList" drag multiple :before-upload="beforeUploadHandle"
              :on-success="(a,b,c)=>successHandle(1,a,b,c)" class="text-center">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text" v-html="$t('upload.text')"></div>
              <div class="el-upload__tip" slot="tip">
                {{
                  $t('upload.tip', {
                    format: 'jpg、png、gif、txt、doc、xlsx、xls、docx、zip、pdf',
                  })
                }}
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="模板说明文件" prop="instructions">
            <!-- <el-input v-model="dataForm.instructions" placeholder="模板说明文件"></el-input> -->
            <el-upload :action="url" :data="{ type: '52' }" :on-remove="(a,b)=>remove(2,a,b)" :headers="myHeaders" :file-list="fileList2" drag multiple
              :before-upload="beforeUploadHandle" :on-success="(a,b,c)=>successHandle(2,a,b,c)" class="text-center">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text" v-html="$t('upload.text')"></div>
              <div class="el-upload__tip" slot="tip">
                {{
                  $t('upload.tip', {
                    format: 'jpg、png、gif、txt、doc、xlsx、xls、docx、zip、pdf',
                  })
                }}
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input v-model.number="dataForm.sort" placeholder="排序"></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="status" size="mini">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="0">停用</el-radio>
              <el-radio :label="1">正常</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{
        $t('confirm')
      }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import { getDict } from '@/utils/index'
import Cookies from 'js-cookie'

export default {
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      issueNameList: [],
      bgTypes: [],
      fileList: [],
      fileList2: [],
      url: '',
      myHeaders: {
        token: Cookies.get('token') || ''
      },
      dataForm: {
        id: '',
        code: '',
        name: '',
        documentNum: '',
        documentDemand: '',
        remark: '',
        template: [],
        instructions: [],
        issueCode: '',
        issueName: '',
        guaranteeType: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        sort: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        code: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        documentNum: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        documentDemand: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        remark: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        sort: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        // instructions: [
        //   {
        //     required: true,
        //     message: this.$t('validate.required'),
        //     trigger: 'blur'
        //   }
        // ],
        issueCode: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        issueName: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        guaranteeType: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        status: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.fileList = []
      this.fileList2 = []
      this.$nextTick(() => {
        this.url = `${window.SITE_CONFIG['apiURL']}/letter/bgPerformanceDocuments/uploadLyYhFile`
        this.getAllIssue()
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    getAllIssue () {
      // letter/bgguaranteeissue/getAllIssue
      this.$http
        .get(`/letter/bgguaranteeissue/getAllIssue`)
        .then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          console.log(res)
          this.issueNameList = res.data
        })
        .catch(() => {})
    },
    issueChange (val) {
      // console.log(val)
      let op = getDict('电子保函类型')
      console.log(op)
      this.bgTypes = []
      this.dataForm.guaranteeType = ''
      this.issueNameList.map((a) => {
        console.log(a)
        if (a.code === val) {
          this.dataForm.issueName = a.name
          let arr = a.types.split(',')
          op.map((a) => {
            arr.map((b) => {
              if (b === a.dictCode) {
                this.bgTypes.push(a)
              }
            })
          })
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http
        .get(`/letter/bgPerformanceDocuments/${this.dataForm.id}`)
        .then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
          if (this.dataForm.template) {
            this.dataForm.template.split(',').map((a, index) => {
              this.fileList.push({ name: `模板文件${index + 1}`, id: a })
            })
            this.dataForm.template = this.dataForm.template.split(',')
          } else {
            this.dataForm.template = []
          }

          if (this.dataForm.instructions) {
            this.dataForm.instructions.split(',').map((a, index) => {
              this.fileList2.push({ name: `模板说明文件${index + 1}`, id: a })
            })
            this.dataForm.instructions = this.dataForm.instructions.split(',')
          } else {
            this.dataForm.instructions = []
          }

          // this.fileList = [{ name: '招标文件', url: data.data.id, type: this.filterType(data.data.url) }]
        })
        .catch(() => {})
    },
    remove (type, flie, list) {
      if (type === 1) {
        this.fileList = list
        this.dataForm.template = []
        list.map(a => this.dataForm.template.push(a.id))
      }
      if (type === 2) {
        this.fileList2 = list
        this.dataForm.instructions = []
        list.map(a => this.dataForm.instructions.push(a.id))
      }
    },
    // 上传之前需要格式微调
    beforeUploadHandle (file) {
      if (file.type !== 'application/x-zip-compressed' && file.type !== 'application/x-zip-compressed' && file.type !== 'application/pdf' &&
        file.type !== 'application/msword' && file.type !== 'application/msword' && file.type !== 'application/vnd.ms-excel' && file.type !== 'application/vnd.ms-excel' && file.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' && file.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' && file.type !== 'text/plain' && file.type !== 'text/plain' && file.type !== 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' && file.type !== 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' && file.type !== 'image/png' && file.type !== 'image/jpg' && file.type !== 'image/jpeg' && file.type !== 'image/png' && file.type !== 'image/gif') {
        this.$message.error(this.$t('upload.tip', { 'format': 'jpg、png、gif、txt、doc、xlsx、xls、docx、zip、pdf' }))
        return false
      }
    },
    // 上传成功需要微调格式去除空格
    successHandle (type, res, file, fileList) {
      if (res.code !== 0) {
        console.log(1111)
        return
      }
      console.log(res, fileList)
      if (type === 1) {
        this.fileList = fileList
        this.dataForm.template.push(res.data.ossId)
      } else {
        this.fileList2 = fileList
        this.dataForm.instructions.push(res.data.ossId)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
        }
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) {
            return false
          }
          console.log(this.dataForm.template, this.dataForm.instructions)
          this.dataForm.template = this.dataForm.template.join(',')
          this.dataForm.instructions = this.dataForm.instructions.join(',')
          this.$http[!this.dataForm.id ? 'post' : 'put'](
            '/letter/bgPerformanceDocuments/',
            this.dataForm
          )
            .then(({ data: res }) => {
              if (res.code !== 0) {
                return this.$message.error(res.msg)
              }
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            })
            .catch(() => {})
        })
      },
      1000,
      { leading: true, trailing: false }
    )
  }
}
</script>
