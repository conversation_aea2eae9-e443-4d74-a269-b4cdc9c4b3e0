<template>
  <div class="card" ref="inCcard" v-loading='loading' element-loading-text="准备表单中..">
    <el-card class="box-card" shadow='never' v-if="!isCompoent">
      <steps :active='active' :customList="['选择保函', '提交申请', '初审审核','补充信息','上传文件','复审审核']"></steps>
    </el-card>
    <div style="padding-bottom:60px;" id="center" v-show="active==2">
      <!-- {{this.$route.params}} -->
      <!-- <scUserListSqr :dataForm='dataForm.scUserListSqr' ref="scUserListSqr" @getData='getData'></scUserListSqr>
      <scUserListBsqr :dataForm='dataForm.scUserListBsqr' ref="scUserListBsqr" @getData='getData'></scUserListBsqr> -->
      <caseSituation :dataForm='dataForm.caseSituation' ref="caseSituation"></caseSituation>
      <linkDto :dataForm='dataForm.linkDto' ref="linkDto"></linkDto>
      <codeInfo v-if="!isCompoent" :dataForm='dataForm.codeInfo' ref="codeInfo"></codeInfo>
    </div>
    <div class="foot" v-if="!isCompoent">
      <span style="margin-bottom:10px;margin-right:20px;">
        <el-checkbox v-model="checked">我已阅读并同意 <el-button type="text" @click="showDia('xieyi')">服务协议</el-button>
        </el-checkbox>
      </span>
      <span>
        <el-button type="primary" :disabled="!checked" @click="backStep()" plain>上一步</el-button>
        <!-- <el-button type="primary" :disabled="!checked" :loading="nextLoading" @click="dataFormSubmitHandle(0,dataForm)">保存</el-button> -->
        <el-button type="primary" :disabled="!checked" :loading="nextLoading" @click="dataFormSubmitHandle(2,dataForm)" v-if="active==2">提交</el-button>
      </span>
    </div>

    <xieyi v-if="visible" ref="xieyi"></xieyi>
    <tip v-if="visible" ref="tip"></tip>
    <errorDia v-if="visible" ref="errorDia"></errorDia>
  </div>
</template>
<script>
import xieyi from '@/views/project/guarantee/components/xieyi'
import steps from '@/views/project/guarantee/components/stepA'
import scUserListSqr from './form/applicant'
import scUserListBsqr from './form/respondent'
import caseSituation from './form/caseSituation'
import linkDto from './form/linkDto'
import codeInfo from './form/codeInfo'
import debounce from 'lodash/debounce'

import { newWin } from '@/utils'
// eslint-disable-next-line no-unused-vars
export default {
  // eslint-disable-next-line vue/no-unused-components
  components: { steps, xieyi, scUserListSqr, scUserListBsqr, caseSituation, linkDto, codeInfo },
  data () {
    return {
      loading: false,
      visible: false,
      checked: false,
      nextLoading: false,
      active: 2,
      letterId: '',
      formReset: [
      //   {
      //   name: 'scUserListSqr',
      //   type: 'Array'
      // }, {
      //   name: 'scUserListBsqr',
      //   type: 'Array'
      // },
        {
          name: 'caseSituation',
          type: 'Object'
        }, {
          name: 'litigationDTO',
          type: 'Object'
        }, {
          name: 'codeInfo',
          type: 'Object'
        }, {
          name: 'linkDto',
          type: 'Object'
        }],
      dataForm: {
        letterId: ''
      },
      fileList: []
    }
  },
  props: {
    isCompoent: {
      type: Boolean,
      default: false
    }
  },
  created () {
    this.setObj()
    if (this.$route.params.id && !this.isCompoent) {
      this.getDetail()
    }
    if (this.isCompoent) {}
  },
  methods: {
    showDia (name) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs[name].init()
        this.$refs[name].letterId = this.letterId
      })
    },
    getData (name, data) {
      this.dataForm[name] = data
    },
    getFile (val) {
      this.fileList = val
    },
    setObj () {
      this.formReset.map((a) => {
        if (!this.dataForm[a.name]) this.$set(this.dataForm, a.name, a.type === 'Array' ? [] : {})
      })
      if (this.dataForm.letterId) this.letterId = this.dataForm.letterId
      console.log(this.dataForm)
    },
    backStep () {
      if (this.active === 2) {
        this.$router.push({
          name: 'project-guarantee-common-chooiseIn'
        })
      } else {
        this.active--
      }
    },
    getDetail () {
      this.loading = true
      this.$http
        .get(
          `/letter/bgletterlitigation/getInfo/?letterId=` +
            this.$route.params.id
        )
        .then(({ data: res }) => {
          this.loading = false
          if (res.code !== 0) {
            this.setObj()
            return
          }
          this.dataForm = res.data
          this.setObj()
        })
    },
    dataFormSubmitHandle: debounce(async function (status, data) {
      this.Verification().then((res) => {
        console.log()
        if (!res.includes(false)) {
          let params = {
            letterId: '', // 保函Id
            guaranteeTypeCode: this.$route.params.type, // 保函类型
            issueCode: this.$route.params.insuranceCode, // 出具方code
            ...data,
            type: this.isCompoent ? 2 : 1
          }
          if (this.active === 2 && status === 2) {
            // 提交申请
            const h = this.$createElement
            //  `该保函由${this.insuranfceIno.name}提供，保费为${this.dataForm.bginsuranceinfo.guaranteePrice}，是否继续？`,
            this.$confirm(
              h('p', null, [
                h('span', null, '该保函由 '),
                h(
                  'span',
                  { style: 'color: teal' },
                  `${this.$route.params.insuranceName}`
                ),
                h('span', null, ' 提供，'),
                h('span', null, '是否继续？')
              ]),
              '提示',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }
            )
              .then(async () => {
                this.submit(params)
              })
              .catch(() => {})
          } else { // 保存下一步
            this.submitData(status, params)
          }
        }
      })
    }, 1000, {
      leading: true,
      trailing: false
    }),
    submit (obj) {
      this.$http
        .post(`/letter/bgletterlitigation/submit`, obj)
        .then(async ({ data: res }) => {
          this.nextLoading = false
          this.dataForm.letterId = res.data.letterId
          this.$message.success('提交成功')
          this.goAwit()
        })
    },
    async submitData (status, obj) {
      this.nextLoading = true
      this.$http
        .post(`/letter/bgletterlitigation/saveBh`, obj)
        .then(async ({ data: res }) => {
          this.nextLoading = false
          if (status === 2) {
            this.active = 3
          }
          this.$message.success('保存成功！')
          this.dataForm.letterId = res.data.letterId
          this.dataForm = res.data
        })
    },
    goAwit () {
      this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
        (item) => item.name !== this.$router.name
      )
      this.$router.push({
        name: 'project-letter-litigation-detail'
      })

      const audit = this.$router.resolve({
        name: 'awaitingAudit',
        query: {
          id: this.dataForm.letterId,
          insuranceCode: this.$route.params.insuranceCode
        }
      })
      newWin(audit.href)
    },
    Verification () {
      return new Promise((resolve, reject) => {
        var arr = []
        this.formReset.map((a) => {
          this.$nextTick(() => {
            console.log(a, this.$refs[`${a.name}`])
            if (this.$refs[`${a.name}`] !== undefined) {
              this.$refs[`${a.name}`].push().then((res) => {
                arr.push(res)
              })
            }
          })
        })
        setTimeout(() => {
          resolve(arr)
        }, 300)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.card {
  min-width: 900px;
  // max-width: 1200px;
  min-height: calc(100vh - 200px);
  // margin: 15px auto;
  // margin-bottom: 85px;
}
.foot {
  width: 100%;
  background: #fff;
  position: fixed;
  bottom: 0;
  padding: 10px 0;
  border-top: 1px solid #ddd;
  background: #f9f9f9;
  text-align: center;
  left: 0px !important;
  z-index: 999;
  padding-left: 210px;
  span {
    text-align: center;
  }
}
</style>
