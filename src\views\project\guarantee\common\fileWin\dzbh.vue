<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON>ang
 * @Date: 2020-10-29 16:09:16
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-05-09 09:34:03
-->
<template>
  <el-card v-if="draw" class="fileWin" :style="draw?'width:calc((100% - 210px) - 45%);':'width:0;'">
      <!-- {{dataForm.guaranteeType}} -->
      <template-model ref="model" :dataForm='{...dataForm.bgbiddingproject,...dataForm.bginsuranceinfo,...dataForm.bgGuaranteeApply,...dataForm.bgGuaranteeApply,bbrName:dataForm.bginsurancebbr.name,projectName:dataForm.bgbiddingproject.name}' height='100vh' :code='dataForm.key' :type='dataForm.guaranteeType' :bgType="dataForm.guaranteeTemplateType?dataForm.guaranteeTemplateType:'0'"/>
    <el-button class="close" @click="close" type="danger" icon="el-icon-close" circle></el-button>
  </el-card>
</template>
<script>
import templateModel from '@/views/project/guarantee/components/templateModel'
export default {
  components: {
    templateModel
  },
  props: {
    draw: Boolean,
    dataForm: Object
  },
  data () {
    return {

    }
  },
  created () {

  },
  watch: {
    dataForm: {
      handler (a) {
        console.log(111111111111)
        if (a.bgbiddingproject) {
          this.change(a.bgbiddingproject.guaranteeAmount)
        }
      },
      deep: true
    },
    draw (a) {
      if (a) {
        this.$nextTick(() => {
          console.log(this.$refs.model)
          let model = this.$refs.model.model
          if (!model) {
            this.$refs.model.getModel()
          }
        })
      }
    }
  },
  methods: {
    close () {
      this.$emit('close')
    },
    change (val) {
      if (val) {
        this.$set(
          this.dataForm.bgbiddingproject,
          'guaranteeAmountS',
          this.smalltoBIG((val * 1000000) / 1000000)
        )
      } else {
        this.$set(this.dataForm.bgbiddingproject, 'guaranteeAmountS', this.smalltoBIG(0))
      }
    },

    smalltoBIG (n) {
      var fraction = ['角', '分']
      var digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
      var unit = [
        ['元', '万', '亿'],
        ['', '拾', '佰', '仟']
      ]
      var head = n < 0 ? '欠' : ''
      n = Math.abs(n)

      var s = ''

      for (var i = 0; i < fraction.length; i++) {
        s += (
          digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]
        ).replace(/零./, '')
      }
      s = s || '整'
      n = Math.floor(n)
      // eslint-disable-next-line no-redeclare
      for (var i = 0; i < unit[0].length && n > 0; i++) {
        var p = ''
        for (var j = 0; j < unit[1].length && n > 0; j++) {
          p = digit[n % 10] + unit[1][j] + p
          n = Math.floor(n / 10)
        }
        s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s
      }
      return (
        head +
        s
          .replace(/(零.)*零元/, '元')
          .replace(/(零.)+/g, '零')
          .replace(/^整$/, '零元整')
      )
    }
  }
}
</script>
<style lang="scss" scoped>
.fileWin {
  width: 0;
  height: 100vh;
  z-index: 2001;
  margin: 0;
  position: fixed;
  top: 0;
  transition: width 0.3s ease-in-out;
  right: 0;
  .close {
    padding: 5px;
    position: absolute;
    right: 15px;
    top: 15px;
  }
}
</style>
