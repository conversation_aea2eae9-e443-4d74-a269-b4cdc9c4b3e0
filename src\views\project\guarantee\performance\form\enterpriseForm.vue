<template>
  <el-row :gutter="20" style="margin:15px 20px 0;">
    <el-form v-if="dataForm" label-position="left" :model="dataForm" ref="dataForm" :rules="dataRule" label-width="140px">
      <el-card class="box-card" shadow='never'>
        <div slot="header" class="clearfix">
          <span class="title">保函信息</span>
        </div>
        <div>
          <el-col :xs="24" :xl="24">
            <div class="formCon">
              <el-row :gutter="40">
                <el-col :xs="24" :xl="24">
                  <el-form-item label="保函类型">

                     {{getDictName($route.params.type)}}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :xs="24" :xl="24">
                  <el-form-item label="出具方">
                     {{pakeage.alias?pakeage.alias:$route.params.insuranceName}}
                  </el-form-item>
                </el-col>
              </el-row>
              <slot></slot>
            </div>
          </el-col>
        </div>
      </el-card>
      <el-card class="box-card" shadow='never'>
        <div slot="header" class="clearfix">
          <span class="title">企业信息（投标方）</span>
        </div>
        <div>
          <el-col :xs="24" :xl="24">
            <div class="formCon">
              <el-row :gutter="40">
                <el-col :xs="24" :xl="24">
                  <el-form-item label="企业名称" prop="name">
                    <el-input v-model.trim="dataForm.name" size="medium" :disabled="dataForm.id&&dataForm.status==2" class="wd180" style="width:100%;" placeholder="名称">
                      <el-button slot="append" icon="el-icon-search" type="success" @click="sqrcx">企查查
                      </el-button>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :xs="24" :lg='24'>
                  <el-form-item label="企业法定代表人" prop="corporation">
                    <el-input v-model.trim="dataForm.corporation" size="medium" style="width:100%;" class="wd180" placeholder="法定代表人"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg='24' :xl="24">
                  <el-form-item label="机构证件类型" prop="certType">
                    <el-select v-model.trim="dataForm.certType" size="small" :disabled="dataForm.id&&dataForm.status==2" class="wd180" style="width:100%;" placeholder="证件类型">
                      <el-option v-for="item in options.certTypeoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg='24' :xl="24">
                  <el-form-item label="机构证件号码" prop="certCode">
                    <el-input v-model.trim="dataForm.certCode" size="small" :disabled="dataForm.id&&dataForm.status==2" class="wd180" style="width:100%;" placeholder="证件号码"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg='24' :xl="24">
                  <el-form-item label="公司固话" prop="phoneNumber">
                    <el-input v-model.trim="dataForm.phoneNumber" size="medium" class="wd180" style="width:100%;" placeholder="公司电话"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg='24' :xl="24">
                  <el-form-item label="联系人" prop="linkman">
                    <el-input v-model.trim="dataForm.linkman" size="medium" class="wd180" style="width:100%;" placeholder="联系人"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg='24' :xl="24">
                  <el-form-item label="联系人电话" prop="linkmanTel">
                    <el-input v-model.trim="dataForm.linkmanTel" size="medium" class="wd180" style="width:100%;" placeholder="联系人电话"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg='24' :xl="24">
                  <el-form-item label="经营地址" prop="address">
                    <el-input v-model.trim="dataForm.address" type="textarea" :rows="2" size="medium" class="wd180" style="width:100%;" placeholder="地址"></el-input>
                  </el-form-item>
                </el-col>
                <template v-if="$route.params.type === 'lydbbh'&&$route.params.insuranceCode === 'ZC'">
                  <el-col :xs="24" :lg='24' :xl="24">
                  <el-form-item label="银行名称" prop="bankName">
                    <el-input v-model.trim="dataForm.bankName" size="medium" class="wd180" style="width:100%;" placeholder="银行名称"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg='24' :xl="24">
                  <el-form-item label="银行账户号码" prop="bankAccountNo">
                    <el-input v-model.trim="dataForm.bankAccountNo" :rows="2" size="medium" class="wd180" style="width:100%;" placeholder="银行账户号码"></el-input>
                  </el-form-item>
                </el-col>
                </template>
              </el-row>
            </div>
          </el-col>
        </div>
      </el-card>

    </el-form>
  </el-row>
</template>
<script>
import { getDict } from '@/utils/index'
export default {
  name: 'bgGuaranteeApply',
  data () {
    return {
      getDicListURL: '/sys/dict/type/',
      options: {
        certTypeoptions: []
      },
      guaranteeTemplate: ''
    }
  },
  props: {
    dataForm: {
      type: Object
    },
    pakeage: {
      type: Object
    }
  },
  computed: {
    dataRule () {
      var checkPhone = (rule, value, callback) => {
        if (!value) {
          return callback(new Error('手机号不能为空'))
        } else {
          const reg = /^1[3|4|5|7|8|9][0-9]\d{8}$/
          console.log(reg.test(value))
          if (reg.test(value)) {
            callback()
          } else {
            return callback(new Error('请输入正确的手机号'))
          }
        }
      }
      var checkZuoPhone = (rule, value, callback) => {
        if (!value) {
          return callback(new Error('请输入正确的电话号'))
        } else {
          // eslint-disable-next-line no-useless-escape
          const reg = /(^[0-9]{3,4}\-[0-9]{3,8}$)|(^[0-9]{3,8}$)|(^\([0-9]{3,4}\)[0-9]{3,8}$)|(^0{0,1}13[0-9]{9}$)/
          console.log(reg.test(value))
          if (reg.test(value)) {
            callback()
          } else {
            return callback(new Error('请输入正确的电话号'))
          }
        }
      }
      return {
        name: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        corporation: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        bankName: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        bankAccountNo: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        certType: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        certCode: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        certTerm: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        certTermType: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        linkman: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        linkmanTel: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          },
          { validator: checkPhone, trigger: 'blur' }
        ],
        phoneNumber: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          },
          { validator: checkZuoPhone, trigger: 'blur' }
        ],
        address: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created () {
    this.getCertTypeTTInfo()
  },
  methods: {
    getDictName (type) {
      console.log(type, getDict('电子保函类型').filter(a => a.dictCode === type))
      let arr = getDict('电子保函类型').filter(a => a.dictCode === type)
      return arr.length > 0 ? arr[0].dictName : ''
    },
    sqrcx () {
      var url = 'https://www.qcc.com/search?key='
      window.open(url + this.dataForm.name)
    },
    // 证件信息
    getCertTypeTTInfo () {
      this.$http
        .get(this.getDicListURL + 'certTypeTT')
        .then(({ data: res }) => {
          this.options.certTypeoptions = [
            ...this.options.certTypeoptions,
            ...res.data.list
          ]
        })
        .catch(() => {})
    },
    push () {
      return new Promise((resolve, reject) => {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
</style>
