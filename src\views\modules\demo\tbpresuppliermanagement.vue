<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-preparation__bimpresuppliermanagement}">
      <el-form :inline="true" :model="dataForm" >
        <el-form-item >
          <el-input v-model="dataForm.code"  clearable  placeholder="编码"></el-input>
        </el-form-item>
        <el-form-item >
          <el-input v-model="dataForm.name" clearable   placeholder="名称"></el-input>
        </el-form-item>
        <el-form-item >
          <el-input v-model="dataForm.projectCode" clearable   placeholder="项目编码"></el-input>
        </el-form-item>
        <el-form-item >
          <el-input v-model="dataForm.projectName"  clearable  placeholder="项目名称"></el-input>
        </el-form-item>
        <el-form-item >
          <el-select v-model="dataForm.status" clearable  placeholder="请选择启用状态">
            <el-option
                    v-for="item in statusoptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="danger"   @click="getDataList(1)">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
           <el-button type="primary" @click="importHandle()">{{ $t('import') }}</el-button>
         </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('preparation:bimpresuppliermanagement:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('preparation:bimpresuppliermanagement:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('preparation:bimpresuppliermanagement:update')" type="danger" @click="enableHandle()">{{ $t('enable') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('preparation:bimpresuppliermanagement:update')" type="danger" @click="stopHandle()">{{ $t('stop') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle"   style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="code" label="供应商管理编码" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="name" label="供应商管理名称" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="projectCode" label="项目编码" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="projectName" label="项目名称" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="contractCode" label="合同编码" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="contractName" label="合同名称" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="creditRating" label="信用等级" sortable="custom" header-align="center" align="center">
                <template slot-scope="scope">
                    <el-tag  size="mini" type="danger"><span>{{fomatMethod1(scope.row.creditRating)}}</span></el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="website" label="网站" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="evaluator" label="评价人" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="pcaName" label="省市区" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="projectAddress" label="供应商地址" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="supplierType" label="供应商类型" sortable="custom" header-align="center" align="center">
                <template slot-scope="scope">
                    <el-tag  size="mini" type="danger"><span>{{fomatMethod(scope.row.supplierType)}}</span></el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="contacts1" label="联系人1" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="telephone1" label="联系电话1" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="email1" label="联系邮箱1" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="contacts2" label="联系人2" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="telephone2" label="联系电话2" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="email2" label="联系邮箱2" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="contacts3" label="联系人3" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="telephone3" label="联系电话3" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="email3" label="联系邮箱3" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="unitName" label="单位名称" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="unitAddress" label="单位地址" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="unitTelephone" label="单位电话" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="openingBank" label="开户行" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="accountNumber" label="账号" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="axpayerId" label="纳税人识别号" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="cooperationEndTime" label="合作结束时间" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="cooperationWays" label="合作方式" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="registeredCapital" label="注册资本" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="provideProductsService" label="提供的产品与服务" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="chargingMethod" label="收费方式" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="remarks" label="备注" sortable="custom" header-align="center" align="center"></el-table-column>
              <el-table-column prop="status" sortable="custom" label="状态" header-align="center" align="center">
   <template slot-scope="scope">
       <el-tag v-if="scope.row.status == 0" size="mini" type="danger">{{ $t('user.status0') }}</el-tag>
       <el-tag v-else size="mini" type="success">{{ $t('user.status1') }}</el-tag>
   </template>
</el-table-column>
              <el-table-column prop="createDate" label="创建时间" sortable="custom" header-align="center" align="center"></el-table-column>
              <el-table-column prop="updateDate" label="更新时间" sortable="custom" header-align="center" align="center"></el-table-column>
              <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button v-if="$hasPermission('preparation:bimpresuppliermanagement:update')" type="text" size="mini" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
              <el-dropdown>
              <span class="el-dropdown-link">
                <el-button  type="text" size="mini">更多操作<i class="el-icon-arrow-down el-icon--right"></i></el-button>
              </span>
                  <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item> <el-button v-if="$hasPermission('preparation:bimpresuppliermanagement:delete')" type="text" size="mini" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button></el-dropdown-item>
                          <el-dropdown-item><el-button v-if="$hasPermission('preparation:bimpresuppliermanagement:update')&&scope.row.status==0" type="text" size="mini" @click="enableHandle(scope.row.id)">{{ $t('enable') }}</el-button></el-dropdown-item>
                              <el-dropdown-item><el-button v-if="$hasPermission('preparation:bimpresuppliermanagement:update')&&scope.row.status==1" type="text" size="mini" @click="stopHandle(scope.row.id)">{{ $t('stop') }}</el-button></el-dropdown-item>
                  </el-dropdown-menu>
              </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
        <!-- 弹窗, 上传文件 -->
      <upload v-if="uploadVisible" ref="upload" @refreshDataList="getDataList"></upload>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './tbpresuppliermanagement-add-or-update'
import Upload from './tbpresuppliermanagement-upload'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/demo/tbpresuppliermanagement/page',
        getDataListIsPage: true,
        exportURL: '/demo/tbpresuppliermanagement/export',
        deleteURL: '/demo/tbpresuppliermanagement',
        enableURL: '/demo/tbpresuppliermanagement/enable',
        stopURL: '/demo/tbpresuppliermanagement/stop',
        deleteIsBatch: true
      },
      statusoptions: [{
        value: 1,
        label: '正常'
      }, {
        value: 0,
        label: '停用'
      }],
      dataForm: {
        code: '',
        name: '',
        projectName: '',
        projectCode: '',
        status: 1
      },
      orderField: 'code',
      order: 'asc',
      getDicListURL: '/sys/dict/type/',
      supplierTypeoptions: [],
      supplierTypemaps: '',
      creditRatingmaps: '',
      creditRatingoptions: '',
      uploadVisible: false
    }
  },
  components: {
    AddOrUpdate,
    Upload
  },
  activated () {
    this.getSupplierTypeInfo()
    this.getCreditRating()
  },
  methods: {
    fomatMethod (value) {
      return this.supplierTypemaps[value]
    },
    fomatMethod1 (value) {
      return this.creditRatingmaps[value]
    },
    // 导入
    importHandle () {
      this.uploadVisible = true
      this.$nextTick(() => {
        this.$refs.upload.init()
      })
    },
    // 获取供应商类型信息
    getSupplierTypeInfo () {
      this.$http.get(this.getDicListURL + 'supplierType').then(({ data: res }) => {
        this.supplierTypeoptions = {
          ...this.supplierTypeoptions,
          ...res.data.list
        }
        this.supplierTypemaps = {
          ...this.supplierTypemaps,
          ...res.data.map
        }
      }).catch(() => {})
    },
    // 获取供应商信用等级
    getCreditRating () {
      this.$http.get(this.getDicListURL + 'creditRating').then(({ data: res }) => {
        this.creditRatingoptions = {
          ...this.creditRatingoptions,
          ...res.data.list
        }

        this.creditRatingmaps = {
          ...this.creditRatingmaps,
          ...res.data.map
        }
      }).catch(() => { })
    },
    // 状态启用具体代码需微调格式去除空格
    enableHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(this.$t('prompt.info', { 'handle': '启用' }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.$http.put(this.mixinViewModuleOptions.enableURL, id ? [id] : this.dataListSelections.map(item => item.id)).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.getDataList()
            }
          })
        }).catch(() => {})
      }).catch(() => {})
    },
    // 状态停用
    stopHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(this.$t('prompt.info', { 'handle': '停用' }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        // 具体代码需微调格式去除空格
        this.$http.put(this.mixinViewModuleOptions.stopURL, id ? [id] : this.dataListSelections.map(item => item.id)).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.getDataList()
            }
          })
        }).catch(() => {})
      }).catch(() => {})
    }
  }
}

</script>
