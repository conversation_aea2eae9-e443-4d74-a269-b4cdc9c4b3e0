<template>
  <el-row :gutter="20"  style="margin:15px 20px 0;" :style="examine?'':'display:none;'">
    <el-card class="box-card" shadow='never'>
      <div slot="header" class="clearfix">
        <span class="title">编号信息</span>
      </div>
      <div>
        <el-col :xs="24" :lg="24" >
          <div class="formCon">
            <template>
              <!-- {{paramsForm.paramsRules}} -->
              <el-form :model="dataForm" ref="dataForm" label-position="left" style="margin-bottom:15px;" :rules="dataRule" label-width="140px">
                  <el-col :xs="24" :lg="12">
                  <el-form-item label="合同编号">
                    <!-- <el-input v-model="dataForm.contractNo" class="wd180" size="mini"></el-input> -->
                    亿&nbsp;<el-input style="width:70px;" size="mini" v-model="bTitle"></el-input>
                    保[&nbsp;<el-input style="width:70px;" size="mini" v-model="bYear"></el-input>&nbsp;]
                    <el-input style="width:70px;" size="mini" v-model="bNum"></el-input>&nbsp;号
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg="12">
                  <el-form-item label="保函编号">
                     亿&nbsp;<el-input style="width:70px;" size="mini" v-model="bhTitle"></el-input>
                    保函[&nbsp;<el-input style="width:70px;" size="mini" v-model="bhYear"></el-input>&nbsp;]
                    <el-input style="width:70px;" size="mini" v-model="bhNum"></el-input>&nbsp;号
                  </el-form-item>
                </el-col>
               <el-col :xs="24" :lg="12">
                  <el-form-item label="审批表编号">
                     亿&nbsp;<el-input style="width:70px;" size="mini" v-model="spTitle"></el-input>
                    审[&nbsp;<el-input style="width:70px;" size="mini" v-model="spYear"></el-input>&nbsp;]
                    <el-input style="width:70px;" size="mini" v-model="spNum"></el-input>&nbsp;号
                  </el-form-item>
                </el-col>
              </el-form>
            </template>
          </div>
        </el-col>
      </div>
    </el-card>
    <!-- propertyPreservationType //财产保全类型 1诉前，2诉中
    preservationAmount //保全金额
    charge //收费
    causeOfAction //案由
    code //编号
    court //受理法院 -->
  </el-row>
</template>
<script>
import moment from 'moment'
export default {
  data () {
    return {
      bTitle: '',
      bYear: '',
      bNum: '',
      bhTitle: '',
      bhYear: '',
      bhNum: '',
      spTitle: '',
      spYear: '',
      spNum: ''
    }
  },
  props: {
    examine: {
      type: Boolean,
      default: false
    },
    dataForm: {
      type: Object
    }
  },
  watch: {
    dataForm: {
      handler (a) {
        if (a && !this.examine) {
          this.$set(a, 'contractNo', '')
          this.$set(a, 'guaranteeNo', '')
          this.$set(a, 'approvalNo', '')
        }
        if (this.examine) {
          console.log(111)
          let time = moment(new Date()).format('YYYY')
          console.log(time)
          this.bYear = time
          this.bhYear = time
          this.spYear = time
        }
      },
      deep: true
    }
  },
  computed: {
    dataRule () {
      return {

      }
    }
  },
  // props: ['dataForm'],
  methods: {
    push () {
      return new Promise((resolve, reject) => {
        this.$set(this.dataForm, 'contractNo', `亿${this.bTitle}保[${this.bYear}]${this.bNum}号`)
        this.$set(this.dataForm, 'guaranteeNo', `亿${this.bhTitle}保函[${this.bhYear}]${this.bhNum}号`)
        this.$set(this.dataForm, 'approvalNo', `亿${this.spTitle}审[${this.spYear}]${this.spNum}号`)
        console.log(this.dataForm)
        resolve(true)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.wd180 {
  width: 260px !important;
}
.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
</style>
