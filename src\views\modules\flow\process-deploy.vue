<!--
 * @Descripttion:
 * @Author: liang
 * @Date: 2021-11-05 08:30:32
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-12-10 15:24:35
-->
<template>
  <el-dialog :visible.sync="visible" :title="$t('process.deployFile')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-upload name="processFile" :action="url" :file-list="fileList" drag :before-upload="beforeUploadHandle" :on-success="successHandle" class="text-center">
      <i class="el-icon-upload"></i>
      <div class="el-upload__text" v-html="$t('upload.text')"></div>
      <template v-slot:tip>
        <div class="el-upload__tip">
          {{ $t("upload.tip", { format: "zip、bpmn20.xml、bpmn" }) }}
        </div>
      </template>
    </el-upload>
  </el-dialog>
</template>

<script>
import Cookies from 'js-cookie'
export default {
  data () {
    return {
      visible: true,
      url: '',
      fileList: []
    }
  },
  methods: {
    init () {
      this.visible = true
      this.url = `${window.SITE_CONFIG['apiURL']}/process/deploy?access_token=${Cookies.get('token')}`
      this.fileList = []
    },
    // 上传之前
    beforeUploadHandle (file) {
      if (!/.+\.zip$/.test(file.name) && !/.+\.xml$/.test(file.name) && !/.+\.bar$/.test(file.name) && !/.+\.bpmn$/.test(file.name)) {
        this.$message.error(this.$t('upload.tip', { format: 'zip、xml、bar、bpmn' }))
        return false
      }
    },
    // 上传成功
    successHandle (res) {
      if (res.code !== 0) {
        return
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          this.visible = false
          this.$emit('refreshDataList')
        }
      })
    }
  }
}
</script>
<style scoped>
.avatar-uploader-icon {
  padding: 60px 0;
}
</style>
