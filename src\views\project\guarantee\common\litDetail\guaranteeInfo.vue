<!--
 * @Descripttion:
 * @Author: kong<PERSON>qiang
 * @Date: 2021-02-22 15:22:23
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-02-22 15:29:19
-->
<template>
  <el-col :span="20">
    <el-col :span="24" class="con-title">
      保函信息
    </el-col>
    <el-col :xs="24" :lg="12">
      <el-form-item prop="guaranteeAmount" label="保全金额">
        {{dataForm.guaranteeAmount}}元
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="12">
      <el-form-item prop="guaranteePrice" label="保费">
        {{dataForm.guaranteePrice}}元
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="24">
      <el-form-item label="业务承办人 / 电话" prop="scUserId">
        {{dataForm.userName}} / {{dataForm.userTel}}
      </el-form-item>
    </el-col>
  </el-col>
</template>
<script>
export default {
  props: {
    dataForm: Object
  },
  data () {
    return {
      options: [
        {
          value: '1',
          label: '诉前'
        },
        {
          value: '2',
          label: '诉中'
        }
      ]
    }
  },
  methods: {}
}
</script>
<style lang="scss" scoped>
.con-title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-bottom: 10px;
}
</style>
