<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-letter__bgguaranteeissue}">
      <el-form :inline="true" :model="dataForm" >
        <el-form-item>
          <el-input v-model="dataForm.name" style="width:300px;" clearable placeholder="机构名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="dataForm.corporation" clearable placeholder="法定代表人"></el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model="dataForm.orgType" clearable placeholder="请选择机构类型类型">
            <el-option v-for="item in orgTypeOptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
            </el-option>
          </el-select>
        </el-form-item>
        <!--<el-form-item >
           <el-select v-model="dataForm.status" clearable  placeholder="请选择启用状态">
            <el-option
                    v-for="item in statusoptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button @click="getDataList(1)">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="danger" @click="resetHandle()">重置</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgguaranteeissue:import')" type="primary" @click="importHandle()">{{ $t('import') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgguaranteeissue:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgguaranteeissue:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
        <!-- <el-form-item>
          <el-button v-if="$hasPermission('letter:bgguaranteeissue:update')" type="danger" @click="enableHandle()">{{ $t('enable') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgguaranteeissue:update')" type="danger" @click="stopHandle()">{{ $t('stop') }}</el-button>
        </el-form-item> -->
        <el-form-item>
          <el-button type="danger" @click="selectIssue()">添加出具机构</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="name" label="机构名称" width="180px" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="phoneNumber" label="机构联系电话" sortable="custom" header-align="center" align="center" width="180"></el-table-column>
        <el-table-column prop="corporation" label="法定代表人" sortable="custom" header-align="center" align="center" width="150"></el-table-column>
        <el-table-column prop="orgType" label="机构类型" width="120px" sortable="custom" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag size="mini" type="danger"><span>{{fomatMethod(scope.row.orgType)}}</span></el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="auditingHour" label="保函审核时长" width="170px" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="linkman" label="联系人" sortable="custom" header-align="center" align="center" width="150"></el-table-column>
        <el-table-column prop="linkmanTel" label="联系人电话" sortable="custom" header-align="center" align="center" width="150"></el-table-column>
        <!-- <el-table-column prop="deptId" label="部门id" sortable="custom" header-align="center" align="center" hidden="hidden"></el-table-column> -->
        <el-table-column prop="status" sortable="custom" label="状态" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == 2" size="small" type="success">已认证</el-tag>
            <el-tag v-else size="small" type="danger">未认证</el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button v-if="$hasPermission('letter:bgguaranteeissue:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
            <!-- <el-button type="text" size="small" @click="userInfoHandle(scope.row.deptId)">用户管理</el-button> -->
            <el-dropdown>
              <span class="el-dropdown-link">
                <el-button type="text" size="mini">更多操作<i class="el-icon-arrow-down el-icon--right"></i></el-button>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button type="text" size="small" @click="displayHandle(scope.row.id)">浏览</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('letter:bgguaranteeprice:page')" type="text" size="small" @click="priceHandle(scope.row.id)">服务定价</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
      <!-- 弹窗, 上传文件 -->
      <upload v-if="uploadVisible" ref="upload" @refreshDataList="getDataList"></upload>
      <IssueSelect v-if="selectVisible" ref="IssueSelect" @refreshDataList="getDataList"></IssueSelect>
      <!-- 浏览 -->
      <display v-if="displayVisible" ref="display" @refreshDataList="refreshList"></display>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './bgguaranteeissue-add-or-update'
import Upload from './bgguaranteeissue-upload'
import IssueSelect from './bgguaranteeplatform-add-or-set'
import Display from './bgguaranteeissue-display'

export default {
  mixins: [mixinViewModule],
  data () {
    return {
      selectVisible: false,
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgguaranteeissue/page',
        getDataListIsPage: true,
        exportURL: '/letter/bgguaranteeissue/export',
        deleteURL: '/letter/bgguaranteeissue',
        enableURL: '/letter/bgguaranteeissue/enable',
        stopURL: '/letter/bgguaranteeissue/stop',
        deleteIsBatch: true
      },
      statusoptions: [
        {
          value: 0,
          label: '未认证'
        },
        {
          value: 2,
          label: '已认证'
        }
      ],
      orgTypeOptions: '',
      dataForm: {
        name: '',
        platformId: this.$route.params.platformId,
        isPlatform: 1,
        corporation: '',
        orgType: ''
      },
      orderField: 'code',
      order: 'asc',
      uploadVisible: false,
      orgTypemaps: '',
      getDicListURL: '/sys/dict/type/'
    }
  },
  components: {
    AddOrUpdate,
    Upload,
    IssueSelect,
    Display
  },
  activated () {
    this.getOrgTypeInfo()
  },
  methods: {
    fomatMethod (value) {
      return this.orgTypemaps[value]
    },
    // 导入
    importHandle () {
      this.uploadVisible = true
      this.$nextTick(() => {
        this.$refs.upload.init()
      })
    },
    // 状态启用具体代码需微调格式去除空格
    enableHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(
        this.$t('prompt.info', { handle: '启用' }),
        this.$t('prompt.title'),
        {
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          this.$http
            .put(
              this.mixinViewModuleOptions.enableURL,
              id ? [id] : this.dataListSelections.map(item => item.id)
            )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getDataList()
                }
              })
            })
            .catch(() => {})
        })
        .catch(() => {})
    },
    // 状态停用
    stopHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(
        this.$t('prompt.info', { handle: '停用' }),
        this.$t('prompt.title'),
        {
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          // 具体代码需微调格式去除空格
          this.$http
            .put(
              this.mixinViewModuleOptions.stopURL,
              id ? [id] : this.dataListSelections.map(item => item.id)
            )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getDataList()
                }
              })
            })
            .catch(() => {})
        })
        .catch(() => {})
    },
    // 查看用户
    userInfoHandle (deptId) {
      if (!deptId || deptId === 0) {
        return this.$message({
          message: this.$t('prompt.selectBatch'),
          type: 'warning',
          duration: 500
        })
      }

      this.$router.push({ name: 'user', params: { deptId: deptId } })
    },
    // 服务定价
    priceHandle (issueId) {
      if (!issueId || issueId === 0) {
        return this.$message({
          message: this.$t('prompt.selectBatch'),
          type: 'warning',
          duration: 500
        })
      }
      this.$router.push({ name: 'price', params: { issueId: issueId } })
    },
    // 获取机构类型
    getOrgTypeInfo () {
      this.$http
        .get(this.getDicListURL + 'orgType')
        .then(({ data: res }) => {
          this.orgTypeOptions = {
            ...this.orgTypeOptions,
            ...res.data.list
          }
          this.orgTypemaps = {
            ...this.orgTypemaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    },
    // 按钮点击事件
    selectIssue () {
      this.selectVisible = true
      this.$nextTick(() => {
        this.$refs.IssueSelect.platFormId = this.platformId
        this.$refs.IssueSelect.init()
      })
    }
  }
}
</script>
