<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-demo__tbauditinstance}">
      <el-form :inline="true" :model="dataForm" >
        <el-form-item >
          <el-input v-model="dataForm.code"  clearable  placeholder="编码"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="dataForm.name" clearable   placeholder="名称"></el-input>
        </el-form-item>
        <el-form-item >
          <el-select v-model="dataForm.status" clearable  placeholder="请选择启用状态">
            <el-option
                    v-for="item in statusoptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
            </el-option>
          </el-select>
            <el-input v-model="dataForm.auditNodeStatus" hidden="hidden"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="danger"   @click="getDataList(1)">{{ $t('query') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle"   style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
            <el-table-column prop="name" label="实例名称" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="tableName" label="业务表名称" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="auditType" label="审核类型" sortable="custom" header-align="center" align="center"></el-table-column>
             <el-table-column prop="auditNodeName" label="节点审核名称" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="auditNodeStatus" label="审核节点状态" sortable="custom" header-align="center" align="center">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.auditNodeStatus == '0'" size="mini" type="danger">申请未提交</el-tag>
                    <el-tag v-if="scope.row.auditNodeStatus == '1'" size="mini" type="success">未审核</el-tag>
                    <el-tag v-if="scope.row.auditNodeStatus == '2'" size="mini" type="danger">审核完成</el-tag>
                </template>
            </el-table-column>
                <el-table-column prop="status" sortable="custom" label="状态" header-align="center" align="center">
   <template slot-scope="scope">
       <el-tag v-if="scope.row.status == 0" size="small" type="danger">{{ $t('user.status0') }}</el-tag>
       <el-tag v-else size="small" type="success">{{ $t('user.status1') }}</el-tag>
   </template>
</el-table-column>
              <el-table-column prop="createDate" label="创建时间" sortable="custom" header-align="center" align="center"></el-table-column>
              <el-table-column prop="updateDate" label="更新时间" sortable="custom" header-align="center" align="center"></el-table-column>
              <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
              <el-button v-if="$hasPermission('demo:tbauditinstance:update')&&scope.row.auditNodeStatus =='1'" type="text"
                         size="mini"   @click="auditNodeHandle(scope.row.id,scope.row.tableId,scope.row.tableName)">审核
              </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
        <!-- 审核窗口 -->
      <audit-instance v-if="auditInstanceVisible" ref="auditInstance" @refreshDataList="refreshList"></audit-instance>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AuditInstance from '@/views/modules/demo/tbauditinstance-audit'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/demo/tbauditinstance/page',
        getDataListIsPage: true,
        exportURL: '/demo/tbauditinstance/export',
        deleteURL: '/demo/tbauditinstance',
        enableURL: '/demo/tbauditinstance/enable',
        stopURL: '/demo/tbauditinstance/stop',
        deleteIsBatch: true
      },
      auditInstanceVisible: false,
      statusoptions: [{
        value: 1,
        label: '正常'
      }, {
        value: 0,
        label: '停用'
      }],
      dataForm: {
        code: '',
        name: '',
        auditNodeStatus: '1',
        status: 1
      },
      orderField: 'create_date',
      order: 'asc',
      uploadVisible: false
    }
  },
  components: {
    AuditInstance
  },
  methods: {
    auditNodeHandle (id, tableId, tableName) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.auditInstanceVisible = true
      this.$nextTick(() => {
        this.$refs.auditInstance.dataForm.id = id
        this.$refs.auditInstance.dataForm.tableId = tableId
        this.$refs.auditInstance.dataForm.tableName = tableName
        this.$refs.auditInstance.init()
      })
    }
  }
}

</script>
