<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-basic__bimregion}">
      <el-form :inline="true" :model="dataForm">
        <el-form-item>
          <el-input v-model="dataForm.code" clearable placeholder="编码"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="dataForm.name" clearable placeholder="名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model="dataForm.nodeLevel" clearable placeholder="请选择所属区划">
            <el-option :key="0" label="国" :value="0">
            </el-option>
            <el-option :key="1" label="省" :value="1">
            </el-option>
            <el-option :key="2" label="市" :value="2">
            </el-option>
            <el-option :key="3" label="区" :value="3">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="dataForm.status" clearable placeholder="请选择启用状态">
            <el-option v-for="item in statusoptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="danger" @click="getDataList(1)">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="importHandle()">{{ $t('import') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('basic:bimregion:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('basic:bimregion:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('basic:bimregion:update')" type="danger" @click="enableHandle()">{{ $t('enable') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('basic:bimregion:update')" type="danger" @click="stopHandle()">{{ $t('stop') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="code" label="区划编码" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="name" label="区划名称" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="pinyin" label="拼音" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="mergerName" label="全名称" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="lat" label="经度" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="lng" label="纬度" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="parentCode" label="父节点编码" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="nodeLevel" label="所属区划" sortable="custom" header-align="center" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.nodeLevel==='0'">国</span>
            <span v-if="scope.row.nodeLevel==='1'">省</span>
            <span v-if="scope.row.nodeLevel==='2'">市</span>
            <span v-if="scope.row.nodeLevel==='3'">区</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" sortable="custom" label="状态" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == 0" size="small" type="danger">{{ $t('user.status0') }}</el-tag>
            <el-tag v-else size="small" type="success">{{ $t('user.status1') }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createDate" label="创建时间" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="updateDate" label="更新时间" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button v-if="$hasPermission('basic:bimregion:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
            <el-dropdown>
              <span class="el-dropdown-link">
                <el-button type="text" size="mini">更多操作<i class="el-icon-arrow-down el-icon--right"></i></el-button>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('basic:bimregion:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('basic:bimregion:update')&&scope.row.status==0" type="text" size="small" @click="enableHandle(scope.row.id)">{{ $t('enable') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('basic:bimregion:update')&&scope.row.status==1" type="text" size="small" @click="stopHandle(scope.row.id)">{{ $t('stop') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
      <!-- 弹窗, 上传文件 -->
      <upload v-if="uploadVisible" ref="upload" @refreshDataList="getDataList"></upload>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './tbregion-add-or-update'
import Upload from './tbregion-upload'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/demo/tbregion/page',
        getDataListIsPage: true,
        exportURL: '/demo/tbregion/export',
        deleteURL: '/demo/tbregion',
        enableURL: '/demo/tbregion/enable',
        stopURL: '/demo/tbregion/stop',
        deleteIsBatch: true
      },
      statusoptions: [
        {
          value: 1,
          label: '正常'
        },
        {
          value: 0,
          label: '停用'
        }
      ],
      dataForm: {
        code: '',
        name: '',
        nodeLevel: '',
        status: 1
      },
      orderField: 'code',
      order: 'asc',
      uploadVisible: false
    }
  },
  components: {
    AddOrUpdate,
    Upload
  },
  methods: {
    // 导入
    importHandle () {
      this.uploadVisible = true
      this.$nextTick(() => {
        this.$refs.upload.init()
      })
    },
    // 状态启用具体代码需微调格式去除空格
    enableHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(
        this.$t('prompt.info', { handle: '启用' }),
        this.$t('prompt.title'),
        {
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          this.$http
            .put(
              this.mixinViewModuleOptions.enableURL,
              id ? [id] : this.dataListSelections.map((item) => item.id)
            )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getDataList()
                }
              })
            })
            .catch(() => {})
        })
        .catch(() => {})
    },
    // 状态停用
    stopHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(
        this.$t('prompt.info', { handle: '停用' }),
        this.$t('prompt.title'),
        {
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          // 具体代码需微调格式去除空格
          this.$http
            .put(
              this.mixinViewModuleOptions.stopURL,
              id ? [id] : this.dataListSelections.map((item) => item.id)
            )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getDataList()
                }
              })
            })
            .catch(() => {})
        })
        .catch(() => {})
    }
  }
}
</script>
