<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"  :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="colConfig">
          <!-- <el-form-item label="收费编码" prop="code">
     <el-input v-model="dataForm.code" placeholder="收费编码"></el-input>
</el-form-item> -->
          <el-form-item label="保函类型" prop="guaranteeType">
            <el-select v-model="dataForm.guaranteeType" clearable placeholder="请选择保函类型">
              <el-option v-for="item in guaranteeTypeOptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="收费名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="收费名称"></el-input>
          </el-form-item>

          <!-- <el-form-item label="收费类型" prop="priceType">
     <el-input v-model="dataForm.priceType" placeholder="收费类型1 定额 2@click="getDataList(1)""></el-input>
</el-form-item> -->
          <el-form-item label="收费类型" prop="priceType">
            <el-select v-model="dataForm.priceType" clearable placeholder="请选择收费类型">
              <el-option v-for="item in priceTypeOptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="出具机构名称" prop="issueName" hidden="hidden">
            <el-input v-model="dataForm.issueName" placeholder="出具机构名称" hidden="hidden"></el-input>
          </el-form-item>
          <el-form-item label="担保金额区间最大值" prop="guaranteeAmount">
            <el-input-number placeholder="请输入担保金额" class="wd240" v-model="dataForm.guaranteeAmount" controls-position="right" :precision="0" :min="0" :max="10000000"></el-input-number>

          </el-form-item>
          <el-form-item label="保函金额" prop="letterPrice">
            <el-input v-model="dataForm.letterPrice" placeholder="保函金额"></el-input>
          </el-form-item>
          <el-form-item label="收取费率" prop="percentValue">
            <el-input v-model="dataForm.percentValue" placeholder="收取费率"></el-input>
          </el-form-item>
          <el-form-item label="平台使用费" prop="platformPrice">
            <el-input v-model="dataForm.platformPrice" placeholder="平台使用费"></el-input>
          </el-form-item>
          <el-form-item label="保证金" prop="letterBond">
            <el-input v-model="dataForm.letterBond" placeholder="保证金"></el-input>
          </el-form-item>

          <el-form-item label="服务描述" prop="description">
            <el-input v-model="dataForm.description" placeholder="服务描述"></el-input>
          </el-form-item>
          <el-form-item label="使用方编码" prop="platformCode">
            <el-input v-model="dataForm.platformCode" placeholder="使用方编码"></el-input>
          </el-form-item>
          <el-form-item label="排序" prop="orderCode">
            <el-input v-model="dataForm.orderCode" placeholder="排序"></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="status" size="mini">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="0">停用</el-radio>
              <el-radio :label="1">正常</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        issueId: '',
        issueCode: '',
        issueName: '',
        guaranteeType: '',
        guaranteeAmount: '',
        percentValue: '',
        letterBond: '',
        letterPrice: '',
        platformPrice: '',
        platformCode: '',
        priceType: '',
        orderCode: '',
        description: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      guaranteeTypeOptions: '',
      guaranteeTypemaps: '',
      getDicListURL: '/sys/dict/type/',
      getGuaranteeTypeUrl: '/letter/bgguaranteeprice/getGuaranteeTypeByIssueId',
      priceTypeOptions: '',
      priceTypemaps: ''
    }
  },
  computed: {
    dataRule () {
      return {
        code: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        // issueId: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        // issueCode: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        // issueName: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        guaranteeType: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        guaranteeAmount: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        letterBond: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        letterPrice: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        platformPrice: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        priceType: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        status: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.getGuaranteeTypeInfo()
      this.getPriceTypeInfo()
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http
        .get(`/letter/bgguaranteeprice/${this.dataForm.id}`)
        .then(({ data: res }) => {
          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
        })
        .catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate(valid => {
          if (!valid) {
            return false
          }
          this.$http[!this.dataForm.id ? 'post' : 'put'](
            '/letter/bgguaranteeprice/',
            this.dataForm
          )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            })
            .catch(() => {})
        })
      },
      1000,
      { leading: true, trailing: false }
    ),
    // 获取机构类型
    getGuaranteeTypeInfo () {
      this.guaranteeTypeOptions = {}
      this.$http
        .post(this.getGuaranteeTypeUrl, {
          dictType: 'guaranteeType',
          issueId: this.dataForm.issueId
        })
        .then(({ data: res }) => {
          this.guaranteeTypeOptions = {
            ...this.guaranteeTypeOptions,
            ...res.data.list
          }
          this.guaranteeTypemaps = {
            ...this.guaranteeTypemaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    },
    // 获取收费类型
    getPriceTypeInfo () {
      this.$http
        .get(this.getDicListURL + 'priceType')
        .then(({ data: res }) => {
          this.priceTypeOptions = {
            ...this.priceTypeOptions,
            ...res.data.list
          }
          this.priceTypemaps = {
            ...this.priceTypemaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    }
  }
}
</script>
