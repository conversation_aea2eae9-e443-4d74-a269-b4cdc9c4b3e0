<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON>qiang
 * @Date: 2020-08-20 11:21:16
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-01-04 11:39:39
-->
<template>
  <el-row :gutter="20" style="margin:15px 20px 0;">
    <el-form label-position="left" ref="dataForm" :rules="dataRule" label-width="140px">
      <el-card class="box-card" shadow='never'>
        <div slot="header" class="clearfix">
          <span class="title">上传委托书</span>
           <el-button size="small" @click="banca" style="float:right;padding:0;" type="text">办理ca</el-button>
        </div>
        <div>
           <el-col :xs="24" :lg="24">
            <el-form-item label="上传方式">
              <el-radio-group v-model="radio">
                <el-radio :label="1">在线ca签章上传</el-radio>
                <el-radio :label="2">本地文件上传（请加盖公章）</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :lg="24" v-if="radio === 1">
            <el-form-item label="委托书CA上传" prop="wts">
              <template >
                <el-button size="small" @click="emitCA(1)" type="primary">生成委托书ca签章</el-button>
                <span class="el-upload__tip">&emsp;要求格式：pdf</span>
              </template>
              <ul class="el-upload-list el-upload-list--text" v-if="fileList.length>0">
                <li tabindex="0" class="el-upload-list__item is-success">
                  <a class="el-upload-list__item-name" @click="onPreview(fileList[0])"><i class="el-icon-document"></i>委托书
                  </a><label class="el-upload-list__item-status-label"><i class="el-icon-upload-success el-icon-circle-check"></i></label><i @click="beforeRemove({status:'success',name:'委托书'},fileList)" class="el-icon-close"></i><i class="el-icon-close-tip">按
                    delete 键可删除</i>
                </li>
              </ul>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :lg="24" v-if="radio === 2">
            <el-form-item label="委托书上传" prop="wts">

              <template>
                <el-button size="small" @click="getModel()" type="text">模板下载</el-button>
                <el-upload class="upload-demo" ref="yyupload" :on-preview='onPreview' :on-remove='handleRemove'  :action="uploadUrl" :data='upData' :headers="myHeaders"
                  :before-remove="beforeRemove" :on-success='SuccessHandle' :before-upload="beforeUpload" :limit="1" :on-exceed="handleExceed" :file-list="fileList">
                  <el-button size="small" type="primary">点击上传</el-button>
                  <span slot="tip" class="el-upload__tip">&emsp;要求格式：pdf</span>
                </el-upload>
              </template>
            </el-form-item>
          </el-col>
        </div>
      </el-card>
    </el-form>
    <preview v-if="visible" ref="preview"></preview>
    <caModel v-if="visible" ref="caModel" @reFresh='getFile'></caModel>

  </el-row>
</template>
<script>
import Cookies from 'js-cookie'
import preview from '@/views/project/guarantee/common/detail/preview'
import { banca } from '@/utils/index'
import caModel from '@/views/project/guarantee/components/caModel'

export default {
  data () {
    return {
      radio: 1,
      visible: false,
      fileList: [],
      uploadUrl: '',
      upData: {},
      myHeaders: {
        token: Cookies.get('token') || ''
      }
    }
  },
  created () {
    this.uploadUrl = window.SITE_CONFIG['apiURL'] + '/letter/bank/wtsFileupload'
    this.upData = { letterId: this.letterId }
  },
  watch: {
    letterId (a) {
      this.upData = { letterId: a }
    },
    active (a) {
      if (a === 3) {
        this.getFile()
      }
    }
  },
  components: {
    preview,
    caModel
  },
  props: {
    sqsId: String,
    letterId: String,
    dataForm: Object,
    active: Number
  },
  computed: {
    dataRule () {
      return { wts: [
        {
          required: true,
          message: '请上传委托书'
        }
      ] }
    }
  },
  methods: {
    banca,
    getFile () {
      this.getBhFile(1)
    },
    async getBhFile (type) {
      let { data } = await this.$http.get(`letter/guarantee/getBhFile?letterId=${this.letterId}&type=${type}`)
      if (data.data && data.data.id) {
        // this.$emit('businessLicenseId', data.data.id)
        if (type === 1) {
          this.fileList = [{ name: '委托书', id: data.data.id, url: data.data.url, type: 1 }]
        }
      } else {
        if (type === 1) {
          this.fileList = []
        }
      }
    },
    filterType (file) {
      if (file) {
        var extension = file.substring(file.lastIndexOf('.') + 1)
        return extension === 'pdf' || extension === 'PDF'
      }
    },
    getModel () {
      this.$http
        .get(`/letter/bank/generateSqs`, { params: {
          bankDTO: JSON.stringify(this.dataForm)
        } })
        .then(({ data: res }) => {
          window.open(`${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${res.data.fileId.id}`)
          // this.zongingList = res.data
        })
        .catch(() => {})
    },
    emitCA (type) {
      // this.$emit('emitCA', type)
      this.showCaDia('caModel', this.dataForm, type)
    },
    showCaDia (name, data, type) {
      this.visible = true
      console.log(this.$route.params)
      this.$nextTick(() => {
        this.$refs[name].dataForm = {}
        var obj = {
          ...data,
          cjfChargeId: this.serPriceId,
          letterId: this.dataForm.letterId,
          guaranteeType: this.$route.params.type,
          key: this.$route.params.insuranceCode
          // biddingDocumentId: this.biddingDocumentId,
          // businessLicenseId: this.businessLicenseId
        }
        this.$refs[name].dataForm = obj
        this.$refs[name].type = type
        this.$refs[name].init()
      })
    },
    SuccessHandle (res, file, fileList) {
      if (res.data.flag !== true) {
        this.fileList = []
        return this.$message.error(res.data.msg)
      }
      this.$message.success('上传成功')
      this.fileList = this.fileList = [{ ...res.data, type: 1, name: '委托书' }]
    },
    beforeRemove (file, fileList) {
      // console.log(file, fileList)
      let a = true
      if (file && file.status === 'success') {
        a = this.$confirm(`确定移除 ${file.name}？`).then(() => {
          this.handleRemove(file, fileList)
        }).catch(() => {

        })
      }
      return a
    },
    handleRemove (file, fileList) {
      console.log(fileList)
      if (file && file.status === 'success') {
        // 移除方法
        // var deleteId = file.id ? file.id : file.response.data.ossId
        return fileList.length > 0 ? this.deleteFilewt(this.letterId, fileList[0].type) : ''
      }
    },
    beforeUpload (file) {
      var extension = file.name.substring(file.name.lastIndexOf('.') + 1)
      var lastName = extension !== 'pdf' && extension !== 'PDF'

      if (lastName) {
        this.$message.warning('文件要求格式为：.pdf')
      }

      return !lastName
    },
    onPreview (file) {
      console.log(file)
      if (file.ossId) {
        window.open(
          `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${file.ossId}`
        )
      } else {
        window.open(
          `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${file.id}`
        )
      }
    },
    handleExceed (files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件`
      )
    },
    deleteFilewt (id, type) {
      return new Promise(async (resolve, reject) => {
        let { data } = await this.$http.get(
          'letter/guarantee/deleteBhFile?letterId=' + id + '&type=' + type
        )
        if (data.code !== 0) {
          this.$message.error(data.msg)
          resolve(false)
        }
        this.getFile()
        // type === 1 ? this.$emit('biddingDocumentId', '') : this.$emit('businessLicenseId', '')
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
        resolve(true)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.el-upload__tip {
  color: red;
}
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
</style>
