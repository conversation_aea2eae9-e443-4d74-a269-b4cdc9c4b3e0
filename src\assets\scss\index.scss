.el-container{
    aside:nth-of-type(1){
        padding: 20px 10px;
    }
    .el-card__body{
        aside,.el-main{
            padding-top:0;
        }
    }
}
.listTab{
  .is-active {
    background: #409EFF !important;
    color: white !important;
  }
}

.inSteps{
  .el-step__head.is-finish {
    color: rgb(241, 130, 65) !important;
    border-color: rgb(241, 130, 65) !important;
  }
  .el-step__title.is-finish {
    color: rgb(241, 130, 65) !important;
  }
}
.detail .el-form-item__label{
  color: #99a9bf;
  padding-right: 25px;
}

.hide{
    width: 0;
    position: absolute;
    border:none;
  }
//消除google浏览器黄色框
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus {
/*背景颜色*/

box-shadow:0 0 0 60px #fff inset; 
/*字的颜色*/
-webkit-text-fill-color: #333;}
.login-main{
  .el-input__inner{
    border-top: none;
    border-right: none;
    border-left: none;
  }
}
.aui-content > .el-tabs > .el-tabs__content > .el-tab-pane {
    height: calc(calc(100vh - 50px - 38px - 30px)) !important;
}
.el-scrollbar__wrap{
    overflow-x: hidden !important;
    margin-bottom: 0 !important;
   
}
.el-step__head.is-process {
    color: #409EFF;
    border-color: #409EFF;
}
.el-step__title.is-process {
    font-weight: 700;
    color: #409EFF;
}
.el-step__description.is-process {
    color: #409EFF;
}
.gantt_grid_head_cell{
    background-color: #f5f7fa;
}
.mod-contract__bimprecontractstatute{
    overflow: auto;
}
.gantt_grid, .gantt_task{
    overflow-x: auto !important;
}
.chart{
    width:100%;
    height: 300px;
   
        canvas{
            width: 100% !important;
        }

}

.grid-con-icon{
    box-sizing: border-box;
    border: 10px solid white;
}
.el-dialog{
    min-width:550px !important;
    .el-dialog__body{
        max-height: 560px;
        overflow-y: auto;
       
    }
    // .el-form-item__label{
    //     width: 96px !important;
    // }
    .el-collapse{
        border: 1px solid #F2F7FD;
        border-radius: 3px;
    }
    .el-collapse-item__header{
        background:#E6F0FB;
        text-indent: 15px;
        border-bottom: 1px solid #F2F7FD;
    }
    .el-collapse-item__content{
        padding: 10px;
    }
    .el-select {
        width: 100%;
    }
    .el-cascader{
        width: 100%;
        
    }
    
    
    .el-tabs--border-card>.el-tabs__header {
        background-color: #E4F1FA;
        border-bottom: 1px solid #E4F1FA;
        margin: 0;
    }
    .el-tabs__content{
        max-height: 450px;
        height: auto !important;
        overflow-y: auto;
    }
}
.el-cascader-menu{
    overflow: hidden !important;
}
.finaDia  .el-dialog__body {
    max-height: 560px;
    height: auto !important;
    overflow-y: auto;
}
.cell{
    .el-dropdown{
        margin-left: 10px;
    }
}
.Meter{
    .item{
        position: relative;
        .btn{
            position: absolute;
            top: -60px;
            left: 50%;
            margin-left: -29px;
        }
    }
}
.site{
    .el-card__header{
        padding: 10px 20px;
    }
    .vw25 .el-card__body{
        height: 20vh;
        min-height: 360px;
        overflow: auto;
    }
    .vw50 .el-card__body{
        height: 39.5vw;
    }
    .el-col{
        margin-bottom: 20px;
    }
    .vwauto .el-card__body{
        height: auto;
    }
}
.home{
    .el-card__header{
        padding: 10px 20px;
    }
    .vw25 .el-card__body{
        height: 18vw;
        min-height: 350px;
    }
    .vw50 .el-card__body{
        height: 37vh;
    }
    .el-col{
        margin-bottom:  20px !important
    }
}
.alltable{
    .el-table thead{
        display: none !important;
    }
}
.el-container{
    max-height: 85vh;
}
.aui-content .ql-container{
    height: auto !important;
    overflow: auto;
    min-height: 300px !important;
    max-height: 550px !important;
}

// 电子保函重置样式
.aui-navbar{
    // background: rgb(250,250,250) !important;
    height: 55px  !important;
    // box-shadow: 0 3px 4px rgba(48,128,254,.1) !important;
}
.aui-wrapper{
    padding-top: 55px !important;
}
.aui-navbar__header{
    background: #263238 !important;
    height: 55px !important;
    .aui-navbar__brand{
        a{
            color: white !important;
        }
    }
}
.aui-navbar__menu .el-menu-item{
    height: 55px !important;
    line-height: 55px !important;
    
}
.aui-navbar--colorful .aui-navbar__menu>.el-menu-item, .aui-navbar--colorful .aui-navbar__menu>.el-submenu>.el-submenu__title{
  color: #303133 !important;
}
.aui-navbar--colorful .aui-navbar__menu .el-menu-item .el-dropdown, .aui-navbar--colorful .aui-navbar__menu .el-menu-item i, .aui-navbar--colorful .aui-navbar__menu .el-menu-item svg, .aui-navbar--colorful .aui-navbar__menu .el-submenu__title i, .aui-navbar--colorful .aui-navbar__menu .el-submenu__title svg{
  color: #303133 !important;
}
.aui-navbar--colorful .aui-navbar__menu>.el-menu-item:focus, .aui-navbar--colorful .aui-navbar__menu>.el-menu-item:hover, .aui-navbar--colorful .aui-navbar__menu>.el-submenu>.el-submenu__title:focus, .aui-navbar--colorful .aui-navbar__menu>.el-submenu>.el-submenu__title:hover{
  background: transparent !important;
}
.aui-sidebar{
    top: 55px !important;
}
.aui-content__wrapper{
    min-height: calc(100vh - 55px) !important;
}
.aui-content{
    min-height: calc(100vh - 55px) !important;
}
.aui-content>.el-tabs>.el-tabs__header,.aui-content--tabs-tools{
    top: 55px !important;
}
.aui-content>.el-tabs>.el-tabs__content>.el-tab-pane {
    min-height: calc(100vh - 55px - 38px - 30px) !important;
    height: calc(100vh - 55px - 38px - 30px) !important;

}

.proDia{
     .el-dialog__body {
        max-height: 1000px !important;
        overflow-y: auto;
    }
}
.el-scrollbar__view{
    height: 100%;
}
.con2{
.el-card__header {
    padding: 10px 20px !important;
    }
}
.layout{
    .el-scrollbar__wrap {
        overflow-x: auto !important;
    }
}
.el-table {
  // max-height: 490px;
  /*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
  ::-webkit-scrollbar {
      width: 7px;
      /*滚动条宽度*/
      height: 10px;
      /*滚动条高度*/
      background-color: white;
      cursor: pointer;
  }

  /*定义滑块 内阴影+圆角*/
  ::-webkit-scrollbar-thumb {
      -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.2);
      background-color: rgba(144,147,153,.2);
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
      border-bottom-left-radius: 4px;
      cursor: pointer;
  }
  ::-webkit-scrollbar-thumb:hover {
      background-color: rgba(144,147,153,.5);
      cursor: pointer;
  }
}
.aui-card--fill{
  .ql-editor{
    min-height: 350px !important;
  }
}
.qiantip{
  .el-timeline-item__wrapper {
    margin-left: 10px !important;
  }
  .el-timeline-item__tail{
    border-left: 1px solid #068DFF;
    left: 16px;
  }
  .el-dialog__header{
    background: #409EFF;
    color: white;
  }
}
.certCode .el-select .el-input {
  width: 200px;
}
.certCode .el-input-group__append,.certCode .el-input-group__prepend  {
  background-color: #fff;
}
.mailing{
  .el-form-item__content,.el-form-item__label{
    line-height: 20px !important;
  }
}
.box .el-card__body{
  height: 100%;
}
.cadia .el-dialog__body{
  height: calc(100vh - 50px - 38px - 100px) !important;
  max-height: none;
}
.detail {
  .el-tabs__header {
      padding: 0;
      position: relative;
      margin: 0 0;
      top: 19px;
  }
   .el-form-item {
    margin-bottom: 5px !important;
  }
}
.CAmodel .diaFull{
  position: absolute;
  top: 9px;
  right: 45px;
  color: #909399;

}
.CAmodel{
  .el-dialog__body {
    padding: 0px 20px 0  !important;
  }
}
.report {
  .el-tabs__active-bar{
    width: 130px !important;
  }
  .el-tabs__item{
    height: 45px;
  }
}

 .el-picker-panel__footer .el-button--text {
    display: none !important;
  }
  .CAmodel .el-tabs__header {
   
    top: 0px;
}
.CAmodel .el-dialog__body {
  max-height: none;
  padding: 5px 20px;
  min-height: 600px;
  height: calc(100% - 81px);
}
.CAmodel .el-tabs__content{
  max-height: none;
}
.fileWin {
  .el-card__body{
    height:100% !important;
  }
  .el-tabs{
    height:100% !important;
  }
}
.await{
  .el-step__head.is-finish {
    color: rgb(241, 130, 65) !important;
    border-color: rgb(241, 130, 65) !important;
  }
  .el-step__title.is-finish {
    color: rgb(241, 130, 65) !important;
  }
}
.detail{
  .el-table__header{
    width: 100% !important;
}
.el-table__body{
    width: 100% !important;
}
}
.Danger{
  color: #F56C6C !important;
}
.Success{
  color: #67C23A !important;
}
.Warning{
  color: #E6A23C !important;
}
.aui-theme-tools__toggle_o {
  position: absolute;
  top: 80px;
  left: -35px;
  width: 30px;
  padding: 10px 8px;
  text-align: center;
  font-size: 16px;
  border-right: 0;
  border-radius: 4px 0 0 4px;
 
  background-color: white; 
  cursor: pointer;
  color: #f18241;
  border-color: #f3d0bc;
  
  box-shadow: -2px 0 18px #cacaca;
}
.avatar-uploader{
  display: inline-block;
  margin-left: 10px;
  position: relative;
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;

  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  display: inline-block;
  width: 170px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}
.avatar {
  width: 170px;
  height: 120px;
  display: block;
}
.avatar-uploader .el-upload-list__item-actions {
  background-color: rgba(0,0,0,1);
  width: 100%;
  height: 100%;
}
.avatar-uploader .el-upload-list__item-actions:hover {
  opacity: 1;
}
.avatar-uploader .el-upload-list__item-actions {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  cursor: default;
  text-align: center;
  color: #fff;
  opacity: 0;
  font-size: 20px;
  background-color: rgba(0,0,0,.5);
  transition: opacity .3s;
}
.avatar-uploader .el-upload-list__item-actions:hover span {
  display: inline-block;
}
.avatar-uploader .el-upload-list__item-actions i{
  text-align: center;
  color: white;
  font-size: 20px;
}
.avatar-uploader .el-upload-list__item-actions span {
  display: none;
  cursor: pointer;
}
.czz .custom-tree-node{
  width: 400px;
}
.czz .cz{
  float: right;
}
.cz .delele{
  color: #F56C6C;
}
@media screen and (max-width: 1360px) {
  // .aui-wrapper{
  //   zoom: 0.8;
  // }
  html{
    // height: 100%;
    zoom: 0.8;
  }
  .fileWin{
    height: calc(125vh) !important;
    .fileCon{
      height:calc(125vh - 40px) !important;
    }
  }
  .template {
    height: calc(125vh - 205px) !important;
  }
  .box {
    height: calc(125vh - 100px) !important;
}
    .cadia .el-dialog__body{
    height: calc(125vh - 50px - 38px - 100px) !important;
    max-height: none;
  }
  .aui-content__wrapper{
    min-height: calc(125vh - 55px) !important;
}
  .aui-content{
      min-height: calc(125vh - 55px) !important;
  }
  .aui-content > .el-tabs > .el-tabs__content > .el-tab-pane {
    min-height: calc(((125vh - 70px) - 38px) - 30px) !important;
    height: calc(((125vh - 70px) - 38px) - 30px) !important;
  } 
  .min550{
    min-height: 690px !important;
    height: 100%;
  }
}
// @media screen and (max-width: 1360px) {
//   .aui-wrapper{
//     zoom: 0.8;
    
//   }
//   .cadia .el-dialog__body{
//     height: calc(125vh - 50px - 38px - 100px) !important;
//     max-height: none;
//   }
//   .aui-content__wrapper{
//     min-height: calc(125vh - 55px) !important;
// }
//   .aui-content{
//       min-height: calc(125vh - 55px) !important;
//   }
//   .box {
//     height: calc(125vh - 205px)  !important;
//   }
//   .aui-content > .el-tabs > .el-tabs__content > .el-tab-pane {
//     min-height: calc(((125vh - 55px) - 38px) - 30px) !important;
//     height: calc(((125vh - 55px) - 38px) - 30px) !important;
//   } 
// }