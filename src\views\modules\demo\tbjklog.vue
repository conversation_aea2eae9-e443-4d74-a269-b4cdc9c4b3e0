<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-demo__tbjklog}">
      <el-form :inline="true" :model="dataForm" >
        <el-form-item >
          <el-input v-model="dataForm.appcode"  clearable  placeholder="应用编码"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="dataForm.interfacecode" clearable   placeholder="接口编码"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="danger"   @click="getDataList(1)">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
          <el-form-item>
              <el-button v-if="$hasPermission('demo:tbjklog:deleteBatch')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
          </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle"   style="width: 100%;">
        <el-table-column prop="terminalcode" label="客户端类型" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="appcode" label="应用编码" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="version" label="接口版本" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="interfacecode" label="接口编码" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="codetype" label="加密方式" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="requesttime" label="数据交换请求时间" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="returncode" label="返回编码" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="returntime" label="返回时间" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="xysj" label="响应时间毫秒" sortable="custom" header-align="center" align="center"  width="100" ></el-table-column>
            <el-table-column prop="ywremark" label="业务说明" sortable="custom" header-align="center" align="center"></el-table-column>
          <el-table-column prop="createDate" label="创建时间" sortable="custom" header-align="center" align="center"></el-table-column>
          <el-table-column prop="updateDate" label="更新时间" sortable="custom" header-align="center" align="center"></el-table-column>
          <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button v-if="$hasPermission('demo:tbjklog:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">浏览</el-button>
              <el-button v-if="$hasPermission('demo:tbjklog:delete')" type="text" size="mini" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './tbjklog-add-or-update'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/demo/tbjklog/page',
        getDataListIsPage: true,
        exportURL: '/demo/tbjklog/export',
        deleteURL: '/demo/tbjklog',
        enableURL: '/demo/tbjklog/enable',
        stopURL: '/demo/tbjklog/stop',
        deleteIsBatch: true
      },
      statusoptions: [{
        value: 1,
        label: '正常'
      }, {
        value: 0,
        label: '停用'
      }],
      dataForm: {
        appcode: '',
        interfacecode: '',
        status: 1
      },
      orderField: 'create_date',
      order: 'desc',
      uploadVisible: false
    }
  },
  components: {
    AddOrUpdate
  },
  methods: {
    // 状态启用具体代码需微调格式去除空格
    enableHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(this.$t('prompt.info', { 'handle': '启用' }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.$http.put(this.mixinViewModuleOptions.enableURL, id ? [id] : this.dataListSelections.map(item => item.id)).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.getDataList()
            }
          })
        }).catch(() => {})
      }).catch(() => {})
    },
    // 状态停用
    stopHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(this.$t('prompt.info', { 'handle': '停用' }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        // 具体代码需微调格式去除空格
        this.$http.put(this.mixinViewModuleOptions.stopURL, id ? [id] : this.dataListSelections.map(item => item.id)).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.getDataList()
            }
          })
        }).catch(() => {})
      }).catch(() => {})
    }
  }
}

</script>
