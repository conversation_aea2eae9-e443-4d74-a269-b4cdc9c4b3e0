<template>
  <el-form :model="dataForm" ref="dataForm" :rules="dataRule" label-width="140px">
    <el-row :gutter="20">
      <div class="temp-subtitle">受益人（不写默认是被保人）</div>
      <el-col :xs="24" :lg="24">
        <div class="formCon">
          <el-col :xs="24" :lg="12">
            <el-form-item label="名称" prop="name">
              <el-input v-model="dataForm.name" size="small" class="wd180" style="width:100%;" placeholder="名称"></el-input>
            </el-form-item>
            <el-form-item label="证件类型" prop="certType">
              <el-select v-model="dataForm.certType" size="small" class="wd180" style="width:100%;" placeholder="证件类型">
                <el-option v-for="item in options.certTypeoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="证件号码" prop="certCode">
              <el-input v-model.trim="dataForm.certCode"  size="small" class="wd180" style="width:100%;" placeholder="证件号码"></el-input>
            </el-form-item>
            <el-form-item label="证件有效期" prop="certTerm">
              <el-date-picker v-model="dataForm.certTerm" type="date" value-format="yyyy-MM-dd" size="small" class="wd180" style="width:100%;" placeholder="证件有效期"></el-date-picker>
            </el-form-item>
            <el-form-item label="证件有效期类型" prop="certTermType">
              <el-select v-model="dataForm.certTermType" size="small" class="wd180" style="width:100%;" placeholder="证件有效期类型">
                <el-option v-for="item in options.certTermTypeoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :lg="12">
            <el-form-item label="联系人" prop="linkman">
              <el-input v-model="dataForm.linkman" size="small" class="wd180" style="width:100%;" placeholder="联系人"></el-input>
            </el-form-item>
            <el-form-item label="联系人电话" prop="linkmanTel">
              <el-input v-model="dataForm.linkmanTel" size="small" class="wd180" style="width:100%;" placeholder="联系人电话"></el-input>
            </el-form-item>
            <el-form-item label="公司电话" prop="phoneNumber">
              <el-input v-model="dataForm.phoneNumber" size="small" class="wd180" style="width:100%;" placeholder="公司电话"></el-input>
            </el-form-item>
            <el-form-item label="地址" prop="address">
              <el-input v-model="dataForm.address" size="small" class="wd180" style="width:100%;" placeholder="地址"></el-input>
            </el-form-item>
          </el-col>
        </div>
      </el-col>
    </el-row>
  </el-form>
</template>
<script>

export default {

  data () {
    return {}
  },
  props: {
    dataForm: {
      type: Object
    },
    options: {
      type: Object
    }
  },
  computed: {
    // dataRule () {
    //   var whiteList = ['address']
    //   var ruleObj = {}
    //   var rule = [{
    //     required: true,
    //     message: this.$t('validate.required'),
    //     trigger: 'blur'
    //   }]
    //   for (let key in this.dataForm) {
    //     ruleObj[key] = []
    //     ruleObj[key].push(...rule)
    //   }
    //   Array.from(
    //     new Set(
    //       [...whiteList].map(x => {
    //         delete ruleObj[x]
    //       })
    //     )
    //   )

    //   return ruleObj
    // }
    dataRule () {
      var checkPhone = (rule, value, callback) => {
        if (!value) {
          return callback(new Error('手机号不能为空'))
        } else {
          const reg = /^1[3|4|5|7|8|9][0-9]\d{8}$/
          console.log(reg.test(value))
          if (reg.test(value)) {
            callback()
          } else {
            return callback(new Error('请输入正确的手机号'))
          }
        }
      }
      var checkZuoPhone = (rule, value, callback) => {
        if (!value) {
          return callback(new Error('请输入正确的电话号'))
        } else {
          // eslint-disable-next-line no-useless-escape
          const reg = /(^[0-9]{3,4}\-[0-9]{3,8}$)|(^[0-9]{3,8}$)|(^\([0-9]{3,4}\)[0-9]{3,8}$)|(^0{0,1}13[0-9]{9}$)/
          console.log(reg.test(value))
          if (reg.test(value)) {
            callback()
          } else {
            return callback(new Error('请输入正确的电话号'))
          }
        }
      }
      return {
        name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certTerm: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certTermType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        linkman: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        linkmanTel: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: checkPhone, trigger: 'blur' }
        ],
        phoneNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: checkZuoPhone, trigger: 'blur' }
        ],
        address: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    push () {
      return new Promise((resolve, reject) => {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
</style>
