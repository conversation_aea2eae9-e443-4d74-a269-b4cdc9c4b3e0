（function（）{function r（e，n，t）{function o（i，f）{if（！n [i]）{if（！e [i]）{var c =“ function” == typeof require && require; if（！f && c）返回c（i，！0）; if（u）返回u（i，！0）; var a = new Error（“找不到模块'” + i +“'”））;抛出一个.code =“ MODULE_NOT_FOUND”，a} var p = n [i] = {exports：{}}; e [i] [0] .call（p.exports，function（r）{var n = e [i] [1] [r];返回o（n || r）}，p，p.exports，r，e，n，t）}返回n [i] .exports} for（var u =“ function” == typeof require && require，i = 0; i <t.length; i ++）o（t [i]）; return o} return r}）（）（{1：[function（_dereq_，module，exports）{
  “使用严格”；
  
  _dereq_（2）;
  
  var _global = _interopRequireDefault（_dereq_（15））;
  
  函数_interopRequireDefault（obj）{return obj && obj .__ esModule？obj：{“默认”：obj}; }
  
  如果（_global [“ default”] ._ babelPolyfill && typeof console！==“未定义” && console.warn）{
    console.warn（“ @ babel / polyfill在此页面上被加载了多次。这可能是不希望的/预期的，” +“可能会导致后果，如果依次应用不同版本的polyfills。” +“如果需要多次加载polyfill，请使用@ babel / polyfill / noConflict“ +”而不是绕过警告。“）;
  }
  
  _global [“ default”] ._ babelPolyfill = true;
  }，{“ 15”：15，“ 2”：2}]，2：[function（_dereq_，module，exports）{
  “使用严格”；
  
  _dereq_（3）;
  
  _dereq_（5）;
  
  _dereq_（4）;
  
  _dereq_（11）;
  
  _dereq_（10）;
  
  _dereq_（13）;
  
  _dereq_（12）;
  
  _dereq_（14）;
  
  _dereq_（7）;
  
  _dereq_（8）;
  
  _dereq_（6）;
  
  _dereq_（9）;
  
  _dereq_（306）;
  
  _dereq_（307）;
  }，{“ 10”：10，“ 11”：11，“ 12”：12，“ 13”：13，“ 14”：14，“ 3”：3，“ 306”：306，“ 307”：307 ，“ 4”：4，“ 5”：5，“ 6”：6，“ 7”：7，“ 8”：8，“ 9”：9}]，3：[function（_dereq_，module，exports） {
  _dereq_（278）;
  _dereq_（214）;
  _dereq_（216）;
  _dereq_（215）;
  _dereq_（218）;
  _dereq_（220）;
  _dereq_（225）;
  _dereq_（219）;
  _dereq_（217）;
  _dereq_（227）;
  _dereq_（226）;
  _dereq_（222）;
  _dereq_（223）;
  _dereq_（221）;
  _dereq_（213）;
  _dereq_（224）;
  _dereq_（228）;
  _dereq_（229）;
  _dereq_（180）;
  _dereq_（182）;
  _dereq_（181）;
  _dereq_（231）;
  _dereq_（230）;
  _dereq_（201）;
  _dereq_（211）;
  _dereq_（212）;
  _dereq_（202）;
  _dereq_（203）;
  _dereq_（204）;
  _dereq_（205）;
  _dereq_（206）;
  _dereq_（207）;
  _dereq_（208）;
  _dereq_（209）;
  _dereq_（210）;
  _dereq_（184）;
  _dereq_（185）;
  _dereq_（186）;
  _dereq_（187）;
  _dereq_（188）;
  _dereq_（189）;
  _dereq_（190）;
  _dereq_（191）;
  _dereq_（192）;
  _dereq_（193）;
  _dereq_（194）;
  _dereq_（195）;
  _dereq_（196）;
  _dereq_（197）;
  _dereq_（198）;
  _dereq_（199）;
  _dereq_（200）;
  _dereq_（265）;
  _dereq_（270）;
  _dereq_（277）;
  _dereq_（268）;
  _dereq_（260）;
  _dereq_（261）;
  _dereq_（266）;
  _dereq_（271）;
  _dereq_（273）;
  _dereq_（256）;
  _dereq_（257）;
  _dereq_（258）;
  _dereq_（259）;
  _dereq_（262）;
  _dereq_（263）;
  _dereq_（264）;
  _dereq_（267）;
  _dereq_（269）;
  _dereq_（272）;
  _dereq_（274）;
  _dereq_（275）;
  _dereq_（276）;
  _dereq_（175）;
  _dereq_（177）;
  _dereq_（176）;
  _dereq_（179）;
  _dereq_（178）;
  _dereq_（163）;
  _dereq_（161）;
  _dereq_（168）;
  _dereq_（165）;
  _dereq_（171）;
  _dereq_（173）;
  _dereq_（160）;
  _dereq_（167）;
  _dereq_（157）;
  _dereq_（172）;
  _dereq_（155）;
  _dereq_（170）;
  _dereq_（169）;
  _dereq_（162）;
  _dereq_（166）;
  _dereq_（154）;
  _dereq_（156）;
  _dereq_（159）;
  _dereq_（158）;
  _dereq_（174）;
  _dereq_（164）;
  _dereq_（247）;
  _dereq_（248）;
  _dereq_（254）;
  _dereq_（249）;
  _dereq_（250）;
  _dereq_（251）;
  _dereq_（252）;
  _dereq_（253）;
  _dereq_（232）;
  _dereq_（183）;
  _dereq_（255）;
  _dereq_（290）;
  _dereq_（291）;
  _dereq_（279）;
  _dereq_（280）;
  _dereq_（285）;
  _dereq_（288）;
  _dereq_（289）;
  _dereq_（283）;
  _dereq_（286）;
  _dereq_（284）;
  _dereq_（287）;
  _dereq_（281）;
  _dereq_（282）;
  _dereq_（233）;
  _dereq_（234）;
  _dereq_（235）;
  _dereq_（236）;
  _dereq_（237）;
  _dereq_（240）;
  _dereq_（238）;
  _dereq_（239）;
  _dereq_（241）;
  _dereq_（242）;
  _dereq_（243）;
  _dereq_（244）;
  _dereq_（246）;
  _dereq_（245）;
  module.exports = _dereq_（52）;
  
  }，{“ 154”：154，“ 155”：155，“ 156”：156，“ 157”：157，“ 158”：158，“ 159”：159，“ 160”：160，“ 161”：161 ，“ 162”：162，“ 163”：163，“ 164”：164，“ 165”：165，“ 166”：166，“ 167”：167，“ 168”：168，“ 169”：169，“ 170“：170，” 171“：171，” 172“：172，” 173“：173，” 174“：174，” 175“：175，” 176“：176，” 177“：177，” 178“ ：178，“ 179”：179，“ 180”：180，“ 181”：181，“ 182”：182，“ 183”：183，“ 184”：184，“ 185”：185，“ 186”：186 ，“ 187”：187，“ 188”：188，“ 189”：189，“ 190”：190，“ 191”：191，“ 192”：192，“ 193”：193，“ 194”：194，“ 195“：195，” 196“：196，“ 197”：197，“ 198”：198，“ 199”：199，“ 200”：200，“ 201”：201，“ 202”：202，“ 203”：203，“ 204”：204， “ 205”：205，“ 206”：206，“ 207”：207，“ 208”：208，“ 209”：209，“ 210”：210，“ 211”：211，“ 212”：212，“ 213 “：213，” 214“：214，” 215“：215，” 216“：216，” 217“：217，” 218“：218，” 219“：219，” 220“：220，” 221“： 221，“ 222”：222，“ 223”：223，“ 224”：224，“ 225”：225，“ 226”：226，“ 227”：227，“ 228”：228，“ 229”：229， “ 230”：230，“ 231”：231，“ 232”：232，“ 233”：233，“ 234”：234，“ 235”：235，“ 236”：236，“ 237”：237，“ 238 “：238，” 239“：239，“ 240”：240，“ 241”：241，“ 242”：242，“ 243”：243，“ 244”：244，“ 245”：245，“ 246”：246，“ 247”：247， “ 248”：248，“ 249”：249，“ 250”：250，“ 251”：251，“ 252”：252，“ 253”：253，“ 254”：254，“ 255”：255，“ 256 “：256，” 257“：257，” 258“：258，” 259“：259，” 260“：260，” 261“：261，” 262“：262，” 263“：263，” 264“： 264，“ 265”：265，“ 266”：266，“ 267”：267，“ 268”：268，“ 269”：269，“ 270”：270，“ 271”：271，“ 272”：272， “ 273”：273，“ 274”：274，“ 275”：275，“ 276”：276，“ 277”：277，“ 278”：278，“ 279”：279，“ 280”：280，“ 281 “：281，” 282“：282，“ 283”：283，“ 284”：284，“ 285”：285，“ 286”：286，“ 287”：287，“ 288”：288，“ 289”：289，“ 290”：290， “ 291”：291，“ 52”：52}]，4：[function（_dereq_，module，exports）{
  _dereq_（292）;
  module.exports = _dereq_（52）.Array.flatMap;
  
  }，{“ 292”：292，“ 52”：52}]，5：[function（_dereq_，module，exports）{
  _dereq_（293）;
  module.exports = _dereq_（52）.Array.includes;
  
  }，{“ 293”：293，“ 52”：52}]，6：[function（_dereq_，module，exports）{
  _dereq_（294）;
  module.exports = _dereq_（52）.Object.entries;
  
  }，{“ 294”：294，“ 52”：52}]，7：[function（_dereq_，module，exports）{
  _dereq_（295）;
  module.exports = _dereq_（52）.Object.getOwnPropertyDescriptors;
  
  }，{“ 295”：295，“ 52”：52}]，8：[function（_dereq_，module，exports）{
  _dereq_（296）;
  module.exports = _dereq_（52）.Object.values;
  
  }，{“ 296”：296，“ 52”：52}]，9：[function（_dereq_，module，exports）{
  “使用严格”；
  _dereq_（232）;
  _dereq_（297）;
  module.exports = _dereq_（52）.Promise ['finally'];
  
  }，{“ 232”：232，“ 297”：297，“ 52”：52}]，10：[function（_dereq_，module，exports）{
  _dereq_（298）;
  module.exports = _dereq_（52）.String.padEnd;
  
  }，{“ 298”：298，“ 52”：52}]，11：[function（_dereq_，module，exports）{
  _dereq_（299）;
  module.exports = _dereq_（52）.String.padStart;
  
  }，{“ 299”：299，“ 52”：52}]，12：[function（_dereq_，module，exports）{
  _dereq_（301）;
  module.exports = _dereq_（52）.String.trimRight;
  
  }，{“ 301”：301，“ 52”：52}]，13：[function（_dereq_，module，exports）{
  _dereq_（300）;
  module.exports = _dereq_（52）.String.trimLeft;
  
  }，{“ 300”：300，“ 52”：52}]，14：[function（_dereq_，module，exports）{
  _dereq_（302）;
  module.exports = _dereq_（151）.f（'asyncIterator'）;
  
  }，{“ 151”：151，“ 302”：302}]，15：[function（_dereq_，module，exports）{
  _dereq_（32）;
  module.exports = _dereq_（18）.global;
  
  }，{“ 18”：18，“ 32”：32}]，16：[function（_dereq_，module，exports）{
  module.exports = function（it）{
    如果（typeof！='function'）抛出TypeError（it +'不是函数！'）;
    把它返还;
  };
  
  }，{}]，17：[function（_dereq_，module，exports）{
  var isObject = _dereq_（28）;
  module.exports = function（it）{
    如果（！isObject（it））抛出TypeError（它+'不是对象！'）;
    把它返还;
  };
  
  }，{“ 28”：28}]，18：[function（_dereq_，module，exports）{
  var core = module.exports = {版本：'2.6.11'};
  if（typeof __e =='number'）__e =核心; // eslint-disable-line no-undef
  
  }，{}]，19：[function（_dereq_，module，exports）{
  //可选/简单上下文绑定
  var aFunction = _dereq_（16）;
  module.exports = function（fn，that，length）{
    aFunction（fn）;
    if（=== undefined）返回fn;
    开关（长度）{
      情况1：返回函数（a）{
        返回fn.call（that，a）;
      };
      情况2：返回函数（a，b）{
        返回fn.call（that，a，b）;
      };
      情况3：返回函数（a，b，c）{
        返回fn.call（that，a，b，c）;
      };
    }
    返回函数（/ * ... args * /）{
      返回fn.apply（that，arguments）;
    };
  };
  
  }，{“ 16”：16}]，20：[function（_dereq_，module，exports）{
  //感谢IE8的有趣的defineProperty
  module.exports =！_dereq_（23）（function（）{
    return Object.defineProperty（{}，'a'，{get：function（）{return 7;}}）。a！= 7;
  }）;
  
  }，{“ 23”：23}]，21：[function（_dereq_，module，exports）{
  var isObject = _dereq_（28）;
  var document = _dereq_（24）.document;
  // typeof document.createElement是旧IE中的“对象”
  var is = isObject（document）&& isObject（document.createElement）;
  module.exports = function（it）{
    回报是？document.createElement（it）：{};
  };
  
  }，{“ 24”：24，“ 28”：28}]，22：[function（_dereq_，module，exports）{
  var global = _dereq_（24）;
  var core = _dereq_（18）;
  var ctx = _dereq_（19）;
  var hide = _dereq_（26）;
  var has = _dereq_（25）;
  var PROTOTYPE ='prototype';
  
  var $ export =函数（类型，名称，源）{
    var IS_FORCED =类型＆$ export.F;
    var IS_GLOBAL =类型＆$ export.G;
    var IS_STATIC =类型＆$ export.S;
    var IS_PROTO =类型＆$ export.P;
    var IS_BIND =类型＆$ export.B;
    var IS_WRAP =类型＆$ export.W;
    var export = IS_GLOBAL？核心：核心[名称] || （core [name] = {}）;
    var expProto = Exports [PROTOTYPE];
    var target = IS_GLOBAL？全局：IS_STATIC？global [name]：（global [name] || {}）[PROTOTYPE];
    var key，拥有，输出；
    如果（IS_GLOBAL）源=名称；
    为（输入来源）{
      //包含在本地
      自己=！IS_FORCED &&目标&&目标[键]！==未定义;
      如果（拥有&&拥有（出口，钥匙））继续;
      //导出本地或通过
      出=拥有？target [key]：来源[key];
      //防止名称空间受到全球污染
      exports [key] = IS_GLOBAL && typeof target [key]！='功能'吗？来源[关键]
      //将计时器绑定到全局，以便从导出上下文进行调用
      ：IS_BIND &&拥有？ctx（输出，全局）
      //包装全局构造函数以防止在库中更改它们
      ：IS_WRAP && target [key] == out？（功能（C）{
        var F =函数（a，b，c）{
          如果（此C的实例）{
            开关（arguments.length）{
              情况0：返回新的C（）；
              情况1：传回新的C（a）；
              情况2：传回新的C（a，b）;
            }返回新的C（a，b，c）;
          } return C.apply（this，arguments）;
        };
        F [PROTOTYPE] = C [PROTOTYPE];
        返回F;
      //为原型方法制作静态版本
      }）（out）：IS_PROTO && typeof out =='function'吗？ctx（Function.call，out）：输出;
      //将原型方法导出到core。％CONSTRUCTOR％.methods。％NAME％
      如果（IS_PROTO）{
        （exports.virtual ||（exports.virtual = {}））[key] = out;
        //将原型方法导出到core。％CONSTRUCTOR％.prototype。％NAME％
        if（type＆$ export.R && expProto &&！expProto [key]）隐藏（expProto，key，out）;
      }
    }
  };
  //输入位图
  $ export.F = 1; //强制
  $ export.G = 2; //全球
  $ export.S = 4; // 静态的
  $ export.P = 8; //原型
  $ export.B = 16; //绑定
  $ export.W = 32; //包装
  $ export.U = 64; //安全
  $ export.R = 128; //真正的库方法
  module.exports = $ export;
  
  }，{“ 18”：18，“ 19”：19，“ 24”：24，“ 25”：25，“ 26”：26}]，23：[function（_dereq_，module，exports）{
  module.exports =函数（exec）{
    尝试{
      返回!! exec（）;
    }抓住（e）{
      返回true；
    }
  };
  
  }，{}]，24：[function（_dereq_，module，exports）{
  // https://github.com/zloirock/core-js/issues/86#issuecomment-115759028
  var global = module.exports = typeof window！='undefined'&& window.Math ==数学
    ？window：typeof self！='undefined'&& self.Math ==数学吗？自
    // eslint-disable-next-line no-new-func
    ：Function（'return this'）（）;
  if（typeof __g =='number'）__g =全局; // eslint-disable-line no-undef
  
  }，{}]，25：[function（_dereq_，module，exports）{
  var hasOwnProperty = {} .hasOwnProperty;
  module.exports =函数（它，键）{
    返回hasOwnProperty.call（it，key）;
  };
  
  }，{}]，26：[function（_dereq_，module，exports）{
  var dP = _dereq_（29）;
  var createDesc = _dereq_（30）;
  module.exports = _dereq_（20）吗？功能（对象，键，值）{
    返回dP.f（object，key，createDesc（1，value））;
  }：函数（对象，键，值）{
    object [key] = value;
    返回对象
  };
  
  }，{“ 20”：20，“ 29”：29，“ 30”：30}]，27：[function（_dereq_，module，exports）{
  module.exports =！_dereq_（20）&&！_dereq_（23）（function（）{
    return Object.defineProperty（_dereq_（21）（'div'），'a'，{get：function（）{return 7;}}）。a！= 7;
  }）;
  
  }，{“ 20”：20，“ 21”：21，“ 23”：23}]，28：[function（_dereq_，module，exports）{
  module.exports = function（it）{
    返回typeof ==='object'吗？它！== null：它的类型==='功能';
  };
  
  }，{}]，29：[function（_dereq_，module，exports）{
  var anObject = _dereq_（17）;
  var IE8_DOM_DEFINE = _dereq_（27）;
  var toPrimitive = _dereq_（31）;
  var dP = Object.defineProperty;
  
  exports.f = _dereq_（20）？Object.defineProperty：函数defineProperty（O，P，Attributes）{
    anObject（O）;
    P = toPrimitive（P，true）;
    anObject（Attributes）;
    如果（IE8_DOM_DEFINE）尝试{
      返回dP（O，P，Attributes）;
    }抓住（e）{/ *空* /}
    如果（属性中的“ get” ||属性中的“ set”）抛出TypeError（“不支持访问器！”）；
    if（属性中的“值”）O [P] = Attributes.value;
    返回O;
  };
  
  }，{“ 17”：17，“ 20”：20，“ 27”：27，“ 31”：31}]，30：[function（_dereq_，module，exports）{
  module.exports =函数（位图，值）{
    返回{
      枚举：！（位图＆1），
      可配置：！（位图和2），
      可写：！（位图和4），
      价值：价值
    };
  };
  
  }，{}]，31：[function（_dereq_，module，exports）{
  // 7.1.1 ToPrimitive（input [，PreferredType]）
  var isObject = _dereq_（28）;
  //而不是ES6规范版本，我们没有实现@@ toPrimitive情况
  //第二个参数-标志-首选类型是字符串
  module.exports = function（it，S）{
    如果（！isObject（it））返回它;
    var fn，val;
    如果（S && typeof（fn = it.toString）=='function'&&！isObject（val = fn.call（it）））返回val;
    if（typeof（fn = it.valueOf）=='function'&&！isObject（val = fn.call（it）））返回val;
    if（！S && typeof（fn = it.toString）=='function'&&！isObject（val = fn.call（it）））返回val;
    抛出TypeError（“无法将对象转换为原始值”）；
  };
  
  }，{“ 28”：28}]，32：[function（_dereq_，module，exports）{
  // https://github.com/tc39/proposal-global
  var $ export = _dereq_（22）;
  
  $ export（$ export.G，{全局：_dereq_（24）}）;
  
  }，{“ 22”：22，“ 24”：24}]，33：[function（_dereq_，module，exports）{
  arguments [4] [16] [0] .apply（exports，arguments）
  }，{“ 16”：16}]，34：[function（_dereq_，module，exports）{
  var cof = _dereq_（48）;
  module.exports = function（it，msg）{
    如果（typeof！='number'&& cof（it）！='Number'）抛出TypeError（msg）;
    返回+ it;
  };
  
  }，{“ 48”：48}]，35：[function（_dereq_，module，exports）{
  // 22.1.3.31 Array.prototype [@@ unscopables]
  var UNSCOPABLES = _dereq_（152）（'unscopables'）;
  var ArrayProto = Array.prototype;
  如果（ArrayProto [UNSCOPABLES] ==未定义）_dereq_（72）（ArrayProto，UNSCOPABLES，{}）;
  module.exports =函数（键）{
    ArrayProto [UNSCOPABLES] [key] = true;
  };
  
  }，{“ 152”：152，“ 72”：72}]，36：[function（_dereq_，module，exports）{
  “使用严格”；
  var at = _dereq_（129）（true）;
  
   //ʻAdvanceStringIndex`抽象操作
  // https://tc39.github.io/ecma262/#sec-advancestringindex
  module.exports =函数（S，索引，Unicode）{
    返回索引+（unicode？at（S，index）.length：1）;
  };
  
  }，{“ 129”：129}]，37：[function（_dereq_，module，exports）{
  module.exports =函数（函数，构造函数，名称，forbiddenField）{
    if（！（它的instanceof构造函数）||（forbiddenField！==未定义&& forbiddenField））{
      抛出TypeError（name +'：不正确的调用！'）;
    } 把它返还;
  };
  
  }，{}]，38：[function（_dereq_，module，exports）{
  arguments [4] [17] [0] .apply（exports，arguments）
  }，{“ 17”：17，“ 81”：81}]，39：[function（_dereq_，module，exports）{
  // 22.1.3.3 Array.prototype.copyWithin（target，start，end = this.length）
  “使用严格”；
  var toObject = _dereq_（142）;
  var toAbsoluteIndex = _dereq_（137）;
  var toLength = _dereq_（141）;
  
  module.exports = [] .copyWithin || 函数copyWithin（target / * = 0 * /，开始/ * = 0，结束= @length * /）{
    var O = toObject（this）;
    var len = toLength（O.length）;
    var to = toAbsoluteIndex（target，len）;
    var from = toAbsoluteIndex（start，len）;
    var end = arguments.length> 2吗？arguments [2]：未定义；
    var count = Math.min（（end === undefined？len：toAbsoluteIndex（end，len））-from，len-to）;
    var inc = 1;
    如果（从<到&&到<从+计数）{
      inc = -1;
      从+ =计数-1;
      到+ =数-1;
    }
    while（count--> 0）{
      如果（来自O中）O [to] = O [来自]；
      否则删除O [to];
      到+ = inc;
      来自+ = inc;
    } return O;
  };
  
  }，{“ 137”：137，“ 141”：141，“ 142”：142}]，40：[function（_dereq_，module，exports）{
  // 22.1.3.6 Array.prototype.fill（值，开始= 0，结束= this.length）
  “使用严格”；
  var toObject = _dereq_（142）;
  var toAbsoluteIndex = _dereq_（137）;
  var toLength = _dereq_（141）;
  module.exports =函数fill（value / *，start = 0，end = @length * /）{
    var O = toObject（this）;
    var length = toLength（O.length）;
    var aLen = arguments.length;
    var index = toAbsoluteIndex（aLen> 1？arguments [1]：未定义，长度）；
    var end = aLen> 2吗？arguments [2]：未定义；
    var endPos = end ===未定义？length：toAbsoluteIndex（end，length）;
    而（endPos> index）O [index ++] =值；
    返回O;
  };
  
  }，{“ 137”：137，“ 141”：141，“ 142”：142}]，41：[function（_dereq_，module，exports）{
  // false-> Array＃indexOf
  // true-> Array＃includes
  var toIObject = _dereq_（140）;
  var toLength = _dereq_（141）;
  var toAbsoluteIndex = _dereq_（137）;
  module.exports =函数（IS_INCLUDES）{
    返回函数（$ this，el，fromIndex）{
      var O = toIObject（$ this）;
      var length = toLength（O.length）;
      var index = toAbsoluteIndex（fromIndex，length）;
      var值;
      // Array＃includes使用SameValueZero相等算法
      // eslint-disable-next-line no-self-compare
      if（IS_INCLUDES && el！= el）而（length> index）{
        值= O [index ++];
        // eslint-disable-next-line no-self-compare
        如果（值！=值）返回true;
      // Array＃indexOf忽略孔，Array＃includes-不
      }如果（IS_INCLUDES || O中的索引）{
        如果（O [index] === el）返回IS_INCLUDES || 索引|| 0;
      } return！IS_INCLUDES && -1;
    };
  };
  
  }，{“ 137”：137，“ 140”：140，“ 141”：141}]，42：[function（_dereq_，module，exports）{
  // 0-> Array＃forEach
  // 1-> Array＃map
  // 2-> Array＃filter
  // 3-> Array＃some
  // 4-> Array＃every
  // 5-> Array＃find
  // 6-> Array＃findIndex
  var ctx = _dereq_（54）;
  var IObject = _dereq_（77）;
  var toObject = _dereq_（142）;
  var toLength = _dereq_（141）;
  var asc = _dereq_（45）;
  module.exports =函数（TYPE，$ create）{
    var IS_MAP = TYPE == 1;
    var IS_FILTER = TYPE == 2;
    var IS_SOME = TYPE == 3;
    var IS_EVERY = TYPE == 4;
    var IS_FIND_INDEX = TYPE == 6;
    var NO_HOLES = TYPE == 5 || IS_FIND_INDEX;
    var create = $ create || asc;
    返回函数（$ this，callbackfn，that）{
      var O = toObject（$ this）;
      var self = IObject（O）;
      var f = ctx（callbackfn，that，3）;
      var length = toLength（self.length）;
      var index = 0;
      var结果= IS_MAP？create（$ this，length）：IS_FILTER吗？create（$ this，0）：未定义;
      var val，res;
      for（; length> index; index ++）如果（NO_HOLES ||自身索引）{
        val = self [index];
        res = f（val，index，O）;
        如果（TYPE）{
          如果（IS_MAP）result [index] = res; //地图
          否则，如果（res）开关（TYPE）{
            情况3：返回true；//一些
            情况5：返回val；// 找
            情况6：返回索引；// findIndex
            情况2：result.push（val）; //过滤器
          }如果（IS_EVERY）返回false；否则 //每个
        }
      }
      返回IS_FIND_INDEX吗？-1：IS_SOME || IS_EVERY？IS_EVERY：结果；
    };
  };
  
  }，{“ 141”：141，“ 142”：142，“ 45”：45，“ 54”：54，“ 77”：77}]，43：[function（_dereq_，module，exports）{
  var aFunction = _dereq_（33）;
  var toObject = _dereq_（142）;
  var IObject = _dereq_（77）;
  var toLength = _dereq_（141）;
  
  module.exports =函数（即callbackfn，aLen，备忘录，isRight）{
    aFunction（callbackfn）;
    var O = toObject（that）;
    var self = IObject（O）;
    var length = toLength（O.length）;
    var index = isRight？长度-1：0;
    var i = isRight吗？-1：1;
    如果（aLen <2）等于（;;）{
      如果（自我索引）{
        memo = self [index];
        索引+ = i;
        打破;
      }
      索引+ = i;
      if（isRight？index <0：长度<= index）{
        抛出TypeError（'减少没有初始值的空数组'）;
      }
    }
    for（; isRight？index> = 0：长度> index; index + = i）if（自身索引）{
      memo = callbackfn（memo，self [index]，index，O）;
    }
    归还备忘录；
  };
  
  }，{“ 141”：141，“ 142”：142，“ 33”：33，“ 77”：77}]，44：[function（_dereq_，module，exports）{
  var isObject = _dereq_（81）;
  var isArray = _dereq_（79）;
  var SPECIES = _dereq_（152）（'species'）;
  
  module.exports =函数（原始）{
    var C;
    如果（isArray（原始））{
      C = original.constructor;
      //跨域后备
      if（typeof C =='function'&&（C === Array || isArray（C.prototype）））C =未定义；
      如果（isObject（C））{
        C = C [SPECIES];
        如果（C === null）C =未定义；
      }
    返回C === undefined吗？数组：C;
  };
  
  }，{“ 152”：152，“ 79”：79，“ 81”：81}]，45：[function（_dereq_，module，exports）{
  // 9.4.2.3 ArraySpeciesCreate（originalArray，length）
  var speciesConstructor = _dereq_（44）;
  
  module.exports =函数（原始长度）{
    返回新的（speciesConstructor（原始））（长度）;
  };
  
  }，{“ 44”：44}]，46：[function（_dereq_，module，exports）{
  “使用严格”；
  var aFunction = _dereq_（33）;
  var isObject = _dereq_（81）;
  var invoke = _dereq_（76）;
  var arraySlice = [] .slice;
  var factory = {};
  
  var结构=函数（F，len，args）{
    如果（！（工厂里的len））{
      对于（var n = []，i = 0; i <len; i ++）n [i] ='a ['+ i +']';
      // eslint-disable-next-line no-new-func
      factory [len] = Function（'F，a'，'return new F（'+ n.join（'，'）+'）'）;
    }返回工厂[len]（F，args）;
  };
  
  module.exports = Function.bind || 函数bind（that / *，... args * /）{
    var fn = aFunction（this）;
    var partArgs = arraySlice.call（arguments，1）;
    var bound = function（/ * args ... * /）{
      var args = partArgs.concat（arraySlice.call（arguments））;
      返回此instanceof bound吗？Construct（fn，args.length，args）：invoke（fn，args，that）;
    };
    如果（isObject（fn.prototype））bound.prototype = fn.prototype;
    返回界限
  };
  
  }，{“ 33”：33，“ 76”：76，“ 81”：81}]，47：[function（_dereq_，module，exports）{
  //从19.1.3.6 Object.prototype.toString（）获取标签
  var cof = _dereq_（48）;
  var TAG = _dereq_（152）（'toStringTag'）;
  // ES3在这里出错
  var ARG = cof（function（）{return arguments;}（））=='Arguments';
  
  // IE11脚本访问被拒绝错误的后备
  var tryGet = function（it，key）{
    尝试{
      返回[key];
    }抓住（e）{/ *空* /}
  };
  
  module.exports = function（it）{
    var O，T，B;
    返回它===未定义？'未定义'：= == null？'空值'
      // @@ toStringTag大小写
      ：typeof（T = tryGet（O = Object（it），TAG））=='字符串'？Ť
      // buildinTag大小写
      ：ARG？cof（O）
      // ES3参数回退
      ：（B = cof（O））=='对象'&&类型为O.callee =='函数'？'参数'：B;
  };
  
  }，{“ 152”：152，“ 48”：48}]，48：[function（_dereq_，module，exports）{
  var toString = {} .toString;
  
  module.exports = function（it）{
    返回toString.call（it）.slice（8，-1）;
  };
  
  }，{}]，49：[function（_dereq_，module，exports）{
  “使用严格”；
  var dP = _dereq_（99）.f;
  var create = _dereq_（98）;
  var redefineAll = _dereq_（117）;
  var ctx = _dereq_（54）;
  var anInstance = _dereq_（37）;
  var forOf = _dereq_（68）;
  var $ iterDefine = _dereq_（85）;
  var step = _dereq_（87）;
  var setSpecies = _dereq_（123）;
  var DESCRIPTORS = _dereq_（58）;
  var fastKey = _dereq_（94）.fastKey;
  var validate = _dereq_（149）;
  var SIZE = DESCRIPTORS吗？'_s'：'大小';
  
  var getEntry = function（that，key）{
    //快速案例
    var index = fastKey（key）;
    var entry;
    _i [index]; if（index！=='F'）返回。
    //冻结对象的情况
    for（entry = that._f; entry; entry = entry.n）{
      if（entry.k == key）返回条目；
    }
  };
  
  module.exports = {
    getConstructor：函数（包装器，NAME，IS_MAP，ADDER）{
      var C = wrapper（function（that，iterable）{
        anInstance（即C，NAME，“ _ i”）；
        _t = NAME; //集合类型
        _i = create（null）; //索引
        那._f =未定义; //第一个条目
        _l =未定义; //最后一个条目
        那[SIZE] = 0; //大小
        if（iterable！= undefined）forOf（iterable，IS_MAP，that [ADDER]，that）;
      }）;
      redefineAll（C.prototype，{
        // ******** Map.prototype.clear（）
        // ******** Set.prototype.clear（）
        clear：function clear（）{
          for（var that = validate（this，NAME），data = that._i，entry = that._f; entry; entry = entry.n）{
            entry.r = true;
            if（entry.p）entry.p = entry.pn =未定义；
            删除数据[entry.i]；
          }
          that._f = that._l =未定义；
          那[SIZE] = 0;
        }，
        // 23.1.3.3 Map.prototype.delete（key）
        // 23.2.3.4 Set.prototype.delete（value）
        'delete'：function（key）{
          var that = validate（this，NAME）;
          var entry = getEntry（that，key）;
          如果（输入）{
            var next = entry.n;
            var prev = entry.p;
            删除那个。_i[entry.i];
            entry.r = true;
            如果（prev）prev.n = next;
            如果（next）next.p = prev;
            如果（that._f == entry）that._f = next;
            如果（that._l ==条目）that._l =上一页
            那[SIZE]-;
          返回！
        }，
        // 23.2.3.6 Set.prototype.forEach（callbackfn，thisArg = undefined）
        // 23.1.3.5 Map.prototype.forEach（callbackfn，thisArg = undefined）
        forEach：函数forEach（callbackfn / *，即=未定义* /）{
          验证（此，NAME）；
          var f = ctx（callbackfn，arguments.length> 1？arguments [1]：undefined，3）;
          var entry;
          while（entry = entry？entry.n：this._f）{
            f（entry.v，entry.k，这个）;
            //恢复到最后一个现有条目
            while（entry && entry.r）entry = entry.p;
          }
        }，
        // ******** Map.prototype.has（key）
        // ******** Set.prototype.has（value）
        has：function has（key）{
          返回!! getEntry（validate（this，NAME），key）;
        }
      }）;
      if（DESCRIPTORS）dP（C.prototype，'size'，{
        get：function（）{
          返回validate（this，NAME）[SIZE];
        }
      }）;
      返回C;
    }，
    def：功能（键，值）{
      var entry = getEntry（that，key）;
      var prev，index;
      //更改现有条目
      如果（输入）{
        entry.v =值;
      //创建新条目
      }其他{
        那。_l=条目= {
          i：index = fastKey（key，true），// <-索引
          k：键，// <-键
          v：值，// <-值
          p：prev = that._l，// <-上一个条目
          n：未定义，// <-下一条目
          r：false // <-删除
        };
        if（！that._f）that._f =条目；
        如果（prev）prev.n =条目；
        那[SIZE] ++;
        //添加到索引
        如果（index！=='F'）that._i [index] = entry;
      }返回；
    }，
    getEntry：getEntry，
    setStrong：函数（C，NAME，IS_MAP）{
      //添加.keys，.values，.entries，[@@ iterator]
      // 23.1.3.4、23.1.3.8、********1、********2、23.2.3.5、23.2.3.8、********0、********1
      $ iterDefine（C，NAME，function（iterated，kind）{
        _t =验证（重复，NAME）；//目标
        _k =种类; //种类
        _l =未定义；//前一个
      }，函数（）{
        var that = this;
        var kind = that._k;
        var entry = that._l;
        //恢复到最后一个现有条目
        while（entry && entry.r）entry = entry.p;
        //获取下一个条目
        if（！that._t ||！（that._l = entry = entry？entry.n：that._t._f））{
          //或完成迭代
          那._t =未定义;
          返回步骤（1）；
        }
        //逐步返回
        if（kind =='keys'）返回step（0，entry.k）;
        如果（种类=='值'）返回step（0，entry.v）;
        返回步骤（0，[entry.k，entry.v]）；
      }，IS_MAP？'entries'：'values'，！IS_MAP，true）;
  
      //添加[@@ species]，23.1.2.2、23.2.2.2
      setSpecies（NAME）;
    }
  };
  
  }，{“ 117”：117，“ 123”：123，“ 149”：149，“ 37”：37，“ 54”：54，“ 58”：58，“ 68”：68，“ 85”：85 ，“ 87”：87，“ 94”：94，“ 98”：98，“ 99”：99}]，50：[function（_dereq_，module，exports）{
  “使用严格”；
  var redefineAll = _dereq_（117）;
  var getWeak = _dereq_（94）.getWeak;
  var anObject = _dereq_（38）;
  var isObject = _dereq_（81）;
  var anInstance = _dereq_（37）;
  var forOf = _dereq_（68）;
  var createArrayMethod = _dereq_（42）;
  var $ has = _dereq_（71）;
  var validate = _dereq_（149）;
  var arrayFind = createArrayMethod（5）;
  var arrayFindIndex = createArrayMethod（6）;
  var id = 0;
  
  //未捕获的冻结键的回退
  var uncaughtFrozenStore = function（that）{
    返回那个。（that._l =新的UncaughtFrozenStore（））;
  };
  var UncaughtFrozenStore = function（）{
    this.a = [];
  };
  var findUncaughtFrozen =函数（存储，键）{
    return arrayFind（store.a，function（it）{
      返回它[0] ===键；
    }）;
  };
  UncaughtFrozenStore.prototype = {
    get：function（key）{
      var entry = findUncaughtFrozen（this，key）;
      如果（输入）返回条目[1]；
    }，
    具有：功能（键）{
      返回!! findUncaughtFrozen（this，key）;
    }，
    设置：功能（键，值）{
      var entry = findUncaughtFrozen（this，key）;
      if（entry）entry [1] =值；
      否则this.a.push（[key，value]）;
    }，
    'delete'：function（key）{
      var index = arrayFindIndex（this.a，function（it）{
        返回它[0] ===键；
      }）;
      如果（〜index）this.a.splice（index，1）;
      返回!!〜索引;
    }
  };
  
  module.exports = {
    getConstructor：函数（包装器，NAME，IS_MAP，ADDER）{
      var C = wrapper（function（that，iterable）{
        anInstance（即C，NAME，“ _ i”）；
        _t = NAME; //集合类型
        _i = id ++; //集合ID
        _l =未定义; //未捕获的冻结对象的泄漏存储
        if（iterable！= undefined）forOf（iterable，IS_MAP，that [ADDER]，that）;
      }）;
      redefineAll（C.prototype，{
        // 23.3.3.2 WeakMap.prototype.delete（key）
        // 23.4.3.3 WeakSet.prototype.delete（value）
        'delete'：function（key）{
          如果（！isObject（key））返回false;
          var data = getWeak（key）;
          if（data === true）返回uncaughtFrozenStore（validate（this，NAME））['delete']（key）;
          返回数据&& $ has（data，this._i）&&删除数据[this._i];
        }，
        // 23.3.3.4 WeakMap.prototype.has（key）
        // 23.4.3.4 WeakSet.prototype.has（value）
        has：function has（key）{
          如果（！isObject（key））返回false;
          var data = getWeak（key）;
          if（data === true）返回uncaughtFrozenStore（validate（this，NAME））。has（key）;
          返回数据&& $ has（data，this._i）;
        }
      }）;
      返回C;
    }，
    def：功能（键，值）{
      var data = getWeak（anObject（key），true）;
      如果（data === true）uncaughtFrozenStore（that）.set（key，value）;
      否则data [that._i] =值；
      退还
    }，
    ufstore：未捕获的FrozenStore
  };
  
  }，{“ 117”：117，“ 149”：149，“ 37”：37，“ 38”：38，“ 42”：42，“ 68”：68，“ 71”：71，“ 81”：81 ，“ 94”：94}]，51：[function（_dereq_，module，exports）{
  “使用严格”；
  var global = _dereq_（70）;
  var $ export = _dereq_（62）;
  var redefine = _dereq_（118）;
  var redefineAll = _dereq_（117）;
  var meta = _dereq_（94）;
  var forOf = _dereq_（68）;
  var anInstance = _dereq_（37）;
  var isObject = _dereq_（81）;
  var failed = _dereq_（64）;
  var $ iterDetect = _dereq_（86）;
  var setToStringTag = _dereq_（124）;
  varInheritIfRequired = _dereq_（75）;
  
  module.exports =函数（名称，包装器，方法，通用，IS_MAP，IS_WEAK）{
    var Base = global [NAME];
    var C = Base;
    var ADDER = IS_MAP吗？'set'：'add';
    var proto = C && C.prototype;
    var O = {};
    var fixMethod = function（KEY）{
      var fn = proto [KEY];
      重新定义（proto，KEY，
        KEY =='删除'？功能（a）{
          返回IS_WEAK &&！isObject（a）吗？false：fn.call（this，a === 0？0：a）;
        }：KEY ==“有”？函数具有（a）{
          返回IS_WEAK &&！isObject（a）吗？false：fn.call（this，a === 0？0：a）;
        }：KEY =='get'？函数get（a）{
          返回IS_WEAK &&！isObject（a）吗？未定义：fn.call（this，a === 0？0：a）;
        KEY =='add'吗？函数add（a）{fn.call（this，a === 0？0：a）; 返回这个 }
          ：function set（a，b）{fn.call（this，a === 0？0：a，b）; 返回这个 }
      ）;
    };
    if（typeof C！='function'||！（IS_WEAK || proto.forEach &&！失败（function（）{
      新的C（）。entries（）。next（）;
    }）））{
      //创建集合构造函数
      C = common.getConstructor（包装器，名称，IS_MAP，添加器）;
      redefineAll（C.prototype，Methods）;
      meta.NEED = true;
    }其他{
      var instance = new C（）;
      //早期实现不支持链接
      var HASNT_CHAINING = instance [ADDER]（IS_WEAK？{}：-0，1）！=实例；
      // V8〜Chromium 40-弱集合引发基本体，但应返回false
      var THROWS_ON_PRIMITIVES = failed（function（）{instance.has（1）;}）;
      //大多数早期的实现不支持可迭代，最现代的-无法正确关闭它
      var ACCEPT_ITERABLES = $ iterDetect（function（iter）{new C（iter）;}）; // eslint-disable-line no new
      //对于早期实现，-0和+0不相同
      var BUGGY_ZERO =！IS_WEAK &&失败（函数（）{
        // V8〜Chromium 42-仅对5个以上的元素失败
        var $ instance = new C（）;
        var index = 5;
        while（index--）$ instance [ADDER]（index，index）;
        返回！$ instance.has（-0）;
      }）;
      如果（！ACCEPT_ITERABLES）{
        C =包装器（函数（目标，可迭代）{
          anInstance（目标，C，NAME）；
          var that = InheritIfRequired（new Base（），target，C）;
          if（iterable！= undefined）forOf（iterable，IS_MAP，that [ADDER]，that）;
          退还
        }）;
        C.prototype = proto;
        proto.constructor = C;
      }
      如果（THROWS_ON_PRIMITIVES || BUGGY_ZERO）{
        fixMethod（'delete'）;
        fixMethod（'has'）;
        IS_MAP && fixMethod（'get'）;
      }
      如果（BUGGY_ZERO || HASNT_CHAINING）fixMethod（ADDER）;
      //弱集合不应包含.clear方法
      如果（IS_WEAK && proto.clear）删除proto.clear;
    }
  
    setToStringTag（C，NAME）;
  
    O [NAME] = C;
    $ export（$ export.G + $ export.W + $ export.F *（C！= Base），O）;
  
    如果（！IS_WEAK）common.setStrong（C，NAME，IS_MAP）;
  
    返回C;
  };
  
  }，{“ 117”：117，“ 118”：118，“ 124”：124，“ 37”：37，“ 62”：62，“ 64”：64，“ 68”：68，“ 70”：70 ，“ 75”：75，“ 81”：81，“ 86”：86，“ 94”：94}]，52：[function（_dereq_，module，exports）{
  arguments [4] [18] [0] .apply（exports，arguments）
  }，{“ 18”：18}]，53：[function（_dereq_，module，exports）{
  “使用严格”；
  var $ defineProperty = _dereq_（99）;
  var createDesc = _dereq_（116）;
  
  module.exports =函数（对象，索引，值）{
    if（对象中的索引）$ defineProperty.f（对象，索引，createDesc（0，值））;
    else object [index] = value;
  };
  
  }，{“ 116”：116，“ 99”：99}]，54：[function（_dereq_，module，exports）{
  arguments [4] [19] [0] .apply（exports，arguments）
  }，{“ 19”：19，“ 33”：33}]，55：[function（_dereq_，module，exports）{
  “使用严格”；
  // 20.3.4.36 / 15.9.5.43 Date.prototype.toISOString（）
  var failed = _dereq_（64）;
  var getTime = Date.prototype.getTime;
  var $ toISOString = Date.prototype.toISOString;
  
  var lz = function（num）{
    返回num> 9吗？num：'0'+ num;
  };
  
  // PhantomJS /旧版WebKit的实现方式已损坏
  module.exports =（fails（function（）{
    return $ toISOString.call（new Date（-5e13-1））！='0385-07-25T07：06：39.999Z';
  }）|| ！失败（功能（）{
    $ toISOString.call（new Date（NaN））;
  }））？函数toISOString（）{
    如果（！isFinite（getTime.call（this）））抛出RangeError（'无效时间值'）;
    var d = this;
    var y = d.getUTCFullYear（）;
    var m = d.getUTCMilliseconds（）;
    var s = y <0吗？'-'：y> 9999？'+'：'';
    返回s +（'00000'+ Math.abs（y））。slice（s？-6：-4）+
      '-'+ lz（d.getUTCMonth（）+ 1）+'-'+ lz（d.getUTCDate（））+
      'T'+ lz（d.getUTCHours（））+'：'+ lz（d.getUTCMinutes（））+
      '：'+ lz（d.getUTCSeconds（））+'。' +（m> 99？m：'0'+ lz（m））+'Z';
  }：$ toISOString;
  
  }，{“ 64”：64}]，56：[function（_dereq_，module，exports）{
  “使用严格”；
  var anObject = _dereq_（38）;
  var toPrimitive = _dereq_（143）;
  var NUMBER ='number';
  
  module.exports =函数（提示）{
    如果（提示！=='字符串'&&提示！== NUM​​BER &&提示！=='默认'）抛出TypeError（'不正确的提示'）;
    返回至Primitive（anObject（this），提示！= NUM​​BER）;
  };
  
  }，{“ 143”：143，“ 38”：38}]，57：[function（_dereq_，module，exports）{
  // 7.2.1 RequireObjectCoercible（参数）
  module.exports = function（it）{
    如果（= =未定义）抛出TypeError（“无法在” + it上调用方法“；
    把它返还;
  };
  
  }，{}]，58：[function（_dereq_，module，exports）{
  arguments [4] [20] [0] .apply（exports，arguments）
  }，{“ 20”：20，“ 64”：64}]，59：[function（_dereq_，module，exports）{
  arguments [4] [21] [0] .apply（exports，arguments）
  }，{“ 21”：21，“ 70”：70，“ 81”：81}]，60：[function（_dereq_，module，exports）{
  // IE 8-不要枚举错误密钥
  module.exports =（
    '构造函数，hasOwnProperty，isPrototypeOf，propertyIsEnumerable，toLocaleString，toString，valueOf'
  ）。分裂（'，'）;
  
  }，{}]，61：[function（_dereq_，module，exports）{
  //所有可枚举的对象键，包括符号
  var getKeys = _dereq_（107）;
  var gOPS = _dereq_（104）;
  var pIE = _dereq_（108）;
  module.exports = function（it）{
    var result = getKeys（it）;
    var getSymbols = gOPS.f;
    如果（getSymbols）{
      var symbol = getSymbols（it）;
      var isEnum = pIE.f;
      var i = 0;
      var key;
      while（symbols.length> i）if（isEnum.call（it，key = symbols [i ++]））result.push（key）;
    }返回结果；
  };
  
  }，{“ 104”：104，“ 107”：107，“ 108”：108}]，62：[function（_dereq_，module，exports）{
  var global = _dereq_（70）;
  var core = _dereq_（52）;
  var hide = _dereq_（72）;
  var redefine = _dereq_（118）;
  var ctx = _dereq_（54）;
  var PROTOTYPE ='prototype';
  
  var $ export =函数（类型，名称，源）{
    var IS_FORCED =类型＆$ export.F;
    var IS_GLOBAL =类型＆$ export.G;
    var IS_STATIC =类型＆$ export.S;
    var IS_PROTO =类型＆$ export.P;
    var IS_BIND =类型＆$ export.B;
    var target = IS_GLOBAL？全局：IS_STATIC？全局[名称] || （global [name] = {}）：( global [name] || {}）[PROTOTYPE];
    var export = IS_GLOBAL？核心：核心[名称] || （core [name] = {}）;
    var expProto =出口[PROTOTYPE] || （exports [PROTOTYPE] = {}）；
    var key，own，out，exp;
    如果（IS_GLOBAL）源=名称；
    为（输入来源）{
      //包含在本地
      自己=！IS_FORCED &&目标&&目标[键]！==未定义;
      //导出本地或通过
      out =（own？target：source）[key];
      //将计时器绑定到全局，以便从导出上下文进行调用
      exp = IS_BIND &&拥有？ctx（out，global）：IS_PROTO && typeof out =='功能'？ctx（Function.call，out）：输出;
      //扩展全球
      如果（target）重新定义（target，key，out，type＆$ export.U）;
      // 出口
      如果（exports [key]！= out）隐藏（exports，key，exp）;
      如果（IS_PROTO && expProto [key]！= out）expProto [key] = out;
    }
  };
  global.core =核心；
  //输入位图
  $ export.F = 1; //强制
  $ export.G = 2; //全球
  $ export.S = 4; // 静态的
  $ export.P = 8; //原型
  $ export.B = 16; //绑定
  $ export.W = 32; //包装
  $ export.U = 64; //安全
  $ export.R = 128; //真正的库方法
  module.exports = $ export;
  
  }，{“ 118”：118，“ 52”：52，“ 54”：54，“ 70”：70，“ 72”：72}]，63：[function（_dereq_，module，exports）{
  var MATCH = _dereq_（152）（'match'）;
  module.exports =函数（KEY）{
    var re = /./;
    尝试{
      '/./'[KEY](re）;
    }抓住（e）{
      尝试{
        re [MATCH] =假；
        返回！'/./'[KEY]（re）;
      }抓（f）{/ *空* /}
    返回true；
  };
  
  }，{“ 152”：152}]，64：[function（_dereq_，module，exports）{
  arguments [4] [23] [0] .apply（exports，arguments）
  }，{“ 23”：23}]，65：[function（_dereq_，module，exports）{
  “使用严格”；
  _dereq_（248）;
  var redefine = _dereq_（118）;
  var hide = _dereq_（72）;
  var failed = _dereq_（64）;
  var defined = _dereq_（57）;
  var wks = _dereq_（152）;
  var regexpExec = _dereq_（120）;
  
  var SPECIES = wks（'species'）;
  
  var REPLACE_SUPPORTS_NAMED_GROUPS =！失败（函数（）{
    // #replace需要对命名组的内置支持。
    // #match可以正常工作，因为它只返回exec结果，即使它具有
    //一个“ grops”属性。
    var re = /./;
    re.exec = function（）{
      var result = [];
      result.groups = {a：'7'};
      返回结果；
    };
    返回''.replace（re，'$ <a>'）！=='7';
  }）;
  
  var SPLIT_WORKS_WITH_OVERWRITTEN_EXEC =（函数（）{
    //当RegExp＃exec！== nativeExec时，Chrome 51具有错误的“拆分”实现
    var re = /（？：）/;
    var originalExec = re.exec;
    re.exec = function（）{return originalExec.apply（this，arguments）; };
    var result ='ab'.split（re）;
    返回result.length === 2 && result [0] ==='a'&& result [1] ==='b';
  }）（）;
  
  module.exports =函数（KEY，长度，执行）{
    var SYMBOL = wks（KEY）;
  
    var DELEGATES_TO_SYMBOL =！fails（function（）{
      //字符串方法调用以符号命名的RegEp方法
      var O = {};
      O [SYMBOL] = function（）{return 7; };
      返回''[KEY]（O）！= 7;
    }）;
  
    var DELEGATES_TO_EXEC = DELEGATES_TO_SYMBOL吗？！失败（功能（）{
      //以符号命名的RegExp方法调用.exec
      var execCalled = false;
      var re = / a /;
      re.exec = function（）{execCalled = true; 返回null; };
      如果（KEY ==='split'）{
        // RegExp [@@ split]不调用regex的exec方法，而是先创建
        //一个新的。创建新的正则表达式时，我们需要返回修补的正则表达式。
        re.constructor = {};
        re.constructor [SPECIES] = function（）{return re; };
      }
      re [SYMBOL]（''）;
      返回！execCalled;
    }）：未定义；
  
    如果（
      ！DELEGATES_TO_SYMBOL ||
      ！DELEGATES_TO_EXEC ||
      （关键字==='替换'&&！REPLACE_SUPPORTS_NAMED_GROUPS）||
      （KEY ==='分割'&&！SPLIT_WORKS_WITH_OVERWRITTEN_EXEC）
    ）{
      var nativeRegExpMethod = /./[SYMBOL];
      var fns = exec（
        定义
        符号，
        ''[键]，
        函数mayCallNative（nativeMethod，regexp，str，arg2，forceStringMethod）{
          如果（regexp.exec === regexpExec）{
            如果（DELEGATES_TO_SYMBOL &&！forceStringMethod）{
              //本机String方法已经委托给@@ method（this
              // polyfilled函数），以进行无限递归。
              //我们通过直接调用本机@@ method方法来避免这种情况。
              返回{完成：true，值：nativeRegExpMethod.call（regexp，str，arg2）};
            }
            return {done：true，value：nativeMethod.call（str，regexp，arg2）};
          }
          返回{done：false};
        }
      ）;
      var strfn = fns [0];
      var rxfn = fns [1];
  
      redefine（String.prototype，KEY，strfn）;
      hide（RegExp.prototype，SYMBOL，长度== 2
        // 21.2.5.8 RegExp.prototype [@@ replace]（string，replaceValue）
        // 21.2.5.11 RegExp.prototype [@@ split]（字符串，限制）
        ？函数（字符串，参数）{返回rxfn.call（字符串，此，参数）；}
        // 21.2.5.6 RegExp.prototype [@@ match]（string）
        // 21.2.5.9 RegExp.prototype [@@ search]（string）
        ：function（string）{return rxfn.call（string，this）; }
      ）;
    }
  };
  
  }，{“ 118”：118，“ 120”：120，“ 152”：152，“ 248”：248，“ 57”：57，“ 64”：64，“ 72”：72}]，66：[函数（_dereq_，模块，出口）{
  “使用严格”；
  // 21.2.5.3获取RegExp.prototype.flags
  var anObject = _dereq_（38）;
  module.exports = function（）{
    var that = anObject（this）;
    var result ='';
    如果（that.global）结果+ ='g';
    如果（that.ignoreCase）结果+ ='i';
    如果（that.multiline）结果+ ='m';
    如果（that.unicode）结果+ ='u';
    if（that.sticky）结果+ ='y';
    返回结果；
  };
  
  }，{“ 38”：38}]，67：[function（_dereq_，module，exports）{
  “使用严格”；
  // https://tc39.github.io/proposal-flatMap/#sec-FlattenIntoArray
  var isArray = _dereq_（79）;
  var isObject = _dereq_（81）;
  var toLength = _dereq_（141）;
  var ctx = _dereq_（54）;
  var IS_CONCAT_SPREADABLE = _dereq_（152）（'isConcatSpread'）;
  
  function flattenIntoArray（target，original，source，sourceLen，start，depth，mapper，thisArg）{
    var targetIndex =开始；
    var sourceIndex = 0;
    var mapFn = mapper吗？ctx（mapper，thisArg，3）：false;
    var元素，可扩展；
  
    而（sourceIndex <sourceLen）{
      如果（来源中的sourceIndex）{
        元素= mapFn？mapFn（source [sourceIndex]，sourceIndex，original）：来源[sourceIndex];
  
        传播=假;
        如果（isObject（element））{
          spreadable =元素[IS_CONCAT_SPREADABLE];
          可扩展=可扩展！==未定义？!! spread：isArray（element）;
        }
  
        如果（可传播&&深度> 0）{
          targetIndex = flattenIntoArray（target，original，element，toLength（element.length），targetIndex，depth-1）-1;
        }其他{
          如果（targetIndex> = 0x1fffffffffffff）抛出TypeError（）;
          target [targetIndex] =元素；
        }
  
        targetIndex ++;
      }
      sourceIndex ++;
    }
    返回targetIndex;
  }
  
  module.exports = flattenIntoArray;
  
  }，{“ 141”：141，“ 152”：152，“ 54”：54，“ 79”：79，“ 81”：81}]，68：[function（_dereq_，module，exports）{
  var ctx = _dereq_（54）;
  var call = _dereq_（83）;
  var isArrayIter = _dereq_（78）;
  var anObject = _dereq_（38）;
  var toLength = _dereq_（141）;
  var getIterFn = _dereq_（153）;
  var BREAK = {};
  var RETURN = {};
  varexports = module.exports = function（可迭代，条目，fn，那个，迭代器）{
    var iterFn = ITERATOR吗？function（）{返回可迭代；}：getIterFn（可迭代）;
    var f = ctx（fn，that，entry？2：1）;
    var index = 0;
    var length，step，迭代器，结果；
    如果（typeof iterFn！='function'）抛出TypeError（iterable +'不可迭代！'）;
    //使用默认迭代器的数组的快速案例
    if（isArrayIter（iterFn））for（length = toLength（iterable.length）; length> index; index ++）{
      结果=条目？f（anObject（step = iterable [index]）[0]，step [1]）：f（iterable [index]）;
      if（结果=== BREAK ||结果===返回）返回结果；
    }否则（iterator = iterFn.call（iterable）;！（step = iterator.next（））。done;）{
      结果=调用（迭代器，f，step.value，条目）;
      if（结果=== BREAK ||结果===返回）返回结果；
    }
  };
  出口。BREAK= BREAK;
  Exports.RETURN = RETURN;
  
  }，{“ 141”：141，“ 153”：153，“ 38”：38，“ 54”：54，“ 78”：78，“ 83”：83}]，69：[function（_dereq_，module，出口）{
  module.exports = _dereq_（126）（'native-function-to-string'，Function.toString）;
  
  }，{“ 126”：126}]，70：[function（_dereq_，module，exports）{
  arguments [4] [24] [0] .apply（exports，arguments）
  }，{“ 24”：24}]，71：[function（_dereq_，module，exports）{
  arguments [4] [25] [0] .apply（exports，arguments）
  }，{“ 25”：25}]，72：[function（_dereq_，module，exports）{
  arguments [4] [26] [0] .apply（exports，arguments）
  }，{“ 116”：116，“ 26”：26，“ 58”：58，“ 99”：99}]，73：[function（_dereq_，module，exports）{
  var document = _dereq_（70）.document;
  module.exports =文档&& document.documentElement;
  
  }，{“ 70”：70}]，74：[function（_dereq_，module，exports）{
  arguments [4] [27] [0] .apply（exports，arguments）
  }，{“ 27”：27，“ 58”：58，“ 59”：59，“ 64”：64}]，75：[function（_dereq_，module，exports）{
  var isObject = _dereq_（81）;
  var setPrototypeOf = _dereq_（122）.set;
  module.exports =函数（目标，C）{
    var S = target.constructor;
    var P;
    if（S！== C && typeof S =='function'&&（P = S.prototype）！== C.prototype && isObject（P）&& setPrototypeOf）{
      setPrototypeOf（that，P）;
    }返回；
  };
  
  }，{“ 122”：122，“ 81”：81}]，76：[function（_dereq_，module，exports）{
  //快速申请，http：//jsperf.lnkit.com/fast-apply/5
  module.exports =函数（fn，args，that）{
    var un = that === undefined;
    开关（args.length）{
      案例0：返回un？fn（）
                        ：fn.call（that）;
      情况1：返回un？fn（args [0]）
                        ：fn.call（that，args [0]）;
      情况2：返回un？fn（args [0]，args [1]）
                        ：fn.call（that，args [0]，args [1]）;
      情况3：返回un？fn（args [0]，args [1]，args [2]）
                        ：fn.call（that，args [0]，args [1]，args [2]）;
      情况4：返回un？fn（args [0]，args [1]，args [2]，args [3]）
                        ：fn.call（that，args [0]，args [1]，args [2]，args [3]）;
    }返回fn.apply（that，args）;
  };
  
  }，{}]，77：[function（_dereq_，module，exports）{
  //对于非数组式ES3和不可枚举的旧V8字符串的回退
  var cof = _dereq_（48）;
  // eslint-disable-next-line no-prototype-builtins
  module.exports = Object（'z'）。propertyIsEnumerable（0）吗？对象：函数（它）{
    return cof（it）=='字符串'？it.split（''）：对象（它）;
  };
  
  }，{“ 48”：48}]，78：[function（_dereq_，module，exports）{
  //检查默认的数组迭代器
  var Iterators = _dereq_（88）;
  var ITERATOR = _dereq_（152）（'iterator'）;
  var ArrayProto = Array.prototype;
  
  module.exports = function（it）{
    返回它！==未定义&&（Iterators.Array ===它|| ArrayProto [ITERATOR] ===它）;
  };
  
  }，{“ 152”：152，“ 88”：88}]，79：[function（_dereq_，module，exports）{
  // 7.2.2 IsArray（参数）
  var cof = _dereq_（48）;
  module.exports = Array.isArray || 函数isArray（arg）{
    return cof（arg）=='数组';
  };
  
  }，{“ 48”：48}]，80：[function（_dereq_，module，exports）{
  // 20.1.2.3 Number.isInteger（number）
  var isObject = _dereq_（81）;
  var floor = Math.floor;
  module.exports =函数isInteger（it）{
    返回！isObject（it）&& isFinite（it）&& floor（it）=== it;
  };
  
  }，{“ 81”：81}]，81：[function（_dereq_，module，exports）{
  arguments [4] [28] [0] .apply（exports，arguments）
  }，{“ 28”：28}]，82：[function（_dereq_，module，exports）{
  // 7.2.8 IsRegExp（参数）
  var isObject = _dereq_（81）;
  var cof = _dereq_（48）;
  var MATCH = _dereq_（152）（'match'）;
  module.exports = function（it）{
    var isRegExp;
    返回isObject（it）&&（（isRegExp = it [MATCH]）！==未定义？!! isRegExp：cof（it）=='RegExp'）;
  };
  
  }，{“ 152”：152，“ 48”：48，“ 81”：81}]，83：[function（_dereq_，module，exports）{
  //在迭代器步骤上调用某项，并在错误发生时安全关闭
  var anObject = _dereq_（38）;
  module.exports =函数（迭代器，fn，值，条目）{
    尝试{
      返回条目？fn（anObject（value）[0]，value [1]）：fn（value）;
    // 7.4.6 IteratorClose（迭代器，完成）
    }抓住（e）{
      var ret = iterator ['return'];
      如果（ret！== undefined）anObject（ret.call（iterator））;
      抛出e;
    }
  };
  
  }，{“ 38”：38}]，84：[function（_dereq_，module，exports）{
  “使用严格”；
  var create = _dereq_（98）;
  var描述符= _dereq_（116）;
  var setToStringTag = _dereq_（124）;
  var IteratorPrototype = {};
  
  // 25.1.2.1.1％IteratorPrototype％[@@ iterator]（）
  _dereq_（72）（IteratorPrototype，_dereq_（152）（'iterator'），function（）{return this;}）;
  
  module.exports =函数（构造函数，名称，下一个）{
    Constructor.prototype = create（IteratorPrototype，{next：描述符（1，next）}）;
    setToStringTag（Constructor，NAME +'Iterator'）;
  };
  
  }，{“ 116”：116，“ 124”：124，“ 152”：152，“ 72”：72，“ 98”：98}]，85：[function（_dereq_，module，exports）{
  “使用严格”；
  var LIBRARY = _dereq_（89）;
  var $ export = _dereq_（62）;
  var redefine = _dereq_（118）;
  var hide = _dereq_（72）;
  var Iterators = _dereq_（88）;
  var $ iterCreate = _dereq_（84）;
  var setToStringTag = _dereq_（124）;
  var getPrototypeOf = _dereq_（105）;
  var ITERATOR = _dereq_（152）（'iterator'）;
  var BUGGY =！（[[.. keys（）中的[] .keys &&'next'））; // Safari的越野车迭代器没有`next`
  var FF_ITERATOR ='@@ iterator';
  var KEYS ='keys';
  var VALUES ='values';
  
  var returnThis = function（）{return this; };
  
  module.exports =函数（基础，名称，构造函数，下一个，默认，IS_SET，强制）{
    $ iterCreate（构造函数，NAME，下一个）;
    var getMethod = function（kind）{
      如果（！BUGGY && proto中的proto）返回proto [kind];
      开关（种类）{
        案例KEYS：返回函数keys（）{返回新的Constructor（this，kind）; };
        情况VALUES：返回函数values（）{返回新的Constructor（this，kind）; };
      } return function entry（）{return new Constructor（this，kind）; };
    };
    var TAG = NAME +'Iterator';
    var DEF_VALUES = DEFAULT == VALUES;
    var VALUES_BUG = false;
    var proto = Base.prototype;
    var $ native = proto [ITERATOR] || 原型[FF_ITERATOR] || DEFAULT && proto [DEFAULT];
    var $ default = $ native || getMethod（DEFAULT）;
    var $ entries = DEFAULT吗？！DEF_VALUES？$ default：getMethod（'entries'）：未定义;
    var $ anyNative =名称=='数组'吗？原始条目|| $ native：$ native;
    var方法，键，IteratorPrototype;
    //修复本地
    如果（$ anyNative）{
      IteratorPrototype = getPrototypeOf（$ anyNative.call（new Base（）））;
      如果（IteratorPrototype！== Object.prototype && IteratorPrototype.next）{
        //将@@ toStringTag设置为本地迭代器
        setToStringTag（IteratorPrototype，TAG，true）;
        //修复一些旧引擎
        if（！LIBRARY && typeof IteratorPrototype [ITERATOR]！='function'）hide（IteratorPrototype，ITERATOR，returnThis）;
      }
    }
    //在V8 / FF中修复Array＃{values，@@ iterator} .name
    如果（DEF_VALUES && $ native && $ native.name！== VALUES）{
      VALUES_BUG = true;
      $ default =函数values（）{返回$ native.call（this）; };
    }
    //定义迭代器
    if（（（！LIBRARY || FORCED）&&（BUGGY || VALUES_BUG ||！proto [ITERATOR]））{
      hide（proto，ITERATOR，$ default）;
    }
    //插入库
    迭代器[NAME] = $ default;
    迭代器[TAG] = returnThis;
    如果（默认）{
      方法= {
        值：DEF_VALUES？$ default：getMethod（VALUES），
        键：IS_SET？$ default：getMethod（KEYS），
        条目：$ entries
      };
      如果（强制）为（键入方法）{
        如果（！（proto中的键））重新定义（proto，键，方法[键]）;
      } else $ export（$ export.P + $ export.F *（BUGGY || VALUES_BUG），NAME，方法）；
    }
    返回方法；
  };
  
  }，{“ 105”：105，“ 118”：118，“ 124”：124，“ 152”：152，“ 62”：62，“ 72”：72，“ 84”：84，“ 88”：88 ，“ 89”：89}]，86：[function（_dereq_，module，exports）{
  var ITERATOR = _dereq_（152）（'iterator'）;
  var SAFE_CLOSING = false;
  
  尝试{
    var riter = [7] [ITERATOR]（）;
    riter ['return'] = function（）{SAFE_CLOSING = true; };
    // eslint-disable-nextline no-throw-literal
    Array.from（riter，function（）{throw 2;}）;
  }抓住（e）{/ *空* /}
  
  module.exports =函数（exec，skipClosing）{
    如果（！skipClosing &&！SAFE_CLOSING）返回false;
    var safe = false;
    尝试{
      var arr = [7];
      var iter = arr [ITERATOR]（）;
      iter.next = function（）{return {done：safe = true}; };
      arr [ITERATOR] = function（）{返回iter; };
      exec（arr）;
    }抓住（e）{/ *空* /}
    安全返回
  };
  
  }，{“ 152”：152}]，87：[function（_dereq_，module，exports）{
  module.exports =函数（完成，值）{
    返回{value：value，done：!! done};
  };
  
  }，{}]，88：[function（_dereq_，module，exports）{
  module.exports = {};
  
  }，{}]，89：[function（_dereq_，module，exports）{
  module.exports = false;
  
  }，{}]，90：[function（_dereq_，module，exports）{
  // ********* Math.expm1（x）
  var $ expm1 = Math.expm1;
  module.exports =（！$ expm1
    //旧的FF错误
    || $ expm1（10）> 22025.465794806719 || $ expm1（10）<22025.4657948067165168
    // Tor浏览器错误
    || $ expm1（-2e-17）！= -2e-17
  ）？函数expm1（x）{
    返回（x = + x）== 0吗？x：x> -1e-6 && x <1e-6？x + x * x / 2：Math.exp（x）-1;
  }：$ expm1;
  
  }，{}]，91：[function（_dereq_，module，exports）{
  // 20.2.2.16 Math.fround（x）
  var sign = _dereq_（93）;
  var pow = Math.pow;
  var EPSILON = pow（2，-52）;
  var EPSILON32 = pow（2，-23）;
  var MAX32 = pow（2，127）*（2-EPSILON32）;
  var MIN32 = pow（2，-126）;
  
  var roundTiesToEven =函数（n）{
    返回n +1 / EPSILON-1 / EPSILON;
  };
  
  module.exports = Math.fround || 函数fround（x）{
    var $ abs = Math.abs（x）;
    var $ sign = sign（x）;
    var a，结果；
    如果（$ abs <MIN32）返回$ sign * roundTiesToEven（$ abs / MIN32 / EPSILON32）* MIN32 * EPSILON32;
    a =（1 + EPSILON32 / EPSILON）* $ abs;
    结果= a-（a-$ abs）;
    // eslint-disable-next-line no-self-compare
    if（结果> MAX32 ||结果！=结果）返回$ sign * Infinity;
    返回$ sign *结果；
  };
  
  }，{“ 93”：93}]，92：[function（_dereq_，module，exports）{
  // 20.2.2.20 Math.log1p（x）
  module.exports = Math.log1p || 函数log1p（x）{
    返回（x = + x）> -1e-8 && x <1e-8吗？x-x * x / 2：Math.log（1 + x）;
  };
  
  }，{}]，93：[function（_dereq_，module，exports）{
  // 20.2.2.28 Math.sign（x）
  module.exports = Math.sign || 功能符号（x）{
    // eslint-disable-next-line no-self-compare
    返回（x = + x）== 0 || x！= x？x：x ＜0？-1：1;
  };
  
  }，{}]，94：[function（_dereq_，module，exports）{
  var META = _dereq_（147）（'meta'）;
  var isObject = _dereq_（81）;
  var has = _dereq_（71）;
  var setDesc = _dereq_（99）.f;
  var id = 0;
  var isExtensible = Object.isExtensible || 函数（）{
    返回true；
  };
  var FREEZE =！_dereq_（64）（function（）{
    返回isExtensible（Object.preventExtensions（{}））;
  }）;
  var setMeta =函数（it）{
    setDesc（it，META，{value：{
      i：'O'+ ++ id，//对象ID
      w：{} //弱集合ID
    }}）;
  };
  var fastKey = function（it，create）{
    //返回带有前缀的原语
    如果（！isObject（it））返回它的type =='symbol'？it：（typeof it =='string'？'S'：'P'）+ it;
    如果（！有（它，META））{
      //无法将元数据设置为未捕获的冻结对象
      如果（！isExtensible（it））返回'F';
      //不需要添加元数据
      如果（！create）返回'E';
      //添加缺少的元数据
      setMeta（it）;
    //返回对象ID
    }返回[META] .i;
  };
  var getWeak = function（it，create）{
    如果（！有（它，META））{
      //无法将元数据设置为未捕获的冻结对象
      如果（！isExtensible（it））返回true;
      //不需要添加元数据
      如果（！create）返回false;
      //添加缺少的元数据
      setMeta（it）;
    //返回散列弱集合ID
    }返回[META] .w;
  };
  //在调用冻结系列方法时添加元数据
  var onFreeze = function（it）{
    如果（FREEZE && meta.NEED && isExtensible（it）&&！has（it，META））setMeta（it）;
    把它返还;
  };
  var meta = module.exports = {
    关键：META，
    需要：错误，
    fastKey：fastKey，
    getWeak：getWeak，
    onFreeze：onFreeze
  };
  
  }，{“ 147”：147，“ 64”：64，“ 71”：71，“ 81”：81，“ 99”：99}]，95：[function（_dereq_，module，exports）{
  var global = _dereq_（70）;
  var macrotask = _dereq_（136）.set;
  var Observer = global.MutationObserver || global.WebKitMutationObserver;
  var process = global.process;
  var Promise = global.Promise;
  var isNode = _dereq_（48）（process）=='进程';
  
  module.exports = function（）{
    var head，最后，通知；
  
    var flush = function（）{
      var parent，fn;
      if（isNode &&（parent = process.domain））parent.exit（）;
      而（头）{
        fn = head.fn;
        head = head.next;
        尝试{
          fn（）;
        }抓住（e）{
          如果（head）notify（）;
          否则last = undefined;
          抛出e;
        }
      } last = undefined;
      如果（父母）parent.enter（）;
    };
  
    // Node.js
    如果（isNode）{
      通知=函数（）{
        process.nextTick（flush）;
      };
    //使用MutationObserver的浏览器，iOS Safari除外-https://github.com/zloirock/core-js/issues/339
    }否则，如果（观察员&&！（global.navigator && global.navigator.standalone））{
      var toggle = true;
      var node = document.createTextNode（''）;
      新的Observer（flush）.observe（node，{characterData：true}）; // eslint-disable-line no new
      通知=函数（）{
        node.data = toggle =！toggle;
      };
    //可能不完全正确但存在的Promise环境
    }否则，如果（Promise && Promise.resolve）{
      //没有参数的Promise.resolve在LG WebOS 2中引发错误
      var promise = Promise.resolve（undefined）;
      通知=函数（）{
        promise.then（flush）;
      };
    //用于其他环境-宏任务基于：
    //-setImmediate
    //-MessageChannel
    //-window.postMessag
    //-onreadystatechange
    //-setTimeout
    }其他{
      通知=函数（）{
        //奇怪的IE + webpack开发服务器错误-使用.call（global）
        macrotask.call（global，flush）;
      };
    }
  
    返回函数（fn）{
      var task = {fn：fn，下一个：undefined};
      如果（last）last.next = task;
      如果（！head）{
        头=任务;
        通知（）;
      } last = task;
    };
  };
  
  }，{“ 136”：136，“ 48”：48，“ 70”：70}]，96：[function（_dereq_，module，exports）{
  “使用严格”；
  // 25.4.1.5 NewPromiseCapability（C）
  var aFunction = _dereq_（33）;
  
  函数PromiseCapability（C）{
    var resolve，reject;
    this.promise = new C（function（$$ resolve，$$ reject）{
      如果（解决！==未定义||拒绝！==未定义）抛出TypeError（'坏承诺构造函数'）;
      resolve = $$ resolve;
      拒绝= $$拒绝；
    }）;
    this.resolve = aFunction（resolve）;
    this.reject = aFunction（reject）;
  }
  
  module.exports.f =函数（C）{
    返回新的PromiseCapability（C）;
  };
  
  }，{“ 33”：33}]，97：[function（_dereq_，module，exports）{
  “使用严格”；
  // ******** Object.assign（target，source，...）
  var DESCRIPTORS = _dereq_（58）;
  var getKeys = _dereq_（107）;
  var gOPS = _dereq_（104）;
  var pIE = _dereq_（108）;
  var toObject = _dereq_（142）;
  var IObject = _dereq_（77）;
  var $ assign = Object.assign;
  
  //应该与符号一起使用并且应该具有确定性的属性顺序（V8错误）
  module.exports =！$ assign || _dereq_（64）（function（）{
    var A = {};
    var B = {};
    // eslint-disable-next-line no-undef
    var S = Symbol（）;
    var K ='abcdefghijklmnopqrst';
    A [S] = 7;
    K.split（''）。forEach（function（k）{B [k] = k;}）;
    返回$ assign（{}，A）[S]！= 7 || Object.keys（$ assign（{}，B））。join（''）！= K;
  }）？function Assign（target，source）{// eslint-disable-line no-unused-vars
    var T = toObject（target）;
    var aLen = arguments.length;
    var index = 1;
    var getSymbols = gOPS.f;
    var isEnum = pIE.f;
    while（aLen>索引）{
      var S = IObject（arguments [index ++]）;
      var keys = getSymbols吗？getKeys（S）.concat（getSymbols（S））：getKeys（S）;
      var length = keys.length;
      var j = 0;
      var key;
      而（长度> j）{
        键=键[j ++];
        if（！DESCRIPTORS || isEnum.call（S，key））T [key] = S [key];
      }
    } return T;
  }：$ assign;
  
  }，{“ 104”：104，“ 107”：107，“ 108”：108，“ 142”：142，“ 58”：58，“ 64”：64，“ 77”：77}]，98：[函数（_dereq_，模块，出口）{
  // ******** / ******** Object.create（O [，Properties]）
  var anObject = _dereq_（38）;
  var dPs = _dereq_（100）;
  var enumBugKeys = _dereq_（60）;
  var IE_PROTO = _dereq_（125）（'IE_PROTO'）;
  var空=函数（）{/ *空* /};
  var PROTOTYPE ='prototype';
  
  //使用伪造的null原型创建对象：将iframe Object与清除的原型一起使用
  var createDict = function（）{
    //抛出，浪费和鸡奸：IE GC错误
    var iframe = _dereq_（59）（'iframe'）;
    var i = enumBugKeys.length;
    var lt ='<';
    var gt ='>';
    var iframeDocument;
    iframe.style.display ='none';
    _dereq_（73）.appendChild（iframe）;
    iframe.src ='javascript：'; // eslint-disable-line no-script-url
    // createDict = iframe.contentWindow.Object;
    // html.removeChild（iframe）;
    iframeDocument = iframe.contentWindow.document;
    iframeDocument.open（）;
    iframeDocument.write（lt +'script'+ gt +'document.F = Object'+ lt +'/ script'+ gt）;
    iframeDocument.close（）;
    createDict = iframeDocument.F;
    （i--）删除createDict [PROTOTYPE] [enumBugKeys [i]];
    返回createDict（）;
  };
  
  module.exports = Object.create || 函数create（O，Properties）{
    var结果；
    如果（O！== null）{
      空[PROTOTYPE] = anObject（O）;
      结果=新的Empty（）;
      空[PROTOTYPE] =空；
      //为Object.getPrototypeOf polyfill添加“ __proto__”
      结果[IE_PROTO] = O;
    } else result = createDict（）;
    返回属性===未定义？结果：dPs（result，Properties）;
  };
  
  }，{“ 100”：100，“ 125”：125，“ 38”：38，“ 59”：59，“ 60”：60，“ 73”：73}]，99：[function（_dereq_，module，出口）{
  arguments [4] [29] [0] .apply（exports，arguments）
  }，{“ 143”：143，“ 29”：29，“ 38”：38，“ 58”：58，“ 74”：74}]，100：[function（_dereq_，module，exports）{
  var dP = _dereq_（99）;
  var anObject = _dereq_（38）;
  var getKeys = _dereq_（107）;
  
  module.exports = _dereq_（58）吗？Object.defineProperties：函数defineProperties（O，Properties）{
    anObject（O）;
    var keys = getKeys（Properties）;
    var length = keys.length;
    var i = 0;
    var P;
    while（length> i）dP.f（O，P = keys [i ++]，Properties [P]）;
    返回O;
  };
  
  }，{“ 107”：107，“ 38”：38，“ 58”：58，“ 99”：99}]，101：[function（_dereq_，module，exports）{
  var pIE = _dereq_（108）;
  var createDesc = _dereq_（116）;
  var toIObject = _dereq_（140）;
  var toPrimitive = _dereq_（143）;
  var has = _dereq_（71）;
  var IE8_DOM_DEFINE = _dereq_（74）;
  var gOPD = Object.getOwnPropertyDescriptor;
  
  exports.f = _dereq_（58）？gOPD：函数getOwnPropertyDescriptor（O，P）{
    O = toIObject（O）;
    P = toPrimitive（P，true）;
    如果（IE8_DOM_DEFINE）尝试{
      返回gOPD（O，P）;
    }抓住（e）{/ *空* /}
    if（has（O，P））返回createDesc（！pIE.f.call（O，P），O [P]）;
  };
  
  }，{“ 108”：108，“ 116”：116，“ 140”：140，“ 143”：143，“ 58”：58，“ 71”：71，“ 74”：74}]，102：[函数（_dereq_，模块，出口）{
  //使用iframe和window的IE11越野车Object.getOwnPropertyNames的后备
  var toIObject = _dereq_（140）;
  var gOPN = _dereq_（103）.f;
  var toString = {} .toString;
  
  var windowNames = typeof window =='object'&& window && Object.getOwnPropertyNames
    ？Object.getOwnPropertyNames（window）：[];
  
  var getWindowNames = function（it）{
    尝试{
      返回gOPN（it）;
    }抓住（e）{
      返回windowNames.slice（）;
    }
  };
  
  module.exports.f =函数getOwnPropertyNames（it）{
    返回windowNames && toString.call（it）=='[对象窗口]'吗？getWindowNames（it）：gOPN（toIObject（it））;
  };
  
  }，{“ 103”：103，“ 140”：140}]，103：[function（_dereq_，module，exports）{
  // ******** / 15.2.3.4 Object.getOwnPropertyNames（O）
  var $ keys = _dereq_（106）;
  var hiddenKeys = _dereq_（60）.concat（'length'，'prototype'）;
  
  exports.f = Object.getOwnPropertyNames || 函数getOwnPropertyNames（O）{
    返回$ keys（O，hiddenKeys）;
  };
  
  }，{“ 106”：106，“ 60”：60}]，104：[function（_dereq_，module，exports）{
  exports.f = Object.getOwnPropertySymbols;
  
  }，{}]，105：[function（_dereq_，module，exports）{
  // ******** / 15.2.3.2 Object.getPrototypeOf（O）
  var has = _dereq_（71）;
  var toObject = _dereq_（142）;
  var IE_PROTO = _dereq_（125）（'IE_PROTO'）;
  var ObjectProto = Object.prototype;
  
  module.exports = Object.getPrototypeOf || 函数（O）{
    O = toObject（O）;
    如果（具有（O，IE_PROTO））返回O [IE_PROTO];
    if（typeof O.constructor =='function'&& O instanceof O.constructor）{
      返回O.constructor.prototype;
    返回O instanceof Object吗？ObjectProto：null;
  };
  
  }，{“ 125”：125，“ 142”：142，“ 71”：71}]，106：[function（_dereq_，module，exports）{
  var has = _dereq_（71）;
  var toIObject = _dereq_（140）;
  var arrayIndexOf = _dereq_（41）（false）;
  var IE_PROTO = _dereq_（125）（'IE_PROTO'）;
  
  module.exports =函数（对象，名称）{
    var O = toIObject（object）;
    var i = 0;
    var result = [];
    var key;
    如果（key！= IE_PROTO）具有（O，key）&& result.push（key）;
    //不要枚举错误和隐藏键
    while（names.length> i）if（has（O，key = names [i ++]））{
      〜arrayIndexOf（result，key）|| result.push（key）;
    }
    返回结果；
  };
  
  }，{“ 125”：125，“ 140”：140，“ 41”：41，“ 71”：71}]，107：[function（_dereq_，module，exports）{
  // ********4 / 15.2.3.14 Object.keys（O）
  var $ keys = _dereq_（106）;
  var enumBugKeys = _dereq_（60）;
  
  module.exports = Object.keys || 功能键（O）{
    返回$ keys（O，enumBugKeys）;
  };
  
  }，{“ 106”：106，“ 60”：60}]，108：[function（_dereq_，module，exports）{
  exports.f = {} .propertyIsEnumerable;
  
  }，{}]，109：[function（_dereq_，module，exports）{
  // ES6的大多数Object方法应接受原语
  var $ export = _dereq_（62）;
  var core = _dereq_（52）;
  var failed = _dereq_（64）;
  module.exports =函数（KEY，exec）{
    var fn =（co re.Object || {}）[KEY] || 对象[KEY];
    var exp = {};
    exp [KEY] = exec（fn）;
    $ export（$ export.S + $ export.F *失败（函数（）{fn（1）;}），'Object'，exp）;
  };
  
  }，{“ 52”：52，“ 62”：62，“ 64”：64}]，110：[function（_dereq_，module，exports）{
  var DESCRIPTORS = _dereq_（58）;
  var getKeys = _dereq_（107）;
  var toIObject = _dereq_（140）;
  var isEnum = _dereq_（108）.f;
  module.exports =函数（isEntries）{
    返回函数（it）{
      var O = toIObject（it）;
      var keys = getKeys（O）;
      var length = keys.length;
      var i = 0;
      var result = [];
      var key;
      而（长度> i）{
        key = keys [i ++];
        如果（！DESCRIPTORS || isEnum.call（O，key））{
          result.push（isEntries？[key，O [key]]：O [key]）;
        }
      }
      返回结果；
    };
  };
  
  }，{“ 107”：107，“ 108”：108，“ 140”：140，“ 58”：58}]，111：[function（_dereq_，module，exports）{
  //所有对象键，包括不可枚举和符号
  var gOPN = _dereq_（103）;
  var gOPS = _dereq_（104）;
  var anObject = _dereq_（38）;
  var Reflect = _dereq_（70）.Reflect;
  module.exports = Reflect && Reflect.ownKeys || 函数ownKeys（it）{
    var键= gOPN.f（anObject（it））;
    var getSymbols = gOPS.f;
    返回getSymbols吗？keys.concat（getSymbols（it））：键;
  };
  
  }，{“ 103”：103，“ 104”：104，“ 38”：38，“ 70”：70}]，112：[function（_dereq_，module，exports）{
  var $ parseFloat = _dereq_（70）.parseFloat;
  var $ trim = _dereq_（134）.trim;
  
  module.exports = 1 / $ parseFloat（_dereq_（135）+'-0'）！==-无穷大？函数parseFloat（str）{
    var string = $ trim（String（str），3）;
    var result = $ parseFloat（string）;
    返回结果=== 0 && string.charAt（0）=='-' -0：结果；
  }：$ parseFloat;
  
  }，{“ 134”：134，“ 135”：135，“ 70”：70}]，113：[function（_dereq_，module，exports）{
  var $ parseInt = _dereq_（70）.parseInt;
  var $ trim = _dereq_（134）.trim;
  var ws = _dereq_（135）;
  var hex = / ^ [-+]？0 [xX] /;
  
  module.exports = $ parseInt（ws +'08'）！== 8 || $ parseInt（ws +'0x16'）！== 22吗？函数parseInt（str，radix）{
    var string = $ trim（String（str），3）;
    返回$ parseInt（string，（radix >>> 0）||（hex.test（string）？16：10））;
  }：$ parseInt;
  
  }，{“ 134”：134，“ 135”：135，“ 70”：70}]，114：[function（_dereq_，module，exports）{
  module.exports =函数（exec）{
    尝试{
      返回{e：false，v：exec（）};
    }抓住（e）{
      返回{e：true，v：e};
    }
  };
  
  }，{}]，115：[function（_dereq_，module，exports）{
  var anObject = _dereq_（38）;
  var isObject = _dereq_（81）;
  var newPromiseCapability = _dereq_（96）;
  
  module.exports =函数（C，x）{
    anObject（C）;
    如果（isObject（x）&& x.constructor === C）返回x;
    var promiseCapability = newPromiseCapability.f（C）;
    var resolve = promiseCapability.resolve;
    解析（x）;
    返回promiseCapability.promise;
  };
  
  }，{“ 38”：38，“ 81”：81，“ 96”：96}]，116：[function（_dereq_，module，exports）{
  arguments [4] [30] [0] .apply（exports，arguments）
  }，{“ 30”：30}]，117：[function（_dereq_，module，exports）{
  var redefine = _dereq_（118）;
  module.exports =函数（目标，src，安全）{
    for（src中的var key）redefine（target，key，src [key]，safe）;
    返回目标；
  };
  
  }，{“ 118”：118}]，118：[function（_dereq_，module，exports）{
  var global = _dereq_（70）;
  var hide = _dereq_（72）;
  var has = _dereq_（71）;
  var SRC = _dereq_（147）（'src'）;
  var $ toString = _dereq_（69）;
  var TO_STRING ='toString';
  var TPL =（''+ $ toString）.split（TO_STRING）;
  
  _dereq_（52）.inspectSource =函数（it）{
    返回$ toString.call（it）;
  };
  
  （module.exports =函数（O，键，val，安全）{
    var isFunction = typeof val =='function';
    如果（isFunction）具有（val，'name'）|| hide（val，'name'，key）;
    如果（O [key] === val）返回；
    如果（isFunction）具有（val，SRC）|| hide（val，SRC，O [key]？''+ O [key]：TPL.join（String（key）））;
    如果（O ===全局）{
      O [key] = val;
    }否则，如果（！safe）{
      删除O [key];
      hide（O，key，val）;
    } else if（O [key]）{
      O [key] = val;
    }其他{
      hide（O，key，val）;
    }
  //添加伪造的Function＃toString用于正确的工作包装方法/带有LoDash isNative之类的方法的构造函数
  }）（Function.prototype，TO_STRING，function toString（）{
    返回此类型的=='函数'&&此[SRC] || $ toString.call（this）;
  }）;
  
  }，{“ 147”：147，“ 52”：52，“ 69”：69，“ 70”：70，“ 71”：71，“ 72”：72}]，119：[function（_dereq_，module，出口）{
  “使用严格”；
  
  var classof = _dereq_（47）;
  var BuiltinExec = RegExp.prototype.exec;
  
   //`RegExpExec`抽象操作
  // https://tc39.github.io/ecma262/#sec-regexpexec
  module.exports =函数（R，S）{
    var exec = R.exec;
    如果（typeof exec ==='function'）{
      var结果= exec.call（R，S）;
      if（typeof result！=='object'）{
        抛出新的TypeError（'RegExp exec方法返回了Object或null以外的值'）；
      }
      返回结果；
    }
    如果（classof（R）！=='RegExp'）{
      抛出新的TypeError（'RegExp＃exec在不兼容的接收器上调用'）;
    }
    返回BuiltinExec.call（R，S）;
  };
  
  }，{“ 47”：47}]，120：[function（_dereq_，module，exports）{
  “使用严格”；
  
  var regexpFlags = _dereq_（66）;
  
  var nativeExec = RegExp.prototype.exec;
  //这总是指本机实现，因为
  // String＃replace polyfill使用./fix-regexp-well-known-symbol-logic.js，
  //在修补方法之前加载此文件。
  var nativeReplace = String.prototype.replace;
  
  var patchedExec = nativeExec;
  
  var LAST_INDEX ='lastIndex';
  
  var UPDATES_LAST_INDEX_WRONG =（函数（）{
    var re1 = / a /，
        re2 = / b * / g;
    nativeExec.call（re1，'a'）;
    nativeExec.call（re2，'a'）;
    返回re1 [LAST_INDEX]！== 0 || re2 [LAST_INDEX]！== 0;
  }）（）;
  
  //从es5-shim的String＃split补丁复制的非参与捕获组。
  var NPCG_INCLUDED = /()??/.exec('')[1]！==未定义；
  
  var PATCH = UPDATES_LAST_INDEX_WRONG || NPCG_INCLUDED;
  
  如果（PATCH）{
    patchedExec =函数exec（str）{
      var re = this;
      var lastIndex，reCopy，match，i;
  
      如果（NPCG_INCLUDED）{
        reCopy = new RegExp（'^'+ re.source +'$（？！\\ s）'，regexpFlags.call（re））;
      }
      如果（UPDATES_LAST_INDEX_WRONG）lastIndex = re [LAST_INDEX];
  
      匹配= nativeExec.call（re，str）;
  
      如果（UPDATES_LAST_INDEX_WRONG &&匹配）{
        re [LAST_INDEX] = re.global吗？match.index + match [0] .length：lastIndex;
      }
      如果（NPCG_INCLUDED && match && match.length> 1）{
        //修复浏览器的exec方法不会一致地返回undefined的浏览器
        //用于NPCG，例如IE8。注意：这不适用于/(.?)?/
        // eslint-disable-next-line no-loop-func
        nativeReplace.call（match [0]，reCopy，function（）{
          for（i = 1; i <arguments.length-2; i ++）{
            if（arguments [i] === undefined）match [i] = undefined;
          }
        }）;
      }
  
      返回比赛；
    };
  }
  
  module.exports = patchedExec;
  
  }，{“ 66”：66}]，121：[function（_dereq_，module，exports）{
  // 7.2.9 SameValue（x，y）
  module.exports = Object.is || 函数为（x，y）{
    // eslint-disable-next-line no-self-compare
    返回x === y？x！== 0 || 1 / x === 1 / y：x！= x && y！= y;
  };
  
  }，{}]，122：[function（_dereq_，module，exports）{
  //仅适用于__proto__。旧版v8无法使用null原型对象。
  / *禁止禁用无协议* /
  var isObject = _dereq_（81）;
  var anObject = _dereq_（38）;
  var check = function（O，proto）{
    anObject（O）;
    如果（！isObject（proto）&& proto！== null）抛出TypeError（proto +“：无法设置为原型！”）;
  };
  module.exports = {
    设置：Object.setPrototypeOf || （{}中的'__proto__'吗？// eslint-disable-line
      功能（测试，错误，设置）{
        尝试{
          set = _dereq_（54）（Function.call，_dereq_（101）.f（Object.prototype，'__proto __'）。set，2）;
          set（test，[]）;
          buggy =！（测试数组实例）;
        }（e）{buggy = true; }
        返回函数setPrototypeOf（O，proto）{
          检查（O，proto）;
          如果（越野车）O .__ proto__ = proto;
          否则设置（O，proto）;
          返回O;
        };
      }（{}，false）：未定义），
    检查：检查
  };
  
  }，{“ 101”：101，“ 38”：38，“ 54”：54，“ 81”：81}]，123：[function（_dereq_，module，exports）{
  “使用严格”；
  var global = _dereq_（70）;
  var dP = _dereq_（99）;
  var DESCRIPTORS = _dereq_（58）;
  var SPECIES = _dereq_（152）（'species'）;
  
  module.exports =函数（KEY）{
    var C = global [KEY];
    if（DESCRIPTORS && C &&！C [SPECIES]）dP.f（C，SPECIES，{
      可配置：true，
      get：function（）{返回此；}
    }）;
  };
  
  }，{“ 152”：152，“ 58”：58，“ 70”：70，“ 99”：99}]，124：[function（_dereq_，module，exports）{
  var def = _dereq_（99）.f;
  var has = _dereq_（71）;
  var TAG = _dereq_（152）（'toStringTag'）;
  
  module.exports =函数（它，标签，统计信息）{
    if（it &&！has（it = stat？it：it.prototype，TAG））def（it，TAG，{可配置：true，value：tag}）;
  };
  
  }，{“ 152”：152，“ 71”：71，“ 99”：99}]，125：[function（_dereq_，module，exports）{
  var shared = _dereq_（126）（'keys'）;
  var uid = _dereq_（147）;
  module.exports =函数（键）{
    返回共享[密钥] || （shared [key] = uid（key））;
  };
  
  }，{“ 126”：126，“ 147”：147}]，126：[function（_dereq_，module，exports）{
  var core = _dereq_（52）;
  var global = _dereq_（70）;
  var SHARED ='__core-js_shared__';
  var store = global [SHARED] || （global [SHARED] = {}）;
  
  （module.exports =函数（键，值）{
    退货商店[键] || （store [key] = value！== undefined？value：{}）；
  }）（'versions'，[]）。push（{
    版本：core.version，
    模式：_dereq_（89）？'pure'：'global'，
    版权：'©2019 Denis Pushkarev（zloirock.ru）'
  }）;
  
  }，{“ 52”：52，“ 70”：70，“ 89”：89}]，127：[function（_dereq_，module，exports）{
  // 7.3.20 SpeciesConstructor（O，defaultConstructor）
  var anObject = _dereq_（38）;
  var aFunction = _dereq_（33）;
  var SPECIES = _dereq_（152）（'species'）;
  module.exports =函数（O，D）{
    var C = anObject（O）.constructor;
    var S;
    返回C ===未定义|| （S = anObject（C）[SPECIES]）==未定义？D：一个功能（S）；
  };
  
  }，{“ 152”：152，“ 33”：33，“ 38”：38}]，128：[function（_dereq_，module，exports）{
  “使用严格”；
  var failed = _dereq_（64）;
  
  module.exports =函数（方法，arg）{
    return !! method && failed（function（）{
      // eslint-disable-next-line no-useless-call
      arg？method.call（null，function（）{/ *空* /}，1）：method.call（null）;
    }）;
  };
  
  }，{“ 64”：64}]，129：[function（_dereq_，module，exports）{
  var toInteger = _dereq_（139）;
  var defined = _dereq_（57）;
  // true-> String＃at
  // false-> String＃codePointAt
  module.exports =函数（TO_STRING）{
    返回函数（即pos）{
      var s = String（defined（that））;
      var i = toInteger（pos）;
      var l = s.length;
      var a，b;
      如果（i <0 || i> = l）返回TO_STRING？''：未定义;
      a = s.charCodeAt（i）;
      返回<0xd800 || a> 0xdbff || i + 1 === l || （b = s.charCodeAt（i + 1））<0xdc00 || b> 0xdfff
        ？TO_STRING？s.charAt（i）：一个
        ：TO_STRING？s.slice（i，i + 2）：（a-0xd800 << 10）+（b-0xdc00）+ 0x10000;
    };
  };
  
  }，{“ 139”：139，“ 57”：57}]，130：[function（_dereq_，module，exports）{
  // String＃{startsWith，endsWith，includes}的帮助程序
  var isRegExp = _dereq_（82）;
  var defined = _dereq_（57）;
  
  module.exports =函数（即searchString，NAME）{
    如果（isRegExp（searchString））抛出TypeError（'String＃'+ NAME +“不接受正则表达式！”）;
    返回String（defined（that））;
  };
  
  }，{“ 57”：57，“ 82”：82}]，131：[function（_dereq_，module，exports）{
  var $ export = _dereq_（62）;
  var failed = _dereq_（64）;
  var defined = _dereq_（57）;
  var quot = /“ / g;
  // B.2.3.2.1 CreateHTML（字符串，标签，属性，值）
  var createHTML =函数（字符串，标签，属性，值）{
    var S = String（defined（string））;
    var p1 ='<'+标记；
    if（attribute！==''）p1 + =''+ attribute +'=“'+ String（value）.replace（”，'“”'）+'“';
    返回p1 +'>'+ S +'</'+标记+'>';
  };
  module.exports =函数（名称，执行）{
    var O = {};
    O [NAME] = exec（createHTML）;
    $ export（$ export.P + $ export.F *失败（函数（）{
      var test =” [NAME]（'“'）;
      返回测试！== test.toLowerCase（）|| test.split（'“'）。length> 3;
    }），'String'，O）;
  };
  
  }，{“ 57”：57，“ 62”：62，“ 64”：64}]，132：[function（_dereq_，module，exports）{
  // https://github.com/tc39/proposal-string-pad-start-end
  var toLength = _dereq_（141）;
  var repeat = _dereq_（133）;
  var defined = _dereq_（57）;
  
  module.exports =函数（即maxLength，fillString，左侧）{
    var S = String（defined（that））;
    var stringLength = S.length;
    var fillStr = fillString ===未定义？''：字符串（fillString）;
    var intMaxLength = toLength（maxLength）;
    如果（intMaxLength <= stringLength || fillStr ==''）返回S;
    var fillLen = intMaxLength-stringLength;
    var stringFiller = repeat.call（fillStr，Math.ceil（fillLen / fillStr.length））;
    如果（stringFiller.length> fillLen）stringFiller = stringFiller.slice（0，fillLen）;
    返回左？stringFiller + S：S + stringFiller;
  };
  
  }，{“ 133”：133，“ 141”：141，“ 57”：57}]，133：[function（_dereq_，module，exports）{
  “使用严格”；
  var toInteger = _dereq_（139）;
  var defined = _dereq_（57）;
  
  module.exports =函数repeat（count）{
    var str = String（defined（this））;
    var res ='';
    var n = toInteger（count）;
    如果（n <0 || n == Infinity）抛出RangeError（“ Count不能为负”）;
    对于（; n> 0;（n >>> = 1）&&（str + = str））如果（n＆1）res + = str;
    返回资源；
  };
  
  }，{“ 139”：139，“ 57”：57}]，134：[function（_dereq_，module，exports）{
  var $ export = _dereq_（62）;
  var defined = _dereq_（57）;
  var failed = _dereq_（64）;
  var spaces = _dereq_（135）;
  var space ='['+空格+']';
  var non ='\ u200b \ u0085';
  var ltrim = RegExp（'^'+空格+空格+'*'）;
  var rtrim = RegExp（space + space +'* $'）;
  
  var exporter = function（KEY，exec，ALIAS）{
    var exp = {};
    var FORCE = failed（function（）{
      返回!! spaces [KEY]（）|| non [KEY]（）！=非;
    }）;
    var fn = exp [KEY] = FORCE？exec（trim）：个空格[KEY];
    如果（ALIAS）exp [ALIAS] = fn；
    $ export（$ export.P + $ export.F * FORCE，'String'，exp）;
  };
  
  // 1-> String＃trimLeft
  // 2-> String＃trimRight
  // 3-> String＃trim
  var trim = exporter.trim =函数（字符串，TYPE）{
    字符串=字符串（定义（字符串））;
    如果（TYPE＆1）字符串= string.replace（ltrim，''）;
    如果（TYPE＆2）string = string.replace（rtrim，''）;
    返回字符串；
  };
  
  module.exports =出口商；
  
  }，{“ 135”：135，“ 57”：57，“ 62”：62，“ 64”：64}]，135：[function（_dereq_，module，exports）{
  module.exports ='\ x09 \ x0A \ x0B \ x0C \ x0D \ x20 \ xA0 \ u1680 \ u180E \ u2000 \ u2001 \ u2002 \ u2003'+
    '\ u2004 \ u2005 \ u2006 \ u2007 \ u2008 \ u2009 \ u200A \ u202F \ u205F \ u3000 \ u2028 \ u2029 \ uFEFF';
  
  }，{}]，136：[function（_dereq_，module，exports）{
  var ctx = _dereq_（54）;
  var invoke = _dereq_（76）;
  var html = _dereq_（73）;
  var cel = _dereq_（59）;
  var global = _dereq_（70）;
  var process = global.process;
  var setTask = global.setImmediate;
  var clearTask = global.clearImmediate;
  var MessageChannel = global.MessageChannel;
  var Dispatch = global.Dispatch;
  var计数器= 0;
  var queue = {};
  var ONREADYSTATECHANGE ='onreadystatechange';
  var defer，channel，port;
  var run = function（）{
    var id = + this;
    // eslint-disable-next-line no-prototype-builtins
    如果（queue.hasOwnProperty（id））{
      var fn = queue [id];
      删除队列[id];
      fn（）;
    }
  };
  var listener =函数（事件）{
    run.call（event.data）;
  };
  // Node.js 0.9+和IE10 +具有setImmediate，否则：
  如果（！setTask ||！clearTask）{
    setTask =函数setImmediate（fn）{
      var args = [];
      var i = 1;
      while（arguments.length> i）args.push（arguments [i ++]）;
      队列[++计数器] =函数（）{
        // eslint-disable-next-line no-new-func
        invoke（typeof fn =='function'？fn：Function（fn），args）;
      };
      递延（柜台）;
      退货柜台
    };
    clearTask = function clearImmediate（id）{
      删除队列[id];
    };
    // Node.js 0.8-
    如果（_dereq_（48）（process）=='process'）{
      defer = function（id）{
        process.nextTick（ctx（run，id，1））;
      };
    // Sphere（JS游戏引擎）调度API
    }否则，如果（Dispatch && Dispatch.now）{
      defer = function（id）{
        Dispatch.now（ctx（run，id，1））;
      };
    //具有MessageChannel的浏览器，包括WebWorkers
    }否则，如果（MessageChannel）{
      channel = new MessageChannel（）;
      端口= channel.port2;
      channel.port1.onmessage =监听器;
      defer = ctx（port.postMessage，port，1）;
    //使用postMessage的浏览器，跳过WebWorkers
    // IE8具有postMessage，但它的同步和type类型的postMessage是'object'
    }否则，如果（global.addEventListener && typeof postMessage =='function'&&！global.importScripts）{
      defer = function（id）{
        global.postMessage（id +''，'*'）;
      };
      global.addEventListener（'message'，listener，false）;
    // IE8-
    }否则，如果（cel（'script'）中的ONREADYSTATECHANGE）{
      defer = function（id）{
        html.appendChild（cel（'script'））[ONREADYSTATECHANGE] =函数（）{
          html.removeChild（this）;
          run.call（id）;
        };
      };
    //休息旧的浏览器
    }其他{
      defer = function（id）{
        setTimeout（ctx（run，id，1），0）;
      };
    }
  }
  module.exports = {
    设置：setTask，
    clear：clearTask
  };
  
  }，{“ 48”：48，“ 54”：54，“ 59”：59，“ 70”：70，“ 73”：73，“ 76”：76}]，137：[function（_dereq_，module，出口）{
  var toInteger = _dereq_（139）;
  var max = Math.max;
  var min = Math.min;
  module.exports =函数（索引，长度）{
    索引= toInteger（索引）;
    返回索引<0？max（索引+长度，0）：min（索引，长度）;
  };
  
  }，{“ 139”：139}]，138：[function（_dereq_，module，exports）{
  // https://tc39.github.io/ecma262/#sec-toindex
  var toInteger = _dereq_（139）;
  var toLength = _dereq_（141）;
  module.exports = function（it）{
    如果（它===未定义）返回0;
    var number = toInteger（it）;
    var length = toLength（number）;
    如果（number！== length）抛出RangeError（'Wrong length！'）;
    返回长度；
  };
  
  }，{“ 139”：139，“ 141”：141}]，139：[function（_dereq_，module，exports）{
  // 7.1.4 ToInteger
  var ceil = Math.ceil;
  var floor = Math.floor;
  module.exports = function（it）{
    返回isNaN（it = + it）吗？0：（它> 0？floor：ceil）（它）;
  };
  
  }，{}]，140：[function（_dereq_，module，exports）{
  //指向索引对象，带有回退的toObject，用于非数组式ES3字符串
  var IObject = _dereq_（77）;
  var defined = _dereq_（57）;
  module.exports = function（it）{
    返回IObject（defined（it））;
  };
  
  }，{“ 57”：57，“ 77”：77}]，141：[function（_dereq_，module，exports）{
  // 7.1.15 ToLength
  var toInteger = _dereq_（139）;
  var min = Math.min;
  module.exports = function（it）{
    返回> 0吗？min（toInteger（it），0x1fffffffffffffff）：0; // pow（2，53）-1 == 9007199254740991
  };
  
  }，{“ 139”：139}]，142：[function（_dereq_，module，exports）{
  // 7.1.13 ToObject（参数）
  var defined = _dereq_（57）;
  module.exports = function（it）{
    返回Object（defined（it））;
  };
  
  }，{“ 57”：57}]，143：[function（_dereq_，module，exports）{
  arguments [4] [31] [0] .apply（exports，arguments）
  }，{“ 31”：31，“ 81”：81}]，144：[function（_dereq_，module，exports）{
  “使用严格”；
  如果（_dereq_（58））{
    var LIBRARY = _dereq_（89）;
    var global = _dereq_（70）;
    var failed = _dereq_（64）;
    var $ export = _dereq_（62）;
    var $ typed = _dereq_（146）;
    var $ buffer = _dereq_（145）;
    var ctx = _dereq_（54）;
    var anInstance = _dereq_（37）;
    var propertyDesc = _dereq_（116）;
    var hide = _dereq_（72）;
    var redefineAll = _dereq_（117）;
    var toInteger = _dereq_（139）;
    var toLength = _dereq_（141）;
    var toIndex = _dereq_（138）;
    var toAbsoluteIndex = _dereq_（137）;
    var toPrimitive = _dereq_（143）;
    var has = _dereq_（71）;
    var classof = _dereq_（47）;
    var isObject = _dereq_（81）;
    var toObject = _dereq_（142）;
    var isArrayIter = _dereq_（78）;
    var create = _dereq_（98）;
    var getPrototypeOf = _dereq_（105）;
    var gOPN = _dereq_（103）.f;
    var getIterFn = _dereq_（153）;
    var uid = _dereq_（147）;
    var wks = _dereq_（152）;
    var createArrayMethod = _dereq_（42）;
    var createArrayIncludes = _dereq_（41）;
    var speciesConstructor = _dereq_（127）;
    var ArrayIterators = _dereq_（164）;
    var Iterators = _dereq_（88）;
    var $ iterDetect = _dereq_（86）;
    var setSpecies = _dereq_（123）;
    var arrayFill = _dereq_（40）;
    var arrayCopyWithin = _dereq_（39）;
    var $ DP = _dereq_（99）;
    var $ GOPD = _dereq_（101）;
    var dP = $ DP.f;
    var gOPD = $ GOPD.f;
    var RangeError = global.RangeError;
    var TypeError = global.TypeError;
    var Uint8Array = global.Uint8Array;
    var ARRAY_BUFFER ='ArrayBuffer';
    var SHARED_BUFFER ='共享'+ ARRAY_BUFFER;
    var BYTES_PER_ELEMENT ='BYTES_PER_ELEMENT';
    var PROTOTYPE ='prototype';
    var ArrayProto = Array [PROTOTYPE];
    var $ ArrayBuffer = $ buffer.ArrayBuffer;
    var $ DataView = $ buffer.DataView;
    var arrayForEach = createArrayMethod（0）;
    var arrayFilter = createArrayMethod（2）;
    var arraySome = createArrayMethod（3）;
    var arrayEvery = createArrayMethod（4）;
    var arrayFind = createArrayMethod（5）;
    var arrayFindIndex = createArrayMethod（6）;
    var arrayIncludes = createArrayIncludes（true）;
    var arrayIndexOf = createArrayIncludes（false）;
    var arrayValues = ArrayIterators.values;
    var arrayKeys = ArrayIterators.keys;
    var arrayEntries = ArrayIterators.entries;
    var arrayLastIndexOf = ArrayProto.lastIndexOf;
    var arrayReduce = ArrayProto.reduce;
    var arrayReduceRight = ArrayProto.reduceRight;
    var arrayJoin = ArrayProto.join;
    var arraySort = ArrayProto.sort;
    var arraySlice = ArrayProto.slice;
    var arrayToString = ArrayProto.toString;
    var arrayToLocaleString = ArrayProto.toLocaleString;
    var ITERATOR = wks（'iterator'）;
    var TAG = wks（'toStringTag'）;
    var TYPED_CONSTRUCTOR = uid（'typed_constructor'）;
    var DEF_CONSTRUCTOR = uid（'def_constructor'）;
    var ALL_CONSTRUCTORS = $ typed.CONSTR;
    var TYPED_ARRAY = $ typed.TYPED;
    var VIEW = $ typed.VIEW;
    var WRONG_LENGTH ='长度错误！';
  
    var $ map = createArrayMethod（1，function（O，length）{
      返回allocate（speciesConstructor（O，O [DEF_CONSTRUCTOR]），长度）;
    }）;
  
    var LITTLE_ENDIAN = failed（function（）{
      // eslint-disable-next-line no-undef
      返回新的Uint8Array（新的Uint16Array（[1]）。buffer）[0] === 1;
    }）;
  
    var FORCED_SET = !! Uint8Array && !! Uint8Array [PROTOTYPE] .set && failed（function（）{
      新的Uint8Array（1）.set（{}）;
    }）;
  
    var toOffset = function（it，BYTES）{
      var offset = toInteger（it）;
      如果（offset <0 || offset％BYTES）抛出RangeError（'Wrong offset！'）;
      返回偏移量；
    };
  
    var validate = function（it）{
      如果（isObject（it）&& TYPED_ARRAY）返回它；
      抛出TypeError（it +'不是类型数组！'）;
    };
  
    varalloc =函数（C，长度）{
      if（！（isObject（C）&& TYPED_CONSTRUCTOR in C））{
        抛出TypeError（'这不是类型化数组构造函数！'）
      }返回新的C（length）;
    };
  
    var speciesFromList =函数（O，列表）{
      从列表返回（speciesConstructor（O，O [DEF_CONSTRUCTOR]），list）;
    };
  
    var fromList = function（C，list）{
      var index = 0;
      var length = list.length;
      var结果= allocate（C，长度）;
      而（长度>索引）结果[索引] =列表[索引++];
      返回结果；
    };
  
    var addGetter =函数（它，键，内部）{
      dP（it，key，{get：function（）{return this._d [internal];}}）;
    };
  
    var $ from = function from（source / *，mapfn，thisArg * /）{
      var O = toObject（source）;
      var aLen = arguments.length;
      var mapfn = aLen> 1吗？arguments [1]：未定义；
      var mapping = mapfn！==未定义；
      var iterFn = getIterFn（O）;
      var i，长度，值，结果，步骤，迭代器；
      如果（iterFn！=未定义&&！isArrayIter（iterFn））{
        for（迭代器= iterFn.call（O），值= []，i = 0；！（step = iterator.next（））。done; i ++）{
          values.push（step.value）;
        } O =值；
      }
      if（映射&& aLen> 2）mapfn = ctx（mapfn，arguments [2]，2）;
      对于（i = 0，长度= toLength（O.length），结果= allocate（this，长度）;长度> i; i ++）{
        result [i] =映射？mapfn（O [i]，i）：O [i];
      }
      返回结果；
    };
  
    var $ of =（/ * ...项目* /）的功能{
      var index = 0;
      var length = arguments.length;
      var result = allocate（this，length）;
      while（长度>索引）result [index] = arguments [index ++];
      返回结果；
    };
  
    // iOS Safari 6.x在这里失败
    var TO_LOCALE_BUG = !! Uint8Array && failed（function（）{arrayToLocaleString.call（new Uint8Array（1））;}）;
  
    var $ toLocaleString = function toLocaleString（）{
      返回arrayToLocaleString.apply（TO_LOCALE_BUG？arraySlice.call（validate（this））：validate（this），arguments）;
    };
  
    var proto = {
      copyWithin：函数copyWithin（target，start / *，end * /）{
        return arrayCopyWithin.call（validate（this），target，start，arguments.length> 2？arguments [2]：undefined）;返回
      }，
      every：function every（callbackfn / *，thisArg * /）{
        返回arrayEvery（validate（this），callbackfn，arguments.length> 1？arguments [1]：undefined）;
      }，
      fill：function fill（value / *，start，end * /）{// eslint-disable-line no-unused-vars
        返回arrayFill.apply（validate（this），arguments）;
      }，
      filter：函数filter（callbackfn / *，thisArg * /）{
        返回speciesFromList（this，arrayFilter（validate（this），callbackfn，
          arguments.length> 1吗？arguments [1]：未定义））；
      }，
      find：函数find（predicate / *，thisArg * /）{
        返回arrayFind（validate（this），谓词，arguments.length> 1？arguments [1]：未定义）;
      }，
      findIndex：函数findIndex（predicate / *，thisArg * /）{
        返回arrayFindIndex（validate（this），谓词，arguments.length> 1？arguments [1]：未定义）;
      }，
      forEach：函数forEach（callbackfn / *，thisArg * /）{
        arrayForEach（validate（this），callbackfn，arguments.length> 1？arguments [1]：undefined）;
      }，
      indexOf：函数indexOf（searchElement / *，fromIndex * /）{
        return arrayIndexOf（validate（this），searchElement，arguments.length> 1？arguments [1]：undefined）;
      }，
      include：函数include（searchElement / *，fromIndex * /）{
        返回arrayIncludes（validate（this），searchElement，arguments.length> 1？arguments [1]：undefined）;
      }，
      join：function join（separator）{// eslint-disable-line no-unused-vars
        返回arrayJoin.apply（validate（this），arguments）;
      }，
      lastIndexOf：function lastIndexOf（searchElement / *，fromIndex * /）{//禁用禁用行无变量
        返回arrayLastIndexOf.apply（validate（this），arguments）;
      }，
      map：function map（mapfn / *，thisArg * /）{
        return $ map（validate（this），mapfn，arguments.length> 1？arguments [1]：undefined）;
      }，
      reduce：function reduce（callbackfn / *，initialValue * /）{// eslint-disable-line no-unused-vars
        返回arrayReduce.apply（validate（this），arguments）;
      }，
      reduceRight：函数reduceRight（callbackfn / *，initialValue * /）{//禁止行不使用变量
        返回arrayReduceRight.apply（validate（this），arguments）;
      }，
      反向：函数reverse（）{
        var that = this;
        var length = validate（that）.length;
        var middle = Math.floor（length / 2）;
        var index = 0;
        var值;
        而（索引<中间）{
          值= that [index];
          that [index ++] = that [-length];
          that [length] = value;
        }返回；
      }，
      some：function some（callbackfn / *，thisArg * /）{
        返回arraySome（validate（this），callbackfn，arguments.length> 1？arguments [1]：undefined）;
      }，
      排序：函数sort（comparefn）{
        返回arraySort.call（validate（this），comparefn）;
      }，
      subarray：function subarray（begin，end）{
        var O = validate（this）;
        var length = O.length;
        var $ begin = toAbsoluteIndex（begin，length）;
        返回新的（speciesConstructor（O，O [DEF_CONSTRUCTOR]））（
          O缓冲
          O.byteOffset + $ begin * O.BYTES_PER_ELEMENT，
          toLength（（end === undefined？length：toAbsoluteIndex（end，length））-$ begin）
        ）;
      }
    };
  
    var $ slice = function slice（start，end）{
      返回speciesFromList（this，arraySlice.call（validate（this），start，end））;
    };
  
    var $ set = function set（arrayLike / *，offset * /）{
      验证（这个）;
      var offset = toOffset（arguments [1]，1）;
      var length = this.length;
      var src = toObject（arrayLike）;
      var len = toLength（src.length）;
      var index = 0;
      如果（len + offset> length）抛出RangeError（WRONG_LENGTH）;
      而（index <len）this [offset + index] = src [index ++];
    };
  
    var $ iterators = {
      条目：函数entry（）{
        返回arrayEntries.call（validate（this））;
      }，
      键：功能keys（）{
        返回arrayKeys.call（validate（this））;
      }，
      值：函数values（）{
        返回arrayValues.call（validate（this））;
      }
    };
  
    var isTAIndex =函数（目标，键）{
      返回isObject（target）
        &&目标[TYPED_ARRAY]
        && typeof键！='符号'
        &&键入目标
        && String（+键）== String（键）;
    };
    var $ getDesc = function getOwnPropertyDescriptor（target，key）{
      返回isTAIndex（target，key = toPrimitive（key，true））
        ？propertyDesc（2，target [key]）
        ：gOPD（target，key）;
    };
    var $ setDesc = function defineProperty（target，key，desc）{
      如果（isTAIndex（target，key = toPrimitive（key，true））
        && isObject（desc）
        && has（desc，'value'）
        &&！has（desc，'get'）
        &&！has（desc，'set'）
        // TODO：添加验证描述符，而无需调用访问器
        &&！desc.configurable
        &&（！has（desc，'writable'）|| desc.writable）
        &&（！has（desc，'enumerable'）|| desc.enumerable）
      ）{
        target [key] = desc.value;
        返回目标；
      } return dP（target，key，desc）;
    };
  
    如果（！ALL_CONSTRUCTORS）{
      $ GOPD.f = $ getDesc;
      $ DP.f = $ setDesc;
    }
  
    $ export（$ export.S + $ export.F *！ALL_CONSTRUCTORS，'对象'，{
      getOwnPropertyDescriptor：$ getDesc，
      defineProperty：$ setDesc
    }）;
  
    如果（失败（功能（）{arrayToString.call（{}）;}））{
      arrayToString = arrayToLocaleString =函数toString（）{
        返回arrayJoin.call（this）;
      };
    }
  
    var $ TypedArrayPrototype $ = redefineAll（{}，proto）;
    redefineAll（$ TypedArrayPrototype $，$ iterators）;
    hide（$ TypedArrayPrototype $，ITERATOR，$ iterators.values）;
    redefineAll（$ TypedArrayPrototype $，{
      片：$ slice，
      设置：$ set，
      构造函数：function（）{/ * noop * /}，
      toString：arrayToString，
      toLocaleString：$ toLocaleString
    }）;
    addGetter（$ TypedArrayPrototype $，'buffer'，'b'）;
    addGetter（$ TypedArrayPrototype $，'byteOffset'，'o'）;
    addGetter（$ TypedArrayPrototype $，'byteLength'，'l'）;
    addGetter（$ TypedArrayPrototype $，'length'，'e'）;
    dP（$ TypedArrayPrototype $，TAG，{
      get：function（）{return this [TYPED_ARRAY]; }
    }）;
  
    // eslint-disable-next-line max-statements
    module.exports =函数（KEY，BYTES，包装器，CLAMPED）{
      CLAMPED = !! CLAMPED;
      var NAME = KEY +（CLAMPED？'Clamped'：''）+'Array';
      var GETTER ='get'+ KEY;
      var SETTER ='set'+ KEY;
      var TypedArray = global [NAME];
      var Base = TypedArray || {};
      var TAC = TypedArray && getPrototypeOf（TypedArray）;
      var FORCED =！TypedArray || ！$ typed.ABV;
      var O = {};
      var TypedArrayPrototype = TypedArray && TypedArray [PROTOTYPE];
      var getter = function（that，index）{
        var data = that._d;
        v [GETTER]（索引*字节+ data.o，LITTLE_ENDIAN）；返回数据。
      };
      var setter =函数（即索引，值）{
        var data = that._d;
        如果（CLAMPED）值=（值= Math.round（值））<0？0：值> 0xff？0xff：值＆0xff;
        data.v [SETTER]（索引*字节+ data.o，值，LITTLE_ENDIAN）；
      };
      var addElement = function（that，index）{
        dP（that，index，{
          get：function（）{
            返回getter（this，index）;
          }，
          设置：函数（值）{
            返回设置器（this，index，value）；
          }，
          枚举：true
        }）;
      };
      如果（强制）{
        TypedArray = wrapper（function（that，data，$ offset，$ length）{
          anInstance（即TypedArray，NAME，'_ d'）；
          var index = 0;
          var offset = 0;
          var缓冲区，byteLength，长度，klass；
          如果（！isObject（data））{
            长度= toIndex（数据）;
            byteLength =长度*字节;
            缓冲区=新的$ ArrayBuffer（byteLength）;
          }否则，如果（data instanceof $ ArrayBuffer ||（klass = classof（data））== ARRAY_BUFFER || klass == SHARED_BUFFER）{
            缓冲区=数据;
            offset = toOffset（$ offset，BYTES）;
            var $ len = data.byteLength;
            如果（$ length === undefined）{
              如果（$ len％BYTES）抛出RangeError（WRONG_LENGTH）;
              byteLength = $ len-偏移量;
              如果（byteLength <0）抛出RangeError（WRONG_LENGTH）;
            }其他{
              byteLength = toLength（$ length）*字节;
              如果（byteLength + offset> $ len）抛出RangeError（WRONG_LENGTH）;
            }
            长度= byteLength / BYTES;
          }否则，如果（数据中为TYPED_ARRAY）{
            返回fromList（TypedArray，data）;
          }其他{
            返回$ from.call（TypedArray，data）;
          }
          hide（that，'_d'，{
            b：缓冲区
            o：偏移量
            l：byteLength，
            e：长度，
            v：新的$ DataView（buffer）
          }）;
          while（index <length）addElement（that，index ++）;
        }）;
        TypedArrayPrototype = TypedArray [PROTOTYPE] = create（$ TypedArrayPrototype $）;
        hide（TypedArrayPrototype，'constructor'，TypedArray）;
      } else if（！fails（function（）{
        TypedArray（1）;
      }）|| ！失败（功能（）{
        新的TypedArray（-1）; // eslint-disable-line no new
      }）|| ！$ iterDetect（function（iter）{
        新的TypedArray（）; // eslint-disable-line no new
        新的TypedArray（null）; // eslint-disable-line no new
        新的TypedArray（1.5）; // eslint-disable-line no new
        新的TypedArray（iter）; // eslint-disable-line no new
      }，true））{
        TypedArray = wrapper（function（that，data，$ offset，$ length）{
          anInstance（即TypedArray，NAME）；
          var klass;
          //`ws`模块错误，暂时删除Uint8Array的验证长度
          // https://github.com/websockets/ws/pull/645
          如果（！isObject（data））返回新的Base（toIndex（data））;
          if（data instanceof $ ArrayBuffer ||（klass = classof（data））== ARRAY_BUFFER || klass == SHARED_BUFFER）{
            返回$ length！==未定义
              ？新Base（数据，toOffset（$ offset，BYTES），$ length）
              ：$ offset！==未定义
                ？新Base（数据，toOffset（$ offset，BYTES））
                ：新的Base（数据）;
          }
          如果（数据中的TYPED_ARRAY）从列表（类型化数组，数据）返回;
          返回$ from.call（TypedArray，data）;
        }）;
        arrayForEach（TAC！== Function.prototype？gOPN（Base）.concat（gOPN（TAC））：gOPN（Base），函数（key）{
          if（！（TypedArray中的键））hide（TypedArray，key，Base [key]）;
        }）;
        TypedArray [PROTOTYPE] = TypedArrayPrototype;
        如果（！LIBRARY）TypedArrayPrototype.constructor = TypedArray;
      }
      var $ nativeIterator = TypedArrayPrototype [ITERATOR];
      var CORRECT_ITER_NAME = !! $ nativeIterator
        &&（$ nativeIterator.name =='值'|| $ nativeIterator.name ==未定义）;
      var $ iterator = $ iterators.values;
      hide（TypedArray，TYPED_CONSTRUCTOR，true）;
      hide（TypedArrayPrototype，TYPED_ARRAY，NAME）;
      hide（TypedArrayPrototype，VIEW，true）;
      hide（TypedArrayPrototype，DEF_CONSTRUCTOR，TypedArray）;
  
      if（CLAMPED？new TypedArray（1）[TAG]！=名称：！（TypedArrayPrototype中的TAG））{
        dP（TypedArrayPrototype，TAG，{
          get：function（）{return NAME; }
        }）;
      }
  
      O [NAME] = TypedArray;
  
      $ export（$ export.G + $ export.W + $ export.F *（TypedArray！= Base），O）;
  
      $ export（$ export.S，NAME，{
        BYTES_PER_ELEMENT：BYTES
      }）;
  
      $ export（$ export.S + $ export.F *失败（函数（）{Base.of.call（TypedArray，1）;}），NAME，{
        来自：$ from，
        of：$ of
      }）;
  
      if（！（TypedArrayPrototype中的BYTES_PER_ELEMENT））hide（TypedArrayPrototype，BYTES_PER_ELEMENT，BYTES）;
  
      $ export（$ export.P，NAME，proto）;
  
      setSpecies（NAME）;
  
      $ export（$ export.P + $ export.F * FORCED_SET，NAME，{set：$ set}）;
  
      $ export（$ export.P + $ export.F *！CORRECT_ITER_NAME，NAME，$ iterators）；
  
      if（！LIBRARY && TypedArrayPrototype.toString！= arrayToString）TypedArrayPrototype.toString = arrayToString;
  
      $ export（$ export.P + $ export.F *失败（函数（）{
        新的TypedArray（1）.slice（）;
      }），NAME，{slice：$ slice}）；
  
      $ export（$ export.P + $ export.F *（失败（函数（）{
        return [1，2] .toLocaleString（）！= new TypedArray（[1，2]）。toLocaleString（）;
      }）|| ！失败（功能（）{
        TypedArrayPrototype.toLocaleString.call（[1，2]）;
      }）），名称，{toLocaleString：$ toLocaleString}）;
  
      迭代器[NAME] = CORRECT_ITER_NAME？$ nativeIterator：$ iterator;
      如果（！LIBRARY &&！CORRECT_ITER_NAME）隐藏（TypedArrayPrototype，ITERATOR，$ iterator）;
    };
  } else module.exports = function（）{/ *空* /};
  
  }，{“ 101”：101，“ 103”：103，“ 105”：105，“ 116”：116，“ 117”：117，“ 123”：123，“ 127”：127，“ 137”：137 ，“ 138”：138，“ 139”：139，“ 141”：141，“ 142”：142，“ 143”：143，“ 145”：145，“ 146”：146，“ 147”：147，“ 152“：152，” 153“：153，” 164“：164，” 37“：37，” 39“：39，” 40“：40，” 41“：41，” 42“：42，” 47“ ：47，“ 54”：54，“ 58”：58，“ 62”：62，“ 64”：64，“ 70”：70，“ 71”：71，“ 72”：72，“ 78”：78 ，“ 81”：81，“ 86”：86，“ 88”：88，“ 89”：89，“ 98”：98，“ 99”：99}]，145：[function（_dereq_，module，exports） {
  “使用严格”；
  var global = _dereq_（70）;
  var DESCRIPTORS = _dereq_（58）;
  var LIBRARY = _dereq_（89）;
  var $ typed = _dereq_（146）;
  var hide = _dereq_（72）;
  var redefineAll = _dereq_（117）;
  var failed = _dereq_（64）;
  var anInstance = _dereq_（37）;
  var toInteger = _dereq_（139）;
  var toLength = _dereq_（141）;
  var toIndex = _dereq_（138）;
  var gOPN = _dereq_（103）.f;
  var dP = _dereq_（99）.f;
  var arrayFill = _dereq_（40）;
  var setToStringTag = _dereq_（124）;
  var ARRAY_BUFFER ='ArrayBuffer';
  var DATA_VIEW ='DataView';
  var PROTOTYPE ='prototype';
  var WRONG_LENGTH ='长度错误！';
  var WRONG_INDEX ='索引错误！';
  var $ ArrayBuffer = global [ARRAY_BUFFER];
  var $ DataView = global [DATA_VIEW];
  var Math = global.Math;
  var RangeError = global.RangeError;
  // eslint-disable-next-line no-shadow-restricted-names
  var Infinity = global.Infinity;
  var BaseBuffer = $ ArrayBuffer;
  var abs = Math.abs;
  var pow = Math.pow;
  var floor = Math.floor;
  var log = Math.log;
  var LN2 = Math.LN2;
  var BUFFER ='缓冲区';
  var BYTE_LENGTH ='byteLength';
  var BYTE_OFFSET ='byteOffset';
  var $ BUFFER = DESCRIPTORS吗？'_b'：缓冲区;
  var $ LENGTH = DESCRIPTORS吗？'_l'：BYTE_LENGTH;
  var $ OFFSET = DESCRIPTORS吗？'_o'：BYTE_OFFSET;
  
  //基于https://github.com/feross/ieee754的IEEE754转换
  功能包IEEE754（value，mLen，nBytes）{
    var buffer = new Array（nBytes）;
    var eLen = nBytes * 8-mLen-1;
    var eMax =（1 << eLen）-1;
    var eBias = eMax >> 1;
    var rt = mLen === 23吗？pow（2，-24）-pow（2，-77）：0;
    var i = 0;
    var s =值<0 || 值=== 0 && 1 /值<0？1：0；
    var e，m，c;
    值= abs（值）;
    // eslint-disable-next-line no-self-compare
    if（值！=值||值===无穷大）{
      // eslint-disable-next-line no-self-compare
      m =值！=值？1：0；
      e = eMax；
    }其他{
      e =下限（log（value）/ LN2）;
      如果（值*（c = pow（2，-e））<1）{
        e--;
        c * = 2;
      }
      如果（e + eBias> = 1）{
        值+ = rt / c;
      }其他{
        值+ = rt * pow（2，1-eBias）;
      }
      如果（值* c> = 2）{
        e ++;
        c / = 2;
      }
      如果（e + eBias> = eMax）{
        m = 0；
        e = eMax；
      } else if（e + eBias> = 1）{
        m =（值* c-1）* pow（2，mLen）;
        e = e + eBias；
      }其他{
        m =值* pow（2，eBias-1）* pow（2，mLen）;
        e = 0;
      }
    }
    for（; mLen> = 8; buffer [i ++] = m＆255，m / = 256，mLen-= 8）;
    e = e << mLen | m;
    eLen + = mLen;
    for（; eLen> 0; buffer [i ++] = e＆255，e / = 256，eLen-= 8）;
    缓冲区[--i] | = s * 128;
    返回缓冲区；
  }
  函数unpackIEEE754（buffer，mLen，nBytes）{
    var eLen = nBytes * 8-mLen-1;
    var eMax =（1 << eLen）-1;
    var eBias = eMax >> 1;
    var nBits = eLen-7;
    var i = nBytes-1;
    var s = buffer [i--];
    var e = s＆127;
    var m;
    s >> = 7;
    for（; nBits> 0; e = e * 256 + buffer [i]，i--，nBits-= 8）;
    m = e＆（1 << -nBits）-1;
    e >> = -nBits;
    nBits + = mLen;
    for（; nBits> 0; m = m * 256 + buffer [i]，i--，nBits-= 8）;
    如果（e === 0）{
      e = 1-eBias；
    }否则，如果（e === eMax）{
      返回m？NaN：是吗？-Infinity：无限
    }其他{
      m = m +战俘（2，毫升）;
      e = e-eBias；
    } return（s？-1：1）* m * pow（2，e-mLen）;
  }
  
  函数unpackI32（bytes）{
    返回字节[3] << 24 | 字节[2] << 16 | 字节[1] << 8 | 字节[0];
  }
  函数packI8（it）{
    返回[it＆0xff];
  }
  功能packI16（it）{
    返回[it＆0xff，它>> 8＆0xff]；
  }
  函数packI32（it）{
    返回[it＆0xff，它>> 8＆0xff，它>> 16＆0xff，它>> 24＆0xff]；
  }
  功能packF64（it）{
    return packIEEE754（it，52，8）;
  }
  功能packF32（it）{
    return packIEEE754（it，23，4）;
  }
  
  函数addGetter（C，key，internal）{
    dP（C [PROTOTYPE]，键，{get：function（）{return this [internal];}}）;
  }
  
  函数get（view，bytes，index，isLittleEndian）{
    var numIndex = + index;
    var intIndex = toIndex（numIndex）;
    如果（intIndex + bytes> view [$ LENGTH]）抛出RangeError（WRONG_INDEX）;
    var store = view [$ BUFFER] ._ b;
    var start = intIndex + view [$ OFFSET];
    var pack = store.slice（start，start + bytes）;
    返回isLittleEndian吗？pack：pack.reverse（）;
  }
  函数集（视图，字节，索引，转换，值，isLittleEndian）{
    var numIndex = + index;
    var intIndex = toIndex（numIndex）;
    如果（intIndex + bytes> view [$ LENGTH]）抛出RangeError（WRONG_INDEX）;
    var store = view [$ BUFFER] ._ b;
    var start = intIndex + view [$ OFFSET];
    var pack = conversion（+ value）;
    for（var i = 0; i <bytes; i ++）store [start + i] = pack [isLittleEndian吗？i：字节-i-1]；
  }
  
  如果（！$ typed.ABV）{
    $ ArrayBuffer =函数ArrayBuffer（length）{
      anInstance（this，$ ArrayBuffer，ARRAY_BUFFER）;
      var byteLength = toIndex（length）;
      this._b = arrayFill.call（new Array（byteLength），0）;
      this [$ LENGTH] = byteLength;
    };
  
    $ DataView =函数DataView（buffer，byteOffset，byteLength）{
      anInstance（this，$ DataView，DATA_VIEW）;
      anInstance（缓冲区，$ ArrayBuffer，DATA_VIEW）;
      var bufferLength = buffer [$ LENGTH];
      var offset = toInteger（byteOffset）;
      如果（offset <0 || offset> bufferLength）抛出RangeError（'Wrong offset！'）;
      byteLength = byteLength ===未定义？bufferLength-offset：toLength（byteLength）;
      如果（offset + byteLength> bufferLength）抛出RangeError（WRONG_LENGTH）;
      this [$ BUFFER] =缓冲区；
      this [$ OFFSET] =偏移量；
      this [$ LENGTH] = byteLength;
    };
  
    如果（DESCRIPTORS）{
      addGetter（$ ArrayBuffer，BYTE_LENGTH，'_l'）;
      addGetter（$ DataView，BUFFER，'_b'）;
      addGetter（$ DataView，BYTE_LENGTH，'_l'）;
      addGetter（$ DataView，BYTE_OFFSET，'_o'）;
    }
  
    redefineAll（$ DataView [PROTOTYPE]，{
      getInt8：函数getInt8（byteOffset）{
        返回get（this，1，byteOffset）[0] << 24 >> 24;
      }，
      getUint8：函数getUint8（byteOffset）{
        返回get（this，1，byteOffset）[0];
      }，
      getInt16：函数getInt16（byteOffset / *，littleEndian * /）{
        var bytes = get（this，2，byteOffset，arguments [1]）;
        返回（bytes [1] << 8 | bytes [0]）<< 16 >> 16;
      }，
      getUint16：函数getUint16（byteOffset / *，littleEndian * /）{
        var bytes = get（this，2，byteOffset，arguments [1]）;
        返回字节[1] << 8 | 字节[0];
      }，
      getInt32：函数getInt32（byteOffset / *，littleEndian * /）{
        返回unpackI32（get（this，4，byteOffset，arguments [1]））;
      }，
      getUint32：函数getUint32（byteOffset / *，littleEndian * /）{
        返回unpackI32（get（this，4，byteOffset，arguments [1]））>>> 0;
      }，
      getFloat32：函数getFloat32（byteOffset / *，littleEndian * /）{
        返回unpackIEEE754（get（this，4，byteOffset，arguments [1]），23，4）;
      }，
      getFloat64：函数getFloat64（byteOffset / *，littleEndian * /）{
        返回unpackIEEE754（get（this，8，byteOffset，arguments [1]），52，8）;
      }，
      setInt8：函数setInt8（byteOffset，value）{
        set（this，1，byteOffset，packI8，value）;
      }，
      setUint8：函数setUint8（byteOffset，value）{
        set（this，1，byteOffset，packI8，value）;
      }，
      setInt16：函数setInt16（byteOffset，值/ *，littleEndian * /）{
        set（this，2，byteOffset，packI16，value，arguments [2]）;
      }，
      setUint16：函数setUint16（byteOffset，值/ *，littleEndian * /）{
        set（this，2，byteOffset，packI16，value，arguments [2]）;
      }，
      setInt32：函数setInt32（byteOffset，值/ *，littleEndian * /）{
        set（this，4，byteOffset，packI32，value，arguments [2]）;
      }，
      setUint32：函数setUint32（byteOffset，值/ *，littleEndian * /）{
        set（this，4，byteOffset，packI32，value，arguments [2]）;
      }，
      setFloat32：函数setFloat32（byteOffset，值/ *，littleEndian * /）{
        set（this，4，byteOffset，packF32，value，arguments [2]）;
      }，
      setFloat64：函数setFloat64（byteOffset，值/ *，littleEndian * /）{
        set（this，8，byteOffset，packF64，value，arguments [2]）;
      }
    }）;
  }其他{
    如果（！失败（功能（）{
      $ ArrayBuffer（1）;
    }）|| ！失败（功能（）{
      新的$ ArrayBuffer（-1）; // eslint-disable-line no new
    }）|| 失败（功能（）{
      新的$ ArrayBuffer（）; // eslint-disable-line no new
      新的$ ArrayBuffer（1.5）; // eslint-disable-line no new
      新的$ ArrayBuffer（NaN）; // eslint-disable-line no new
      return $ ArrayBuffer.name！= ARRAY_BUFFER;
    }））{
      $ ArrayBuffer =函数ArrayBuffer（length）{
        anInstance（this，$ ArrayBuffer）;
        返回新的BaseBuffer（toIndex（length））;
      };
      var ArrayBufferProto = $ ArrayBuffer [PROTOTYPE] = BaseBuffer [PROTOTYPE];
      for（var keys = gOPN（BaseBuffer），j = 0，key; keys.length> j;）{
        if（！（（$ ArrayBuffer中的（key = keys [j ++]]）））hide（$ ArrayBuffer，key，BaseBuffer [key]）;
      }
      如果（！LIBRARY）ArrayBufferProto.constructor = $ ArrayBuffer;
    }
    // iOS Safari 7.x错误
    var view = new $ DataView（new $ ArrayBuffer（2））;
    var $ setInt8 = $ DataView [PROTOTYPE] .setInt8;
    view.setInt8（0，2147483648）;
    view.setInt8（1，2147483649）;
    如果（view.getInt8（0）||！view.getInt8（1））redefineAll（$ DataView [PROTOTYPE]，{
      setInt8：函数setInt8（byteOffset，value）{
        $ setInt8.call（this，byteOffset，值<< 24 >> 24）;
      }，
      setUint8：函数setUint8（byteOffset，value）{
        $ setInt8.call（this，byteOffset，值<< 24 >> 24）;
      }
    }，是对的）；
  }
  setToStringTag（$ ArrayBuffer，ARRAY_BUFFER）;
  setToStringTag（$ DataView，DATA_VIEW）;
  hide（$ DataView [PROTOTYPE]，$ typed.VIEW，true）;
  exports [ARRAY_BUFFER] = $ ArrayBuffer;
  exports [DATA_VIEW] = $ DataView;
  
  }，{“ 103”：103，“ 117”：117，“ 124”：124，“ 138”：138，“ 139”：139，“ 141”：141，“ 146”：146，“ 37”：37 ，“ 40”：40，“ 58”：58，“ 64”：64，“ 70”：70，“ 72”：72，“ 89”：89，“ 99”：99}]，146：[function（ _dereq_，模块，出口）{
  var global = _dereq_（70）;
  var hide = _dereq_（72）;
  var uid = _dereq_（147）;
  var TYPED = uid（'typed_array'）;
  var VIEW = uid（'view'）;
  var ABV = !!（global.ArrayBuffer && global.DataView）;
  var CONSTR = ABV;
  var i = 0;
  var l = 9;
  var Typed;
  
  var TypedArrayConstructors =（
    'Int8Array，Uint8Array，Uint8ClampedArray，Int16Array，Uint16Array，Int32Array，Uint32Array，Float32Array，Float64Array'
  ）。分裂（'，'）;
  
  而（i <l）{
    如果（Typed = global [TypedArrayConstructors [i ++]]）{
      hide（Typed.prototype，TYPED，true）;
      hide（Typed.prototype，VIEW，true）;
    } else CONSTR = false;
  }
  
  module.exports = {
    ABV：ABV，
    构造：构造，
    TYPED：TYPED，
    查看：查看
  };
  
  }，{“ 147”：147，“ 70”：70，“ 72”：72}]，147：[function（_dereq_，module，exports）{
  var id = 0;
  var px = Math.random（）;
  module.exports =函数（键）{
    返回'Symbol（'。concat（key === undefined？''：key，'）_'，（++ id + px）.toString（36））;
  };
  
  }，{}]，148：[function（_dereq_，module，exports）{
  var global = _dereq_（70）;
  var navigator = global.navigator;
  
  module.exports =导航器&& navigator.userAgent || '';
  
  }，{“ 70”：70}]，149：[function（_dereq_，module，exports）{
  var isObject = _dereq_（81）;
  module.exports = function（it，TYPE）{
    如果（！isObject（it）|| it._t！== TYPE）抛出TypeError（'不兼容的接收器，'+ TYPE +'必需！'）;
    把它返还;
  };
  
  }，{“ 81”：81}]，150：[function（_dereq_，module，exports）{
  var global = _dereq_（70）;
  var core = _dereq_（52）;
  var LIBRARY = _dereq_（89）;
  var wksExt = _dereq_（151）;
  var defineProperty = _dereq_（99）.f;
  module.exports =函数（名称）{
    var $ Symbol = core.Symbol || （core.Symbol = LIBRARY？{}：global.Symbol || {}）；
    if（name.charAt（0）！='_'&&！（$ Symbol中的名称））defineProperty（$ Symbol，name，{value：wksExt.f（name）}）;
  };
  
  }，{“ 151”：151，“ 52”：52，“ 70”：70，“ 89”：89，“ 99”：99}]，151：[function（_dereq_，module，exports）{
  Exports.f = _dereq_（152）;
  
  }，{“ 152”：152}]，152：[function（_dereq_，module，exports）{
  var store = _dereq_（126）（'wks'）;
  var uid = _dereq_（147）;
  var Symbol = _dereq_（70）.Symbol;
  var USE_SYMBOL = typeof Symbol =='功能';
  
  var $ exports = module.exports =函数（名称）{
    返回商店[名称] || （商店[名称] =
      USE_SYMBOL &&符号[名称] || （USE_SYMBOL？Symbol：uid）（'Symbol。'+名称））;
  };
  
  $ exports.store =商店；
  
  }，{“ 126”：126，“ 147”：147，“ 70”：70}]，153：[function（_dereq_，module，exports）{
  var classof = _dereq_（47）;
  var ITERATOR = _dereq_（152）（'iterator'）;
  var Iterators = _dereq_（88）;
  module.exports = _dereq_（52）.getIteratorMethod =函数（it）{
    如果（它！=未定义）返回它[ITERATOR]
      || 它['@@ iterator']
      || 迭代器[classof（it）];
  };
  
  }，{“ 152”：152，“ 47”：47，“ 52”：52，“ 88”：88}]，154：[function（_dereq_，module，exports）{
  // 22.1.3.3 Array.prototype.copyWithin（target，start，end = this.length）
  var $ export = _dereq_（62）;
  
  $ export（$ export.P，'Array'，{copyWithin：_dereq_（39）}）;
  
  _dereq_（35）（'copyWithin'）;
  
  }，{“ 35”：35，“ 39”：39，“ 62”：62}]，155：[function（_dereq_，module，exports）{
  “使用严格”；
  var $ export = _dereq_（62）;
  var $ every = _dereq_（42）（4）;
  
  $ export（$ export.P + $ export.F *！_dereq_（128）（[]。every，true），'Array'，{
    // 22.1.3.5 / 15.4.4.16 Array.prototype.every（callbackfn [，thisArg]）
    every：function every（callbackfn / *，thisArg * /）{
      返回$ every（this，callbackfn，arguments [1]）;
    }
  }）;
  
  }，{“ 128”：128，“ 42”：42，“ 62”：62}]，156：[function（_dereq_，module，exports）{
  // 22.1.3.6 Array.prototype.fill（值，开始= 0，结束= this.length）
  var $ export = _dereq_（62）;
  
  $ export（$ export.P，'Array'，{fill：_dereq_（40）}）;
  
  _dereq_（35）（'填充'）;
  
  }，{“ 35”：35，“ 40”：40，“ 62”：62}]，157：[function（_dereq_，module，exports）{
  “使用严格”；
  var $ export = _dereq_（62）;
  var $ filter = _dereq_（42）（2）;
  
  $ export（$ export.P + $ export.F *！_dereq_（128）（[]。filter，true），'Array'，{
    // 22.1.3.7 / 15.4.4.20 Array.prototype.filter（callbackfn [，thisArg]）
    filter：函数filter（callbackfn / *，thisArg * /）{
      返回$ filter（this，callbackfn，arguments [1]）;
    }
  }）;
  
  }，{“ 128”：128，“ 42”：42，“ 62”：62}]，158：[function（_dereq_，module，exports）{
  “使用严格”；
  // 22.1.3.9 Array.prototype.findIndex（谓词，thisArg = undefined）
  var $ export = _dereq_（62）;
  var $ find = _dereq_（42）（6）;
  var KEY ='findIndex';
  var force = true;
  //不应该跳过漏洞
  如果（[]中的键）Array（1）[KEY]（function（）{force = false;}）;
  $ export（$ export.P + $ export.F *强制，'Array'，{
    findIndex：函数findIndex（callbackfn / *，that = undefined * /）{
      返回$ find（this，callbackfn，arguments.length> 1？arguments [1]：undefined）;
    }
  }）;
  _dereq_（35）（KEY）;
  
  }，{“ 35”：35，“ 42”：42，“ 62”：62}]，159：[function（_dereq_，module，exports）{
  “使用严格”；
  // 22.1.3.8 Array.prototype.find（谓词，thisArg = undefined）
  var $ export = _dereq_（62）;
  var $ find = _dereq_（42）（5）;
  var KEY ='find';
  var force = true;
  //不应该跳过漏洞
  如果（[]中的键）Array（1）[KEY]（function（）{force = false;}）;
  $ export（$ export.P + $ export.F *强制，'Array'，{
    find：函数find（callbackfn / *，that = undefined * /）{
      返回$ find（this，callbackfn，arguments.length> 1？arguments [1]：undefined）;
    }
  }）;
  _dereq_（35）（KEY）;
  
  }，{“ 35”：35，“ 42”：42，“ 62”：62}]，160：[function（_dereq_，module，exports）{
  “使用严格”；
  var $ export = _dereq_（62）;
  var $ forEach = _dereq_（42）（0）;
  var STRICT = _dereq_（128）（[]。forEach，true）;
  
  $ export（$ export.P + $ export.F *！STRICT，'Array'，{
    // 22.1.3.10 / 15.4.4.18 Array.prototype.forEach（callbackfn [，thisArg]）
    forEach：函数forEach（callbackfn / *，thisArg * /）{
      返回$ forEach（this，callbackfn，arguments [1]）;
    }
  }）;
  
  }，{“ 128”：128，“ 42”：42，“ 62”：62}]，161：[function（_dereq_，module，exports）{
  “使用严格”；
  var ctx = _dereq_（54）;
  var $ export = _dereq_（62）;
  var toObject = _dereq_（142）;
  var call = _dereq_（83）;
  var isArrayIter = _dereq_（78）;
  var toLength = _dereq_（141）;
  var createProperty = _dereq_（53）;
  var getIterFn = _dereq_（153）;
  
  $ export（$ export.S + $ export.F *！_dereq_（86）（function（iter）{Array.from（iter）;}），'Array'，{
    // 22.1.2.1 Array.from（arrayLike，mapfn =未定义，thisArg =未定义）
    from：function from（arrayLike / *，mapfn = undefined，thisArg = undefined * /）{
      var O = toObject（arrayLike）;
      var C = typeof this =='function'吗？这：数组;
      var aLen = arguments.length;
      var mapfn = aLen> 1吗？arguments [1]：未定义；
      var mapping = mapfn！==未定义；
      var index = 0;
      var iterFn = getIterFn（O）;
      var length，result，step，iterator;
      if（mapping）mapfn = ctx（mapfn，aLen> 2？arguments [2]：undefined，2）;
      //如果对象是不可迭代的，或者它是带有默认迭代器的数组-使用简单情况
      if（iterFn！=未定义&&！（C == Array && isArrayIter（iterFn）））{
        for（迭代器= iterFn.call（O），结果=新C（）;！（step = iterator.next（））。done; index ++）{
          createProperty（结果，索引，映射？调用（迭代器，mapfn，[step.value，index]，true）：step.value）;
        }
      }其他{
        长度= toLength（O.length）;
        for（结果=新的C（长度）;长度>索引;索引++）{
          createProperty（结果，索引，映射？mapfn（O [index]，index）：O [index]）;
        }
      }
      result.length =索引;
      返回结果；
    }
  }）;
  
  }，{“ 141”：141，“ 142”：142，“ 153”：153，“ 53”：53，“ 54”：54，“ 62”：62，“ 78”：78，“ 83”：83 ，“ 86”：86}]，162：[function（_dereq_，module，exports）{
  “使用严格”；
  var $ export = _dereq_（62）;
  var $ indexOf = _dereq_（41）（false）;
  var $ native = [] .indexOf;
  var NEGATIVE_ZERO = !! $ native && 1 / [1] .indexOf（1，-0）<0;
  
  $ export（$ export.P + $ export.F *（NEGATIVE_ZERO ||！_dereq_（128）（$ native）），'Array'，{
    // 22.1.3.11 / 15.4.4.14 Array.prototype.indexOf（searchElement [，fromIndex]）
    indexOf：函数indexOf（searchElement / *，fromIndex = 0 * /）{
      返回NEGATIVE_ZERO
        //将-0转换为+0
        ？$ native.apply（this，arguments）|| 0
        ：$ indexOf（this，searchElement，arguments [1]）;
    }
  }）;
  
  }，{“ 128”：128，“ 41”：41，“ 62”：62}]，163：[function（_dereq_，module，exports）{
  // 22.1.2.2 / 15.4.3.2 Array.isArray（arg）
  var $ export = _dereq_（62）;
  
  $ export（$ export.S，'Array'，{isArray：_dereq_（79）}）;
  
  }，{“ 62”：62，“ 79”：79}]，164：[function（_dereq_，module，exports）{
  “使用严格”；
  var addToUnscopables = _dereq_（35）;
  var step = _dereq_（87）;
  var Iterators = _dereq_（88）;
  var toIObject = _dereq_（140）;
  
  // 22.1.3.4 Array.prototype.entries（）
  // 22.1.3.13 Array.prototype.keys（）
  // 22.1.3.29 Array.prototype.values（）
  // 22.1.3.30 Array.prototype [@@ iterator]（）
  module.exports = _dereq_（85）（Array，'Array'，function（iterated，kind）{
    this._t = toIObject（iterated）; //目标
    _i = 0; //下一个索引
    _k =种类; //种类
  // 22.1.5.2.1％ArrayIteratorPrototype％.next（）
  }，函数（）{
    var O = this._t;
    var kind = this._k;
    var index = this._i ++;
    if（！O || index> = O.length）{
      this._t =未定义；
      返回步骤（1）；
    }
    if（kind =='keys'）返回step（0，index）;
    if（种类=='值'）返回step（0，O [index]）;
    返回步骤（0，[索引，O [索引]]）；
  }，“值”）；
  
  // argumentsList [@@ iterator]是％ArrayProto_values％（9.4.4.6，9.4.4.7）
  Iterators.Arguments = Iterators.Array;
  
  addToUnscopables（'keys'）;
  addToUnscopables（'values'）;
  addToUnscopables（'entries'）;
  
  }，{“ 140”：140，“ 35”：35，“ 85”：85，“ 87”：87，“ 88”：88}]，165：[function（_dereq_，module，exports）{
  “使用严格”；
  // 22.1.3.13 Array.prototype.join（separator）
  var $ export = _dereq_（62）;
  var toIObject = _dereq_（140）;
  var arrayJoin = [] .join;
  
  //对于非数组式字符串的后备
  $ export（$ export.P + $ export.F *（_dereq_（77）！=对象||！_dereq_（128）（arrayJoin）），'Array'，{
    join：function join（separator）{
      return arrayJoin.call（toIObject（this），分隔符===未定义？'，'：分隔符）;
    }
  }）;
  
  }，{“ 128”：128，“ 140”：140，“ 62”：62，“ 77”：77}]，166：[function（_dereq_，module，exports）{
  “使用严格”；
  var $ export = _dereq_（62）;
  var toIObject = _dereq_（140）;
  var toInteger = _dereq_（139）;
  var toLength = _dereq_（141）;
  var $ native = [] .lastIndexOf;
  var NEGATIVE_ZERO = !! $ native && 1 / [1] .lastIndexOf（1，-0）<0;
  
  $ export（$ export.P + $ export.F *（NEGATIVE_ZERO ||！_dereq_（128）（$ native）），'Array'，{
    // 22.1.3.14 / 15.4.4.15 Array.prototype.lastIndexOf（searchElement [，fromIndex]）
    lastIndexOf：函数lastIndexOf（searchElement / *，fromIndex = @ [*-1] * /）{
      //将-0转换为+0
      如果（NEGATIVE_ZERO）返回$ native.apply（this，arguments）|| 0;
      var O = toIObject（this）;
      var length = toLength（O.length）;
      var index = length-1;
      如果（arguments.length> 1）index = Math.min（index，toInteger（arguments [1]））;
      如果（索引<0）索引=长度+索引;
      for（; index> = 0; index--）if（O中的index）if（O [index] === searchElement）返回索引|| 0;
      返回-1;
    }
  }）;
  
  }，{“ 128”：128，“ 139”：139，“ 140”：140，“ 141”：141，“ 62”：62}]，167：[function（_dereq_，module，exports）{
  “使用严格”；
  var $ export = _dereq_（62）;
  var $ map = _dereq_（42）（1）;
  
  $ export（$ export.P + $ export.F *！_dereq_（128）（[]。map，true），'Array'，{
    // 22.1.3.15 / 15.4.4.19 Array.prototype.map（callbackfn [，thisArg]）
    map：function map（callbackfn / *，thisArg * /）{
      返回$ map（this，callbackfn，arguments [1]）;
    }
  }）;
  
  }，{“ 128”：128，“ 42”：42，“ 62”：62}]，168：[function（_dereq_，module，exports）{
  “使用严格”；
  var $ export = _dereq_（62）;
  var createProperty = _dereq_（53）;
  
  // WebKit Array.of不是通用的
  $ export（$ export.S + $ export.F * _dereq_（64）（function（）{
    函数F（）{/ *空* /}
    返回！（Array.of.call（F）F的instance）;
  }），“数组”，{
    // 22.1.2.3 Array.of（...项目）
    of：function of（/ * ... args * /）{
      var index = 0;
      var aLen = arguments.length;
      var result = new（typeof this =='function'？this：Array）（aLen）;
      while（aLen> index）createProperty（result，index，arguments [index ++]）;
      result.length = aLen;
      返回结果；
    }
  }）;
  
  }，{“ 53”：53，“ 62”：62，“ 64”：64}]，169：[function（_dereq_，module，exports）{
  “使用严格”；
  var $ export = _dereq_（62）;
  var $ reduce = _dereq_（43）;
  
  $ export（$ export.P + $ export.F *！_dereq_（128）（[]。reduceRight，true），'Array'，{
    // 22.1.3.19 / 15.4.4.22 Array.prototype.reduceRight（callbackfn [，initialValue]）
    reduceRight：函数reduceRight（callbackfn / *，initialValue * /）{
      返回$ reduce（this，callbackfn，arguments.length，arguments [1]，true）;
    }
  }）;
  
  }，{“ 128”：128，“ 43”：43，“ 62”：62}]，170：[function（_dereq_，module，exports）{
  “使用严格”；
  var $ export = _dereq_（62）;
  var $ reduce = _dereq_（43）;
  
  $ export（$ export.P + $ export.F *！_dereq_（128）（[]。reduce，true），'Array'，{
    // 22.1.3.18 / 15.4.4.21 Array.prototype.reduce（callbackfn [，initialValue]）
    reduce：函数reduce（callbackfn / *，initialValue * /）{
      返回$ reduce（this，callbackfn，arguments.length，arguments [1]，false）;
    }
  }）;
  
  }，{“ 128”：128，“ 43”：43，“ 62”：62}]，171：[function（_dereq_，module，exports）{
  “使用严格”；
  var $ export = _dereq_（62）;
  var html = _dereq_（73）;
  var cof = _dereq_（48）;
  var toAbsoluteIndex = _dereq_（137）;
  var toLength = _dereq_（141）;
  var arraySlice = [] .slice;
  
  //对于非数组式ES3字符串和DOM对象的后备
  $ export（$ export.P + $ export.F * _dereq_（64）（function（）{
    如果（html）arraySlice.call（html）;
  }），“数组”，{
    slice：function slice（begin，end）{
      var len = toLength（this.length）;
      var klass = cof（this）;
      结束=结束===未定义？len：结尾；
      如果（klass =='Array'）返回arraySlice.call（this，begin，end）;
      var start = toAbsoluteIndex（begin，len）;
      var upTo = toAbsoluteIndex（end，len）;
      var size = toLength（upTo-start）;
      var cloned =新Array（size）;
      var i = 0;
      for（; i <size; i ++）cloned [i] = klass =='字符串'
        ？this.charAt（开始+我）
        ：this [开始+我];
      返回克隆；
    }
  }）;
  
  }，{“ 137”：137，“ 141”：141，“ 48”：48，“ 62”：62，“ 64”：64，“ 73”：73}]，172：[function（_dereq_，module，出口）{
  “使用严格”；
  var $ export = _dereq_（62）;
  var $ some = _dereq_（42）（3）;
  
  $ export（$ export.P + $ export.F *！_dereq_（128）（[]。some，true），'Array'，{
    // 22.1.3.23 / 15.4.4.17 Array.prototype.some（callbackfn [，thisArg]）
    some：function some（callbackfn / *，thisArg * /）{
      返回$ some（this，callbackfn，arguments [1]）;
    }
  }）;
  
  }，{“ 128”：128，“ 42”：42，“ 62”：62}]，173：[function（_dereq_，module，exports）{
  “使用严格”；
  var $ export = _dereq_（62）;
  var aFunction = _dereq_（33）;
  var toObject = _dereq_（142）;
  var failed = _dereq_（64）;
  var $ sort = [] .sort;
  var test = [1,2,3];
  
  $ export（$ export.P + $ export.F *（失败（函数（）{
    // IE8-
    test.sort（未定义）;
  }）|| ！失败（功能（）{
    // V8错误
    test.sort（null）;
    //旧版WebKit
  }）|| ！_dereq_（128）（$ sort）），'Array'，{
    // 22.1.3.25 Array.prototype.sort（comparefn）
    排序：函数sort（comparefn）{
      返回comparefn ===未定义
        ？$ sort.call（toObject（this））
        ：$ sort.call（toObject（this），aFunction（comparefn））;
    }
  }）;
  
  }，{“ 128”：128，“ 142”：142，“ 33”：33，“ 62”：62，“ 64”：64}]，174：[function（_dereq_，module，exports）{
  _dereq_（123）（'Array'）;
  
  }，{“ 123”：123}]，175：[function（_dereq_，module，exports）{
  // 20.3.3.1 / 15.9.4.4 Date.now（）
  var $ export = _dereq_（62）;
  
  $ export（$ export.S，'Date'，{now：function（）{return new Date（）。getTime（）;}}）;
  
  }，{“ 62”：62}]，176：[function（_dereq_，module，exports）{
  // 20.3.4.36 / 15.9.5.43 Date.prototype.toISOString（）
  var $ export = _dereq_（62）;
  var toISOString = _dereq_（55）;
  
  // PhantomJS /旧版WebKit的实现方式已损坏
  $ export（$ export.P + $ export.F *（Date.prototype.toISOString！== toISOString），'Date'，{
    toISOString：toISOString
  }）;
  
  }，{“ 55”：55，“ 62”：62}]，177：[function（_dereq_，module，exports）{
  “使用严格”；
  var $ export = _dereq_（62）;
  var toObject = _dereq_（142）;
  var toPrimitive = _dereq_（143）;
  
  $ export（$ export.P + $ export.F * _dereq_（64）（function（）{
    返回新的Date（NaN）.toJSON（）！== null
      || Date.prototype.toJSON.call（{toISOString：function（）{return 1;}}）！== 1;
  }），“日期”，{
    // eslint-disable-next-line no-unused-vars
    toJSON：函数toJSON（key）{
      var O = toObject（this）;
      var pv = toPrimitive（O）;
      返回typeof pv =='number'&&！isFinite（pv）？null：O.toISOString（）;
    }
  }）;
  
  }，{“ 142”：142，“ 143”：143，“ 62”：62，“ 64”：64}]，178：[function（_dereq_，module，exports）{
  var TO_PRIMITIVE = _dereq_（152）（'toPrimitive'）;
  var proto = Date.prototype;
  
  if（！（原语中的TO_PRIMITIVE））_dereq_（72）（proto，TO_PRIMITIVE，_dereq_（56））;
  
  }，{“ 152”：152，“ 56”：56，“ 72”：72}]，179：[function（_dereq_，module，exports）{
  var DateProto = Date.prototype;
  var INVALID_DATE ='无效日期';
  var TO_STRING ='toString';
  var $ toString = DateProto [TO_STRING];
  var getTime = DateProto.getTime;
  如果（new Date（NaN）+''！= INVALID_DATE）{
    _dereq_（118）（DateProto，TO_STRING，函数toString（）{
      var value = getTime.call（this）;
      // eslint-disable-next-line no-self-compare
      返回值===值？$ toString.call（this）：INVALID_DATE;
    }）;
  }
  
  }，{“ 118”：118}]，180：[function（_dereq_，module，exports）{
  // 19.2.3.2 / 15.3.4.5 Function.prototype.bind（thisArg，args ...）
  var $ export = _dereq_（62）;
  
  $ export（$ export.P，'Function'，{绑定：_dereq_（46）}）;
  
  }，{“ 46”：46，“ 62”：62}]，181：[function（_dereq_，module，exports）{
  “使用严格”；
  var isObject = _dereq_（81）;
  var getPrototypeOf = _dereq_（105）;
  var HAS_INSTANCE = _dereq_（152）（'hasInstance'）;
  var FunctionProto = Function.prototype;
  // 19.2.3.6 Function.prototype [@@ hasInstance]（V）
  if（！（FunctionProto中的HAS_INSTANCE））_dereq_（99）.f（FunctionProto，HAS_INSTANCE，{值：函数（O）{
    if（typeof this！='function'||！isObject（O））返回false;
    如果（！isObject（this.prototype））返回此对象的O实例;
    //对于没有本地`@@ hasInstance`逻辑的环境，没有足够的`i​​nstanceof`，但是添加以下内容：
    而（O = getPrototypeOf（O））如果（this.prototype === O）返回true;
    返回false；
  }}）;
  
  }，{“ 105”：105，“ 152”：152，“ 81”：81，“ 99”：99}]，182：[function（_dereq_，module，exports）{
  var dP = _dereq_（99）.f;
  var FProto = Function.prototype;
  var nameRE = / ^ \ s * function（[^（] *）/;
  var NAME ='name';
  
  // 19.2.4.2名称
  FProto中的NAME || _dereq_（58）&& dP（FProto，NAME，{
    可配置：true，
    get：function（）{
      尝试{
        return（''+ this）.match（nameRE）[1];
      }抓住（e）{
        返回'';
      }
    }
  }）;
  
  }，{“ 58”：58，“ 99”：99}]，183：[function（_dereq_，module，exports）{
  “使用严格”；
  var strong = _dereq_（49）;
  var validate = _dereq_（149）;
  var MAP ='地图';
  
  // 23.1映射对象
  module.exports = _dereq_（51）（MAP，函数（获取）{
    返回函数Map（）{return get（this，arguments.length> 0？arguments [0]：undefined）; };
  }，{
    // 23.1.3.6 Map.prototype.get（key）
    get：function get（key）{
      var entry = strong.getEntry（validate（this，MAP），key）;
      返回条目&& entry.v;
    }，
    // 23.1.3.9 Map.prototype.set（key，value）
    设置：功能集（键，值）{
      返回strong.def（validate（this，MAP），key === 0？0：key，value）;
    }
  }，强壮，真实）；
  
  }，{“ 149”：149，“ 49”：49，“ 51”：51}]，184：[function（_dereq_，module，exports）{
  // 20.2.2.3 Math.acosh（x）
  var $ export = _dereq_（62）;
  var log1p = _dereq_（92）;
  var sqrt = Math.sqrt;
  var $ acosh = Math.acosh;
  
  $ export（$ export.S + $ export.F *！（$ acosh
    // V8错误：https：//code.google.com/p/v8/issues/detail？id = 3509
    && Math.floor（$ acosh（Number.MAX_VALUE））== 710
    // Tor浏览器错误：Math.acosh（Infinity）-> NaN
    && $ acosh（无限）==无限
  ）， '数学'， {
    acosh：函数acosh（x）{
      返回（x = + x）<1？NaN：x> 94906265.62425156
        ？Math.log（x）+ Math.LN2
        ：log1p（x-1 + sqrt（x-1）* sqrt（x + 1））;
    }
  }）;
  
  }，{“ 62”：62，“ 92”：92}]，185：[function（_dereq_，module，exports）{
  // 20.2.2.5 Math.asinh（x）
  var $ export = _dereq_（62）;
  var $ asinh = Math.asinh;
  
  函数asinh（x）{
    返回！isFinite（x = + x）|| x == 0？x：x ＜0？-asinh（-x）：Math.log（x + Math.sqrt（x * x + 1））;
  }
  
  // Tor浏览器错误：Math.asinh（0）-> -0
  $ export（$ export.S + $ export.F *！（$ asinh && 1 / $ asinh（0）> 0），'Math'，{asinh：asinh}）;
  
  }，{“ 62”：62}]，186：[function（_dereq_，module，exports）{
  // 20.2.2.7 Math.atanh（x）
  var $ export = _dereq_（62）;
  var $ atanh = Math.atanh;
  
  // Tor浏览器错误：Math.atanh（-0）-> 0
  $ export（$ export.S + $ export.F *！（$ atanh && 1 / $ atanh（-0）<0），'Math'，{
    atanh：函数atanh（x）{
      返回（x = + x）== 0吗？x：Math.log（（1 + x）/（1-x））/ 2;
    }
  }）;
  
  }，{“ 62”：62}]，187：[function（_dereq_，module，exports）{
  // 20.2.2.9 Math.cbrt（x）
  var $ export = _dereq_（62）;
  var sign = _dereq_（93）;
  
  $ export（$ export.S，'Math'，{
    cbrt：函数cbrt（x）{
      返回符号（x = + x）* Math.pow（Math.abs（x），1/3）;
    }
  }）;
  
  }，{“ 62”：62，“ 93”：93}]，188：[function（_dereq_，module，exports）{
  // 20.2.2.11 Math.clz32（x）
  var $ export = _dereq_（62）;
  
  $ export（$ export.S，'Math'，{
    clz32：函数clz32（x）{
      返回（x >>> = 0）？31-Math.floor（Math.log（x + 0.5）* Math.LOG2E）：32;
    }
  }）;
  
  }，{“ 62”：62}]，189：[function（_dereq_，module，exports）{
  // 20.2.2.12 Math.cosh（x）
  var $ export = _dereq_（62）;
  var exp = Math.exp;
  
  $ export（$ export.S，'Math'，{
    cosh：函数cosh（x）{
      返回（exp（x = + x）+ exp（-x））/ 2;
    }
  }）;
  
  }，{“ 62”：62}]，190：[function（_dereq_，module，exports）{
  // ********* Math.expm1（x）
  var $ export = _dereq_（62）;
  var $ expm1 = _dereq_（90）;
  
  $ export（$ export.S + $ export.F *（$ expm1！= Math.expm1），'Math'，{expm1：$ expm1}）;
  
  }，{“ 62”：62，“ 90”：90}]，191：[function（_dereq_，module，exports）{
  // 20.2.2.16 Math.fround（x）
  var $ export = _dereq_（62）;
  
  $ export（$ export.S，'Math'，{fround：_dereq_（91）}）;
  
  }，{“ 62”：62，“ 91”：91}]，192：[function（_dereq_，module，exports）{
  // 20.2.2.17 Math.hypot（[value1 [，value2 [，…]]]）
  var $ export = _dereq_（62）;
  var abs = Math.abs;
  
  $ export（$ export.S，'Math'，{
    hypot：函数hypot（value1，value2）{// eslint-disable-line no-unused-vars
      var sum = 0;
      var i = 0;
      var aLen = arguments.length;
      var larg = 0;
      var arg，div;
      而（i <aLen）{
        arg = abs（arguments [i ++]）;
        如果（larg <arg）{
          div =大/ arg;
          sum = sum * div * div +1;
          larg = arg;
        } else if（arg> 0）{
          div = arg / larg;
          sum + = div * div;
        } else sum + = arg;
      }
      return larg === Infinity？无限：大* Math.sqrt（sum）;
    }
  }）;
  
  }，{“ 62”：62}]，193：[function（_dereq_，module，exports）{
  // 20.2.2.18 Math.imul（x，y）
  var $ export = _dereq_（62）;
  var $ imul = Math.imul;
  
  //有些WebKit版本失败，出现大量错误，有些Web版本错误
  $ export（$ export.S + $ export.F * _dereq_（64）（function（）{
    返回$ imul（0xffffffff，5）！= -5 || $ imul.length！= 2;
  }）， '数学'， {
    imul：函数imul（x，y）{
      var UINT16 = 0xffff;
      var xn = + x;
      var yn = + y;
      var xl = UINT16＆xn;
      var yl = UINT16＆yn;
      返回0 | xl * yl +（（UINT16＆xn >>> 16）* yl + xl *（UINT16＆yn >>> 16）<< 16 >>> 0）;
    }
  }）;
  
  }，{“ 62”：62，“ 64”：64}]，194：[function（_dereq_，module，exports）{
  // 20.2.2.21 Math.log10（x）
  var $ export = _dereq_（62）;
  
  $ export（$ export.S，'Math'，{
    log10：函数log10（x）{
      返回Math.log（x）* Math.LOG10E;
    }
  }）;
  
  }，{“ 62”：62}]，195：[function（_dereq_，module，exports）{
  // 20.2.2.20 Math.log1p（x）
  var $ export = _dereq_（62）;
  
  $ export（$ export.S，'Math'，{log1p：_dereq_（92）}）;
  
  }，{“ 62”：62，“ 92”：92}]，196：[function（_dereq_，module，exports）{
  // 20.2.2.22 Math.log2（x）
  var $ export = _dereq_（62）;
  
  $ export（$ export.S，'Math'，{
    log2：函数log2（x）{
      返回Math.log（x）/ Math.LN2;
    }
  }）;
  
  }，{“ 62”：62}]，197：[function（_dereq_，module，exports）{
  // 20.2.2.28 Math.sign（x）
  var $ export = _dereq_（62）;
  
  $ export（$ export.S，'Math'，{sign：_dereq_（93）}）;
  
  }，{“ 62”：62，“ 93”：93}]，198：[function（_dereq_，module，exports）{
  // 20.2.2.30 Math.sinh（x）
  var $ export = _dereq_（62）;
  var expm1 = _dereq_（90）;
  var exp = Math.exp;
  
  // Chromium 38附近的V8的数量非常少
  $ export（$ export.S + $ export.F * _dereq_（64）（function（）{
    return！Math.sinh（-2e-17）！= -2e-17;
  }）， '数学'， {
    sinh：函数sinh（x）{
      返回Math.abs（x = + x）<1
        ？（expm1（x）-expm1（-x））/ 2
        ：（exp（x-1）-exp（-x-1））*（数学E / 2）;
    }
  }）;
  
  }，{“ 62”：62，“ 64”：64，“ 90”：90}]，199：[function（_dereq_，module，exports）{
  // 20.2.2.33 Math.tanh（x）
  var $ export = _dereq_（62）;
  var expm1 = _dereq_（90）;
  var exp = Math.exp;
  
  $ export（$ export.S，'Math'，{
    tanh：函数tanh（x）{
      var a = expm1（x = + x）;
      var b = expm1（-x）;
      返回a == Infinity？1：b ==无限大？-1：（a-b）/（exp（x）+ exp（-x））;
    }
  }）;
  
  }，{“ 62”：62，“ 90”：90}]，200：[function（_dereq_，module，exports）{
  // 20.2.2.34 Math.trunc（x）
  var $ export = _dereq_（62）;
  
  $ export（$ export.S，'Math'，{
    trunc：函数trunc（it）{
      return（它> 0？Math.floor：Math.ceil）（它）;
    }
  }）;
  
  }，{“ 62”：62}]，201：[function（_dereq_，module，exports）{
  “使用严格”；
  var global = _dereq_（70）;
  var has = _dereq_（71）;
  var cof = _dereq_（48）;
  varInheritIfRequired = _dereq_（75）;
  var toPrimitive = _dereq_（143）;
  var failed = _dereq_（64）;
  var gOPN = _dereq_（103）.f;
  var gOPD = _dereq_（101）.f;
  var dP = _dereq_（99）.f;
  var $ trim = _dereq_（134）.trim;
  var NUMBER ='Number';
  var $ Number = global [NUMBER];
  var Base = $ Number;
  var proto = $ Number.prototype;
  // Opera〜12损坏了Object＃toString
  var BROKEN_COF = cof（_dereq_（98）（proto））== NUM​​BER;
  var TRIM ='trim'in String.prototype;
  
  // 7.1.3 ToNumber（参数）
  var toNumber =函数（参数）{
    var it = toPrimitive（argument，false）;
    if（typeof it =='string'&& it.length> 2）{
      = TRIM吗？it.trim（）：$ trim（it，3）;
      var first = it.charCodeAt（0）;
      var third，radix，maxCode;
      if（first === 43 || first === 45）{
        第三= it.charCodeAt（2）;
        如果（第三=== 88 ||第三=== 120）返回NaN；// Number（'+ 0x1'）应该为NaN，旧的V8修复
      } else if（first === 48）{
        开关（it.charCodeAt（1））{
          情况66：情况98：基数= 2；maxCode = 49; 打破; //快速等于/ ^ 0b [01] + $ / i
          情况79：情况111：基数= 8；maxCode = 55; 打破; //快速等于/ ^ 0o [0-7] + $ / i
          默认值：return + it;
        }
        for（var digits = it.slice（2），i = 0，l = digits.length，code; i <l; i ++）{
          代码= digits.charCodeAt（i）;
          // parseInt将字符串解析为第一个不可用的符号
          //，但是如果字符串包含不可用的符号，则ToNumber应该返回NaN
          if（代码<48 ||代码> maxCode）返回NaN；
        } return parseInt（digits，radix）;
      }
    } return + it;
  };
  
  if（！$ Number（'0o1'）||！$ Number（'0b1'）|| $ Number（'+ 0x1'））{
    $ Number =函数Number（value）{
      var it = arguments.length <1吗？0：值；
      var that = this;
      返回该实例的$ Number
        //检查1..constructor（foo）大小写
        &&（BROKEN_COF？failed（function（）{proto.valueOf.call（that）;}）：cof（that）！= NUM​​BER）
          ？nativeIfRequired（new Base（toNumber（it）），that，$ Number）：toNumber（it）;
    };
    对于（var keys = _dereq_（58）？gOPN（Base）：（
      // ES3：
      'MAX_VALUE，MIN_VALUE，NaN，NEGATIVE_INFINITY，POSITIVE_INFINITY'，+
      // ES6（以防万一，如果之前需要具有ES6 Number静态信息的模块）：
      'EPSILON，isFinite，isInteger，isNaN，isSafeInteger，MAX_SAFE_INTEGER，'+
      'MIN_SAFE_INTEGER，parseFloat，parseInt，isInteger'
    ）.split（'，'），j = 0，键; keys.length> j; j ++）{
      if（has（Base，key = keys [j]）&&！has（$ Number，key））{
        dP（$ Number，key，gOPD（Base，key））;
      }
    }
    $ Number.prototype = proto;
    proto.constructor = $ Number;
    _dereq_（118）（全局，NUMBER，$ Number）;
  }
  
  }，{“ 101”：101，“ 103”：103，“ 118”：118，“ 134”：134，“ 143”：143，“ 48”：48，“ 58”：58，“ 64”：64 ，“ 70”：70，“ 71”：71，“ 75”：75，“ 98”：98，“ 99”：99}]，202：[function（_dereq_，module，exports）{
  // 20.1.2.1 Number.EPSILON
  var $ export = _dereq_（62）;
  
  $ export（$ export.S，'Number'，{EPSILON：Math.pow（2，-52）}）;
  
  }，{“ 62”：62}]，203：[function（_dereq_，module，exports）{
  // 20.1.2.2 Number.isFinite（number）
  var $ export = _dereq_（62）;
  var _isFinite = _dereq_（70）.isFinite;
  
  $ export（$ export.S，'Number'，{
    isFinite：函数isFinite（it）{
      返回它的type =='number'&& _isFinite（it）;
    }
  }）;
  
  }，{“ 62”：62，“ 70”：70}]，204：[function（_dereq_，module，exports）{
  // 20.1.2.3 Number.isInteger（number）
  var $ export = _dereq_（62）;
  
  $ export（$ export.S，'Number'，{isInteger：_dereq_（80）}）;
  
  }，{“ 62”：62，“ 80”：80}]，205：[function（_dereq_，module，exports）{
  // 20.1.2.4 Number.isNaN（number）
  var $ export = _dereq_（62）;
  
  $ export（$ export.S，'Number'，{
    isNaN：函数isNaN（number）{
      // eslint-disable-next-line no-self-compare
      返回数字！=数字；
    }
  }）;
  
  }，{“ 62”：62}]，206：[function（_dereq_，module，exports）{
  // 20.1.2.5 Number.isSafeInteger（number）
  var $ export = _dereq_（62）;
  var isInteger = _dereq_（80）;
  var abs = Math.abs;
  
  $ export（$ export.S，'Number'，{
    isSafeInteger：函数isSafeInteger（number）{
      返回isInteger（number）&& abs（number）<= 0x1fffffffffffff;
    }
  }）;
  
  }，{“ 62”：62，“ 80”：80}]，207：[function（_dereq_，module，exports）{
  // 20.1.2.6 Number.MAX_SAFE_INTEGER
  var $ export = _dereq_（62）;
  
  $ export（$ export.S，'Number'，{MAX_SAFE_INTEGER：0x1fffffffffffff}）;
  
  }，{“ 62”：62}]，208：[function（_dereq_，module，exports）{
  // 20.1.2.10 Number.MIN_SAFE_INTEGER
  var $ export = _dereq_（62）;
  
  $ export（$ export.S，'Number'，{MIN_SAFE_INTEGER：-0x1fffffffffffff}）;
  
  }，{“ 62”：62}]，209：[function（_dereq_，module，exports）{
  var $ export = _dereq_（62）;
  var $ parseFloat = _dereq_（112）;
  // 20.1.2.12 Number.parseFloat（string）
  $ export（$ export.S + $ export.F *（Number.parseFloat！= $ parseFloat），'Number'，{parseFloat：$ parseFloat}）;
  
  }，{“ 112”：112，“ 62”：62}]，210：[function（_dereq_，module，exports）{
  var $ export = _dereq_（62）;
  var $ parseInt = _dereq_（113）;
  // 20.1.2.13 Number.parseInt（string，radix）
  $ export（$ export.S + $ export.F *（Number.parseInt！= $ parseInt），'Number'，{parseInt：$ parseInt}）;
  
  }，{“ 113”：113，“ 62”：62}]，211：[function（_dereq_，module，exports）{
  “使用严格”；
  var $ export = _dereq_（62）;
  var toInteger = _dereq_（139）;
  var aNumberValue = _dereq_（34）;
  var repeat = _dereq_（133）;
  var $ toFixed = 1.0.toFixed;
  var floor = Math.floor;
  var data = [0，0，0，0，0，0];
  var ERROR ='Number.toFixed：不正确的调用！';
  var ZERO ='0';
  
  var乘法=函数（n，c）{
    var i = -1;
    var c2 = c;
    而（++ i <6）{
      c2 + = n * data [i];
      数据[i] = c2％1e7;
      c2 =下限（c2 / 1e7）;
    }
  };
  var除法=函数（n）{
    var i = 6;
    var c = 0;
    而（--i> = 0）{
      c + = data [i];
      data [i] = floor（c / n）;
      c =（c％n）* 1e7;
    }
  };
  var numToString = function（）{
    var i = 6;
    var s ='';
    而（--i> = 0）{
      if（s！==''|| i === 0 || data [i]！== 0）{
        var t = String（data [i]）;
        s = s ===''？t：s + repeat.call（零，7-t.length）+ t;
      }
    } return s;
  };
  var pow =函数（x，n，acc）{
    返回n === 0吗？acc：n％2 === 1？pow（x，n-1，acc * x）：pow（x * x，n / 2，acc）;
  };
  var log = function（x）{
    var n = 0;
    var x2 = x;
    而（x2> = 4096）{
      n + = 12;
      x2 / = 4096;
    }
    而（x2> = 2）{
      n + = 1;
      x2 / = 2;
    } return n;
  };
  
  $ export（$ export.P + $ export.F *（!! $ toFixed &&（
    0.00008.toFixed（3）！=='0.000'||
    0.9.toFixed（0）！=='1'||
    1.255.toFixed（2）！=='1.25'||
    1000000000000000128.0.toFixed（0）！=='1000000000000000128'
  ）|| ！_dereq_（64）（function（）{
    // V8〜Android 4.3-
    $ toFixed.call（{}）;
  }）），“数字”，{
    toFixed：函数toFixed（fractionDigits）{
      var x = aNumberValue（this，ERROR）;
      var f = toInteger（fractionDigits）;
      var s ='';
      var m =零；
      var e，z，j，k;
      如果（f <0 || f> 20）抛出RangeError（ERROR）;
      // eslint-disable-next-line no-self-compare
      如果（x！= x）返回'NaN';
      如果（x <= -1e21 || x> = 1e21）返回String（x）;
      如果（x <0）{
        s ='-';
        x = -x;
      }
      如果（x> 1e-21）{
        e = log（x * pow（2，69，1））-69;
        z = e <0？x * pow（2，-e，1）：x / pow（2，e，1）;
        z * = 0x10000000000000;
        e = 52-e;
        如果（e> 0）{
          相乘（0，z）;
          j = f;
          而（j> = 7）{
            乘法（1e7，0）;
            j-= 7;
          }
          乘法（pow（10，j，1），0）;
          j = e-1;
          而（j> = 23）{
            除（1 << 23）;
            j-= 23;
          }
          除（1 << j）;
          乘法（1，1）;
          除（2）;
          m = numToString（）;
        }其他{
          相乘（0，z）;
          乘法（1 << -e，0）;
          m = numToString（）+ repeat.call（零，f）;
        }
      }
      如果（f> 0）{
        k =长度
        m = s +（k <= f？'0.'+ repeat.call（零，f-k）+ m：m.slice（0，k-f）+'。'+ m.slice（k-f ））；
      }其他{
        m = s + m;
      } return m;
    }
  }）;
  
  }，{“ 133”：133，“ 139”：139，“ 34”：34，“ 62”：62，“ 64”：64}]，212：[function（_dereq_，module，exports）{
  “使用严格”；
  var $ export = _dereq_（62）;
  var $ fails = _dereq_（64）;
  var aNumberValue = _dereq_（34）;
  var $ toPrecision = 1.0.toPrecision;
  
  $ export（$ export.P + $ export.F *（$ fails（function（）{
    // IE7-
    return $ toPrecision.call（1，undefined）！=='1';
  }）|| ！$ fails（function（）{
    // V8〜Android 4.3-
    $ toPrecision.call（{}）;
  }）），“数字”，{
    toPrecision：函数toPrecision（precision）{
      var that = aNumberValue（this，'Number＃toPrecision：错误的调用！'）；
      返回精度===未定义？$ toPrecision.call（that）：$ toPrecision.call（that，precision）;
    }
  }）;
  
  }，{“ 34”：34，“ 62”：62，“ 64”：64}]，213：[function（_dereq_，module，exports）{
  // 19.1.3.1 Object.assign（目标，源）
  var $ export = _dereq_（62）;
  
  $ export（$ export.S + $ export.F，'Object'，{分配：_dereq_（97）}）;
  
  }，{“ 62”：62，“ 97”：97}]，214：[function（_dereq_，module，exports）{
  var $ export = _dereq_（62）;
  // ******** / ******** Object.create（O [，Properties]）
  $ export（$ export.S，'Object'，{创建：_dereq_（98）}）;
  
  }，{“ 62”：62，“ 98”：98}]，215：[function（_dereq_，module，exports）{
  var $ export = _dereq_（62）;
  // ******** / 15.2.3.7 Object.defineProperties（O，Properties）
  $ export（$ export.S + $ export.F *！_dereq_（58），'Object'，{defineProperties：_dereq_（100）}）;
  
  }，{“ 100”：100，“ 58”：58，“ 62”：62}]，216：[function（_dereq_，module，exports）{
  var $ export = _dereq_（62）;
  // ******** / 15.2.3.6 Object.defineProperty（O，P，Attributes）
  $ export（$ export.S + $ export.F *！_dereq_（58），'Object'，{defineProperty：_dereq_（99）.f}）;
  
  }，{“ 58”：58，“ 62”：62，“ 99”：99}]，217：[function（_dereq_，module，exports）{
  // ******** Object.freeze（O）
  var isObject = _dereq_（81）;
  var meta = _dereq_（94）.onFreeze;
  
  _dereq_（109）（'freeze'，function（$ freeze）{
    返回函数Frozen（it）{
      返回$ freeze && isObject（it）吗？$ freeze（meta（it））：它；
    };
  }）;
  
  }，{“ 109”：109，“ 81”：81，“ 94”：94}]，218：[function（_dereq_，module，exports）{
  // ******** Object.getOwnPropertyDescriptor（O，P）
  var toIObject = _dereq_（140）;
  var $ getOwnPropertyDescriptor = _dereq_（101）.f;
  
  _dereq_（109）（'getOwnPropertyDescriptor'，函数（）{
    返回函数getOwnPropertyDescriptor（it，key）{
      返回$ getOwnPropertyDescriptor（toIObject（it），键）;
    };
  }）;
  
  }，{“ 101”：101，“ 109”：109，“ 140”：140}]，219：[function（_dereq_，module，exports）{
  // ******** Object.getOwnPropertyNames（O）
  _dereq_（109）（'getOwnPropertyNames'，function（）{
    返回_dereq_（102）.f;
  }）;
  
  }，{“ 102”：102，“ 109”：109}]，220：[function（_dereq_，module，exports）{
  // ******** Object.getPrototypeOf（O）
  var toObject = _dereq_（142）;
  var $ getPrototypeOf = _dereq_（105）;
  
  _dereq_（109）（'getPrototypeOf'，函数（）{
    返回函数getPrototypeOf（it）{
      返回$ getPrototypeOf（toObject（it））;
    };
  }）;
  
  }，{“ 105”：105，“ 109”：109，“ 142”：142}]，221：[function（_dereq_，module，exports）{
  // ********1 Object.isExtensible（O）
  var isObject = _dereq_（81）;
  
  _dereq_（109）（'isExtensible'，函数（$ isExtensible）{
    返回函数isExtensible（it）{
      返回isObject（it）吗？$ isExtensible吗？$ isExtensible（it）：true：false;
    };
  }）;
  
  }，{“ 109”：109，“ 81”：81}]，222：[function（_dereq_，module，exports）{
  // ********2 Object.isFrozen（O）
  var isObject = _dereq_（81）;
  
  _dereq_（109）（'isFrozen'，函数（$ isFrozen）{
    返回函数isFrozen（it）{
      返回isObject（it）吗？$ isFrozen吗？$ isFrozen（it）：false：true;
    };
  }）;
  
  }，{“ 109”：109，“ 81”：81}]，223：[function（_dereq_，module，exports）{
  // ********3 Object.isSealed（O）
  var isObject = _dereq_（81）;
  
  _dereq_（109）（'isSealed'，函数（$ isSealed）{
    返回函数isSealed（it）{
      返回isObject（it）吗？$ isSealed？$ isSealed（it）：false：true;
    };
  }）;
  
  }，{“ 109”：109，“ 81”：81}]，224：[function（_dereq_，module，exports）{
  // 19.1.3.10 Object.is（value1，value2）
  var $ export = _dereq_（62）;
  $ export（$ export.S，'Object'，{是：_dereq_（121）}）;
  
  }，{“ 121”：121，“ 62”：62}]，225：[function（_dereq_，module，exports）{
  // ********4 Object.keys（O）
  var toObject = _dereq_（142）;
  var $ keys = _dereq_（107）;
  
  _dereq_（109）（'keys'，function（）{
    返回功能键{
      返回$ keys（toObject（it））;
    };
  }）;
  
  }，{“ 107”：107，“ 109”：109，“ 142”：142}]，226：[function（_dereq_，module，exports）{
  // ********5 Object.preventExtensions（O）
  var isObject = _dereq_（81）;
  var meta = _dereq_（94）.onFreeze;
  
  _dereq_（109）（'preventExtensions'，函数（$ preventExtensions）{
    返回函数preventExtensions（it）{
      返回$ preventExtensions && isObject（it）吗？$ preventExtensions（meta（it））：它；
    };
  }）;
  
  }，{“ 109”：109，“ 81”：81，“ 94”：94}]，227：[function（_dereq_，module，exports）{
  // ********7 Object.seal（O）
  var isObject = _dereq_（81）;
  var meta = _dereq_（94）.onFreeze;
  
  _dereq_（109）（'seal'，function（$ seal）{
    返回函数seal（it）{
      返回$ seal && isObject（it）吗？$ seal（meta（it））：它；
    };
  }）;
  
  }，{“ 109”：109，“ 81”：81，“ 94”：94}]，228：[function（_dereq_，module，exports）{
  // 19.1.3.19 Object.setPrototypeOf（O，proto）
  var $ export = _dereq_（62）;
  $ export（$ export.S，'Object'，{setPrototypeOf：_dereq_（122）.set}）;
  
  }，{“ 122”：122，“ 62”：62}]，229：[function（_dereq_，module，exports）{
  “使用严格”；
  // 19.1.3.6 Object.prototype.toString（）
  var classof = _dereq_（47）;
  var test = {};
  test [_dereq_（152）（'toStringTag'）] ='z';
  if（test +''！='[object z]'）{
    _dereq_（118）（Object.prototype，'toString'，function toString（）{
      返回'[object'+ classof（this）+']';
    }，是对的）；
  }
  
  }，{“ 118”：118，“ 152”：152，“ 47”：47}]，230：[function（_dereq_，module，exports）{
  var $ export = _dereq_（62）;
  var $ parseFloat = _dereq_（112）;
  // 18.2.4 parseFloat（string）
  $ export（$ export.G + $ export.F *（parseFloat！= $ parseFloat），{parseFloat：$ parseFloat}）;
  
  }，{“ 112”：112，“ 62”：62}]，231：[function（_dereq_，module，exports）{
  var $ export = _dereq_（62）;
  var $ parseInt = _dereq_（113）;
  // 18.2.5 parseInt（字符串，基数）
  $ export（$ export.G + $ export.F *（parseInt！= $ parseInt），{parseInt：$ parseInt}）;
  
  }，{“ 113”：113，“ 62”：62}]，232：[function（_dereq_，module，exports）{
  “使用严格”；
  var LIBRARY = _dereq_（89）;
  var global = _dereq_（70）;
  var ctx = _dereq_（54）;
  var classof = _dereq_（47）;
  var $ export = _dereq_（62）;
  var isObject = _dereq_（81）;
  var aFunction = _dereq_（33）;
  var anInstance = _dereq_（37）;
  var forOf = _dereq_（68）;
  var speciesConstructor = _dereq_（127）;
  var task = _dereq_（136）.set;
  var microtask = _dereq_（95）（）;
  var newPromiseCapabilityModule = _dereq_（96）;
  var perform = _dereq_（114）;
  var userAgent = _dereq_（148）;
  var promiseResolve = _dereq_（115）;
  var PROMISE ='Promise';
  var TypeError = global.TypeError;
  var process = global.process;
  var版本= process && process.versions;
  var v8 =版本&&版本.v8 || '';
  var $ Promise = global [PROMISE];
  var isNode = classof（process）=='process';
  var empty = function（）{/ *空* /};
  var内部，newGenericPromiseCapability，OwnPromiseCapability，包装器；
  var newPromiseCapability = newGenericPromiseCapability = newPromiseCapabilityModule.f;
  
  var USE_NATIVE = !! function（）{
    尝试{
      //在@@ species支持下正确的子类化
      var promise = $ Promise.resolve（1）;
      var FakePromise =（promise.constructor = {}）[_ dereq_（152）（'species'）] =函数（exec）{
        exec（空，空）;
      };
      //未处理的拒绝跟踪支持，没有它的NodeJS Promise无法通过@@ species测试
      返回（isNode || PromiseRejectionEvent类型=='function'）
        && promise.then（空）FakePromise的实例
        // v8 6.6（节点10和Chrome 66）存在一个错误，无法解决自定义的ableables
        // https://bugs.chromium.org/p/chromium/issues/detail?id=830565
        //我们无法同步检测到它，因此只需检查版本
        && v8.indexOf（'6.6'）！== 0
        && userAgent.indexOf（'Chrome / 66'）=== -1;
    }抓住（e）{/ *空* /}
  }（）;
  
  //帮手
  var isThenable = function（it）{
    var然后;
    返回isObject（it）&& typeof（then = it.then）=='function'吗？然后：false;
  };
  var notify = function（promise，isReject）{
    如果（promise._n）返回；
    诺言。
    var chain = promise._c;
    微任务（功能（）{
      var value = promise._v;
      var ok = promise._s == 1;
      var i = 0;
      var run =函数（反应）{
        var handler =好吗？reaction.ok：反应失败
        var resolve = reaction.resolve;
        var reject = reaction.reject;
        var domain = reaction.domain;
        var结果，然后退出；
        尝试{
          如果（处理程序）{
            如果（！确定）{
              如果（promise._h == 2）onHandleUnhandled（promise）;
              promise._h = 1;
            }
            if（handler === true）结果=值；
            其他{
              如果（domain）domain.enter（）;
              结果=处理程序（值）; //可能会抛出
              如果（网域）{
                domain.exit（）;
                退出=真;
              }
            }
            如果（结果=== reaction.promise）{
              reject（TypeError（'Promise-chain cycle'））;
            } else if（then = isThenable（result））{
              then.call（结果，解决，拒绝）;
            } else resolve（result）;
          } else reject（value）;
        }抓住（e）{
          如果（domain &&！exited）domain.exit（）;
          拒绝（e）；
        }
      };
      while（chain.length> i）运行（chain [i ++]）; //可变长度-不能使用forEach
      promise._c = [];
      诺言。
      if（isReject &&！promise._h）onUnhandled（promise）;
    }）;
  };
  var onUnhandled = function（promise）{
    task.call（global，function（）{
      var value = promise._v;
      var unhandled = isUnhandled（promise）;
      var结果，处理程序，控制台；
      如果（未处理）{
        结果= perform（function（）{
          如果（isNode）{
            process.emit（'unhandledRejection'，价值，承诺）;
          } else if（handler = global.onunhandledrejection）{
            handler（{promise：promise，reason：value}）;
          } else if（（（console = global.console）&& console.error）{
            console.error（'未处理的承诺被拒绝'，值）;
          }
        }）;
        //如果在此处处理了NodeJS，浏览器不应触发`rejectionHandled`事件-应该
        promise._h = isNode || isUnhandled（promise）吗？2：1；
      } promise._a =未定义；
      如果（未处理的&& result.e）抛出result.v;
    }）;
  };
  var isUnhandled = function（promise）{
    返回promise._h！== 1 &&（promise._a || promise._c）.length === 0;
  };
  var onHandleUnhandled =函数（承诺）{
    task.call（global，function（）{
      var handler;
      如果（isNode）{
        process.emit（'rejectionHandled'，promise）;
      } else if（handler = global.onrejectionhandled）{
        handler（{promise：promise，reason：promise._v}）;
      }
    }）;
  };
  var $ reject =函数（值）{
    var promise = this;
    如果（promise._d）返回；
    promise._d = true;
    promise = promise._w || 诺言; //展开
    promise._v =值;
    promise._s = 2;
    如果（！promise._a）promise._a = promise._c.slice（）;
    通知（promise，true）;
  };
  var $ resolve =函数（值）{
    var promise = this;
    var然后;
    如果（promise._d）返回；
    promise._d = true;
    promise = promise._w || 诺言; //展开
    尝试{
      如果（承诺===值）抛出TypeError（“承诺本身无法解决”）；
      if（then = isThenable（value））{
        微任务（功能（）{
          var wrapper = {_w：promise，_d：false}; //包装
          尝试{
            then.call（值，ctx（$ resolve，包装器，1），ctx（$ reject，包装器，1））;
          }抓住（e）{
            $ reject.call（包装器，e）;
          }
        }）;
      }其他{
        promise._v =值;
        promise._s = 1;
        通知（promise，false）;
      }
    }抓住（e）{
      $ reject.call（{_w：promise，_d：false}，e）; //包装
    }
  };
  
  //构造函数polyfill
  如果（！USE_NATIVE）{
    // 25.4.3.1 Promise（执行程序）
    $ Promise =函数Promise（executor）{
      anInstance（this，$ Promise，PROMISE，'_h'）;
      aFunction（执行程序）;
      Internal.call（this）;
      尝试{
        executor（ctx（$ resolve，this，1），ctx（$ reject，this，1））;
      } catch（err）{
        $ reject.call（this，err）;
      }
    };
    // eslint-disable-next-line no-unused-vars
    内部=函数Promise（执行程序）{
      this._c = []; // <-等待反应
      _a =未定义; // <-签入isUnhandled反应
      this._s = 0; // <-状态
      this._d = false; // <-完成
      this._v =未定义；// <-值
      this._h = 0; // <-拒绝状态，0-默认，1-处理，2-未处理
      _n = false; // <-通知
    };
    Internal.prototype = _dereq_（117）（$ Promise.prototype，{
      // 25.4.5.3 Promise.prototype.then（onFulfilled，onRejected）
      然后：函数then（onFulfilled，onRejected）{
        var reaction = newPromiseCapability（speciesConstructor（this，$ Promise））;
        react.ok = typeof onFulfilled =='功能'？onFulfilled：true;
        reaction.fail = typeof onRejected =='函数'&& onRejected;
        reaction.domain = isNode？process.domain：未定义;
        this._c.push（reaction）;
        如果（this._a）this._a.push（reaction）;
        如果（this._s）notify（this，false）;
        退货反应。
      }，
      // 25.4.5.1 Promise.prototype.catch（onRejected）
      'catch'：函数（onRejected）{
        返回this.then（undefined，onRejected）;
      }
    }）;
    OwnPromiseCapability =函数（）{
      var promise = new Internal（）;
      this.promise =许诺；
      this.resolve = ctx（$ resolve，promise，1）;
      this.reject = ctx（$ reject，promise，1）;
    };
    newPromiseCapabilityModule.f = newPromiseCapability =函数（C）{
      返回C === $ Promise || C ===包装器
        ？新的OwnPromiseCapability（C）
        ：newGenericPromiseCapability（C）;
    };
  }
  
  $ export（$ export.G + $ export.W + $ export.F *！USE_NATIVE，{承诺：$ Promise}）;
  _dereq_（124）（$ Promise，PROMISE）;
  _dereq_（123）（PROMISE）;
  包装= _dereq_（52）[PROMISE];
  
  //静态
  $ export（$ export.S + $ export.F *！USE_NATIVE，PROMISE，{
    // 25.4.4.5 Promise.reject（r）
    拒绝：函数reject（r）{
      var能力= newPromiseCapability（this）;
      var $$ reject = capability.reject;
      $$ reject（r）;
      退货能力。承诺;
    }
  }）;
  $ export（$ export.S + $ export.F *（库||！USE_NATIVE），PROMISE，{
    // 25.4.4.6 Promise.resolve（x）
    resolve：函数resolve（x）{
      return promiseResolve（LIBRARY && this === Wrapper？$ Promise：this，x）;
    }
  }）;
  $ export（$ export.S + $ export.F *！（USE_NATIVE && _dereq_（86）（function（iter）{
    $ Promise.all（iter）['catch']（空）;
  }））， 诺言， {
    // 25.4.4.1 Promise.all（可迭代）
    全部：功能全部（可迭代）{
      var C = this;
      var能力= newPromiseCapability（C）;
      var resolve = capability.resolve;
      var reject = capability.reject;
      var result = perform（function（）{
        var值= []；
        var index = 0;
        剩余的var = 1;
        forOf（iterable，false，function（promise）{
          var $ index = index ++;
          var beenCalled = false;
          values.push（undefined）;
          剩余++;
          C.resolve（promise）.then（function（value）{
            如果（已经调用）返回；
            yetCalled = true;
            values [$ index] =值；
            -剩余|| 解析（值）;
          }， 拒绝）;
        }）;
        -剩余|| 解析（值）;
      }）;
      如果（结果e）拒绝（结果v）;
      退货能力。承诺;
    }，
    // 25.4.4.4 Promise.race（可迭代）
    race：function race（iterable）{
      var C = this;
      var能力= newPromiseCapability（C）;
      var reject = capability.reject;
      var result = perform（function（）{
        forOf（iterable，false，function（promise）{
          C.resolve（承诺）.then（capability.resolve，拒绝）;
        }）;
      }）;
      如果（结果e）拒绝（结果v）;
      退货能力。承诺;
    }
  }）;
  
  }，{“ 114”：114，“ 115”：115，“ 117”：117，“ 123”：123，“ 124”：124，“ 127”：127，“ 136”：136，“ 148”：148 ，“ 152”：152，“ 33”：33，“ 37”：37，“ 47”：47，“ 52”：52，“ 54”：54，“ 62”：62，“ 68”：68，“ 70“：70，” 81“：81，” 86“：86，” 89“：89，” 95“：95，” 96“：96}]，233：[function（_dereq_，module，exports）{
  // 26.1.1 Reflect.apply（target，thisArgument，argumentsList）
  var $ export = _dereq_（62）;
  var aFunction = _dereq_（33）;
  var anObject = _dereq_（38）;
  var rApply =（_dereq_（70）.Reflect || {}）。apply;
  var fApply = Function.apply;
  // MS Edge argumentsList参数是可选的
  $ export（$ export.S + $ export.F *！_dereq_（64）（function（）{
    rApply（function（）{/ *空* /}）;
  }），“反映”，{
    apply：function apply（target，thisArgument，argumentsList）{
      var T = aFunction（target）;
      var L = anObject（argumentsList）;
      返回rApply？rApply（T，thisArgument，L）：fApply.call（T，thisArgument，L）;
    }
  }）;
  
  }，{“ 33”：33，“ 38”：38，“ 62”：62，“ 64”：64，“ 70”：70}]，234：[function（_dereq_，module，exports）{
  // 26.1.2 Reflect.construct（target，argumentsList [，newTarget]）
  var $ export = _dereq_（62）;
  var create = _dereq_（98）;
  var aFunction = _dereq_（33）;
  var anObject = _dereq_（38）;
  var isObject = _dereq_（81）;
  var failed = _dereq_（64）;
  var bind = _dereq_（46）;
  var rConstruct =（_dereq_（70）.Reflect || {}）。construct;
  
  // MS Edge仅支持2个参数，argumentsList参数是可选的
  // FF Nightly将第三个参数设置为“ new.target”，但不会从中创建“ this”
  var NEW_TARGET_BUG = failed（function（）{
    函数F（）{/ *空* /}
    返回！（rConstruct（function（）{/ *空* /}，[]，F）F的实例）；
  }）;
  var ARGS_BUG =！fails（function（）{
    rConstruct（function（）{/ *空* /}）;
  }）;
  
  $ export（$ export.S + $ export.F *（NEW_TARGET_BUG || ARGS_BUG），'Reflect'，{
    构造函数：function Construct（Target，args / *，newTarget * /）{
      aFunction（Target）;
      anObject（args）;
      var newTarget = arguments.length <3吗？目标：aFunction（arguments [2]）;
      如果（ARGS_BUG &&！NEW_TARGET_BUG）返回rConstruct（Target，args，newTarget）;
      如果（目标== newTarget）{
        //不更改newTarget，针对0-4个参数进行优化
        开关（args.length）{
          情况0：返回新的Target（）；
          情况1：返回新的Target（args [0]）;
          情况2：返回新的Target（args [0]，args [1]）;
          情况3：返回新的Target（args [0]，args [1]，args [2]）；
          情况4：返回新的Target（args [0]，args [1]，args [2]，args [3]）；
        }
        //没有更改newTarget，参数很多
        var $ args = [null];
        $ args.push.apply（$ args，args）;
        返回新的（bind.apply（Target，$ args））（）;
      }
      //更改了newTarget，不支持内置构造函数
      var proto = newTarget.prototype;
      var instance = create（isObject（proto）？proto：Object.prototype）;
      var result = Function.apply.call（Target，instance，args）;
      返回isObject（result）吗？结果：实例；
    }
  }）;
  
  }，{“ 33”：33，“ 38”：38，“ 46”：46，“ 62”：62，“ 64”：64，“ 70”：70，“ 81”：81，“ 98”：98 }]，235：[function（_dereq_，module，exports）{
  // 26.1.3 Reflect.defineProperty（target，propertyKey，attribute）
  var dP = _dereq_（99）;
  var $ export = _dereq_（62）;
  var anObject = _dereq_（38）;
  var toPrimitive = _dereq_（143）;
  
  // MS Edge破坏了Reflect.defineProperty-抛出而不是返回false
  $ export（$ export.S + $ export.F * _dereq_（64）（function（）{
    // eslint-disable-next-line no-undef
    Reflect.defineProperty（dP.f（{}，1，{value：1}），1，{value：2}）;
  }），“反映”，{
    defineProperty：函数defineProperty（target，propertyKey，attribute）{
      anObject（target）;
      propertyKey = toPrimitive（propertyKey，true）;
      anObject（attributes）;
      尝试{
        dP.f（target，propertyKey，属性）;
        返回true；
      }抓住（e）{
        返回false；
      }
    }
  }）;
  
  }，{“ 143”：143，“ 38”：38，“ 62”：62，“ 64”：64，“ 99”：99}]，236：[function（_dereq_，module，exports）{
  // 26.1.4 Reflect.deleteProperty（target，propertyKey）
  var $ export = _dereq_（62）;
  var gOPD = _dereq_（101）.f;
  var anObject = _dereq_（38）;
  
  $ export（$ export.S，'Reflect'，{
    deleteProperty：函数deleteProperty（target，propertyKey）{
      var desc = gOPD（anObject（target），propertyKey）;
      返回desc &&！desc.configurable吗？false：删除目标[propertyKey]；
    }
  }）;
  
  }，{“ 101”：101，“ 38”：38，“ 62”：62}]，237：[function（_dereq_，module，exports）{
  “使用严格”；
  // 26.1.5 Reflect.enumerate（target）
  var $ export = _dereq_（62）;
  var anObject = _dereq_（38）;
  var枚举=函数（迭代）{
    this._t = anObject（iterated）; //目标
    _i = 0; //下一个索引
    var键= this._k = []; //键
    var key;
    for（迭代中的键）keys.push（key）;
  };
  _dereq_（84）（枚举，“对象”，函数（）{
    var that = this;
    var keys = that._k;
    var key;
    做{
      如果（that._i> = keys.length）返回{值：未定义，完成：true}；
    } while（！（（key。keys [that._i ++]）in that._t））;
    返回{value：key，done：false};
  }）;
  
  $ export（$ export.S，'Reflect'，{
    枚举：函数枚举（目标）{
      返回新的Enumerate（target）;
    }
  }）;
  
  }，{“ 38”：38，“ 62”：62，“ 84”：84}]，238：[function（_dereq_，module，exports）{
  // 26.1.7 Reflect.getOwnPropertyDescriptor（target，propertyKey）
  var gOPD = _dereq_（101）;
  var $ export = _dereq_（62）;
  var anObject = _dereq_（38）;
  
  $ export（$ export.S，'Reflect'，{
    getOwnPropertyDescriptor：函数getOwnPropertyDescriptor（target，propertyKey）{
      返回gOPD.f（anObject（target），propertyKey）;
    }
  }）;
  
  }，{“ 101”：101，“ 38”：38，“ 62”：62}]，239：[function（_dereq_，module，exports）{
  // 26.1.8 Reflect.getPrototypeOf（target）
  var $ export = _dereq_（62）;
  var getProto = _dereq_（105）;
  var anObject = _dereq_（38）;
  
  $ export（$ export.S，'Reflect'，{
    getPrototypeOf：函数getPrototypeOf（target）{
      返回getProto（anObject（target））;
    }
  }）;
  
  }，{“ 105”：105，“ 38”：38，“ 62”：62}]，240：[function（_dereq_，module，exports）{
  // 26.1.6 Reflect.get（target，propertyKey [，接收者]）
  var gOPD = _dereq_（101）;
  var getPrototypeOf = _dereq_（105）;
  var has = _dereq_（71）;
  var $ export = _dereq_（62）;
  var isObject = _dereq_（81）;
  var anObject = _dereq_（38）;
  
  函数get（target，propertyKey / *，接收者* /）{
    var receiver = arguments.length <3？目标：arguments [2];
    var desc，proto;
    if（anObject（target）===接收者）返回target [propertyKey];
    如果（desc = gOPD.f（target，propertyKey））返回has（desc，'value'）
      ？描述值
      ：desc.get！==未定义
        ？desc.get.call（接收者）
        ：未定义;
    如果（isObject（proto = getPrototypeOf（target）））返回get（proto，propertyKey，receiver）;
  }
  
  $ export（$ export.S，'Reflect'，{get：get}）;
  
  }，{“ 101”：101，“ 105”：105，“ 38”：38，“ 62”：62，“ 71”：71，“ 81”：81}]，241：[function（_dereq_，module，出口）{
  // 26.1.9 Reflect.has（target，propertyKey）
  var $ export = _dereq_（62）;
  
  $ export（$ export.S，'Reflect'，{
    has：function has（target，propertyKey）{
      返回target中的propertyKey；
    }
  }）;
  
  }，{“ 62”：62}]，242：[function（_dereq_，module，exports）{
  // 26.1.10 Reflect.isExtensible（target）
  var $ export = _dereq_（62）;
  var anObject = _dereq_（38）;
  var $ isExtensible = Object.isExtensible;
  
  $ export（$ export.S，'Reflect'，{
    isExtensible：函数isExtensible（target）{
      anObject（target）;
      返回$ isExtensible吗？$ isExtensible（target）：true;
    }
  }）;
  
  }，{“ 38”：38，“ 62”：62}]，243：[function（_dereq_，module，exports）{
  // 26.1.11 Reflect.ownKeys（target）
  var $ export = _dereq_（62）;
  
  $ export（$ export.S，'Reflect'，{ownKeys：_dereq_（111）}）;
  
  }，{“ 111”：111，“ 62”：62}]，244：[function（_dereq_，module，exports）{
  // 26.1.12 Reflect.preventExtensions（target）
  var $ export = _dereq_（62）;
  var anObject = _dereq_（38）;
  var $ preventExtensions = Object.preventExtensions;
  
  $ export（$ export.S，'Reflect'，{
    preventExtensions：函数preventExtensions（target）{
      anObject（target）;
      尝试{
        if（$ preventExtensions）$ preventExtensions（目标）;
        返回true；
      }抓住（e）{
        返回false；
      }
    }
  }）;
  
  }，{“ 38”：38，“ 62”：62}]，245：[function（_dereq_，module，exports）{
  // 26.1.14 Reflect.setPrototypeOf（target，proto）
  var $ export = _dereq_（62）;
  var setProto = _dereq_（122）;
  
  if（setProto）$ export（$ export.S，'Reflect'，{
    setPrototypeOf：函数setPrototypeOf（target，proto）{
      setProto.check（target，proto）;
      尝试{
        setProto.set（target，proto）;
        返回true；
      }抓住（e）{
        返回false；
      }
    }
  }）;
  
  }，{“ 122”：122，“ 62”：62}]，246：[function（_dereq_，module，exports）{
  // 26.1.13 Reflect.set（target，propertyKey，V [，接收者]）
  var dP = _dereq_（99）;
  var gOPD = _dereq_（101）;
  var getPrototypeOf = _dereq_（105）;
  var has = _dereq_（71）;
  var $ export = _dereq_（62）;
  var createDesc = _dereq_（116）;
  var anObject = _dereq_（38）;
  var isObject = _dereq_（81）;
  
  函数集（target，propertyKey，V / *，接收者* /）{
    var receiver = arguments.length <4吗？目标：arguments [3];
    var ownDesc = gOPD.f（anObject（target），propertyKey）;
    var existingDescriptor，proto;
    如果（！ownDesc）{
      如果（isObject（proto = getPrototypeOf（target）））{
        返回集合（proto，propertyKey，V，接收者）；
      }
      ownDesc = createDesc（0）;
    }
    if（has（ownDesc，'value'））{
      if（ownDesc.writable === false ||！isObject（receiver））返回false；
      如果（existingDescriptor = gOPD.f（receiver，propertyKey））{
        如果（existingDescriptor.get || existingDescriptor.set || existingDescriptor.writable === false）返回false；
        existingDescriptor.value = V;
        dP.f（receiver，propertyKey，existingDescriptor）;
      } else dP.f（receiver，propertyKey，createDesc（0，V））;
      返回true；
    }
    返回ownDesc.set ===未定义？false：（ownDesc.set.call（receiver，V），true）;
  }
  
  $ export（$ export.S，'Reflect'，{set：set}）;
  
  }，{“ 101”：101，“ 105”：105，“ 116”：116，“ 38”：38，“ 62”：62，“ 71”：71，“ 81”：81，“ 99”：99 }]，247：[function（_dereq_，module，exports）{
  var global = _dereq_（70）;
  varInheritIfRequired = _dereq_（75）;
  var dP = _dereq_（99）.f;
  var gOPN = _dereq_（103）.f;
  var isRegExp = _dereq_（82）;
  var $ flags = _dereq_（66）;
  var $ RegExp = global.RegExp;
  var Base = $ RegExp;
  var proto = $ RegExp.prototype;
  var re1 = / a / g;
  var re2 = / a / g;
  //“ new”创建一个新对象，旧的webkit越野车在这里
  var CORRECT_NEW =新的$ RegExp（re1）！== re1;
  
  if（_dereq_（58）&&（！CORRECT_NEW || _dereq_（64）（function（）{
    re2 [_dereq_（152）（'match'）] = false;
    // RegExp构造函数可以更改标志，IsRegExp可与@@ match一起正常工作
    返回$ RegExp（re1）！= re1 || $ RegExp（re2）== re2 || $ RegExp（re1，'i'）！='/ a / i';
  }）））{
    $ RegExp =函数RegExp（p，f）{
      var tiRE = $ RegExp的此实例；
      var piRE = isRegExp（p）;
      var fiU = f === undefined;
      返回！tiRE && piRE && p.constructor === $ RegExp && fiU？p
        ：InheritIfRequired（CORRECT_NEW
          ？新的基础（piRE &&！fiU？p.source：p，f）
          ：Base（（piRE = p instanceof $ RegExp）？p.source：p，piRE && fiU $ flags.call（p）：f）
        ，轮胎？这个：proto，$ RegExp）;
    };
    var proxy = function（key）{
      键入$ RegExp || dP（$ RegExp，key，{
        可配置：true，
        get：function（）{return Base [key]; }，
        设置：function（it）{Base [key] = it; }
      }）;
    };
    for（var keys = gOPN（Base），i = 0; keys.length> i;）proxy（keys [i ++]）;
    proto.constructor = $ RegExp;
    $ RegExp.prototype = proto;
    _dereq_（118）（全局，'RegExp'，$ RegExp）;
  }
  
  _dereq_（123）（'RegExp'）;
  
  }，{“ 103”：103，“ 118”：118，“ 123”：123，“ 152”：152，“ 58”：58，“ 64”：64，“ 66”：66，“ 70”：70 ，“ 75”：75，“ 82”：82，“ 99”：99}]，248：[function（_dereq_，module，exports）{
  “使用严格”；
  var regexpExec = _dereq_（120）;
  _dereq_（62）（{
    目标：“ RegExp”，
    原型：是的，
    强制：regexpExec！== /./.exec
  }，{
    执行：regexpExec
  }）;
  
  }，{“ 120”：120，“ 62”：62}]，249：[function（_dereq_，module，exports）{
  // 21.2.5.3获取RegExp.prototype.flags（）
  if（_dereq_（58）&& /./g.flags！='g'）_dereq_（99）.f（RegExp.prototype，'flags'，{
    可配置：true，
    得到：_dereq_（66）
  }）;
  
  }，{“ 58”：58，“ 66”：66，“ 99”：99}]，250：[function（_dereq_，module，exports）{
  “使用严格”；
  
  var anObject = _dereq_（38）;
  var toLength = _dereq_（141）;
  var advanceStringIndex = _dereq_（36）;
  var regExpExec = _dereq_（119）;
  
  // @@ match逻辑
  _dereq_（65）（'match'，1，function（defined，MATCH，$ match，也许CallNative）{
    返回[
      //`String.prototype.match`方法
      // https://tc39.github.io/ecma262/#sec-string.prototype.match
      函数match（regexp）{
        var O = defined（this）;
        var fn = regexp ==未定义？未定义：regexp [MATCH];
        返回fn！==未定义？fn.call（regexp，O）：新的RegExp（regexp）[MATCH]（String（O））;
      }，
      //`RegExp.prototype [@@ match]`方法
      // https://tc39.github.io/ecma262/#sec-regexp.prototype-@@match
      函数（正则表达式）{
        var res =也许CallNative（$ match，regexp，this）;
        如果（res.done）返回res.value;
        var rx = anObject（regexp）;
        var S = String（this）;
        如果（！rx.global）返回regExpExec（rx，S）;
        var fullUnicode = rx.unicode;
        rx.lastIndex = 0;
        var A = [];
        var n = 0;
        var结果；
        while（（结果= regExpExec（rx，S））！==空）{
          var matchStr = String（result [0]）;
          A [n] = matchStr;
          如果（matchStr ===''）rx.lastIndex = advanceStringIndex（S，toLength（rx.lastIndex），fullUnicode）;
          n ++;
        }
        返回n === 0吗？null：A;
      }
    ];
  }）;
  
  }，{“ 119”：119，“ 141”：141，“ 36”：36，“ 38”：38，“ 65”：65}]，251：[function（_dereq_，module，exports）{
  “使用严格”；
  
  var anObject = _dereq_（38）;
  var toObject = _dereq_（142）;
  var toLength = _dereq_（141）;
  var toInteger = _dereq_（139）;
  var advanceStringIndex = _dereq_（36）;
  var regExpExec = _dereq_（119）;
  var max = Math.max;
  var min = Math.min;
  var floor = Math.floor;
  var SUBSTITUTION_SYMBOLS = / \ $（[$＆`'] | \ d \ d？| <[^>] *>）/ g;
  var SUBSTITUTION_SYMBOLS_NO_NAMED = / \ $（[$＆`'] | \ d \ d？）/ g;
  
  var mightToString = function（it）{
    返回它===未定义？it：字符串（它）;
  };
  
  // @@替换逻辑
  _dereq_（65）（'replace'，2，function（defined，REPLACE，$ replace，也许CallNative）{
    返回[
      //`String.prototype.replace`方法
      // https://tc39.github.io/ecma262/#sec-string.prototype.replace
      函数replace（searchValue，replaceValue）{
        var O = defined（this）;
        var fn = searchValue ==未定义？未定义：searchValue [REPLACE];
        返回fn！==未定义
          ？fn.call（searchValue，O，replaceValue）
          ：$ replace.call（String（O），searchValue，replaceValue）;
      }，
      //`RegExp.prototype [@@ replace]`方法
      // https://tc39.github.io/ecma262/#sec-regexp.prototype-@@replace
      函数（regexp，replaceValue）{
        var res =也许CallNative（$ replace，regexp，this，replaceValue）;
        如果（res.done）返回res.value;
  
        var rx = anObject（regexp）;
        var S = String（this）;
        var FunctionalReplace = typeof replaceValue ==='function';
        如果（！functionalReplace）replaceValue = String（replaceValue）;
        var global = rx.global;
        如果（全局）{
          var fullUnicode = rx.unicode;
          rx.lastIndex = 0;
        }
        var结果= [];
        而（true）{
          var结果= regExpExec（rx，S）;
          如果（结果===空）中断;
          results.push（result）;
          如果（！global）中断;
          var matchStr = String（result [0]）;
          如果（matchStr ===''）rx.lastIndex = advanceStringIndex（S，toLength（rx.lastIndex），fullUnicode）;
        }
        var accumatedResult ='';
        var nextSourcePosition = 0;
        for（var i = 0; i <results.length; i ++）{
          结果=结果[i];
          var matched = String（result [0]）;
          var position = max（min（toInteger（result.index），S.length），0）;
          var captures = [];
          //注意：这等效于
          //捕获= result.slice（1）.map（maybeToString）
          //但由于某种原因`nativeSlice.call（result，1，result.length）`（在
          //在对原生数组进行切片时切片polyfill）在safari 9和
          //尝试调试时会导致崩溃（https://pastebin.com/N21QzeQA）。
          for（var j = 1; j <result.length; j ++）captures.push（maybeToString（result [j]））;
          var namedCaptures = result.groups;
          如果（functionalReplace）{
            var replacerArgs = [matched] .concat（captures，position，S）;
            如果（namedCaptures！==未定义）replacerArgs.push（namedCaptures）;
            var replacement = String（replaceValue.apply（undefined，replacerArgs））;
          }其他{
            替换= getSubstitution（匹配，S，位置，捕获，namedCaptures，replaceValue）;
          }
          如果（位置> = nextSourcePosition）{
            accumatedResult + = S.slice（nextSourcePosition，position）+替换；
            nextSourcePosition =位置+ matched.length;
          }
        }
        返回accumulatedResult + S.slice（nextSourcePosition）;
      }
    ];
  
      // https://tc39.github.io/ecma262/#sec-getsubstitution
    函数getSubstitution（matched，str，position，captures，namedCaptures，replacement）{
      var tailPos =位置+ matched.length;
      var m = captures.length;
      var符号= SUBSTITUTION_SYMBOLS_NO_NAMED；
      如果（namedCaptures！==未定义）{
        namedCaptures = toObject（namedCaptures）;
        符号= SUBSTITUTION_SYMBOLS；
      }
      return $ replace.call（替换，符号，函数（match，ch）{
        var capture;
        开关（ch.charAt（0））{
          情况“ $”：返回“ $”；
          情况“＆”：返回匹配项；
          情况'`'：返回str.slice（0，position）;
          情况“'”：返回str.slice（tailPos）;
          情况'<'：
            捕获= namedCaptures [ch.slice（1，-1）];
            打破;
          默认值：// \ d \ d？
            var n = + ch;
            如果（n === 0）返回匹配;
            如果（n> m）{
              var f = floor（n / 10）;
              如果（f === 0）返回匹配;
              如果（f <= m）返回捕获[f-1] ===未定义？ch.charAt（1）：捕获[f-1] + ch.charAt（1）;
              返回比赛；
            }
            捕获=捕获[n-1];
        }
        返回捕获===未定义？''：捕获;
      }）;
    }
  }）;
  
  }，{“ 119”：119，“ 139”：139，“ 141”：141，“ 142”：142，“ 36”：36，“ 38”：38，“ 65”：65}]，252：[函数（_dereq_，模块，出口）{
  “使用严格”；
  
  var anObject = _dereq_（38）;
  var sameValue = _dereq_（121）;
  var regExpExec = _dereq_（119）;
  
  // @@搜索逻辑
  _dereq_（65）（'search'，1，function（defined，SEARCH，$ search，也许CallNative）{
    返回[
      //`String.prototype.search`方法
      // https://tc39.github.io/ecma262/#sec-string.prototype.search
      函数搜索（regexp）{
        var O = defined（this）;
        var fn = regexp ==未定义？未定义：regexp [SEARCH];
        返回fn！==未定义？fn.call（regexp，O）：新的RegExp（regexp）[SEARCH]（String（O））;
      }，
      //`RegExp.prototype [@@ search]`方法
      // https://tc39.github.io/ecma262/#sec-regexp.prototype-@@search
      函数（正则表达式）{
        var res =也许CallNative（$ search，regexp，this）;
        如果（res.done）返回res.value;
        var rx = anObject（regexp）;
        var S = String（this）;
        var previousLastIndex = rx.lastIndex;
        如果（！sameValue（previousLastIndex，0））rx.lastIndex = 0;
        var结果= regExpExec（rx，S）;
        如果（！sameValue（rx.lastIndex，previousLastIndex））rx.lastIndex = previousLastIndex;
        返回结果=== null？-1：result.index;
      }
    ];
  }）;
  
  }，{“ 119”：119，“ 121”：121，“ 38”：38，“ 65”：65}]，253：[function（_dereq_，module，exports）{
  “使用严格”；
  
  var isRegExp = _dereq_（82）;
  var anObject = _dereq_（38）;
  var speciesConstructor = _dereq_（127）;
  var advanceStringIndex = _dereq_（36）;
  var toLength = _dereq_（141）;
  var callRegExpExec = _dereq_（119）;
  var regexpExec = _dereq_（120）;
  var failed = _dereq_（64）;
  var $ min = Math.min;
  var $ push = [] .push;
  var $ SPLIT ='split';
  var LENGTH ='长度';
  var LAST_INDEX ='lastIndex';
  var MAX_UINT32 = 0xffffffff;
  
  // babel-minify转译RegExp（'x'，'y'）-> / x / y会导致SyntaxError
  var SUPPORTS_Y =！fails（function（）{RegExp（MAX_UINT32，'y'）;}）;
  
  // @@分割逻辑
  _dereq_（65）（'split'，2，function（defined，SPLIT，$ split，也许CallNative）{
    var internalSplit;
    如果（
      'abbc'[$ SPLIT]（/（b）* /）[1] =='c'||
      'test'[$ SPLIT]（/（？：）/，-1）[LENGTH]！= 4 ||
      'ab'[$ SPLIT]（/（?: ab）* /）[LENGTH]！= 2 ||
      '。'[$ SPLIT]（/（。？）（。？）/）[LENGTH]！= 4 ||
      '。'[$ SPLIT]（/（）（）/）[LENGTH]> 1 ||
      ” [$ SPLIT]（/.?/）[LENGTH]
    ）{
      //基于es5-shim的实现，需要进行重做
      internalSplit =函数（分隔符，限制）{
        var string = String（this）;
        如果（分隔符===未定义&&限制=== 0）返回[]；
        //如果`separator`不是正则表达式，请使用本机拆分
        如果（！isRegExp（separator））返回$ split.call（字符串，分隔符，限制）;
        var输出= [];
        var标志=（separator.ignoreCase？'i'：''）+
                    （separator.multiline？'m'：''）+
                    （separator.unicode？'u'：''）+
                    （separator.sticky？'y'：''）;
        var lastLastIndex = 0;
        var splitLimit =限制===未定义？MAX_UINT32：限制>>> 0;
        //使`global`并通过处理副本来避免`lastIndex`问题
        var spacerCopy =新的RegExp（separator.source，标志+'g'）;
        var match，lastIndex，lastLength;
        while（match = regexpExec.call（separatorCopy，string））{
          lastIndex = spacerCopy [LAST_INDEX];
          如果（lastIndex> lastLastIndex）{
            output.push（string.slice（lastLastIndex，match.index））;
            如果（match [LENGTH]> 1 && match.index <string [LENGTH]）$ push.apply（output，match.slice（1））;
            lastLength = match [0] [LENGTH];
            lastLastIndex = lastIndex;
            如果（output [LENGTH]> = splitLimit）中断；
          }
          如果（separatorCopy [LAST_INDEX] === match.index）eparatorCopy [LAST_INDEX] ++; //避免无限循环
        }
        如果（lastLastIndex === string [LENGTH]）{
          if（lastLength ||！separatorCopy.test（''））output.push（''）;
        } else output.push（string.slice（lastLastIndex））;
        返回输出[LENGTH]> splitLimit？output.slice（0，splitLimit）：输出;
      };
    // V8查克拉
    } else if（'0'[$ SPLIT]（undefined，0）[LENGTH]）{
      internalSplit =函数（分隔符，限制）{
        返回分隔符===未定义&&限制=== 0吗？[]：$ split.call（此，分隔符，限制）；
      };
    }其他{
      internalSplit = $ split;
    }
  
    返回[
      //`String.prototype.split`方法
      // https://tc39.github.io/ecma262/#sec-string.prototype.split
      函数split（分隔符，限制）{
        var O = defined（this）;
        var splitter =分隔符==未定义？未定义：分隔符[SPLIT];
        返回拆分器！==未定义
          ？splitter.call（分隔符，O，限制）
          ：internalSplit.call（String（O），分隔符，限制）;
      }，
      //`RegExp.prototype [@@ split]`方法
      // https://tc39.github.io/ecma262/#sec-regexp.prototype-@@split
      //
      //注意：无法在不支持的引擎中正确填充
      //'y'标志。
      函数（正则表达式，限制）{
        var res = mayCallNative（internalSplit，regexp，this，limit，internalSplit！== $ split）;
        如果（res.done）返回res.value;
  
        var rx = anObject（regexp）;
        var S = String（this）;
        var C = speciesConstructor（rx，RegExp）;
  
        var unicodeMatching = rx.unicode;
        var标志=（rx.ignoreCase？'i'：''）+
                    （rx.multiline？'m'：''）+
                    （rx.unicode？'u'：''）+
                    （SUPPORTS_Y？'y'：'g'）;
  
        //需要^（？+ rx +），结合一些S切片，以
        //模拟y标志。
        var splitter = new C（SUPPORTS_Y？rx：'^（?:'+ rx.source +'）'，标志）;
        var lim =极限===未定义？MAX_UINT32：限制>>> 0;
        如果（lim === 0）返回[]；
        如果（S.length === 0）返回callRegExpExec（splitter，S）=== null？[S]：[]；
        var p = 0;
        var q = 0;
        var A = [];
        而（q <S.length）{
          splitter.lastIndex = SUPPORTS_Y吗？q：0；
          var z = callRegExpExec（splitter，SUPPORTS_Y？S：S.slice（q））;
          var e;
          如果（
            z === null ||
            （e = $ min（toLength（splitter.lastIndex +（SUPPORTS_Y？0：q）），S.length））=== p
          ）{
            q = advanceStringIndex（S，q，unicodeMatching）;
          }其他{
            A.push（S.slice（p，q））;
            如果（A.length === lim）返回A;
            for（var i = 1; i <= z.length-1; i ++）{
              A.push（z [i]）;
              如果（A.length === lim）返回A;
            }
            q = p = e;
          }
        }
        A.push（S.slice（p））;
        返回A；
      }
    ];
  }）;
  
  }，{“ 119”：119，“ 120”：120，“ 127”：127，“ 141”：141，“ 36”：36，“ 38”：38，“ 64”：64，“ 65”：65 ，“ 82”：82}]，254：[function（_dereq_，module，exports）{
  “使用严格”；
  _dereq_（249）;
  var anObject = _dereq_（38）;
  var $ flags = _dereq_（66）;
  var DESCRIPTORS = _dereq_（58）;
  var TO_STRING ='toString';
  var $ toString = /./[TO_STRING];
  
  var define = function（fn）{
    _dereq_（118）（RegExp.prototype，TO_STRING，fn，true）;
  };
  
  // ********* RegExp.prototype.toString（）
  if（_dereq_（64）（function（）{return $ toString.call（{source：'a'，flags：'b'}）！='/ a / b';}））{
    define（function toString（）{
      var R = anObject（this）;
      返回'/'.concat(R.source，'/'，
        R中的“标志”？R.flags：！DESCRIPTORS && RegExp的instance？$ flags.call（R）：未定义）;
    }）;
  // FF44- RegExp＃toString名称错误
  } else if（$ toString.name！= TO_STRING）{
    define（function toString（）{
      返回$ toString.call（this）;
    }）;
  }
  
  }，{“ 118”：118，“ 249”：249，“ 38”：38，“ 58”：58，“ 64”：64，“ 66”：66}]，255：[function（_dereq_，module，出口）{
  “使用严格”；
  var strong = _dereq_（49）;
  var validate = _dereq_（149）;
  var SET ='Set';
  
  // 23.2设置对象
  module.exports = _dereq_（51）（SET，函数（获取）{
    返回函数Set（）{return get（this，arguments.length> 0？arguments [0]：undefined）; };
  }，{
    // ******** Set.prototype.add（value）
    添加：函数add（value）{
      返回strong.def（validate（this，SET），value = value === 0？0：value，value）;
    }
  }，强）；
  
  }，{“ 149”：149，“ 49”：49，“ 51”：51}]，256：[function（_dereq_，module，exports）{
  “使用严格”；
  // B.2.3.2 String.prototype.anchor（name）
  _dereq_（131）（'anchor'，function（createHTML）{
    返回函数anchor（name）{
      返回createHTML（this，'a'，'name'，name）;
    };
  }）;
  
  }，{“ 131”：131}]，257：[function（_dereq_，module，exports）{
  “使用严格”；
  // B.2.3.3 String.prototype.big（）
  _dereq_（131）（'big'，function（createHTML）{
    返回函数big（）{
      返回createHTML（this，'big'，``，''）;
    };
  }）;
  
  }，{“ 131”：131}]，258：[function（_dereq_，module，exports）{
  “使用严格”；
  // B.2.3.4 String.prototype.blink（）
  _dereq_（131）（'blink'，function（createHTML）{
    返回函数blink（）{
      返回createHTML（this，'blink'，``，''）;
    };
  }）;
  
  }，{“ 131”：131}]，259：[function（_dereq_，module，exports）{
  “使用严格”；
  // B.2.3.5 String.prototype.bold（）
  _dereq_（131）（'bold'，function（createHTML）{
    返回函数bold（）{
      返回createHTML（this，'b'，''，''）;
    };
  }）;
  
  }，{“ 131”：131}]，260：[function（_dereq_，module，exports）{
  “使用严格”；
  var $ export = _dereq_（62）;
  var $ at = _dereq_（129）（false）;
  $ export（$ export.P，'String'，{
    // 21.1.3.3 String.prototype.codePointAt（pos）
    codePointAt：函数codePointAt（pos）{
      返回$ at（this，pos）;
    }
  }）;
  
  }，{“ 129”：129，“ 62”：62}]，261：[function（_dereq_，module，exports）{
  // 21.1.3.6 String.prototype.endsWith（searchString [，endPosition]）
  “使用严格”；
  var $ export = _dereq_（62）;
  var toLength = _dereq_（141）;
  var context = _dereq_（130）;
  var ENDS_WITH ='endsWith';
  var $ endsWith =''[ENDS_WITH];
  
  $ export（$ export.P + $ export.F * _dereq_（63）（ENDS_WITH），'String'，{
    endsWith：函数endsWith（searchString / *，endPosition = @length * /）{
      var that = context（this，searchString，ENDS_WITH）;
      var endPosition = arguments.length> 1吗？arguments [1]：未定义；
      var len = toLength（that.length）;
      var end = endPosition ===未定义？len：Math.min（toLength（endPosition），len）;
      var search = String（searchString）;
      返回$ endsWith
        ？$ endsWith.call（that，search，end）
        ：that.slice（end-search.length，end）===搜索;
    }
  }）;
  
  }，{“ 130”：130，“ 141”：141，“ 62”：62，“ 63”：63}]，262：[function（_dereq_，module，exports）{
  “使用严格”；
  // B.2.3.6 String.prototype.fixed（）
  _dereq_（131）（'fixed'，function（createHTML）{
    返回函数fixed（）{
      返回createHTML（this，'tt'，``，''）;
    };
  }）;
  
  }，{“ 131”：131}]，263：[function（_dereq_，module，exports）{
  “使用严格”；
  // B.2.3.7 String.prototype.fontcolor（color）
  _dereq_（131）（'fontcolor'，function（createHTML）{
    返回函数fontcolor（color）{
      返回createHTML（this，'font'，'color'，color）;
    };
  }）;
  
  }，{“ 131”：131}]，264：[function（_dereq_，module，exports）{
  “使用严格”；
  // B.2.3.8 String.prototype.fontsize（size）
  _dereq_（131）（'fontsize'，function（createHTML）{
    返回函数fontsize（size）{
      返回createHTML（this，'font'，'size'，size）;
    };
  }）;
  
  }，{“ 131”：131}]，265：[function（_dereq_，module，exports）{
  var $ export = _dereq_（62）;
  var toAbsoluteIndex = _dereq_（137）;
  var fromCharCode = String.fromCharCode;
  var $ fromCodePoint = String.fromCodePoint;
  
  //长度应为1，旧的FF问题
  $ export（$ export.S + $ export.F *（!! $ fromCodePoint && $ fromCodePoint.length！= 1），'String'，{
    // 21.1.2.2 String.fromCodePoint（... codePoints）
    fromCodePoint：function fromCodePoint（x）{//禁止行禁用无变量
      var res = [];
      var aLen = arguments.length;
      var i = 0;
      变码
      while（aLen> i）{
        代码= +参数[i ++]；
        如果（toAbsoluteIndex（code，0x10ffff）！==代码）抛出RangeError（code +'不是有效的代码点'）;
        res.push（代码<0x10000
          ？fromCharCode（代码）
          ：fromCharCode（（（（code-= 0x10000）>> 10）+ 0xd800，代码％0x400 + 0xdc00）
        ）;
      }返回res.join（''）;
    }
  }）;
  
  }，{“ 137”：137，“ 62”：62}]，266：[function（_dereq_，module，exports）{
  // 21.1.3.7 String.prototype.includes（searchString，position = 0）
  “使用严格”；
  var $ export = _dereq_（62）;
  var context = _dereq_（130）;
  var INCLUDES ='includes';
  
  $ export（$ export.P + $ export.F * _dereq_（63）（INCLUDES），'String'，{
    include：函数include（searchString / *，position = 0 * /）{
      返回!!〜context（this，searchString，INCLUDES）
        .indexOf（searchString，arguments.length> 1？arguments [1]：未定义）；
    }
  }）;
  
  }，{“ 130”：130，“ 62”：62，“ 63”：63}]，267：[function（_dereq_，module，exports）{
  “使用严格”；
  // B.2.3.9 String.prototype.italics（）
  _dereq_（131）（'斜体'，函数（createHTML）{
    返回函数italics（）{
      返回createHTML（this，'i'，``，''）;
    };
  }）;
  
  }，{“ 131”：131}]，268：[function（_dereq_，module，exports）{
  “使用严格”；
  var $ at = _dereq_（129）（true）;
  
  // 21.1.3.27 String.prototype [@@ iterator]（）
  _dereq_（85）（String，'String'，function（iterated）{
    _t =字符串（迭代）; //目标
    _i = 0; //下一个索引
  // 21.1.5.2.1％StringIteratorPrototype％.next（）
  }，函数（）{
    var O = this._t;
    var index = this._i;
    var point;
    if（index> = O.length）返回{值：未定义，完成：true};
    点= $ at（O，index）;
    this._i + = point.length;
    返回{值：点，完成：假};
  }）;
  
  }，{“ 129”：129，“ 85”：85}]，269：[function（_dereq_，module，exports）{
  “使用严格”；
  // B.2.3.10 String.prototype.link（url）
  _dereq_（131）（'link'，函数（createHTML）{
    返回函数link（url）{
      返回createHTML（this，'a'，'href'，url）;
    };
  }）;
  
  }，{“ 131”：131}]，270：[function（_dereq_，module，exports）{
  var $ export = _dereq_（62）;
  var toIObject = _dereq_（140）;
  var toLength = _dereq_（141）;
  
  $ export（$ export.S，'String'，{
    // 21.1.2.4 String.raw（callSite，... substitutions）
    raw：函数raw（callSite）{
      var tpl = toIObject（callSite.raw）;
      var len = toLength（tpl.length）;
      var aLen = arguments.length;
      var res = [];
      var i = 0;
      而（len> i）{
        res.push（String（tpl [i ++]））;
        如果（i <aLen）res.push（String（arguments [i]））;
      }返回res.join（''）;
    }
  }）;
  
  }，{“ 140”：140，“ 141”：141，“ 62”：62}]，271：[function（_dereq_，module，exports）{
  var $ export = _dereq_（62）;
  
  $ export（$ export.P，'String'，{
    // 21.1.3.13 String.prototype.repeat（count）
    重复：_dereq_（133）
  }）;
  
  }，{“ 133”：133，“ 62”：62}]，272：[function（_dereq_，module，exports）{
  “使用严格”；
  // B.2.3.11 String.prototype.small（）
  _dereq_（131）（'small'，function（createHTML）{
    返回函数small（）{
      返回createHTML（this，'small'，``，''）;
    };
  }）;
  
  }，{“ 131”：131}]，273：[function（_dereq_，module，exports）{
  // 21.1.3.18 String.prototype.startsWith（searchString [，position]）
  “使用严格”；
  var $ export = _dereq_（62）;
  var toLength = _dereq_（141）;
  var context = _dereq_（130）;
  var STARTS_WITH ='startsWith';
  var $ startsWith =''[STARTS_WITH];
  
  $ export（$ export.P + $ export.F * _dereq_（63）（STARTS_WITH），'String'，{
    startsWith：函数startsWith（searchString / *，position = 0 * /）{
      var that = context（this，searchString，STARTS_WITH）;
      var index = toLength（Math.min（arguments.length> 1？arguments [1]：undefined，that.length））;
      var search = String（searchString）;
      返回$ startsWith
        ？$ startsWith.call（that，search，index）
        ：that.slice（index，index + search.length）===搜索；
    }
  }）;
  
  }，{“ 130”：130，“ 141”：141，“ 62”：62，“ 63”：63}]，274：[function（_dereq_，module，exports）{
  “使用严格”；
  // B.2.3.12 String.prototype.strike（）
  _dereq_（131）（'strike'，function（createHTML）{
    返回函数strike（）{
      返回createHTML（this，'strike'，``，''）;
    };
  }）;
  
  }，{“ 131”：131}]，275：[function（_dereq_，module，exports）{
  “使用严格”；
  // B.2.3.13 String.prototype.sub（）
  _dereq_（131）（'sub'，函数（createHTML）{
    返回函数sub（）{
      返回createHTML（this，'sub'，''，''）;
    };
  }）;
  
  }，{“ 131”：131}]，276：[function（_dereq_，module，exports）{
  “使用严格”；
  // B.2.3.14 String.prototype.sup（）
  _dereq_（131）（'sup'，函数（createHTML）{
    返回函数sup（）{
      返回createHTML（this，'sup'，``，''）;
    };
  }）;
  
  }，{“ 131”：131}]，277：[function（_dereq_，module，exports）{
  “使用严格”；
  // ********* String.prototype.trim（）
  _dereq_（134）（'trim'，function（$ trim）{
    返回函数trim（）{
      返回$ trim（this，3）;
    };
  }）;
  
  }，{“ 134”：134}]，278：[function（_dereq_，module，exports）{
  “使用严格”；
  // ECMAScript 6符号填充
  var global = _dereq_（70）;
  var has = _dereq_（71）;
  var DESCRIPTORS = _dereq_（58）;
  var $ export = _dereq_（62）;
  var redefine = _dereq_（118）;
  var META = _dereq_（94）.KEY;
  var $ fails = _dereq_（64）;
  var shared = _dereq_（126）;
  var setToStringTag = _dereq_（124）;
  var uid = _dereq_（147）;
  var wks = _dereq_（152）;
  var wksExt = _dereq_（151）;
  var wksDefine = _dereq_（150）;
  var enumKeys = _dereq_（61）;
  var isArray = _dereq_（79）;
  var anObject = _dereq_（38）;
  var isObject = _dereq_（81）;
  var toObject = _dereq_（142）;
  var toIObject = _dereq_（140）;
  var toPrimitive = _dereq_（143）;
  var createDesc = _dereq_（116）;
  var _create = _dereq_（98）;
  var gOPNExt = _dereq_（102）;
  var $ GOPD = _dereq_（101）;
  var $ GOPS = _dereq_（104）;
  var $ DP = _dereq_（99）;
  var $ keys = _dereq_（107）;
  var gOPD = $ GOPD.f;
  var dP = $ DP.f;
  var gOPN = gOPNExt.f;
  var $ Symbol = global.Symbol;
  var $ JSON = global.JSON;
  var _stringify = $ JSON && $ JSON.stringify;
  var PROTOTYPE ='prototype';
  var HIDDEN = wks（'_ hidden'）;
  var TO_PRIMITIVE = wks（'toPrimitive'）;
  var isEnum = {} .propertyIsEnumerable;
  var SymbolRegistry = shared（'symbol-registry'）;
  var AllSymbols = shared（'symbols'）;
  var OPSymbols = shared（'op-symbols'）;
  var ObjectProto = Object [PROTOTYPE];
  var USE_NATIVE = typeof $ Symbol =='function'&& !! $ GOPS.f;
  var QObject = global.QObject;
  //不要在Qt脚本中使用设置器，https：//github.com/zloirock/core-js/issues/173
  var setter =！QObject || ！QObject [PROTOTYPE] || ！QObject [PROTOTYPE] .findChild;
  
  //旧版Android的备用广告，https：//code.google.com/p/v8/issues/detail？id = 687
  var setSymbolDesc = DESCRIPTORS && $ fails（function（）{
    return _create（dP（{}，'a'，{
      get：function（）{return dP（this，'a'，{value：7}）。a; }
    }））。a！= 7;
  }）？功能（它，键，D）{
    var protoDesc = gOPD（ObjectProto，key）;
    如果（protoDesc）删除ObjectProto [key];
    dP（it，key，D）;
    如果（protoDesc && it！== ObjectProto）dP（ObjectProto，key，protoDesc）;
  }：dP；
  
  var wrap =函数（标签）{
    var sym = AllSymbols [tag] = _create（$ Symbol [PROTOTYPE]）;
    sym._k =标签；
    返回符号
  };
  
  var isSymbol = USE_NATIVE && type $ Symbol.iterator =='symbol'吗？功能（它）{
    返回typeof =='symbol';
  }：函数（它）{
    返回它的instanceof $ Symbol;
  };
  
  var $ defineProperty = function defineProperty（it，key，D）{
    如果（它=== ObjectProto）$ defineProperty（OPSymbols，键，D）;
    anObject（it）;
    key = toPrimitive（key，true）;
    anObject（D）;
    if（has（AllSymbols，key））{
      如果（！D。可枚举）{
        if（！ha（it，HIDDEN））dP（it，HIDDEN，createDesc（1，{}）））;
        it [HIDDEN] [key] = true;
      }其他{
        if（has（it，HIDDEN）&& it [HIDDEN] [key]）it [HIDDEN] [key] = false;
        D = _create（D，{枚举：createDesc（0，false）}）;
      } return setSymbolDesc（it，key，D）;
    } return dP（it，key，D）;
  };
  var $ defineProperties =函数defineProperties（it，P）{
    anObject（it）;
    var keys = enumKeys（P = toIObject（P））;
    var i = 0;
    var l = keys.length;
    var key;
    while（l> i）$ defineProperty（it，key = keys [i ++]，P [key]）;
    把它返还;
  };
  var $ create = function create（it，P）{
    返回P ===未定义？_create（it）：$ defineProperties（_create（it），P）;
  };
  var $ propertyIsEnumerable =函数propertyIsEnumerable（key）{
    var E = isEnum.call（this，key = toPrimitive（key，true））;
    if（this === ObjectProto && has（AllSymbols，key）&&！has（OPSymbols，key））返回false;
    返回E || ！有（此键）|| ！has（AllSymbols，key）|| 有（this，HIDDEN）&& this [HIDDEN] [key]吗？E：真；
  };
  var $ getOwnPropertyDescriptor = function getOwnPropertyDescriptor（it，key）{
    它= toIObject（it）;
    key = toPrimitive（key，true）;
    如果（它=== ObjectProto && has（AllSymbols，key）&&！has（OPSymbols，key））返回;
    var D = gOPD（it，key）;
    if（D && has（AllSymbols，key）&&！（has（it，HIDDEN）&& it [HIDDEN] [key]））D.enumerable = true;
    返回D；
  };
  var $ getOwnPropertyNames = function getOwnPropertyNames（it）{
    var名称= gOPN（toIObject（it））;
    var result = [];
    var i = 0;
    var key;
    while（names.length> i）{
      如果（！has（AllSymbols，键=名称[i ++]）&&键！=隐藏&&键！=元）result.push（key）;
    }返回结果；
  };
  var $ getOwnPropertySymbols =函数getOwnPropertySymbols（it）{
    var IS_OP = it === ObjectProto;
    var名称= gOPN（IS_OP？OPSymbols：toIObject（it））;
    var result = [];
    var i = 0;
    var key;
    while（names.length> i）{
      if（has（AllSymbols，key = names [i ++]）&&（IS_OP？has（ObjectProto，key）：true））result.push（AllSymbols [key]）;
    }返回结果；
  };
  
  // ******** Symbol（[description]）
  如果（！USE_NATIVE）{
    $ Symbol =函数Symbol（）{
      如果（此$ Symbol实例）抛出TypeError（'Symbol不是构造函数！'）;
      var tag = uid（arguments.length> 0？arguments [0]：undefined）;
      var $ set =函数（值）{
        if（this === Obje ctProto）$ set.call（OPSymbols，value）;
        if（有（this，HIDDEN）&& has（this [HIDDEN]，tag））this [HIDDEN] [tag] = false;
        setSymbolDesc（this，tag，createDesc（1，value））;
      };
      如果（DESCRIPTORS && setter）setSymbolDesc（ObjectProto，tag，{可配置：true，设置：$ set}）;
      返回wrap（tag）;
    };
    redefine（$ Symbol [PROTOTYPE]，'toString'，function toString（）{
      返回this._k;
    }）;
  
    $ GOPD.f = $ getOwnPropertyDescriptor;
    $ DP.f = $ defineProperty;
    _dereq_（103）.f = gOPNExt.f = $ getOwnPropertyNames;
    _dereq_（108）.f = $ propertyIsEnumerable;
    $ GOPS.f = $ getOwnPropertySymbols;
  
    如果（DESCRIPTORS &&！_dereq_（89））{
      redefine（ObjectProto，'propertyIsEnumerable'，$ propertyIsEnumerable，true）;
    }
  
    wksExt.f =函数（名称）{
      返回wrap（wks（name））;
    };
  }
  
  $ export（$ export.G + $ export.W + $ export.F *！USE_NATIVE，{符号：$ Symbol}）;
  
  对于（var es6Symbols =（
    // ********，********，********，********，********，********，********0，********1，********2，********3，********4
    “ hasInstance，isConcatSpread，迭代器，匹配项，替换，搜索，物种，拆分，toPrimitive，toStringTag，不可定范围”
  ）.split（'，'），j = 0; es6Symbols.length> j;）wks（es6Symbols [j ++]）;
  
  for（var wellKnownSymbols = $ keys（wks.store），k = 0; wellKnownSymbols.length> k;）wksDefine（wellKnownSymbols [k ++]）;
  
  $ export（$ export.S + $ export.F *！USE_NATIVE，'Symbol'，{
    // ******** Symbol.for（密钥）
    'for'：功能（键）{
      返回has（SymbolRegistry，键+ =''）
        ？SymbolRegistry [键]
        ：SymbolRegistry [key] = $ Symbol（key）;
    }，
    // 19.4.2.5 Symbol.keyFor（sym）
    keyFor：函数keyFor（sym）{
      如果（！isSymbol（sym））抛出TypeError（sym +'不是符号！'）;
      对于（SymbolRegistry中的var键），如果（SymbolRegistry [key] === sym）返回键；
    }，
    useSetter：function（）{setter = true; }，
    useSimple：function（）{setter = false; }
  }）;
  
  $ export（$ export.S + $ export.F *！USE_NATIVE，'对象'，{
    // ******** Object.create（O [，Properties]）
    创建：$ create，
    // ******** Object.defineProperty（O，P，Attributes）
    defineProperty：$ defineProperty，
    // ******** Object.defineProperties（O，Properties）
    defineProperties：$ defineProperties，
    // ******** Object.getOwnPropertyDescriptor（O，P）
    getOwnPropertyDescriptor：$ getOwnPropertyDescriptor，
    // ******** Object.getOwnPropertyNames（O）
    getOwnPropertyNames：$ getOwnPropertyNames，
    // ******** Object.getOwnPropertySymbols（O）
    getOwnPropertySymbols：$ getOwnPropertySymbols
  }）;
  
  // Chrome 38和39ʻObject.getOwnPropertySymbols`在基本元素上失败
  // https://bugs.chromium.org/p/v8/issues/detail?id=3443
  var FAILS_ON_PRIMITIVES = $ fails（function（）{$ GOPS.f（1）;}）;
  
  $ export（$ export.S + $ export.F * FAILS_ON_PRIMITIVES，'Object'，{
    getOwnPropertySymbols：函数getOwnPropertySymbols（it）{
      返回$ GOPS.f（toObject（it））;
    }
  }）;
  
  // 24.3.2 JSON.stringify（value [，replacer [，space]]）
  $ JSON && $ export（$ export.S + $ export.F *（！USE_NATIVE || $ fails（function（）{
    var S = $ Symbol（）;
    // MS Edge将符号值转换为{}
    // WebKit将符号值转换为null的JSON
    // V8抛出框状符号
    返回_stringify（[S]）！='[null]'|| _stringify（{a：S}）！='{}'|| _stringify（Object（S））！='{}';
  }）），'JSON'，{
    stringify：函数stringify（it）{
      var args = [it];
      var i = 1;
      var replacer，$ replacer;
      while（arguments.length> i）args.push（arguments [i ++]）;
      $ replacer = replacer = args [1];
      如果（！isObject（replacer）&& it === undefined || isSymbol（it））返回；// IE8返回未定义的字符串
      if（！isArray（replacer））replacer = function（key，value）{
        if（typeof $ replacer =='function'）value = $ replacer.call（this，key，value）;
        如果（！isSymbol（value））返回值;
      };
      args [1] =替换程序；
      返回_stringify.apply（$ JSON，args）;
    }
  }）;
  
  // 19.4.3.4 Symbol.prototype [@@ toPrimitive]（提示）
  $ Symbol [PROTOTYPE] [TO_PRIMITIVE] || _dereq_（72）（$ Symbol [PROTOTYPE]，TO_PRIMITIVE，$ Symbol [PROTOTYPE] .valueOf）;
  // 19.4.3.5 Symbol.prototype [@@ toStringTag]
  setToStringTag（$ Symbol，'Symbol'）;
  // 20.2.1.9 Math [@@ toStringTag]
  setToStringTag（Math，'Math'，true）;
  // 24.3.3 JSON [@@ toStringTag]
  setToStringTag（global.JSON，'JSON'，true）;
  
  }，{“ 101”：101，“ 102”：102，“ 103”：103，“ 104”：104，“ 107”：107，“ 108”：108，“ 116”：116，“ 118”：118 ，“ 124”：124，“ 126”：126，“ 140”：140，“ 142”：142，“ 143”：143，“ 147”：147，“ 150”：150，“ 151”：151，“ 152“：152，” 38“：38，” 58“：58，” 61“：61，” 62“：62，” 64“：64，” 70“：70，” 71“：71，” 72“ ：72，“ 79”：79，“ 81”：81，“ 89”：89，“ 94”：94，“ 98”：98，“ 99”：99}]，279：[function（_dereq_，module，出口）{
  “使用严格”；
  var $ export = _dereq_（62）;
  var $ typed = _dereq_（146）;
  var buffer = _dereq_（145）;
  var anObject = _dereq_（38）;
  var toAbsoluteIndex = _dereq_（137）;
  var toLength = _dereq_（141）;
  var isObject = _dereq_（81）;
  var ArrayBuffer = _dereq_（70）.ArrayBuffer;
  var speciesConstructor = _dereq_（127）;
  var $ ArrayBuffer = buffer.ArrayBuffer;
  var $ DataView = buffer.DataView;
  var $ isView = $ typed.ABV && ArrayBuffer.isView;
  var $ slice = $ ArrayBuffer.prototype.slice;
  var VIEW = $ typed.VIEW;
  var ARRAY_BUFFER ='ArrayBuffer';
  
  $ export（$ export.G + $ export.W + $ export.F *（ArrayBuffer！== $ ArrayBuffer），{ArrayBuffer：$ ArrayBuffer}）;
  
  $ export（$ export.S + $ export.F *！$ typed.CONSTR，ARRAY_BUFFER，{
    // 24.1.3.1 ArrayBuffer.isView（arg）
    isView：函数isView（it）{
      返回$ isView && $ isView（it）|| isObject（it）&&在其中查看；
    }
  }）;
  
  $ export（$ export.P + $ export.U + $ export.F * _dereq_（64）（function（）{
    返回！new $ ArrayBuffer（2）.slice（1，undefined）.byteLength;
  }），ARRAY_BUFFER，{
    // 24.1.4.3 ArrayBuffer.prototype.slice（开始，结束）
    slice：函数slice（开始，结束）{
      如果（$ slice！== undefined && end === undefined）返回$ slice.call（anObject（this），start）; // FF修复
      var len = anObject（this）.byteLength;
      var first = toAbsoluteIndex（start，len）;
      var fin = toAbsoluteIndex（end === undefined？len：end，len）;
      var result = new（speciesConstructor（this，$ ArrayBuffer））（toLength（fin-first））;
      var viewS = new $ DataView（this）;
      var viewT = new $ DataView（result）;
      var index = 0;
      而（第一<鳍）{
        viewT.setUint8（index ++，viewS.getUint8（first ++））;
      }返回结果；
    }
  }）;
  
  _dereq_（123）（ARRAY_BUFFER）;
  
  }，{“ 123”：123，“ 127”：127，“ 137”：137，“ 141”：141，“ 145”：145，“ 146”：146，“ 38”：38，“ 62”：62 ，“ 64”：64，“ 70”：70，“ 81”：81}]，280：[function（_dereq_，module，exports）{
  var $ export = _dereq_（62）;
  $ export（$ export.G + $ export.W + $ export.F *！_dereq_（146）.ABV，{
    数据视图：_dereq_（145）.DataView
  }）;
  
  }，{“ 145”：145，“ 146”：146，“ 62”：62}]，281：[function（_dereq_，module，exports）{
  _dereq_（144）（'Float32'，4，function（init）{
    返回函数Float32Array（data，byteOffset，length）{
      返回init（this，data，byteOffset，length）;
    };
  }）;
  
  }，{“ 144”：144}]，282：[function（_dereq_，module，exports）{
  _dereq_（144）（'Float64'，8，function（init）{
    返回函数Float64Array（data，byteOffset，length）{
      返回init（this，data，byteOffset，length）;
    };
  }）;
  
  }，{“ 144”：144}]，283：[function（_dereq_，module，exports）{
  _dereq_（144）（'Int16'，2，function（init）{
    返回函数Int16Array（data，byteOffset，length）{
      返回init（this，data，byteOffset，length）;
    };
  }）;
  
  }，{“ 144”：144}]，284：[function（_dereq_，module，exports）{
  _dereq_（144）（'Int32'，4，function（init）{
    返回函数Int32Array（data，byteOffset，length）{
      返回init（this，data，byteOffset，length）;
    };
  }）;
  
  }，{“ 144”：144}]，285：[function（_dereq_，module，exports）{
  _dereq_（144）（'Int8'，1，function（init）{
    返回函数Int8Array（data，byteOffset，length）{
      返回init（this，data，byteOffset，length）;
    };
  }）;
  
  }，{“ 144”：144}]，286：[function（_dereq_，module，exports）{
  _dereq_（144）（'Uint16'，2，function（init）{
    返回函数Uint16Array（data，byteOffset，length）{
      返回init（this，data，byteOffset，length）;
    };
  }）;
  
  }，{“ 144”：144}]，287：[function（_dereq_，module，exports）{
  _dereq_（144）（'Uint32'，4，function（init）{
    返回函数Uint32Array（data，byteOffset，length）{
      返回init（this，data，byteOffset，length）;
    };
  }）;
  
  }，{“ 144”：144}]，288：[function（_dereq_，module，exports）{
  _dereq_（144）（'Uint8'，1，function（init）{
    返回函数Uint8Array（data，byteOffset，length）{
      返回init（this，data，byteOffset，length）;
    };
  }）;
  
  }，{“ 144”：144}]，289：[function（_dereq_，module，exports）{
  _dereq_（144）（'Uint8'，1，function（init）{
    返回函数Uint8ClampedArray（data，byteOffset，length）{
      返回init（this，data，byteOffset，length）;
    };
  }，是对的）；
  
  }，{“ 144”：144}]，290：[function（_dereq_，module，exports）{
  “使用严格”；
  var global = _dereq_（70）;
  var each = _dereq_（42）（0）;
  var redefine = _dereq_（118）;
  var meta = _dereq_（94）;
  var Assign = _dereq_（97）;
  var weak = _dereq_（50）;
  var isObject = _dereq_（81）;
  var validate = _dereq_（149）;
  var NATIVE_WEAK_MAP = _dereq_（149）;
  var IS_IE11 =！global.ActiveXObject &&'ActiveXObject'in global;
  var WEAK_MAP ='WeakMap';
  var getWeak = meta.getWeak;
  var isExtensible = Object.isExtensible;
  var uncaughtFrozenStore = weak.ufstore;
  var InternalMap;
  
  var wrapper = function（get）{
    返回函数WeakMap（）{
      返回get（this，arguments.length> 0？arguments [0]：未定义）;
    };
  };
  
  var方法= {
    // 23.3.3.3 WeakMap.prototype.get（key）
    get：function get（key）{
      如果（isObject（key））{
        var data = getWeak（key）;
        如果（data === true）返回uncaughtFrozenStore（validate（this，WEAK_MAP））。get（key）;
        返回数据？数据[this._i]：未定义；
      }
    }，
    // 23.3.3.5 WeakMap.prototype.set（key，value）
    设置：功能集（键，值）{
      返回weak.def（validate（this，WEAK_MAP），密钥，值）;
    }
  };
  
  // 23.3 WeakMap对象
  var $ WeakMap = module.exports = _dereq_（51）（WEAK_MAP，包装器，方法，弱，真，真）;
  
  // IE11 WeakMap冻结键修复
  如果（NATIVE_WEAK_MAP && IS_IE11）{
    InternalMap = weak.getConstructor（wrapper，WEAK_MAP）;
    分配（InternalMap.prototype，方法）;
    meta.NEED = true;
    each（[['delete'，'has'，'get'，'set']，function（key）{
      var proto = $ WeakMap.prototype;
      var method = proto [key];
      重新定义（原型，键，函数（a，b）{
        //将冻结的对象存储在内部weakmap匀场上
        如果（isObject（a）&&！isE​​xtensible（a））{
          如果（！this._f）this._f = new InternalMap（）;
          var结果= this._f [key]（a，b）;
          返回键=='设置' 结果：
        //将其余所有内容存储在本机weakmap上
        } return method.call（this，a，b）;
      }）;
    }）;
  }
  
  }，{“ 118”：118，“ 149”：149，“ 42”：42，“ 50”：50，“ 51”：51，“ 70”：70，“ 81”：81，“ 94”：94 ，“ 97”：97}]，291：[function（_dereq_，module，exports）{
  “使用严格”；
  var weak = _dereq_（50）;
  var validate = _dereq_（149）;
  var WEAK_SET ='WeakSet';
  
  // 23.4 WeakSet对象
  _dereq_（51）（WEAK_SET，函数（获取）{
    返回函数WeakSet（）{返回get（this，arguments.length> 0？arguments [0]：undefined）; };
  }，{
    // 23.4.3.1 WeakSet.prototype.add（值）
    添加：函数add（value）{
      返回weak.def（validate（this，WEAK_SET），value，true）;
    }
  }，弱，假，真）；
  
  }，{“ 149”：149，“ 50”：50，“ 51”：51}]，292：[function（_dereq_，module，exports）{
  “使用严格”；
  // https://tc39.github.io/proposal-flatMap/#sec-Array.prototype.flatMap
  var $ export = _dereq_（62）;
  var flattenIntoArray = _dereq_（67）;
  var toObject = _dereq_（142）;
  var toLength = _dereq_（141）;
  var aFunction = _dereq_（33）;
  var arraySpeciesCreate = _dereq_（45）;
  
  $ export（$ export.P，'Array'，{
    flatMap: function flatMap(callbackfn /* , thisArg */) {
      var O = toObject(this);
      var sourceLen, A;
      aFunction(callbackfn);
      sourceLen = toLength(O.length);
      A = arraySpeciesCreate(O, 0);
      flattenIntoArray(A, O, O, sourceLen, 0, 1, callbackfn, arguments[1]);
      return A;
    }
  });
  
  _dereq_(35)('flatMap');
  
  },{"141":141,"142":142,"33":33,"35":35,"45":45,"62":62,"67":67}],293:[function(_dereq_,module,exports){
  'use strict';
  // https://github.com/tc39/Array.prototype.includes
  var $export = _dereq_(62);
  var $includes = _dereq_(41)(true);
  
  $export($export.P, 'Array', {
    includes: function includes(el /* , fromIndex = 0 */) {
      return $includes(this, el, arguments.length > 1 ? arguments[1] : undefined);
    }
  });
  
  _dereq_(35)('includes');
  
  },{"35":35,"41":41,"62":62}],294:[function(_dereq_,module,exports){
  // https://github.com/tc39/proposal-object-values-entries
  var $export = _dereq_(62);
  var $entries = _dereq_(110)(true);
  
  $export($export.S, 'Object', {
    entries: function entries(it) {
      return $entries(it);
    }
  });
  
  },{"110":110,"62":62}],295:[function(_dereq_,module,exports){
  // https://github.com/tc39/proposal-object-getownpropertydescriptors
  var $export = _dereq_(62);
  var ownKeys = _dereq_(111);
  var toIObject = _dereq_(140);
  var gOPD = _dereq_(101);
  var createProperty = _dereq_(53);
  
  $export($export.S, 'Object', {
    getOwnPropertyDescriptors: function getOwnPropertyDescriptors(object) {
      var O = toIObject(object);
      var getDesc = gOPD.f;
      var keys = ownKeys(O);
      var result = {};
      var i = 0;
      var key, desc;
      while (keys.length > i) {
        desc = getDesc(O, key = keys[i++]);
        if (desc !== undefined) createProperty(result, key, desc);
      }
      return result;
    }
  });
  
  },{"101":101,"111":111,"140":140,"53":53,"62":62}],296:[function(_dereq_,module,exports){
  // https://github.com/tc39/proposal-object-values-entries
  var $export = _dereq_(62);
  var $values = _dereq_(110)(false);
  
  $export($export.S, 'Object', {
    values: function values(it) {
      return $values(it);
    }
  });
  
  },{"110":110,"62":62}],297:[function(_dereq_,module,exports){
  // https://github.com/tc39/proposal-promise-finally
  'use strict';
  var $export = _dereq_(62);
  var core = _dereq_(52);
  var global = _dereq_(70);
  var speciesConstructor = _dereq_(127);
  var promiseResolve = _dereq_(115);
  
  $export($export.P + $export.R, 'Promise', { 'finally': function (onFinally) {
    var C = speciesConstructor(this, core.Promise || global.Promise);
    var isFunction = typeof onFinally == 'function';
    return this.then(
      isFunction ? function (x) {
        return promiseResolve(C, onFinally()).then(function () { return x; });
      } : onFinally,
      isFunction ? function (e) {
        return promiseResolve(C, onFinally()).then(function () { throw e; });
      } : onFinally
    );
  } });
  
  },{"115":115,"127":127,"52":52,"62":62,"70":70}],298:[function(_dereq_,module,exports){
  'use strict';
  // https://github.com/tc39/proposal-string-pad-start-end
  var $export = _dereq_(62);
  var $pad = _dereq_(132);
  var userAgent = _dereq_(148);
  
  // https://github.com/zloirock/core-js/issues/280
  var WEBKIT_BUG = /Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(userAgent);
  
  $export($export.P + $export.F * WEBKIT_BUG, 'String', {
    padEnd: function padEnd(maxLength /* , fillString = ' ' */) {
      return $pad(this, maxLength, arguments.length > 1 ? arguments[1] : undefined, false);
    }
  });
  
  },{"132":132,"148":148,"62":62}],299:[function(_dereq_,module,exports){
  'use strict';
  // https://github.com/tc39/proposal-string-pad-start-end
  var $export = _dereq_(62);
  var $pad = _dereq_(132);
  var userAgent = _dereq_(148);
  
  // https://github.com/zloirock/core-js/issues/280
  var WEBKIT_BUG = /Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(userAgent);
  
  $export($export.P + $export.F * WEBKIT_BUG, 'String', {
    padStart: function padStart(maxLength /* , fillString = ' ' */) {
      return $pad(this, maxLength, arguments.length > 1 ? arguments[1] : undefined, true);
    }
  });
  
  },{"132":132,"148":148,"62":62}],300:[function(_dereq_,module,exports){
  'use strict';
  // https://github.com/sebmarkbage/ecmascript-string-left-right-trim
  _dereq_(134)('trimLeft', function ($trim) {
    return function trimLeft() {
      return $trim(this, 1);
    };
  }, 'trimStart');
  
  },{"134":134}],301:[function(_dereq_,module,exports){
  'use strict';
  // https://github.com/sebmarkbage/ecmascript-string-left-right-trim
  _dereq_(134)('trimRight', function ($trim) {
    return function trimRight() {
      return $trim(this, 2);
    };
  }, 'trimEnd');
  
  },{"134":134}],302:[function(_dereq_,module,exports){
  _dereq_(150)('asyncIterator');
  
  },{"150":150}],303:[function(_dereq_,module,exports){
  var $iterators = _dereq_(164);
  var getKeys = _dereq_(107);
  var redefine = _dereq_(118);
  var global = _dereq_(70);
  var hide = _dereq_(72);
  var Iterators = _dereq_(88);
  var wks = _dereq_(152);
  var ITERATOR = wks('iterator');
  var TO_STRING_TAG = wks('toStringTag');
  var ArrayValues = Iterators.Array;
  
  var DOMIterables = {
    CSSRuleList: true, // TODO: Not spec compliant, should be false.
    CSSStyleDeclaration: false,
    CSSValueList: false,
    ClientRectList: false,
    DOMRectList: false,
    DOMStringList: false,
    DOMTokenList: true,
    DataTransferItemList: false,
    FileList: false,
    HTMLAllCollection: false,
    HTMLCollection: false,
    HTMLFormElement: false,
    HTMLSelectElement: false,
    MediaList: true, // TODO: Not spec compliant, should be false.
    MimeTypeArray: false,
    NamedNodeMap: false,
    NodeList: true,
    PaintRequestList: false,
    Plugin: false,
    PluginArray: false,
    SVGLengthList: false,
    SVGNumberList: false,
    SVGPathSegList: false,
    SVGPointList: false,
    SVGStringList: false,
    SVGTransformList: false,
    SourceBufferList: false,
    StyleSheetList: true, // TODO: Not spec compliant, should be false.
    TextTrackCueList: false,
    TextTrackList: false,
    TouchList: false
  };
  
  for (var collections = getKeys(DOMIterables), i = 0; i < collections.length; i++) {
    var NAME = collections[i];
    var explicit = DOMIterables[NAME];
    var Collection = global[NAME];
    var proto = Collection && Collection.prototype;
    var key;
    if (proto) {
      if (!proto[ITERATOR]) hide(proto, ITERATOR, ArrayValues);
      if (!proto[TO_STRING_TAG]) hide(proto, TO_STRING_TAG, NAME);
      Iterators[NAME] = ArrayValues;
      if (explicit) for (key in $iterators) if (!proto[key]) redefine(proto, key, $iterators[key], true);
    }
  }
  
  },{"107":107,"118":118,"152":152,"164":164,"70":70,"72":72,"88":88}],304:[function(_dereq_,module,exports){
  var $export = _dereq_(62);
  var $task = _dereq_(136);
  $export($export.G + $export.B, {
    setImmediate: $task.set,
    clearImmediate: $task.clear
  });
  
  },{"136":136,"62":62}],305:[function(_dereq_,module,exports){
  // ie9- setTimeout & setInterval additional parameters fix
  var global = _dereq_(70);
  var $export = _dereq_(62);
  var userAgent = _dereq_(148);
  var slice = [].slice;
  var MSIE = /MSIE .\./.test(userAgent); // <- dirty ie9- check
  var wrap = function (set) {
    return function (fn, time /* , ...args */) {
      var boundArgs = arguments.length > 2;
      var args = boundArgs ? slice.call(arguments, 2) : false;
      return set(boundArgs ? function () {
        // eslint-disable-next-line no-new-func
        (typeof fn == 'function' ? fn : Function(fn)).apply(this, args);
      } : fn, time);
    };
  };
  $export($export.G + $export.B + $export.F * MSIE, {
    setTimeout: wrap(global.setTimeout),
    setInterval: wrap(global.setInterval)
  });
  
  },{"148":148,"62":62,"70":70}],306:[function(_dereq_,module,exports){
  _dereq_(305);
  _dereq_(304);
  _dereq_(303);
  module.exports = _dereq_(52);
  
  },{"303":303,"304":304,"305":305,"52":52}],307:[function(_dereq_,module,exports){
  /**
   * Copyright (c) 2014-present, Facebook, Inc.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */
  
  var runtime = (function (exports) {
    "use strict";
  
    var Op = Object.prototype;
    var hasOwn = Op.hasOwnProperty;
    var undefined; // More compressible than void 0.
    var $Symbol = typeof Symbol === "function" ? Symbol : {};
    var iteratorSymbol = $Symbol.iterator || "@@iterator";
    var asyncIteratorSymbol = $Symbol.asyncIterator || "@@asyncIterator";
    var toStringTagSymbol = $Symbol.toStringTag || "@@toStringTag";
  
    function wrap(innerFn, outerFn, self, tryLocsList) {
      // If outerFn provided and outerFn.prototype is a Generator, then outerFn.prototype instanceof Generator.
      var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator;
      var generator = Object.create(protoGenerator.prototype);
      var context = new Context(tryLocsList || []);
  
      // The ._invoke method unifies the implementations of the .next,
      // .throw, and .return methods.
      generator._invoke = makeInvokeMethod(innerFn, self, context);
  
      return generator;
    }
    exports.wrap = wrap;
  
    // Try/catch helper to minimize deoptimizations. Returns a completion
    // record like context.tryEntries[i].completion. This interface could
    // have been (and was previously) designed to take a closure to be
    // invoked without arguments, but in all the cases we care about we
    // already have an existing method we want to call, so there's no need
    // to create a new function object. We can even get away with assuming
    // the method takes exactly one argument, since that happens to be true
    // in every case, so we don't have to touch the arguments object. The
    // only additional allocation required is the completion record, which
    // has a stable shape and so hopefully should be cheap to allocate.
    function tryCatch(fn, obj, arg) {
      try {
        return { type: "normal", arg: fn.call(obj, arg) };
      } catch (err) {
        return { type: "throw", arg: err };
      }
    }
  
    var GenStateSuspendedStart = "suspendedStart";
    var GenStateSuspendedYield = "suspendedYield";
    var GenStateExecuting = "executing";
    var GenStateCompleted = "completed";
  
    // Returning this object from the innerFn has the same effect as
    // breaking out of the dispatch switch statement.
    var ContinueSentinel = {};
  
    // Dummy constructor functions that we use as the .constructor and
    // .constructor.prototype properties for functions that return Generator
    // objects. For full spec compliance, you may wish to configure your
    // minifier not to mangle the names of these two functions.
    function Generator() {}
    function GeneratorFunction() {}
    function GeneratorFunctionPrototype() {}
  
    // This is a polyfill for %IteratorPrototype% for environments that
    // don't natively support it.
    var IteratorPrototype = {};
    IteratorPrototype[iteratorSymbol] = function () {
      return this;
    };
  
    var getProto = Object.getPrototypeOf;
    var NativeIteratorPrototype = getProto && getProto(getProto(values([])));
    if (NativeIteratorPrototype &&
        NativeIteratorPrototype !== Op &&
        hasOwn.call(NativeIteratorPrototype, iteratorSymbol)) {
      // This environment has a native %IteratorPrototype%; use it instead
      // of the polyfill.
      IteratorPrototype = NativeIteratorPrototype;
    }
  
    var Gp = GeneratorFunctionPrototype.prototype =
      Generator.prototype = Object.create(IteratorPrototype);
    GeneratorFunction.prototype = Gp.constructor = GeneratorFunctionPrototype;
    GeneratorFunctionPrototype.constructor = GeneratorFunction;
    GeneratorFunctionPrototype[toStringTagSymbol] =
      GeneratorFunction.displayName = "GeneratorFunction";
  
    // Helper for defining the .next, .throw, and .return methods of the
    // Iterator interface in terms of a single ._invoke method.
    function defineIteratorMethods(prototype) {
      ["next", "throw", "return"].forEach(function(method) {
        prototype[method] = function(arg) {
          return this._invoke(method, arg);
        };
      });
    }
  
    exports.isGeneratorFunction = function(genFun) {
      var ctor = typeof genFun === "function" && genFun.constructor;
      return ctor
        ? ctor === GeneratorFunction ||
          // For the native GeneratorFunction constructor, the best we can
          // do is to check its .name property.
          (ctor.displayName || ctor.name) === "GeneratorFunction"
        : false;
    };
  
    exports.mark = function(genFun) {
      if (Object.setPrototypeOf) {
        Object.setPrototypeOf(genFun, GeneratorFunctionPrototype);
      } else {
        genFun.__proto__ = GeneratorFunctionPrototype;
        if (!(toStringTagSymbol in genFun)) {
          genFun[toStringTagSymbol] = "GeneratorFunction";
        }
      }
      genFun.prototype = Object.create(Gp);
      return genFun;
    };
  
    // Within the body of any async function, `await x` is transformed to
    // `yield regeneratorRuntime.awrap(x)`, so that the runtime can test
    // `hasOwn.call(value, "__await")` to determine if the yielded value is
    // meant to be awaited.
    exports.awrap = function(arg) {
      return { __await: arg };
    };
  
    function AsyncIterator(generator, PromiseImpl) {
      function invoke(method, arg, resolve, reject) {
        var record = tryCatch(generator[method], generator, arg);
        if (record.type === "throw") {
          reject(record.arg);
        } else {
          var result = record.arg;
          var value = result.value;
          if (value &&
              typeof value === "object" &&
              hasOwn.call(value, "__await")) {
            return PromiseImpl.resolve(value.__await).then(function(value) {
              invoke("next", value, resolve, reject);
            }, function(err) {
              invoke("throw", err, resolve, reject);
            });
          }
  
          return PromiseImpl.resolve(value).then(function(unwrapped) {
            // When a yielded Promise is resolved, its final value becomes
            // the .value of the Promise<{value,done}> result for the
            // current iteration.
            result.value = unwrapped;
            resolve(result);
          }, function(error) {
            // If a rejected Promise was yielded, throw the rejection back
            // into the async generator function so it can be handled there.
            return invoke("throw", error, resolve, reject);
          });
        }
      }
  
      var previousPromise;
  
      function enqueue(method, arg) {
        function callInvokeWithMethodAndArg() {
          return new PromiseImpl(function(resolve, reject) {
            invoke(method, arg, resolve, reject);
          });
        }
  
        return previousPromise =
          // If enqueue has been called before, then we want to wait until
          // all previous Promises have been resolved before calling invoke,
          // so that results are always delivered in the correct order. If
          // enqueue has not been called before, then it is important to
          // call invoke immediately, without waiting on a callback to fire,
          // so that the async generator function has the opportunity to do
          // any necessary setup in a predictable way. This predictability
          // is why the Promise constructor synchronously invokes its
          // executor callback, and why async functions synchronously
          // execute code before the first await. Since we implement simple
          // async functions in terms of async generators, it is especially
          // important to get this right, even though it requires care.
          previousPromise ? previousPromise.then(
            callInvokeWithMethodAndArg,
            // Avoid propagating failures to Promises returned by later
            // invocations of the iterator.
            callInvokeWithMethodAndArg
          ) : callInvokeWithMethodAndArg();
      }
  
      // Define the unified helper method that is used to implement .next,
      // .throw, and .return (see defineIteratorMethods).
      this._invoke = enqueue;
    }
  
    defineIteratorMethods(AsyncIterator.prototype);
    AsyncIterator.prototype[asyncIteratorSymbol] = function () {
      return this;
    };
    exports.AsyncIterator = AsyncIterator;
  
    // Note that simple async functions are implemented on top of
    // AsyncIterator objects; they just return a Promise for the value of
    // the final result produced by the iterator.
    exports.async = function(innerFn, outerFn, self, tryLocsList, PromiseImpl) {
      if (PromiseImpl === void 0) PromiseImpl = Promise;
  
      var iter = new AsyncIterator(
        wrap(innerFn, outerFn, self, tryLocsList),
        PromiseImpl
      );
  
      return exports.isGeneratorFunction(outerFn)
        ? iter // If outerFn is a generator, return the full iterator.
        : iter.next().then(function(result) {
            return result.done ? result.value : iter.next();
          });
    };
  
    function makeInvokeMethod(innerFn, self, context) {
      var state = GenStateSuspendedStart;
  
      return function invoke(method, arg) {
        if (state === GenStateExecuting) {
          throw new Error("Generator is already running");
        }
  
        if (state === GenStateCompleted) {
          if (method === "throw") {
            throw arg;
          }
  
          // Be forgiving, per 25.3.3.3.3 of the spec:
          // https://people.mozilla.org/~jorendorff/es6-draft.html#sec-generatorresume
          return doneResult();
        }
  
        context.method = method;
        context.arg = arg;
  
        while (true) {
          var delegate = context.delegate;
          if (delegate) {
            var delegateResult = maybeInvokeDelegate(delegate, context);
            if (delegateResult) {
              if (delegateResult === ContinueSentinel) continue;
              return delegateResult;
            }
          }
  
          if (context.method === "next") {
            // Setting context._sent for legacy support of Babel's
            // function.sent implementation.
            context.sent = context._sent = context.arg;
  
          } else if (context.method === "throw") {
            if (state === GenStateSuspendedStart) {
              state = GenStateCompleted;
              throw context.arg;
            }
  
            context.dispatchException(context.arg);
  
          } else if (context.method === "return") {
            context.abrupt("return", context.arg);
          }
  
          state = GenStateExecuting;
  
          var record = tryCatch(innerFn, self, context);
          if (record.type === "normal") {
            // If an exception is thrown from innerFn, we leave state ===
            // GenStateExecuting and loop back for another invocation.
            state = context.done
              ? GenStateCompleted
              : GenStateSuspendedYield;
  
            if (record.arg === ContinueSentinel) {
              continue;
            }
  
            return {
              value: record.arg,
              done: context.done
            };
  
          } else if (record.type === "throw") {
            state = GenStateCompleted;
            // Dispatch the exception by looping back around to the
            // context.dispatchException(context.arg) call above.
            context.method = "throw";
            context.arg = record.arg;
          }
        }
      };
    }
  
    // Call delegate.iterator[context.method](context.arg) and handle the
    // result, either by returning a { value, done } result from the
    // delegate iterator, or by modifying context.method and context.arg,
    // setting context.delegate to null, and returning the ContinueSentinel.
    function maybeInvokeDelegate(delegate, context) {
      var method = delegate.iterator[context.method];
      if (method === undefined) {
        // A .throw or .return when the delegate iterator has no .throw
        // method always terminates the yield* loop.
        context.delegate = null;
  
        if (context.method === "throw") {
          // Note: ["return"] must be used for ES3 parsing compatibility.
          if (delegate.iterator["return"]) {
            // If the delegate iterator has a return method, give it a
            // chance to clean up.
            context.method = "return";
            context.arg = undefined;
            maybeInvokeDelegate(delegate, context);
  
            if (context.method === "throw") {
              // If maybeInvokeDelegate(context) changed context.method from
              // "return" to "throw", let that override the TypeError below.
              return ContinueSentinel;
            }
          }
  
          context.method = "throw";
          context.arg = new TypeError(
            "The iterator does not provide a 'throw' method");
        }
  
        return ContinueSentinel;
      }
  
      var record = tryCatch(method, delegate.iterator, context.arg);
  
      if (record.type === "throw") {
        context.method = "throw";
        context.arg = record.arg;
        context.delegate = null;
        return ContinueSentinel;
      }
  
      var info = record.arg;
  
      if (! info) {
        context.method = "throw";
        context.arg = new TypeError("iterator result is not an object");
        context.delegate = null;
        return ContinueSentinel;
      }
  
      if (info.done) {
        // Assign the result of the finished delegate to the temporary
        // variable specified by delegate.resultName (see delegateYield).
        context[delegate.resultName] = info.value;
  
        // Resume execution at the desired location (see delegateYield).
        context.next = delegate.nextLoc;
  
        // If context.method was "throw" but the delegate handled the
        // exception, let the outer generator proceed normally. If
        // context.method was "next", forget context.arg since it has been
        // "consumed" by the delegate iterator. If context.method was
        // "return", allow the original .return call to continue in the
        // outer generator.
        if (context.method !== "return") {
          context.method = "next";
          context.arg = undefined;
        }
  
      } else {
        // Re-yield the result returned by the delegate method.
        return info;
      }
  
      // The delegate iterator is finished, so forget it and continue with
      // the outer generator.
      context.delegate = null;
      return ContinueSentinel;
    }
  
    // Define Generator.prototype.{next,throw,return} in terms of the
    // unified ._invoke helper method.
    defineIteratorMethods(Gp);
  
    Gp[toStringTagSymbol] = "Generator";
  
    // A Generator should always return itself as the iterator object when the
    // @@iterator function is called on it. Some browsers' implementations of the
    // iterator prototype chain incorrectly implement this, causing the Generator
    // object to not be returned from this call. This ensures that doesn't happen.
    // See https://github.com/facebook/regenerator/issues/274 for more details.
    Gp[iteratorSymbol] = function() {
      return this;
    };
  
    Gp.toString = function() {
      return "[object Generator]";
    };
  
    function pushTryEntry(locs) {
      var entry = { tryLoc: locs[0] };
  
      if (1 in locs) {
        entry.catchLoc = locs[1];
      }
  
      if (2 in locs) {
        entry.finallyLoc = locs[2];
        entry.afterLoc = locs[3];
      }
  
      this.tryEntries.push(entry);
    }
  
    function resetTryEntry(entry) {
      var record = entry.completion || {};
      record.type = "normal";
      delete record.arg;
      entry.completion = record;
    }
  
    function Context(tryLocsList) {
      // The root entry object (effectively a try statement without a catch
      // or a finally block) gives us a place to store values thrown from
      // locations where there is no enclosing try statement.
      this.tryEntries = [{ tryLoc: "root" }];
      tryLocsList.forEach(pushTryEntry, this);
      this.reset(true);
    }
  
    exports.keys = function(object) {
      var keys = [];
      for (var key in object) {
        keys.push(key);
      }
      keys.reverse();
  
      // Rather than returning an object with a next method, we keep
      // things simple and return the next function itself.
      return function next() {
        while (keys.length) {
          var key = keys.pop();
          if (key in object) {
            next.value = key;
            next.done = false;
            return next;
          }
        }
  
        // To avoid creating an additional object, we just hang the .value
        // and .done properties off the next function object itself. This
        // also ensures that the minifier will not anonymize the function.
        next.done = true;
        return next;
      };
    };
  
    function values(iterable) {
      if (iterable) {
        var iteratorMethod = iterable[iteratorSymbol];
        if (iteratorMethod) {
          return iteratorMethod.call(iterable);
        }
  
        if (typeof iterable.next === "function") {
          return iterable;
        }
  
        if (!isNaN(iterable.length)) {
          var i = -1, next = function next() {
            while (++i < iterable.length) {
              if (hasOwn.call(iterable, i)) {
                next.value = iterable[i];
                next.done = false;
                return next;
              }
            }
  
            next.value = undefined;
            next.done = true;
  
            return next;
          };
  
          return next.next = next;
        }
      }
  
      // Return an iterator with no values.
      return { next: doneResult };
    }
    exports.values = values;
  
    function doneResult() {
      return { value: undefined, done: true };
    }
  
    Context.prototype = {
      constructor: Context,
  
      reset: function(skipTempReset) {
        this.prev = 0;
        this.next = 0;
        // Resetting context._sent for legacy support of Babel's
        // function.sent implementation.
        this.sent = this._sent = undefined;
        this.done = false;
        this.delegate = null;
  
        this.method = "next";
        this.arg = undefined;
  
        this.tryEntries.forEach(resetTryEntry);
  
        if (!skipTempReset) {
          for (var name in this) {
            // Not sure about the optimal order of these conditions:
            if (name.charAt(0) === "t" &&
                hasOwn.call(this, name) &&
                !isNaN(+name.slice(1))) {
              this[name] = undefined;
            }
          }
        }
      },
  
      stop: function() {
        this.done = true;
  
        var rootEntry = this.tryEntries[0];
        var rootRecord = rootEntry.completion;
        if (rootRecord.type === "throw") {
          throw rootRecord.arg;
        }
  
        return this.rval;
      },
  
      dispatchException: function(exception) {
        if (this.done) {
          throw exception;
        }
  
        var context = this;
        function handle(loc, caught) {
          record.type = "throw";
          record.arg = exception;
          context.next = loc;
  
          if (caught) {
            // If the dispatched exception was caught by a catch block,
            // then let that catch block handle the exception normally.
            context.method = "next";
            context.arg = undefined;
          }
  
          return !! caught;
        }
  
        for (var i = this.tryEntries.length - 1; i >= 0; --i) {
          var entry = this.tryEntries[i];
          var record = entry.completion;
  
          if (entry.tryLoc === "root") {
            // Exception thrown outside of any try block that could handle
            // it, so set the completion value of the entire function to
            // throw the exception.
            return handle("end");
          }
  
          if (entry.tryLoc <= this.prev) {
            var hasCatch = hasOwn.call(entry, "catchLoc");
            var hasFinally = hasOwn.call(entry, "finallyLoc");
  
            if (hasCatch && hasFinally) {
              if (this.prev < entry.catchLoc) {
                return handle(entry.catchLoc, true);
              } else if (this.prev < entry.finallyLoc) {
                return handle(entry.finallyLoc);
              }
  
            } else if (hasCatch) {
              if (this.prev < entry.catchLoc) {
                return handle(entry.catchLoc, true);
              }
  
            } else if (hasFinally) {
              if (this.prev < entry.finallyLoc) {
                return handle(entry.finallyLoc);
              }
  
            } else {
              throw new Error("try statement without catch or finally");
            }
          }
        }
      },
  
      abrupt: function(type, arg) {
        for (var i = this.tryEntries.length - 1; i >= 0; --i) {
          var entry = this.tryEntries[i];
          if (entry.tryLoc <= this.prev &&
              hasOwn.call(entry, "finallyLoc") &&
              this.prev < entry.finallyLoc) {
            var finallyEntry = entry;
            break;
          }
        }
  
        if (finallyEntry &&
            (type === "break" ||
             type === "continue") &&
            finallyEntry.tryLoc <= arg &&
            arg <= finallyEntry.finallyLoc) {
          // Ignore the finally entry if control is not jumping to a
          // location outside the try/catch block.
          finallyEntry = null;
        }
  
        var record = finallyEntry ? finallyEntry.completion : {};
        record.type = type;
        record.arg = arg;
  
        if (finallyEntry) {
          this.method = "next";
          this.next = finallyEntry.finallyLoc;
          return ContinueSentinel;
        }
  
        return this.complete(record);
      },
  
      complete: function(record, afterLoc) {
        if (record.type === "throw") {
          throw record.arg;
        }
  
        if (record.type === "break" ||
            record.type === "continue") {
          this.next = record.arg;
        } else if (record.type === "return") {
          this.rval = this.arg = record.arg;
          this.method = "return";
          this.next = "end";
        } else if (record.type === "normal" && afterLoc) {
          this.next = afterLoc;
        }
  
        return ContinueSentinel;
      },
  
      finish: function(finallyLoc) {
        for (var i = this.tryEntries.length - 1; i >= 0; --i) {
          var entry = this.tryEntries[i];
          if (entry.finallyLoc === finallyLoc) {
            this.complete(entry.completion, entry.afterLoc);
            resetTryEntry(entry);
            return ContinueSentinel;
          }
        }
      },
  
      "catch": function(tryLoc) {
        for (var i = this.tryEntries.length - 1; i >= 0; --i) {
          var entry = this.tryEntries[i];
          if (entry.tryLoc === tryLoc) {
            var record = entry.completion;
            if (record.type === "throw") {
              var thrown = record.arg;
              resetTryEntry(entry);
            }
            return thrown;
          }
        }
  
        // The context.catch method must only be called with a location
        // argument that corresponds to a known catch block.
        throw new Error("illegal catch attempt");
      },
  
      delegateYield: function(iterable, resultName, nextLoc) {
        this.delegate = {
          iterator: values(iterable),
          resultName: resultName,
          nextLoc: nextLoc
        };
  
        if (this.method === "next") {
          // Deliberately forget the last sent value so that we don't
          // accidentally pass it on to the delegate.
          this.arg = undefined;
        }
  
        return ContinueSentinel;
      }
    };
  
    // Regardless of whether this script is executing as a CommonJS module
    // or not, return the runtime object so that we can declare the variable
    // regeneratorRuntime in the outer scope, which allows this module to be
    // injected easily by `bin/regenerator --include-runtime script.js`.
    return exports;
  
  }(
    // If this script is executing as a CommonJS module, use module.exports
    // as the regeneratorRuntime namespace. Otherwise create a new empty
    // object. Either way, the resulting object will be used to initialize
    // the regeneratorRuntime variable at the top of this file.
    typeof module === "object" ? module.exports : {}
  ));
  
  try {
    regeneratorRuntime = runtime;
  } catch (accidentalStrictMode) {
    // This module should not be running in strict mode, so the above
    // assignment should always work unless something is misconfigured. Just
    // in case runtime.js accidentally runs in strict mode, we can escape
    // strict mode using a global Function call. This could conceivably fail
    // if a Content Security Policy forbids using Function, but in that case
    // the proper solution is to fix the accidental strict mode problem. If
    // you've misconfigured your bundler to force strict mode and applied a
    // CSP to forbid Function, and you're not willing to fix either of those
    // problems, please detail your unique predicament in a GitHub issue.
    Function("r", "regeneratorRuntime = r")(runtime);
  }
  
  },{}]},{},[1]);