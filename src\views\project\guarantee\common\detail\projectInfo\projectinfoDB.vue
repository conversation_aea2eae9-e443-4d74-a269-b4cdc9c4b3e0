<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2020-08-24 15:28:48
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-06-05 09:39:00
-->
<template>
  <div class="detail">

    <!-- {{dataForm}} -->
    <!-- <el-col :xs="24" :lg="draw?24:12">
      <el-form-item label="项目所属区域">
        {{fomate('bgbiddingproject','region')}}
      </el-form-item>
    </el-col> -->
    <el-col :xs="24" :lg="draw?24:12">
      <el-form-item label="招标人名称">
        {{fomate('bginsurancebbr','name')}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="draw?24:12">
      <el-form-item label="招标代理">
        {{fomate('bgbiddingproject','tendereeAgent')}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="draw?24:12">
      <el-form-item label="招标人地址">
        {{dataForm.bidAddress}}
        <!-- {{fomate('bgbiddingproject','tendereeAgent')}} -->
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="draw?24:12" v-if="dataForm.key=='GS'||dataForm.key=='SHB'||dataForm.key=='YFDDB'||dataForm.key === 'TAIBDB'||dataForm.key  === 'HZDB' ||dataForm.key  === 'JXDB' ||dataForm.key  === 'HAZDB' ||dataForm.key === 'JHY'">
      <el-form-item label="招标编号">
        {{fomate('bgbiddingproject','bidCode')}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="draw?24:12" >
      <el-form-item :label="`${(dataForm.bginsuranceinfo.insuranceType=='tbdbbh' ||dataForm.bginsuranceinfo.insuranceType=='tbbhyh')?'开标':'投标'}日期`">
        {{fomate('bginsuranceinfo','startDate') | fomateTime}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="draw?24:12" v-if="dataForm.bginsuranceinfo.insuranceType=='tbdbbh' ||dataForm.bginsuranceinfo.insuranceType=='tbbhyh'">
      <el-form-item label="投标有效期">
        {{fomate('bgbiddingproject','tenderValid')}}天
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg='24'>
      <el-form-item label="保证金" prop="bond" v-if="dataForm.bginsuranceinfo.insuranceType=='zfdbbh'">
        {{fomate('bgbiddingproject','bond')}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="draw?24:12" v-if="dataForm.bginsuranceinfo.insuranceType=='zfdbbh'">
      <el-form-item label="反担保质押" prop="pledge">
        {{fomate('bgbiddingproject','pledge')}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="draw?24:12">
      <el-form-item label="反担保抵押" prop="mortgage" v-if="dataForm.bginsuranceinfo.insuranceType=='zfdbbh'">
        {{fomate('bgbiddingproject','mortgage')}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg='24'>
      <el-form-item label="第三方反担保" prop="thirdGuarantee" v-if="dataForm.bginsuranceinfo.insuranceType=='zfdbbh'">
        {{fomate('bgbiddingproject','thirdGuarantee')}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg='24'>
      <el-form-item label="第三方担保金额" prop="thirdGuaranteeAmount" v-if="dataForm.bginsuranceinfo.insuranceType=='zfdbbh'">
        {{fomate('bgbiddingproject','thirdGuaranteeAmount')}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="draw?24:12" v-if="dataForm.bginsuranceinfo.insuranceType!='tbdbbh' &&dataForm.bginsuranceinfo.insuranceType!='tbbhyh'">
      <el-form-item label="中标日期" prop="winBidDate">
        {{fomate('bgbiddingproject','winBidDate')}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="draw?24:12" v-if="dataForm.bginsuranceinfo.insuranceType!='tbdbbh' &&dataForm.bginsuranceinfo.insuranceType!='tbbhyh'">
      <el-form-item label="中标通知书编号" prop="winBidNumber">
        {{fomate('bgbiddingproject','winBidNumber')}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="draw?24:12" v-if="dataForm.bginsuranceinfo.insuranceType!='tbdbbh' &&dataForm.bginsuranceinfo.insuranceType!='tbbhyh'">
      <el-form-item label="中标价" prop="winBidPrice">
        <!-- {{dataForm.guaranteeAmountS}} -->
        {{fomate('bgbiddingproject','winBidPrice')}}{{(dataForm.bginsuranceinfo.insuranceType==='lydbbh'||dataForm.bginsuranceinfo.insuranceType==='lybhyh')?'元':'万元'}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="draw?24:12" v-if="dataForm.bginsuranceinfo.insuranceType!='tbdbbh' &&dataForm.bginsuranceinfo.insuranceType!='tbbhyh'">
      <el-form-item label="合同工期" prop="contractDuration">
        {{fomate('bgbiddingproject','contractDuration')}}天
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="draw?24:12" v-if="dataForm.bginsuranceinfo.insuranceType!='tbdbbh' &&dataForm.bginsuranceinfo.insuranceType!='tbbhyh'">
      <el-form-item label="合同签订日期" prop="contractDate">
        {{fomate('bgbiddingproject','contractDate')}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg="draw?24:12" v-if="dataForm.bginsuranceinfo.insuranceType!='tbdbbh' &&dataForm.bginsuranceinfo.insuranceType!='tbbhyh'">
      <el-form-item label="竣工日期">
        {{fomate('bgbiddingproject','completionDate')}}
      </el-form-item>
    </el-col>
    <!-- <el-col :xs="24" :lg="draw?24:12" v-if="dataForm.key=='YFDDB'">
      <el-form-item label="投标延长截止日期">
        {{fomate('bginsuranceinfo','bidEndDate')}}
      </el-form-item>
    </el-col> -->
    <el-col :xs="24" :lg="draw?24:12">
      <el-form-item label="担保金额">
        {{(dataForm.bginsuranceinfo.insuranceType==='lydbbh'||dataForm.bginsuranceinfo.insuranceType==='lybhyh')?fomate('bginsuranceinfo','guaranteeAmount')+'元':fomate('bginsuranceinfo','guaranteeAmount')/10000+"万元"}}
      </el-form-item>
    </el-col>

    <el-col :xs="24" :lg="draw?24:12" v-if="dataForm.bginsuranceinfo.guaranteePrice">
      <el-form-item label="保费">
        <span style="font-size:20px;color:#e6a23c;font-weight: bold;letter-spacing: 2px;">
          {{fomate('bginsuranceinfo','guaranteePrice')}}
        </span>元
      </el-form-item>
    </el-col>

    <!-- <el-col :xs="24" :lg="draw?24:12">
      <el-form-item label="业务流水号(验真)">
        {{fomate('bginsuranceinfo','insuranceCode')}}
      </el-form-item>
    </el-col> -->
    <el-col :span="24" v-if="dataForm.bgguaranteeinvoice.id&&$route.name!='openbidtimeDetail'">
      <el-divider></el-divider>
      <el-card class="box-card" shadow='never'>
        <div slot="header" class="clearfix">
          <span class="title">发票信息</span>
          <span style="float: right; padding: 3px 0" type="text">
            <el-tag type="warning" v-if="dataForm.invoiceStatus == 0">已申请</el-tag>
            <el-tag type="warning" v-if="dataForm.invoiceStatus == 2">申请成功，未出具</el-tag>
            <el-tag type="success" v-if="dataForm.invoiceStatus == 1">申请成功，已出具</el-tag>
            &nbsp;
            <!-- <el-button v-if="dataForm.invoiceStatus == 1&&dataForm.bgguaranteeinvoice.invoiceType=='1'" @click="down" type="text">下载发票</el-button> -->
          </span>
        </div>
        <div>
          <el-row>
            <el-col :span="draw?24:12">
              <el-form-item label="发票类型">
                {{certType(dataForm.bgguaranteeinvoice.invoiceType,'invoiceTypeoptions')}}
              </el-form-item>
            </el-col>
            <el-col :span="draw?24:12">
              <el-form-item label="开票对象">
                {{certType(dataForm.bgguaranteeinvoice.invoiceObject,'invoiceObjectoptions')}}
              </el-form-item>
            </el-col>
            <el-col :span="draw?24:12">
              <el-form-item label="纳税人类型">
                {{certType(dataForm.bgguaranteeinvoice.taxpayerType,'taxpayerTypeoptions')}}
              </el-form-item>
            </el-col>
            <el-col :span="draw?24:12">
              <el-form-item label="纳税人识别号">
                {{fomate('bgguaranteeinvoice','taxpayerNumber')}}
              </el-form-item>
            </el-col>
            <template>
              <el-col :xs="24" :lg="24">
                <el-form-item label="发票抬头" prop="invoiceTitle">
                  {{fomate('bgguaranteeinvoice','invoiceTitle')}}
                </el-form-item>
                <el-form-item label="开户行名称" prop="bankAccountName">
                  {{fomate('bgguaranteeinvoice','bankAccountName')}}
                </el-form-item>
              </el-col>
              <el-col :xs="24" :lg="draw?24:12">
                <el-form-item label="银行账户号码" prop="bankAccountNo">
                  {{fomate('bgguaranteeinvoice','bankAccountNo')}}
                </el-form-item>
                <el-form-item label="税务登记电话" prop="phoneNumber">
                  {{fomate('bgguaranteeinvoice','phoneNumber')}}
                </el-form-item>
              </el-col>
              <el-col :xs="24" :lg="draw?24:12">
                <el-form-item label="发票接收邮箱" prop="electronicInvoiceEmail">
                  {{fomate('bgguaranteeinvoice','electronicInvoiceEmail')}}
                </el-form-item>
                <el-form-item label="发票接收手机号" prop="electronicInvoicePhone">
                  {{fomate('bgguaranteeinvoice','electronicInvoicePhone')}}
                </el-form-item>
              </el-col>
              <el-col :xs="24" :lg="24">
                <el-form-item label="税务登记地址" prop="address">
                  {{fomate('bgguaranteeinvoice','address')}}
                </el-form-item>
              </el-col>
              <el-col :xs="24" :lg="24">
                <el-form-item label="备注" prop="remark">
                  {{fomate('bgguaranteeinvoice','remark')}}
                </el-form-item>
              </el-col>
            </template>
          </el-row>
        </div>
      </el-card>
    </el-col>
    <el-col :span="24">
      <!-- <el-divider></el-divider> -->
      <el-card class="box-card" shadow='never' v-if="dataForm.bgGuaranteeMailing.id">
        <div slot="header" class="clearfix">
          <span class="title">收件信息</span>
        </div>
        <div>
          <el-col :xs="24" :lg="draw?24:12">
            <el-form-item label="收件人">
              {{fomate('bgGuaranteeMailing','mailingUser')}}
            </el-form-item>

          </el-col>
          <el-col :xs="24" :lg="draw?24:12">
            <el-form-item label="收件电话">
              {{fomate('bgGuaranteeMailing','mailingTel')}}
            </el-form-item>
          </el-col>
          <el-col :xs="24" :lg="draw?24:12">
            <el-form-item label="收件地址">
              {{fomate('bgGuaranteeMailing','mailingAddress')}}
            </el-form-item>
          </el-col>
        </div>
      </el-card>
    </el-col>
    <el-col :span="24" v-if="dataForm.bginsuranceinfo.insuranceType !== 'tbbxbh'&&$route.name!='openbidtimeDetail'">
      <el-divider></el-divider>
      <el-card class="box-card" shadow='never'>
        <div slot="header" class="clearfix">
          <span class="title">审核记录</span>
        </div>
        <div v-if="$route.query.taskId">
          <ren-process-detail ref="renProcessDetail"></ren-process-detail>
        </div>
        <div style="width: 100%;" v-else>
          <el-table v-loading="dataListLoading" :data="hisdata" style="width: 100%;">
            <el-table-column prop="name" label="审核信息" align="center" width="200px">
            </el-table-column>
            <el-table-column prop="bussnessType" label="审核类型" align="center" width="200px">
            </el-table-column>
            <el-table-column prop="description" label="备注" align="center">
            </el-table-column>
            <el-table-column prop="updateDate" label="审核时间" align="center" width="200px">
            </el-table-column>
          </el-table>
        </div>

      </el-card>
    </el-col>
  </div>
</template>
<script>
import moment from 'moment'
import renProcessDetail from '../../flow/ren-process-detail'

export default {
  props: {
    dataForm: Object,
    options: Object,
    hisdata: Array,
    draw: Boolean
  },
  data () {
    return {
      dataListLoading: false
    }
  },
  components: { renProcessDetail },
  filters: {
    fomateTime (val) {
      // let _self = this
      console.log(val)
      val = val.substring(0, 10)
      return val
    }
  },
  created () {},
  activated () {},
  methods: {
    moment,
    down () {
      this.$emit('down')
    },
    fomate (key, val) {
      return this.dataForm[key][val] ? this.dataForm[key][val] : '-'
    },
    certType (val, name) {
      // console.log(this.options)
      var aa = this.options[name].filter((a) => a.dictCode === val)
      if (name === 'letterStatusoptions') {
        console.log(aa)
      }
      return aa.length > 0 ? aa[0].dictName : '-'
    },
    refresh () {
      this.$nextTick(() => {
        this.$refs['renProcessDetail'].init()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  /* padding: 16px 0; */
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
