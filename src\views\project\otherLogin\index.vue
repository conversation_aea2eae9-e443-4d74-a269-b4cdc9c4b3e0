<template>
  <div>
    第三方登录中。。。。({{count}}s)
  </div>
</template>

<script>
import Cookies from 'js-cookie'
export default {
  data () {
    return {
      count: 0
    }
  },
  mounted () {
    this.checktoken()
  },
  methods: {
    checktoken () {
      let timer = setTimeout(() => {
        if (this.count < 3) {
          if (!Cookies.get('token')) {
            console.log('检查token')
            this.count++
            this.checktoken()
          } else {
            this.$router.push('/home')
          }
        } else {
          this.$message.error('登陆失败，请手动登录')
          this.$router.push('/home')
        }

        clearTimeout(timer)
      }, 1000)
    }
  }
}
</script>
