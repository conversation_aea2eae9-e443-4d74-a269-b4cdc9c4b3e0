<!--
 * @Descripttion:
 * @Author: kong<PERSON><PERSON>ang
 * @Date: 2020-10-13 09:38:40
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-09-01 09:15:46
-->
<template>
  <span style="float:right">
    <template v-if="this.$route.query.JumpName=='bgguaranteeletterSqf'||this.$route.query.JumpName=='letter-bgguaranteeletterSqf'">
      <template>
        <el-button v-if="(Number(dataForm.letterStatus) === 40&& Number(dataForm.payStatus) !== 20) &&
            (Number(dataForm.payStatus) === 40 ||
              Number(dataForm.payStatus) === 10)&& dataForm.guaranteeType === 'tbdbbh'" type="success" size="small" @click="pay(dataForm.letterId,dataForm.key)">支付</el-button>
              <!-- 投标银行保函，保险 -->
        <template v-if="(Number(dataForm.letterStatus) === 40 || Number(dataForm.letterStatus) === 49)&&
      (Number(dataForm.payStatus) === 40 ||
        Number(dataForm.payStatus) === 10)">
            <!-- 投标银行保函，保险 -->
            <el-button v-if="(dataForm.guaranteeType == 'tbbxbh' || dataForm.guaranteeType == 'tbbhyh')" type="success" size="small"
             @click="pay(dataForm.letterId,dataForm.key)">支付</el-button>
            <!-- 履约，支付，预付款 -->
            <el-button v-if="isLv(dataForm.guaranteeType)&&dataForm.thirdGuaranteeDTO.contractConfirmation == 1&&(dataForm.issueCode == 'YFDDB'||dataForm.issueCode == 'HZDB')" type="success" size="small"
             @click="pay(dataForm.letterId,dataForm.key)">支付</el-button>
            <el-button v-if="isLv(dataForm.guaranteeType)&&(dataForm.issueCode != 'YFDDB')" type="success" size="small"
             @click="pay(dataForm.letterId,dataForm.key)">支付</el-button>
          </template>
      </template>
      <!-- <el-button v-if="dataForm.letterStatus == 50&&dataForm.key =='zcdb'" type="primary" size="small" @click="downloadLetter(dataForm.letterId,'01',dataForm.key)">下载保函</el-button> -->
      <el-button v-if="dataForm.letterStatus == 50&&(dataForm.bginsuranceinfo.insuranceType =='tbdbbh'||dataForm.bginsuranceinfo.insuranceType =='lydbbh'||dataForm.bginsuranceinfo.insuranceType =='zfdbbh'||dataForm.bginsuranceinfo.insuranceType =='yfkdbbh')" type="primary" size="small" @click="downloadLetter(dataForm.letterId,'01',dataForm.key)">下载保函</el-button>
      <template v-if="dataForm.key =='tabx'">
        <el-button v-if="dataForm.letterStatus == 50" type="primary" size="small" @click="downloadLetter(dataForm.letterId,'01',dataForm.key)">下载保函</el-button>
        <el-button v-if="dataForm.letterStatus == 50" type="primary" size="small" @click="downloadLetter(dataForm.letterId,'00',dataForm.key)">下载保单</el-button>
        <el-button v-if="dataForm.letterStatus == 50&&dataForm.invoiceStatus == 1&&dataForm.bgguaranteeinvoice.invoiceType=='1'" type="primary" size="small"
          @click="down()">下载发票</el-button>
      </template>
      <template v-if="dataForm.key =='ygbx'">
        <el-button v-if="dataForm.letterStatus == 50" type="primary" size="small" @click="downloadLetter(dataForm.letterId,'01',dataForm.key)">下载保函/保单</el-button>
        <el-button v-if="dataForm.letterStatus == 50" type="primary" size="small" @click="downloadLetter(dataForm.letterId,'00',dataForm.key)">下载证明文件</el-button>
        <el-button v-if="dataForm.letterStatus == 50&&dataForm.invoiceStatus == 1&&dataForm.bgguaranteeinvoice.invoiceType=='1'" type="primary" size="small"
          @click="down()">下载发票</el-button>
      </template>
      <template v-if="dataForm.key =='ddbx'">
        <el-button v-if="dataForm.letterStatus == 50" type="primary" size="small" @click="downloadLetter(dataForm.letterId,'01',dataForm.key)">下载保函</el-button>
        <el-button v-if="dataForm.letterStatus == 50" type="primary" size="small" @click="downloadLetter(dataForm.letterId,'00',dataForm.key)">下载保单</el-button>
      </template>
    </template>
    <el-button type="danger" size="small" v-if="this.$route.query.JumpName!='letter-bgguaranteeletterSqf'&&this.$route.query.JumpName!='bgguaranteeletterSqf'&&dataForm.bginsuranceinfo.insuranceType!=='tbbxbh'"  plain @click="drawerOpen('bh')">电子保函预览</el-button>
    <el-button type="danger" size="small" v-if="this.$route.query.JumpName!='letter-bgguaranteeletterSqf'&&this.$route.query.JumpName!='bgguaranteeletterSqf'&&dataForm.bginsuranceinfo.insuranceType!=='tbbxbh'"  plain @click="drawerOpen('file')">附件预览</el-button>
    <el-button type="danger" size="small" v-if="this.$route.query.JumpName" @click="goPrveStep()">返回</el-button>

  </span>
</template>
<script>
import Cookies from 'js-cookie'
export default {
  props: {
    dataForm: Object,
    options: Object,
    fileData: Array,
    draw: Boolean
  },
  data () {
    return {
      drawer: false
    }
  },
  watch: {
    draw (a) {
      this.drawer = a
    }
  },
  methods: {
    down () {
      this.$emit('down')
    },
    drawerOpen (type) {
      this.drawer = !this.drawer
      // this.$store.state.sidebarFold = !this.$store.state.sidebarFold
      this.$emit('drawer', this.drawer, type)
    },
    downloadLetter (id, type, key) {
      this.$http
        .get(
          `/letter/bgguaranteeletter/obtainDownloadLetter/` +
            id +
            '?type=' +
            type +
            '&key=' +
            key
        )
        .then(({ data: res }) => {
          if (res.data) {
            window.open(res.data, '_blank')
          } else {
            this.$confirm('正在生成文件，请稍后再试！', '提示', {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'warning'
            }).then(() => {})
          }
        })
        .catch((rej) => {})
    },
    downloadDB () {
      console.log(1)
      // let data = []
      if (this.fileData.filter((a) => a.type === 5).length === 0) {
        return false
      } else {
        var data = [...this.fileData.filter((a) => a.type === 5)]
      }
      console.log(data[0])
      window.open(
        `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${data[0].id}` + '?token=' +
          Cookies.get('token')
      )
    },
    isLv (val) {
      const lv = ['lydbbh', 'lybhyh', 'zfdbbh', 'yfkdbbh']
      return lv.includes(val)
    },
    async pay (letterId, key) {
      console.log(letterId, key)
      this.$confirm(
        '警告：支付时请根据招标文件要求慎重选择支付方式，如因支付方式选择错误导致保函失效，不予退款！',
        '提示',
        {
          confirmButtonText: '支付',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(async () => {
          this.$loading({
            lock: true,
            text: `请求支付中`
          })
          var data = await this.getPayMentInfo(letterId, key)
          console.log(data)
          if (data.code !== 0) {
            this.$message.error(data.msg)
            this.$loading().close()
            return
          }
          var payInfo = data.data
          this.$loading().close()
          window.open(encodeURI(payInfo), '_blank')

          console.log(payInfo)

          // this.$loading().close()
          // window.open(encodeURI(payInfo), '_blank')
        })
        .catch(() => {})
    },
    getPayMentInfo (letterId, key) {
      // 获取信息
      return new Promise((resolve, reject) => {
        this.$http
          .get(
            `/letter/bgguaranteeletter/getPayUrl/` + letterId + `?key=${key}`
          )
          .then(({ data: res }) => {
            this.updateLoading = false
            resolve(res)
          })
          .catch((rej) => {
            reject(rej.msg)
          })
      })
    },
    goPrveStep () {
      // if (this.$route.query.JumpName) {
      //   this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
      //     (item) => item.name !== this.$route.name
      //   )
      //   this.$router.push({
      //     path: `letter-${this.$route.query.JumpName}`
      //   })
      // }
      // console.log(window.history)
      if (window.history.length > 1) {
        window.history.go(-1)
      } else {
        this.$router.push({
          path: 'letter-bgguaranteeletterSqf'
        })
      }
    }

  }
}
</script>
<style lang="scss" scoped>
.con-title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  // border-left: 6px solid rgb(241, 130, 65);
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-bottom: 10px;
}
</style>
