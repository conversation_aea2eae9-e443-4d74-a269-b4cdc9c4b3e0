<!--
 * @Descripttion:
 * @Author: kong<PERSON>qiang
 * @Date: 2019-12-18 16:52:10
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-03-17 09:15:04
 -->
<template>
  <div class="inSteps">
    <!-- {{active}} -->
     <el-steps :active="active" finish-status="success" process-status='process' align-center ref='steps'>
       <!-- {{this.$store.state.pdfType}} -->
        <el-step title="法务部初核"></el-step>
        <el-step title="法务部复核"></el-step>
        <el-step title="主管审核"></el-step>
        <el-step title="出具"></el-step>
        <!-- <el-step title="财务审核"></el-step> -->
      </el-steps>
  </div>
</template>
<script>
export default {
  data () {
    return {

    }
  },
  mounted () {
    // this.$refs.steps.$children.map((item, index) => {
    //   item.$el.getElementsByClassName('el-step__head')[0].lastChild.innerHTML =
    //     index + 1
    // })
  },
  props: {
    active: {
      type: Number
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
