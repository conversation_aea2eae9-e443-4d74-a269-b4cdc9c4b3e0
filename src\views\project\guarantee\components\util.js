/*
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-02-19 10:40:46
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-02-19 11:01:15
 */
import { newWin } from '@/utils'
import Vue from 'vue'

const pay = async function (letterId, key) {
  Vue.prototype.$confirm(
    '警告：支付时请根据招标文件要求慎重选择支付方式，如因支付方式选择错误导致保函失效，不予退款！',
    '提示',
    {
      confirmButtonText: '支付',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(async () => {
      Vue.prototype.$loading({
        lock: true,
        text: `请求支付中`
      })
      var data = await getPayMentInfo(letterId, key)
      if (data.code !== 0) {
        Vue.prototype.$loading().close()
        return
      }
      var payInfo = data.data
      Vue.prototype.$loading().close()
      newWin(encodeURI(payInfo))
    })
    .catch(() => {})
}
function getPayMentInfo (letterId, key) {
  // 获取信息
  return new Promise((resolve, reject) => {
    Vue.prototype.$http
      .get(
        `/letter/bgguaranteeletter/getPayUrl/` + letterId + `?key=${key}`
      )
      .then(({ data: res }) => {
        resolve(res)
      })
      .catch((rej) => {
        reject(rej.msg)
      })
  })
}
export { pay }
