<template>
  <div class="site-wrapper site-page--login">
    <div class="site-content__wrapper">
      <div class="site-content">
        <!-- <div class="brand-info">
          <h2 class="brand-info__text">应用场景</h2>
          <h3 class="brand-info__intro">各级政府采购办、各级公共资源管理中心、各级招标代理机构、各大保险机构</h3>
          <h2 class="brand-info__text">标准化流程</h2>
          <h3 class="brand-info__intro">保函申请→信息审核→保费支付→保函下发与使用</h3>
          <h2 class="brand-info__text">流程简化 操作简单 管理规范</h2>
        </div> -->
        <img :src="bgimg" class="loginrightimg">
        <div class="login-border-text"></div>
        <div class="login-main">
          <el-form v-if="!isCA" :model="dataForm" ref="dataForm" status-icon>
            <div class="login-logo"><img src="@/assets/img/logo1.png"></div>
            <p class="login-tip"><img style="width:75px;" src="@/assets/img/logo22.png"></p>
            <!-- <el-tabs v-model="activeName" @tab-click="handleClick">

              <el-tab-pane label="短信验证码登录" name="2">

                <el-form-item prop="mobile" :rules="[
                    {
                      required: this.activeName === '2',
                      message: this.$t('validate.required'),
                      trigger: 'change'
                    }
                  ]">
                  <el-input v-model="dataForm.mobile" placeholder="用户手机号">
                    <span slot="prefix" class="el-input__icon">
                      <svg class="icon-svg" aria-hidden="true">
                        <use xlink:href="#icon-user"></use>
                      </svg>
                    </span>
                  </el-input>
                </el-form-item>
                <el-form-item prop="smsyzm" :rules="[
                    {
                      required: this.activeName === '2',
                      message: this.$t('validate.required'),
                      trigger: 'change'
                    }
                  ]">
                  <el-input v-model="dataForm.smsyzm" size="small" style="width:174px;margin-right:15px;" placeholder="手机验证码">
                    <span slot="prefix" class="el-input__icon">
                      <svg class="icon-svg" aria-hidden="true">
                        <use xlink:href="#icon-code"></use>
                      </svg>
                    </span>
                  </el-input>
                  <el-button plain v-if="btnShow" style="padding: 9px 34px;" type="primary" size="small" @click="sendMsg">发送短信</el-button>
                  <el-button plain v-else type="primary" style="width:80px;" size="small" disabled>{{count}} s</el-button>
                </el-form-item>

              </el-tab-pane>
              <el-tab-pane label="密码登录" name="1" :rules="[
                    {
                      required: this.activeName === '1',
                      message: this.$t('validate.required'),
                      trigger: 'change'
                    }
                  ]"> -->
            <el-form-item prop="username" :rules="[
                    {
                      required: this.activeName === '1',
                      message: this.$t('validate.required'),
                      trigger: 'change'
                    }
                  ]">
              <el-input v-model.trim="dataForm.username" placeholder="用户名或手机号">
                <span slot="prefix" class="el-input__icon">
                  <svg class="icon-svg" aria-hidden="true">
                    <use xlink:href="#icon-user"></use>
                  </svg>
                </span>
              </el-input>
            </el-form-item>
            <el-form-item prop="password" :rules="[
                    {
                      required: this.activeName === '1',
                      message: this.$t('validate.required'),
                      trigger: 'change'
                    }
                  ]">
              <el-input v-model.trim="dataForm.password" type="password" :placeholder="$t('login.password')">
                <span slot="prefix" class="el-input__icon">
                  <svg class="icon-svg" aria-hidden="true">
                    <use xlink:href="#icon-lock"></use>
                  </svg>
                </span>
              </el-input>
            </el-form-item>
            <el-form-item prop="captcha" :rules="[
                    {
                      required: this.activeName === '1',
                      message: this.$t('validate.required'),
                      trigger: 'change'
                    }
                  ]">
              <el-row :gutter="20">
                <el-col :span="14">
                  <el-input v-model.trim="dataForm.captcha" :placeholder="$t('login.captcha')">
                    <span slot="prefix" class="el-input__icon">
                      <svg class="icon-svg" aria-hidden="true">
                        <use xlink:href="#icon-safetycertificate"></use>
                      </svg>
                    </span>
                  </el-input>
                </el-col>
                <el-col :span="10" class="login-captcha">
                  <img :src="captchaPath" @click="getCaptcha()">
                </el-col>
              </el-row>
            </el-form-item>

            <!-- </el-tab-pane>
                  <el-tab-pane label="第三方登录" name="3" :rules="[
                    {
                      required: this.activeName === '3',
                      message: this.$t('validate.required'),
                      trigger: 'change'
                    }
                  ]">
                <el-form-item prop="username">
                  <el-input v-model="dataForm.username" placeholder="用户名或手机号">
                    <span slot="prefix" class="el-input__icon">
                      <svg class="icon-svg" aria-hidden="true">
                        <use xlink:href="#icon-user"></use>
                      </svg>
                    </span>
                  </el-input>
                </el-form-item>
                <el-form-item prop="password" :rules="[
                    {
                      required: this.activeName === '3',
                      message: this.$t('validate.required'),
                      trigger: 'change'
                    }
                  ]">
                  <el-input v-model="dataForm.password" type="password" :placeholder="$t('login.password')">
                    <span slot="prefix" class="el-input__icon">
                      <svg class="icon-svg" aria-hidden="true">
                        <use xlink:href="#icon-lock"></use>
                      </svg>
                    </span>
                  </el-input>
                </el-form-item>
                <el-form-item prop="captcha" :rules="[
                    {
                      required: this.activeName === '3',
                      message: this.$t('validate.required'),
                      trigger: 'change'
                    }
                  ]">
                  <el-row :gutter="20">
                    <el-col :span="14">
                      <el-input v-model="dataForm.captcha" :placeholder="$t('login.captcha')">
                        <span slot="prefix" class="el-input__icon">
                          <svg class="icon-svg" aria-hidden="true">
                            <use xlink:href="#icon-safetycertificate"></use>
                          </svg>
                        </span>
                      </el-input>
                    </el-col>
                    <el-col :span="10" class="login-captcha">
                      <img :src="captchaPath" @click="getCaptcha()">
                    </el-col>
                  </el-row>
                </el-form-item>

              </el-tab-pane>
            </el-tabs> -->
            <el-form-item>
              <el-button type="primary" style="margin:15px 0;" @click="dataFormSubmitHandle()" :loading="btnLoading" class="w-percent-100 loginColor">{{ $t('login.title') }}</el-button>
            </el-form-item>
          </el-form>
          <!-- <el-row :gutter="20">
            <el-col :span="12">

              <el-button type="success" size="mini" v-if="registerVisible" class="w-percent-100" plain @click="toRes">{{ $t('login.register') }}</el-button>

            </el-col>
            <el-col :span="12">

              <el-button type="danger" size="mini" v-if="uppwdBtnVisible" class="w-percent-100" plain @click="updatePasswordHandle">{{ $t('updatePassword.title') }}</el-button>

            </el-col>
          </el-row> -->
          <div class="CA" v-if="isCA" style="width:100%;padding: 0 20px;">
            <el-button type="primary" class="w-percent-100" :loading='btnLoading' @click="isIE?beforeSubmit():msg()">CA登录</el-button>
            <span class="tip" v-if="!isIE">注：CA登录只支持IE登录</span>
          </div>
          <el-divider>
            <!-- <el-link type="primary" @click="checkLogin()">{{!isCA?'CA登录':'登录'}}</el-link> -->
            <span style="color: #909399;font-family: '黑体';">其他登录方式</span>
          </el-divider>
          <div class="ourther">
            <div>
              <p><img @click="isIE?beforeSubmit():msg()" src="@/assets/img/ca.png" alt=""><span>CA登录</span></p>
            </div>
            <div>
              <p><img @click="showDia('手机登录','2')" src="@/assets/img/sj.png" alt=""><span>手机登录</span></p>
            </div>
            <div>
              <p><img @click="showDia('招标通登录','3')" src="@/assets/img/zbt.png" alt=""><span>招标通登录</span></p>
            </div>
          </div>
          <div class="SignContainer-tip">
            <span>忘记密码？点击 <a style="color:#409eff" @click="updatePasswordHandle">修改密码</a></span>
            <span class="reg"><a class="reg" @click="toRes">注册</a></span>
          </div>
        </div>
      </div>
    </div>
    <loginDialog v-if="visible" ref="loginDialog" @closeDia="closeDia"></loginDialog>
    <!-- 弹窗, 修改密码 -->
    <update-password v-if="updatePassowrdVisible" ref="updatePassowrd"></update-password>
  </div>
</template>
<script>
import Cookies from 'js-cookie'
import screenfull from 'screenfull'
import debounce from 'lodash/debounce'
import { messages } from '@/i18n'
import UpdatePassword from '@/views/pages/update-password'
import { getUUID } from '@/utils'
import HebcaClient from '@/api/HebcaClient'
import loginDialog from './login-dialog'
export default {
  data () {
    return {
      bgimg: require('../../assets/img/loginbg.png'),
      visible: false,
      count: '',
      activeName: '1',
      i18nMessages: messages,
      uppwdBtnVisible: true,
      registerVisible: true,
      updatePassowrdVisible: false,
      captchaPath: '',
      dialog: false,
      form: {},
      dataForm: {
        loginType: '1',
        smsyzm: '',
        username: '',
        mobile: '',
        password: '',
        uuid: '',
        signCert: '',
        random: '',
        signData: '',
        captcha: ''
      },
      btnLoading: false,
      isCA: false,
      isIE: window.ActiveXObject !== undefined
    }
  },
  components: {
    UpdatePassword,
    loginDialog
  },
  computed: {},
  created () {
    this.dataForm.loginType = '1'
    this.getCaptcha()
  },
  methods: {

    showDia (name, type) {
      this.visible = true
      this.$nextTick(() => {
        this.dataForm.loginType = type
        this.$refs['loginDialog'].title = name
        this.$refs['loginDialog'].type = type
        this.$refs['loginDialog'].form = this.dataForm
        this.$refs['loginDialog'].init()
      })
    },
    closeDia () {
      this.getCaptcha()
      this.dataForm.loginType = '1'
    },
    checkLogin () {
      this.isCA = !this.isCA
    },
    msg () {
      this.$message.error('CA登录只支持ie浏览器！')
    },
    dialogClose (val) {
      this.dialog = val
    },
    toRes () {
      this.$router.push({
        name: 'register'
      })
    },
    // 修改密码
    updatePasswordHandle () {
      this.updatePassowrdVisible = true
      this.$nextTick(() => {
        this.$refs.updatePassowrd.init()
      })
    },
    // 获取验证码
    getCaptcha () {
      this.dataForm.uuid = getUUID()
      this.captchaPath = `${window.SITE_CONFIG['apiURL']}/captcha?uuid=${this.dataForm.uuid}`
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) {
            return false
          }
          this.btnLoading = true
          this.$http
            .post('/login', this.dataForm)
            .then(({ data: res }) => {
              this.btnLoading = false
              if (res.code !== 0) {
                this.getCaptcha()
                return
              }
              this.getPdfInfo(res.data.token)
            })
            .catch(() => {})
        })
      },
      1000,
      { leading: true, trailing: false }
    ),
    getAllDict () {
      this.$http
        .get(`sys/dict/type/getAllDict`)
        .then(({ data: res }) => {
          sessionStorage.setItem('dict', JSON.stringify(res.data))
        })
        .catch(() => {})
    },
    getPdfInfo (token) {
      this.$http
        .get(`/sys/params/getValue/${'isSign'}`)
        .then(({ data: res }) => {
          this.loading = false

          this.$store.state.pdfType = res.data
          sessionStorage.setItem('store', JSON.stringify(this.$store.state))
          Cookies.set('token', token)
          this.getAllDict()
          this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
            (item) => item.name === 'home'
          )
          this.$router.replace({ name: 'home' })
        })
        .catch(() => {})
    },
    // 全屏
    fullscreenHandle () {
      if (!screenfull.enabled) {
        return this.$message({
          message: this.$t('fullscreen.prompt'),
          type: 'warning',
          duration: 500
        })
      }
      screenfull.toggle()
    },

    beforeSubmit () {
      this.btnLoading = true
      this.dataForm.uuid = getUUID()
      this.$http
        .get(`${window.SITE_CONFIG['apiURL']}/random/${this.dataForm.uuid}`)
        .then(({ data: res }) => {
          this.btnLoading = false
          if (res.code !== 0) {
            return false
          } else {
            this.dataForm.random = res.data
            // this.dataForm.random = getUUID()
            var isIE = window.ActiveXObject !== undefined
            var hebcaClient = new HebcaClient()
            if (isIE) {
              /**
               *    身份认证流程第二步，登陆页面对后台产生的随机数进行签名
               *    并将签名结果及其证书提交到后台验证
               */
              try {
                var signCert = hebcaClient.GetSignCert()
                var source = this.dataForm.random
                var signData = hebcaClient.Sign(source)
                this.dataForm.signCert = signCert
                this.dataForm.signData = signData
              } catch (e) {
                return this.$message.error(
                  '证书信息获取失败,' + e.getMessages()
                )
              }
            } else {
              hebcaClient.Sign(this.dataForm.random, function (signature) {
                this.dataForm.signData = signature
                hebcaClient.GetCertB64(function (cert) {
                  this.dataForm.signCert = cert
                })
              })
            }
            if (
              this.dataForm.signData !== '' &&
              this.dataForm.random !== '' &&
              this.dataForm.signCert !== ''
            ) {
              this.$http
                .post('/caLogin', this.dataForm)
                .then(({ data: res }) => {
                  if (res.code !== 0) {
                    this.btnLoading = false
                    return
                  }
                  Cookies.set('token', res.data.token)
                  this.btnLoading = false
                  this.$router.replace({ name: 'home' })
                })
                .catch(() => {})
            } else {
              return this.$message.error('证书信息获取失败请重新点击登录按钮')
            }
          }
        })
        .catch(() => {
          return false
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.ourther {
  display: flex;
  div {
    flex: 1;
    p {
      width: 100%;
      margin: 0 auto;
      cursor: pointer;
      img {
        // width: 100%;
        width: 33px;
        display: block;
        margin: 0 auto 10px;
        transition: .1s all ease-in-out;
      }
      img:hover{
        transform: scale(1.1);
      }
      span {
        font-size: 12px;
        display: block;
        text-align: center;
        width: 100%;
        color: #333;
      }
    }
  }
}
.login-logo {
  overflow: hidden;
  width: 110px;
  height: 110px;
  margin: -75px auto 20px auto;
  border-radius: 50%;
  -webkit-box-shadow: 0 4px 40px rgba(0, 0, 0, 0.15);
  box-shadow: 0 4px 40px rgba(0, 0, 0, 0.15);
  padding: 10px;
  background-color: #fff;
  z-index: 1;
  position: relative;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 20px;
  img {
    width: 100%;
    position: relative;
    // top: 10px;
  }
}
.login-tip {
  color: #409eff;
  text-align: center;
  font-weight: 700;
  font-size: 16px;
}
.login-border-text {
  z-index: 3;
  display: flex;
  position: absolute;
  left: 150px;
  top: calc(50% - 300px);
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 150px 40px 25px 100px;
  width: 90%;
  height: 680px;
  background: rgba(0, 0, 0, 0.3);
  animation: mymove 1s infinite;
  animation-iteration-count: 1; /*动画只执行一次*/
  -moz-animation-iteration-count: 1;
  -webkit-animation-iteration-count: 1;
  animation-fill-mode: forwards; /*让动画停留在最后一帧 */
  -moz-animation-fill-mode: forwards;
  -webkit-animation-fill-mode: forwards;
  -o-animation-fill-mode: forwards;
}
.loginrightimg {
  height: 100%;
  position: absolute;
  z-index: 4;
  right: 0;
  top: 0;
  animation: mymoveright 1s infinite;
  animation-iteration-count: 1; /*动画只执行一次*/
  -moz-animation-iteration-count: 1;
  -webkit-animation-iteration-count: 1;
  animation-fill-mode: forwards; /*让动画停留在最后一帧 */
  -moz-animation-fill-mode: forwards;
  -webkit-animation-fill-mode: forwards;
  -o-animation-fill-mode: forwards;
}
@keyframes mymove {
  from {
    left: 0px;
  }
  to {
    left: 150px;
  }
}
@keyframes mymoveright {
  from {
    right: -450px;
  }
  to {
    right: 0;
  }
}
.el-form {
  padding: 0 !important;
}
.reg {
  color: #409eff;
  font-size: 18px;
  font-weight: bold;
}
.loginColor {
  background: #409eff;
  text-indent: 20px;
  /* font-weight: 600; */
  font-family: 'neo';
  font-size: 15px;
  letter-spacing: 20px;
  letter-spacing: 20px;
}
.el-tab-pane {
  margin-top: 15px;
}
.site-wrapper.site-page--login {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(38, 50, 56, 0.17);
  overflow: hidden;
  &:before {
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    content: '';
    background-image: url(~@/assets/img/bg.jpg);
    background-size: auto 100%;
  }
  .CA {
    padding: 0 20px;
  }
  .tip {
    display: inline-block;
    padding: 30px 0;
    font-size: 14px;
    color: #f56c6c;
  }
  .login-brand {
    font-size: 16px;
    color: #333;
    line-height: 20px;
    padding-left: 5px;
    text-indent: 20px;
    margin-bottom: 35px;
  }
  .site-content__wrapper {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 0;
    margin: 0;
    overflow-x: hidden;
    overflow-y: hidden;
    background-color: transparent;
  }
  .site-content {
    min-height: 100%;
    overflow-x: hidden;
    overflow-y: hidden;
    padding: 30px 500px 30px 30px;
  }
  .brand-info {
    margin: 220px 100px 0 90px;
    color: #fff;
  }
  .brand-info__text {
    margin: 0 0 22px 0;
    color: #ccc;
    font-weight: 700;
    font-size: 28px;
    text-transform: uppercase;
    position: relative;
    .line {
      position: absolute;
      left: 55px;
      transform: translate(-50%, 0);
      top: 40px;
      width: 60px;
      height: 0;
      border-top: 1px solid white;
      opacity: 0.6;
    }
  }
  .brand-info__title {
    margin: 0 0 35px 0;
    color: rgba(252, 247, 251, 1);
    font-weight: 700;
    font-size: 38px;
    text-transform: uppercase;
    position: relative;
    .line {
      position: absolute;
      left: 55px;
      transform: translate(-50%, 0);
      top: 40px;
      width: 60px;
      height: 0;
      border-top: 1px solid white;
      opacity: 0.6;
    }
  }
  .brand-info__intro {
    margin: 0 auto;
    line-height: 26px;
    font-size: 16px;
    color: #ccc;
    padding-bottom: 30px;
  }
  .login-main {
    z-index: 5;
    position: absolute;
    top: 43%;
    left: 300px;
    padding: 20px 45px 100px;
    // box-shadow: 0 0 30px #333;
    box-shadow: 0 7px 30px rgba(0,0,0,0.25);
    width: 400px;
    margin-top: -170px;
    max-height: 530px;
    /* min-height: 100%; */
    background-color: white;
    border-radius: 15px;
  }
  .login-title {
    font-size: 16px;
  }
  .login-captcha {
    overflow: hidden;
    > img {
      width: 100%;
      cursor: pointer;
    }
  }
  .login-btn-submit {
    width: 100%;
    margin-top: 38px;
  }
}
.SignContainer-tip {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 12px 24px;
  color: grey;
  font-size: 13px;
  background-color: #f6f6f6;
  span {
    cursor: pointer;
  }
}

@media screen and (max-width: 1360px) {
   .login-main{
    zoom: 0.8;
  }
   .site-wrapper.site-page--login .login-main {
    top: 40%;
   }
}
@media screen and (max-width: 751px) {
  .site-wrapper.site-page--login .login-main {
    z-index: 5;
    position: absolute;
    top: 50%;
    left: 5%;
    width: 90%;
    top: 43%;
    padding: 20px 45px 100px;
    // box-shadow: 0 0 30px #333;
    box-shadow: 0 7px 30px rgba(0,0,0,0.25);
    margin-top: -170px;
    max-height: 530px;
    /* min-height: 100%; */
    background-color: white;
    border-radius: 15px;
  }
}
</style>
