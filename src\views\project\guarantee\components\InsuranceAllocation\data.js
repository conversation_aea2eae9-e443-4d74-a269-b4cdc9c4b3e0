/*
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-13 18:37:21
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-05-09 09:44:30
 */
import moment from 'moment'
export function concatData (obj) {
  let date = new Date().getTime()
  let endYear = moment(obj.endDate).format('YYYY')
  let endMonth = moment(obj.endDate).format('MM')
  let endDay = moment(obj.endDate).format('DD')
  let year = moment(date).format('YYYY')
  let month = moment(date).format('MM')
  let day = moment(date).format('DD')
  let openBidYear = moment(obj.startDate).format('YYYY')
  let openBidMonth = moment(obj.startDate).format('MM')
  let openBidDay = moment(obj.startDate).format('DD')
  let contractYear = moment(obj.contractDate).format('YYYY')
  let contractMonth = moment(obj.contractDate).format('MM')
  let contractDay = moment(obj.contractDate).format('DD')
  return {
    endYear: endYear === 'Invalid date' ? '' : endYear,
    endMonth: endMonth === 'Invalid date' ? '' : endMonth,
    endDay: endDay === 'Invalid date' ? '' : endDay,
    year: year,
    month: month,
    day: day,
    openBidYear: openBidYear === 'Invalid date' ? '' : openBidYear,
    openBidMonth: openBidMonth === 'Invalid date' ? '' : openBidMonth,
    openBidDay: openBidDay === 'Invalid date' ? '' : openBidDay,
    contractYear: contractYear === 'Invalid date' ? '' : contractYear,
    contractMonth: contractMonth === 'Invalid date' ? '' : contractMonth,
    contractDay: contractDay === 'Invalid date' ? '' : contractDay
  }
}
