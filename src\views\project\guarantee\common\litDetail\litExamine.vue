<!--
 * @Descripttion:
 * @Author: kong<PERSON>qiang
 * @Date: 2020-08-06 18:47:13
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-06-23 11:14:09
-->
<template>
  <el-drawer :visible.sync="visible" title="审核" :close-on-click-modal="false" :close-on-press-escape="false" size="700px">
    <div class="litE" >
      <!-- {{dataForm}} -->
      <el-form ref="form" :model="form" :rules="dataRule" label-width="140px" v-if="Number(dataForm.auditSort)==$store.state.user.auditProcedures&&$store.state.user.auditProcedures!=4">
        <el-form-item label="状态" prop="auditStatus">
          <el-radio-group v-model="form.auditStatus">
            <el-radio :label="1">通过</el-radio>
            <el-radio :label="2">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <template v-if="form.auditStatus==1&&Number(dataForm.auditSort)==1">
          <el-form-item label="合同编号" prop='contractNo'>
            <!-- <el-input v-model="dataForm.contractNo" class="wd180" size="mini"></el-input> -->
            亿&nbsp;<el-input style="width:70px;" size="mini" v-model="bTitle"></el-input>
            保[&nbsp;<el-input style="width:70px;" size="mini" v-model="bYear"></el-input>&nbsp;]
            <el-input style="width:70px;" size="mini" v-model="bNum"></el-input>&nbsp;号
          </el-form-item>

          <el-form-item label="保函编号" prop='guaranteeNo'>
            亿&nbsp;<el-input style="width:70px;" size="mini" v-model="bTitle" readonly></el-input>
            保函[&nbsp;<el-input style="width:70px;" size="mini" v-model="bYear" readonly></el-input>&nbsp;]
            <el-input style="width:70px;" size="mini" v-model="bNum" readonly></el-input>&nbsp;号
          </el-form-item>

          <el-form-item label="审批表编号" prop='approvalNo'>
            亿&nbsp;<el-input style="width:70px;" size="mini" v-model="bTitle"></el-input>
            审[&nbsp;<el-input style="width:70px;" size="mini" v-model="bYear"></el-input>&nbsp;]
            <el-input style="width:70px;" size="mini" v-model="bNum"></el-input>&nbsp;号
          </el-form-item>
          <el-form-item label="法务部初审意见" prop="fwbFirstInstance">
            <el-input type="textarea" :rows="3" placeholder="法务部初审" v-model="form.fwbFirstInstance">
            </el-input>
          </el-form-item>
          <el-form-item label="法务部初审签字" prop="fwbFirstName">
            <el-input placeholder="法务部初审签字" v-model="form.fwbFirstName">
            </el-input>
          </el-form-item>
          <el-form-item label="法务部初审日期" prop="fwbFirstDate">
            <el-date-picker v-model="form.fwbFirstDate" value-format="yyyy-MM-dd" type="date" placeholder="选择日期时间" align="right" :picker-options="pickerOptions">
            </el-date-picker>
          </el-form-item>
        </template>
          <template v-if="form.auditStatus==1&&Number(dataForm.auditSort)==2">
          <el-form-item label="法务部复审意见" prop="fwbRepeatInstance">
            <el-input type="textarea" :rows="3" placeholder="法务部复审" v-model="form.fwbRepeatInstance">
            </el-input>
          </el-form-item>
          <el-form-item label="法务部复审签字" prop="fwbRepeatName">
            <el-input placeholder="法务部复审签字" v-model="form.fwbRepeatName">
            </el-input>
          </el-form-item>
          <el-form-item label="法务部复审日期" prop="fwbRepeatDate">
            <el-date-picker v-model="form.fwbRepeatDate" value-format="yyyy-MM-dd" type="date" placeholder="选择日期时间" align="right" :picker-options="pickerOptions">
            </el-date-picker>
          </el-form-item>
        </template>
        <template v-if="form.auditStatus==1&&Number(dataForm.auditSort)==3">
          <el-form-item label="主管审核" prop="zgFirstInstance">
            <el-input type="textarea" :rows="3" placeholder="主管审核" v-model="form.zgFirstInstance">
            </el-input>
          </el-form-item>
          <el-form-item label="主管签字" prop="zgFirstName">
            <el-input placeholder="主管签字" v-model="form.zgFirstName">
            </el-input>
          </el-form-item>
          <el-form-item label="主管审核日期" prop="zgFirstDate">
            <el-date-picker v-model="form.zgFirstDate" type="date" value-format="yyyy-MM-dd" placeholder="选择日期时间" align="right" :picker-options="pickerOptions">
            </el-date-picker>
          </el-form-item>
        </template>
        <el-form-item label="不通过原因" prop="auditOpinion" v-if="form.auditStatus==2">
          <el-input type="textarea" :rows="3" placeholder="请输入不通过原因" v-model="form.auditOpinion">
          </el-input>
        </el-form-item>
        <el-divider></el-divider>
      </el-form>
      <div>
        <auditRecords :dataForm='dataForm' ref="auditRecords"></auditRecords>
      </div>
    </div>
    <div style="text-align:center; margin-top:15px;">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()" v-if="Number(dataForm.auditSort)==$store.state.user.auditProcedures">确认</el-button>
    </div>
  </el-drawer>
</template>

<script>
import debounce from 'lodash/debounce'
import auditRecords from './auditRecords'
import moment from 'moment'
import { rule } from './rule'
export default {
  data () {
    return {
      visible: false,
      type: 'bh', // bh:保函审核 cw:财务审核 pg:批改审核
      remark: '',
      dataForm: {},
      options: [],
      value: 1,
      btnLoading: false,
      visibleBH: false,
      pickerOptions: {
        shortcuts: [
          {
            text: '今天',
            onClick (picker) {
              picker.$emit('pick', new Date())
            }
          }
        ]
      },
      bTitle: '',
      bYear: '',
      bNum: '',
      bhTitle: '',
      bhYear: '',
      bhNum: '',
      spTitle: '',
      spYear: '',
      spNum: '',
      tempId: '',
      form: {
        auditStatus: 1,
        auditOpinion: '',
        contractNo: '',
        guaranteeNo: '',
        approvalNo: '',
        tempId: '',
        fwbFirstInstance: '',
        fwbFirstName: '',
        fwbFirstDate: '',
        fwbRepeatInstance: '',
        fwbRepeatName: '',
        fwbRepeatDate: '',
        zgFirstInstance: '',
        zgFirstName: '',
        zgFirstDate: '',
        zjlFirstName: '',
        zjlFirstDate: ''
      }
    }
  },
  components: {
    auditRecords
  },
  props: {},
  computed: {
    dataRule () {
      return rule
    }
  },
  mounted () {},
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        // this.$refs['form'].resetFields()
        this.$refs.auditRecords.getList()
        // this.getModelList()
        let time = moment(new Date()).format('YYYY')
        this.bYear = time
        this.bhYear = time
        this.spYear = time
        this.setVal()
        if (this.dataForm.auditSort === '3') {
        }
      })
    },
    setVal () {
      this.$set(this.form, 'contractNo', `亿${this.bTitle}保(${this.bYear})${this.bNum}号`)
      this.$set(this.form, 'guaranteeNo', `亿${this.bTitle}保函(${this.bYear})${this.bNum}号`)
      this.$set(this.form, 'approvalNo', `亿${this.bTitle}审(${this.bYear})${this.bNum}号`)
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        if (Number(this.dataForm.auditSort) === 1 && this.form.auditStatus === 1) {
          if (!this.bTitle || !this.bYear || !this.bNum) {
            return this.$message.error('请输入完整合同编号')
          }
        }
        this.setVal()
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false
          }
          this.submit()
        })
      },
      1000,
      { leading: true, trailing: false }
    ),
    // getModelList () {
    //   this.$http
    //     .get(
    //       `/letter/bgguaranteetemplate/getListByIssueCodeAndType?issueCode=${this.dataForm.issueCode}&type=0&guaranteeType=${this.dataForm.guaranteeTypeCode}`
    //     )
    //     .then(({ data: res }) => {
    //       this.options = res.data
    //       this.tempId = this.options[0].id
    //       this.setModel()
    //     })
    // },
    // setModel () {
    //   this.$http
    //     .get(
    //       `/letter/bgletterlitigation/updateTempId?letterId=${this.dataForm.letterId}&tempId=${this.tempId}`
    //     )
    //     .then(({ data: res }) => {
    //       if (res.code !== 0) {
    //         return false
    //       }
    //       // this.$message.success('选择模板成功')
    //     })
    // },
    submit () {
      this.$http
        .post('/letter/bgletterlitigation/audit', {
          letterId: this.$route.query.seeLetterId,
          auditSort: this.dataForm.auditSort,
          ...this.form
        })
        .then(({ data: res }) => {
          this.$message.success('审核成功！')
          this.visible = false
          if (Number(this.dataForm.auditSort) === 3 && this.form.auditStatus === 1) {
            // this.getModelList()
          }
          this.$emit('refresh', this.form.auditStatus)
          this.$nextTick(() => {
            this.$refs.auditRecords.getList()
          })
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 22px !important;
}
.litE{
  height: calc(100vh - 140px);
  overflow-y: scroll;
  padding:0 20px;
}
</style>
