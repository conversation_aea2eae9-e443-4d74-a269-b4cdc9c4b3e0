<template>
    <div>
    <el-row
      :gutter="20"
      class="con2"
    >
      <el-col :md="24" :lg="12">
        <el-card
          class="box-card"
          shadow="hover"
        >
          <div
            slot="header"
            class="clearfix bim-header"
            shadow="hover"
          >
            <span>新闻动态</span>
           <!-- <el-button
              style="float: right; padding: 3px 0"
              type="text" @click="goPath('/main/demo-news')"
            >更多>></el-button>-->
          </div>
          <div class="item news">
            <div
              class="newsCon"
              v-for="(item,index) in news"
              :key='index'
              @click="goNews(item.id,1)"
            >
              <div>{{item.title}} </div>
              <div>{{item.pubDate | fomatedate}}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :md="24" :lg="12">
        <el-card
          class="box-card"
          shadow="hover"
        >
          <div
            slot="header"
            class="clearfix bim-header"
            shadow="hover"
          >
            <span>通知公告</span>
            <!--el-button
              style="float: right; padding: 3px 0"
              type="text" @click="goPath('/main/demo-news')"
            >更多>></el-button>-->
          </div>
          <div class="item news">
            <div
              class="newsCon"
              v-for="(item,index) in Notice"
              :key='index'
               @click="goNews(item.id,2)"
            >
              <div>{{item.title}} </div>
              <div>{{item.pubDate | fomatedate}}</div>
            </div>
          </div>
        </el-card>
      </el-col>
       </el-row>
    </div>
</template>
<script>
export default {
  filters: {
    fomatedate (val) {
      //
      return val.substring(0, 10)
    }
  },
  data () {
    return {
      // 获取新闻公告信息  传入参数  类型 1公告 2新闻 和条数限制
      getNewsLimitURL: '/demo/news/dashboard',
      news: [],
      Notice: []
    }
  },
  created () {
    this.getNews()
    this.getNotice()
  },
  methods: {
    // 获取新闻
    getNews () {
      let params = {
        newType: 2,
        limit: 6
      }
      this.$http.get(`${this.getNewsLimitURL}`, {
        params: params
      }).then(({ data: res }) => {
        this.news = res.data
      })
    },
    // 获取公告
    getNotice () {
      let params = {
        newType: 1,
        limit: 6
      }
      // ?newType=${1}&limit=${6}
      this.$http.get(`${this.getNewsLimitURL}`, {
        params: params
      }).then(({ data: res }) => {
        //

        this.Notice = res.data
      })
    },
    goNews (id, type) {
      //
      if (type === 1) {
        this.$http.get(`/demo/news/${id}`).then(({ data: res }) => {
          //
          this.data = res.data
          var str = res.data.content
          window.open(this.IsURL(str), '_blank')
        })
        return
      }

      let routeData = this.$router.resolve({
        name: 'news',
        params: { 'id': id }
      })
      window.open(routeData.href, '_blank')
    },
    goPath (path) {
      this.$router.push({
        path: path
      })
    },
    // eslint-disable-next-line camelcase
    IsURL (str_url) {
      // eslint-disable-next-line no-useless-escape
      var re = /(http:\/\/)?([A-Za-z0-9]+\.[A-Za-z0-9]+[\/=\?%\-&_~`@[\]\':+!]*([^<>\"\"])*)/g
      let str = str_url.match(re)[0]
      return `http://${str}`
    }
  }
}
</script>
<style lang="scss" scoped>
.con2 {
  .el-col {
    height: 325px;
    // background: white;
    .el-card {
      height: 100%;
    }
    .el-card__header {
      padding: 10px 20px !important;
    }
  }
}

.bim-header {
  position: relative;
  line-height: 22px;
  .year {
    width: 80px;
    position: absolute;
    right: 70px;
    top: -3px;
  }
}
.news {
  .newsCon {
    overflow: hidden;
    padding: 10px 0;
    border-bottom: 1px solid #ebeef5;
    cursor: pointer;
    div:first-child {
      float: left;
    }
    div:last-child {
      float: right;
    }
  }
  .newsCon:first-child {
    padding-top: 0;
  }
  .newsCon:last-child {
    border-bottom: none;
  }
}

</style>
