<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2021-03-08 09:27:45
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-03-16 17:27:32
-->
<template>
  <div>
    <el-form class="diaForm" label-position="right" ref="diaForm" :model="data" :rules="rule" label-width="120px">
      <el-form-item :label=" this.type ==='1' ?'申请人类型':'被申请人类型'" prop='groupOrIndividual'>
        <el-select v-model="data.groupOrIndividual" size="medium" @change='initSetForm' :placeholder="'请选择'+this.type ==='1' ?'申请人类型':'被申请人类型'">
          <el-option label="公司" :value="1"></el-option>
          <el-option label="自然人" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <template v-if="data.groupOrIndividual">
        <el-form-item prop="userName"  label="名称">
          <el-input v-model="data.userName" size="medium"></el-input>
        </el-form-item>
        <!-- {{data.documentType}} -->
        <el-form-item prop="documentType"  label="证件类型">
          {{data.documentType=='1'?'身份证':'社会统一信用代码'}}
        </el-form-item>
        <el-form-item prop="cardNumber"  label="证件号">
          <el-input v-model="data.cardNumber" size="medium"></el-input>
        </el-form-item>
        <el-form-item prop="userTel"  label="联系方式">
          <el-input v-model="data.userTel" size="medium"></el-input>
        </el-form-item>
        <el-form-item prop="legalPerson"  label="法定代表人" v-if="data.groupOrIndividual=='1'">
          <el-input v-model="data.legalPerson"  size="medium"></el-input>
        </el-form-item>
        <el-form-item prop="address"  label="住址">
          <el-input v-model="data.address" type="textarea" :rows="2" size="medium"></el-input>
        </el-form-item>
        <template v-if="type=='1'">
           <el-form-item prop="litigationAgent"  label="诉讼代理人">
              <el-input v-model="data.litigationAgent" size="medium"></el-input>
            </el-form-item>
            <el-form-item prop="litigationAgentTel"  label="代理人联系方式">
              <el-input v-model.number="data.litigationAgentTel" size="medium"></el-input>
            </el-form-item>
        </template>

      </template>
    </el-form>
  </div>
</template>
<script>
import { rule, rule2 } from './rule'
export default {
  data () {
    return {
      // rule: rule
    }
  },
  props: {
    data: {
      type: Object
    },
    type: String
  },
  computed: {
    rule () {
      return this.type === '1' ? rule : rule2
    }
  },
  methods: {
    init () {
      this.$nextTick(() => {
        this.$refs['diaForm'].resetFields()
      })
    },
    initSetForm (val) {
      console.log(val)
      // eslint-disable-next-line eqeqeq
      if (val == '1') {
        this.data.documentType = '2'
      }
      // eslint-disable-next-line eqeqeq
      if (val == '2') {
        this.data.documentType = '1'
      }
    },
    push () {
      return new Promise((resolve, reject) => {
        this.$refs['diaForm'].validate((valid) => {
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
//   .diaForm/deep/ .el-form-item, .diaForm/deep/ .el-input__inner {
//    width: 300px;
// }
</style>
