<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false"
    :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"
      :label-width="$i18n.locale === 'en-US' ? '180px' : '156px'">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="colConfig">
          <!-- <el-form-item label="标段编码" prop="code">
            <el-input v-model="dataForm.code" placeholder="标段编码"></el-input>
          </el-form-item> -->
          <el-form-item label="招标项目名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="招标项目名称"></el-input>
          </el-form-item>
          <!-- {{gTypeList}} -->
          <el-form-item label="所属出具方" prop="issueCode">
            <!-- {{issueCode}} -->
            <!-- <el-input v-model="dataForm.issueCode" placeholder="所属出具方"></el-input> -->
            <el-select v-model="issueCode"  @change="issueCodeChange" multiple placeholder="请选择">
              <el-option v-for="item in gTypeList"   :key="item.code" :label="item.name" :value="item.code">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="可选保函类型" prop="guaranteeType">
            <!-- {{guaranteeTypeList}} -->
            <!-- {{guaranteeType}} -->
            <el-checkbox-group v-model="guaranteeType" @change="guaranteeTypeChange">
              <el-checkbox :label="item.dictCode" v-for="(item,index) in guaranteeTypeList" :key="index">{{item.dictName}}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="投标日期" prop="tbDate">
            <el-date-picker size="small" class="wd240" value-format="yyyy-MM-dd" v-model="dataForm.tbDate" type="date"
              placeholder="选择投标日期" align="right">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="项目所属区域" prop="regionId">
            <span v-if="dataForm.region && casCilck">{{ dataForm.region | regFil }} &emsp;&emsp;<el-button type="text"
                @click="casCilck = false">修改</el-button></span>
            <template v-else>
              <el-cascader :options="regionIds" v-model.trim="regionId" ref='region' @change='regionIdChange'
                size="small" clearable class="wd180" style="width:250px;" :props="props"></el-cascader>
              &emsp;&emsp;<el-button type="text" v-if="dataForm.region" @click="casCilck = true">取消</el-button>
            </template>
          </el-form-item>
          <el-form-item label="开标日期" prop="bidOpenDate">
            <el-date-picker size="small" class="wd240" value-format="yyyy-MM-dd" v-model="dataForm.bidOpenDate"
              type="date" placeholder="选择开标日期" align="right">
            </el-date-picker>
          </el-form-item>
          <!--  <el-form-item label="标段ID" prop="biddingid">
            <el-input v-model="dataForm.biddingid" placeholder="标段ID"></el-input>
          </el-form-item>-->
          <el-form-item label="担保金额" prop="guaranteeAmount">
            <el-input-number placeholder="请输入担保金额" v-model="dataForm.guaranteeAmount" controls-position="right"
              :precision="0" :min="0" :max="10000000"></el-input-number>（元）
          </el-form-item>
          <el-form-item label="招标人" prop="tenderee">
            <el-input v-model="dataForm.tenderee" placeholder="招标人"></el-input>
          </el-form-item>
          <el-form-item label="招标代理公司" prop="tendereeAgent">
            <el-input v-model="dataForm.tendereeAgent" placeholder="招标代理公司"></el-input>
          </el-form-item>
          <!-- <el-form-item label="投标工程名" prop="biddingName">
            <el-input v-model="dataForm.biddingName" placeholder="投标工程名"></el-input>
          </el-form-item>
          <el-form-item label="所属项目部" prop="biddingToOffces">
            <el-input v-model="dataForm.biddingToOffces" placeholder="所属项目部"></el-input>
          </el-form-item>
          <el-form-item label="标段开始日期" prop="bidStartDate">
            <el-date-picker v-model="dataForm.bidStartDate" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择标段开始日期"></el-date-picker>
          </el-form-item>
          <el-form-item label="标段结束日期" prop="bidEndDate">
            <el-date-picker v-model="dataForm.bidEndDate" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择标段结束日期"></el-date-picker>
          </el-form-item>-->
          <!-- <el-form-item label="交易平台编码" prop="platformCode">
            <el-input v-model="dataForm.platformCode" placeholder="交易平台编码"></el-input>
          </el-form-item>-->
          <el-form-item label="状态" prop="status" size="mini">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="0">停用</el-radio>
              <el-radio :label="1">正常</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="招标文件" size="mini">
            <el-upload class="upload-demo" ref="upload" :on-remove='handleRemove' :action="uploadUrl" :data='zbData'
              :headers="myHeaders" :before-remove="BeforeRemove" :on-success='biddingDocumentSuccessHandle'
              :before-upload="beforeUpload" :limit="1" :on-exceed="handleExceed" :file-list="fileList">
              <el-button size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">招标文件只上传前附表，要求格式：.jpg,.JPG,.jpeg,.JPEG,.png,.PNG,.pdf,PDF</div>
            </el-upload>
          </el-form-item>
          <el-form-item label="备注信息" prop="remars">
            <el-input type="textarea" rows="3" v-model="dataForm.remars" placeholder="备注信息"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template slot="footer">
      <el-button @click="close()">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import Cookies from 'js-cookie'
import debounce from 'lodash/debounce'
import { getDict } from '@/utils/index'
export default {
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      regionId: '',
      regionIds: [],
      zbData: { type: '2' },
      myHeaders: {
        token: Cookies.get('token') || ''
      },
      gTypeList: getDict('出具机构'),
      guaranteeTypeList: getDict('电子保函类型'),
      fileList: [],
      casCilck: true,
      props: {
        value: 'code',
        label: 'name',
        children: 'child',
        isLeaf: 'leaf',
        lazy: true,
        lazyLoad: this.lazyLoad,
        collapseTags: true
      },
      uploadUrl: '',
      issueCode: [],
      guaranteeType: [],
      dataForm: {
        id: '',
        code: '',
        regionId: '',
        name: '',
        bidOpenDate: '',
        biddingid: '',
        guaranteeAmount: '',
        tenderee: '',
        tbDate: '',
        issueCode: '',
        tendereeAgent: '',
        biddingName: '',
        biddingToOffces: '',
        remars: '',
        bidStartDate: '',
        bidEndDate: '',
        platformCode: 'zbt',
        deptId: '',
        guaranteeType: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        code: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        issueCode: [
          {
            required: false,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        guaranteeAmount: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        tbDate: [
          {
            required: false,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        regionId: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        status: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  filters: {
    regFil (val) {
      return val
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.guaranteeType = []
        this.regionId = ''
        this.issueCode = []
        // this.casCilck = false
        this.dataForm.region = ''
        this.getIssue()
        if (this.dataForm.id) {
          this.regionId = ''
          this.uploadUrl =
            window.SITE_CONFIG['apiURL'] +
            '/letter/bgbiddingproject/biddingInvoice'
          this.zbData = { ...this.zbData, ...{ projectId: this.dataForm.id } }
          this.getInfo()
          this.getDocuments()
        }
      })
    },
    issueCodeChange (val) {
      this.dataForm.issueCode = val ? val.join(',') : ''
      console.log(this.dataForm.issueCode)
    },
    guaranteeTypeChange (val) {
      this.dataForm.guaranteeType = val ? val.join(',') : ''
      console.log(this.dataForm.guaranteeType)
    },
    async getIssue () {
      let { data } = await this.$http.get('letter/bgguaranteeissue/getAllIssue')
      console.log('getIssue', data)
      this.gTypeList = data.data
    },
    onPreview (file) {
      console.log(file)
      if (file.response) {
        window.open(
          `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${file.response.data.ossId}`
        )
      } else {
        window.open(
          `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${file.url}`
        )
      }
    },
    filterType (file) {
      if (file) {
        var extension = file.substring(file.lastIndexOf('.') + 1)
        return extension === 'pdf' || extension === 'PDF'
      }
    },
    biddingDocumentSuccessHandle (res, file, fileList) {
      if (res.data.success !== true) {
        this.fileList = []
        return this.$message.error(res.data.msg)
      }
      this.$message.success('上传成功')
      // this.$emit('biddingDocumentId', res.data.ossId)
      this.getDocuments()
    },
    BeforeRemove (file, fileList) {
      console.log(file, fileList)
      let a = true
      if (file && file.status === 'success') {
        a = this.$confirm(`确定移除 ${file.name}？`)
      }
      return a
    },
    handleRemove (file, fileList) {
      if (file && file.status === 'success') {
        // 移除方法
        var deleteId = file.url ? file.url : file.response.data.ossId
        return this.deleteFile(deleteId, 1)
      }
    },
    handleExceed (files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件`
      )
    },
    deleteFile (id, type) {
      return new Promise(async (resolve, reject) => {
        let { data } = await this.$http.get('sys/oss/delete/' + id + '/' + type)
        if (data.code !== 0) {
          this.$message.error(data.msg)
          resolve(false)
        }
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
        resolve(true)
      })
    },
    beforeUpload (file) {
      var extension = file.name.substring(file.name.lastIndexOf('.') + 1)
      var lastName =
        extension !== 'jpg' &&
        extension !== 'JPG' &&
        extension !== 'jpeg' &&
        extension !== 'JPEG' &&
        extension !== 'png' &&
        extension !== 'PNG' &&
        extension !== 'pdf' &&
        extension !== 'PDF'

      if (lastName) {
        this.$message.warning(
          '文件要求格式为：.jpg,.JPG,.jpeg,.JPEG,.png,.PNG,.pdf,PDF'
        )
      }

      return !lastName
    },
    async getDocuments () {
      let { data } = await this.$http.get(
        'letter/bgbiddingproject/getDocuments?projectId=' + this.dataForm.id
      )
      if (data.data) {
        this.fileList = [
          {
            name: '招标文件',
            url: data.data.id,
            type: this.filterType(data.data.url)
          }
        ]
      }
    },
    close () {
      this.visible = false
    },
    regionIdChange (val) {
      console.log(val)
      this.$emit('clearProAndBBr', val ? val[1] : '')
      this.$set(this.dataForm, 'regionId', val ? val[1] : '')
    },
    async lazyLoad (node, resolve) {
      // 点击节点
      const { level } = node
      let nodes = await this.lazyloadFn(level === 0 ? 100000 : node.value)
      let nodes2 = nodes.map((item) => ({
        code: item.code,
        name: item.name,
        leaf: level > 0
      }))

      resolve(nodes2)
    },
    lazyloadFn (parentCode) {
      // 获取node
      return new Promise((resolve, reject) => {
        this.$http
          .get(`/demo/tbregion/regionTree?code=${parentCode}`)
          .then(({ data: res }) => {
            resolve(res.data)
            // this.zongingList = res.data
          })
          .catch(() => { })
      })
    },
    // 获取信息
    getInfo () {
      this.$http
        .get(`/letter/bgbiddingproject/${this.dataForm.id}`)
        .then(({ data: res }) => {
          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
          if (this.dataForm.guaranteeType) {
            this.guaranteeType = this.dataForm.guaranteeType.split(',')
          }
          if (this.dataForm.issueCode) {
            this.issueCode = this.dataForm.issueCode.split(',')
          }
          // this.issueCode = this.dataForm.issueCode.split(',')
          // console.log(this.issueCode)
        })
        .catch(() => { })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) {
            return false
          }
          this.$http[!this.dataForm.id ? 'post' : 'put'](
            '/letter/bgbiddingproject/',
            this.dataForm
          )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            })
            .catch(() => { })
        })
      },
      1000,
      { leading: true, trailing: false }
    )
  }
}
</script>
