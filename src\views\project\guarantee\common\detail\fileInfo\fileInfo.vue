<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-08-25 09:50:02
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-09-24 10:26:42
-->
<template>
  <div>
    <div v-if="!ossId" style="text-align:center;padding:15px 0;color:#aaa;">暂无附件信息</div>
    <div style="max-width:1200px;margin:0 auto;" v-else>
      <img :src="src" alt="" style="width:100%;">
    </div>
  </div>
</template>
<script>

import Cookies from 'js-cookie'

export default {
  data () {
    return {
      src:
        `${window.SITE_CONFIG['apiURL']}` +
        '/letter/bgguaranteeletter/getPDFUrlById/' +
        this.$route.query.seeLetterId + '?token=' + Cookies.get('token')
    }
  },
  props: ['ossId'],
  created () {
  },
  methods: {

  }
}
</script>
