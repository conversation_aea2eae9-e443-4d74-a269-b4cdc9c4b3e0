<template>
  <div class="detail" v-loading='loading' :style="draw?'width:50%;':''">
   <el-card class="box-card " style="margin-bottom:15px;" shadow="never" v-if="$route.query.JumpName==='project-IssuedBy-litigation-index'">
      <step v-if="dataForm.auditSort" :active='Number(dataForm.auditSort)-1'></step>
   </el-card>
   <!-- <operation :dataForm='this.dataForm' v-if="$route.query.JumpName==='project-IssuedBy-litigation-index'" @refresh='getDetail'></operation> -->
   <operationUser :dataForm='this.dataForm'  @refresh='getDetail'></operationUser>
    <el-form :model="dataForm" v-if="JSON.stringify(dataForm) !== '{}'" label-position="left" ref="dataForm" label-width="145px">
      <el-card class="box-card " shadow="never">
        <el-row :gutter="40">
          <el-col class="header" :span="24" style="margin-bottom:20px;">
            <!-- 头部 -->
            <span class="header-title">
              保函类型： {{dataForm.guaranteeTypeCode?certType(dataForm.guaranteeTypeCode,'insuranceTypeoptions'):''}}
            </span>
            <!-- 头部操作 -->
            <!-- <headOperation :options='this.options' @drawer='drawer' :draw='draw' :fileData='fileData' :dataForm='this.dataForm' @down='down' /> -->
          </el-col>
          <statusOperation :dataForm='this.dataForm' @refresh='getDetail'></statusOperation>
          <caseSituationInfo :draw='draw' :dataForm='this.dataForm.caseSituation' />
          <guaranteeInfo :draw='draw' :dataForm='{...this.dataForm.litigationDTO,...this.dataForm.scUserDTO}'/>
          <codeInfo :draw='draw' :dataForm='this.dataForm.codeInfo' />
          <linkDto :draw='draw' :dataForm='this.dataForm'></linkDto>
          <payInfo v-if="dataForm.payInfo.ossId" :dataForm='this.dataForm.payInfo'/>
          <invoiceInfo v-if="dataForm.bgguaranteeinvoice" :dataForm='this.dataForm'/>
          <applicantInfo :draw='draw'  v-if="this.dataForm.scUserListSqr" :dataList='this.dataForm.scUserListSqr' />
          <respondentInfo :draw='draw' v-if="this.dataForm.scUserListBsqr" :dataList='this.dataForm.scUserListBsqr' />
        </el-row>
      </el-card>
      <!-- <el-card class="box-card " shadow="never">
        <el-row :gutter="40">
          <el-col class="header" :span="24" style="margin-bottom:20px;">
            <span class="header-title">
              保函文件列表
            </span>
          </el-col>
          <el-col class="header" :span="24" style="margin-bottom:20px;">
            <uploadList v-if="dataForm.letterId" detail :letterId='dataForm.letterId' :active='3' :dataForm='dataForm' :issueCode='dataForm.issueCode' :guaranteeTypeCodes='dataForm.guaranteeTypeCode'></uploadList>
          </el-col>
        </el-row>
      </el-card> -->
    </el-form>
  </div>
</template>
<script>
import dictionaries from '@/views/project/guarantee/components/dictionaries'
import uploadList from '@/views/project/guarantee/warrantGuarantee/compoents/uploadList'
import applicantInfo from '@/views/project/guarantee/common/litDetail/applicantInfo'
import respondentInfo from '@/views/project/guarantee/common/litDetail/respondentInfo'
import caseSituationInfo from '@/views/project/guarantee/common/litDetail/caseSituationInfo'
import guaranteeInfo from '@/views/project/guarantee/common/litDetail/guaranteeInfo'
import codeInfo from '@/views/project/guarantee/common/litDetail/codeInfo'
import invoiceInfo from '@/views/project/guarantee/common/litDetail/invoiceInfo'
import payInfo from '@/views/project/guarantee/common/litDetail/payInfo'
import linkDto from '@/views/project/guarantee/common/litDetail/linkDto'
import statusOperation from '@/views/project/guarantee/common/litDetail/statusOperation'
import step from '@/views/project/guarantee/common/litDetail/step'
import operation from '@/views/project/guarantee/common/litDetail/operation'
import operationUser from '@/views/project/guarantee/common/litDetail/operationUser'
export default {
  mixins: [dictionaries],
  // eslint-disable-next-line vue/no-unused-components
  components: { linkDto, payInfo, uploadList, applicantInfo, guaranteeInfo, codeInfo, respondentInfo, caseSituationInfo, invoiceInfo, step, operation, operationUser, statusOperation },
  data () {
    return {
      dataForm: {},
      loading: false,
      draw: false
    }
  },
  created () {
    this.getDetail()
  },
  activated () {
    this.getDetail()
  },
  methods: {
    certType (val, name) {
      var aa = this.options[name].filter((a) => a.dictCode === val)
      return aa.length > 0 ? aa[0].dictName : '-'
    },
    getDetail () {
      this.loading = true
      this.$http
        .get(
          `/letter/bgletterlitigation/getInfo/?letterId=` +
            this.$route.query.seeLetterId
        )
        .then(({ data: res }) => {
          this.loading = false
          if (res.code !== 0) {
            return false
          }
          this.dataForm = res.data
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.header {
  .header-title {
    display: block;
    float: left;
    margin-bottom: 0;
    padding-right: 12px;
    color: rgba(0, 0, 0, 0.75);
    border-left: 6px solid rgb(241, 130, 65);
    text-indent: 15px;
    font-weight: 600;
    font-size: 20px;
    background: rgba(241, 130, 65, 0.05);
    line-height: 45px;
  }
}
.detail {
  width: 100%;
  transition: width 0.3s ease-in-out;
  position: relative;
  .el-form-item {
    margin-bottom: 5px;
  }
  .el-tabs__header {
    padding: 0;
    position: relative;
    margin: 0 0;
    top: 19px;
  }
}

.de-con {
  margin: 15px;
}
.status {
  position: absolute;
  right: 0;
  top: 50px;
  text-align: right;
  padding-right: 30px !important;
  div:first-child {
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 10px;
  }
  .s_s {
    color: rgba(0, 0, 0, 0.85);
    font-size: 20px;
  }
}
.con-title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  // border-left: 6px solid rgb(241, 130, 65);
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-bottom: 10px;
}
</style>
