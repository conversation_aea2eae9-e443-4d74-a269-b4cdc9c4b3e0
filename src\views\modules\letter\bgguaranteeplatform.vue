<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-letter__bgguaranteeplatform}">
      <el-form :inline="true" :model="dataForm" >
        <el-form-item>
          <el-input v-model="dataForm.name" style="width:250px;" clearable placeholder="机构名称"></el-input>
        </el-form-item>
        <el-form-item prop="orgType">
          <el-select v-model="dataForm.orgType" clearable placeholder="请选择机构类型">
            <el-option v-for="item in orgTypeOptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="secretType">
          <el-select v-model="dataForm.secretType" clearable placeholder="请选择加密方式">
            <el-option v-for="item in secretTypeOptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="dataForm.status" clearable placeholder="请选择审核状态">
            <el-option v-for="item in statusoptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="danger" @click="getDataList(1)">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <!-- <el-form-item>
           <el-button type="primary" @click="importHandle()">{{ $t('import') }}</el-button>
         </el-form-item> -->
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgguaranteeplatform:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgguaranteeplatform:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
        <!-- <el-form-item>
          <el-button v-if="$hasPermission('letter:bgguaranteeplatform:update')" type="danger" @click="enableHandle()">{{ $t('enable') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgguaranteeplatform:update')" type="danger" @click="stopHandle()">{{ $t('stop') }}</el-button>
        </el-form-item> -->
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="name" label="机构名称" sortable="custom" header-align="center" align="center" width="170"></el-table-column>
        <el-table-column prop="orgType" label="机构类型" width="150px" sortable="custom" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag size="mini" type="danger"><span>{{fomatMethod(scope.row.orgType)}}</span></el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="phoneNumber" label="机构联系电话" sortable="custom" header-align="center" align="center" width="150"></el-table-column>
        <!--<el-table-column prop="guaranteeLimitAmount" label="担保金额限制" width="150px" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="bankAccountName" label="开户行名称" width="120px" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="bankAccountNo" label="银行账户号码" width="120px" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="bankName" label="银行名称" sortable="custom" header-align="center" align="center"></el-table-column>
            <el-table-column prop="bankNo" label="银行编码" sortable="custom" header-align="center" align="center"></el-table-column> -->
        <el-table-column prop="secretType" label="加密方式" width="120px" sortable="custom" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag size="mini" type="danger"><span>{{fomatMethodSecretType(scope.row.secretType)}}</span></el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="corporation" label="法定代表人" width="120px" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="linkman" label="联系人" width="120px" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="linkmanTel" label="联系人电话" width="120px" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="isIncludePlatformFee" label="是否收取平台使用费" width="190px" sortable="custom" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.isIncludePlatformFee == 1" size="small" type="danger">是</el-tag>
            <el-tag v-else size="small" type="success">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" sortable="custom" label="状态" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == 0" size="small" type="danger">未提交</el-tag>
            <el-tag v-if="scope.row.status == 1" size="small" type="info">待审核</el-tag>
            <el-tag v-if="scope.row.status == 2" size="small" type="success">已认证</el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button v-if="$hasPermission('letter:bgguaranteeplatform:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>

            <el-dropdown>
              <span class="el-dropdown-link">
                <el-button type="text" size="mini">更多操作<i class="el-icon-arrow-down el-icon--right"></i></el-button>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button type="text" size="small" @click="detail(scope.row.id)">浏览</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('letter:bgguaranteeplatform:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button type="text" size="small" @click="userInfoHandle(scope.row.deptId)">用户管理</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button type="text" size="small" @click="issueInfo(scope.row.id)">保函出具机构管理</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!--使用方浏览 -->
      <platform-detail v-if="detailVisible" ref="platformDetail" @refreshDataList="getDataList"></platform-detail>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
      <add-or-set v-if="addOrSetVisible" ref="addOrUpSet" @refreshDataList="getDataList"></add-or-set>
      <!-- 弹窗, 上传文件 -->
      <upload v-if="uploadVisible" ref="upload" @refreshDataList="getDataList"></upload>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './bgguaranteeplatform-add-or-update'
import AddOrSet from './bgguaranteeplatform-add-or-set'
import Upload from './bgguaranteeplatform-upload'
import platformDetail from './bgguaranteeplatform-detail'

export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgguaranteeplatform/page',
        getDataListIsPage: true,
        exportURL: '/letter/bgguaranteeplatform/export',
        deleteURL: '/letter/bgguaranteeplatform',
        enableURL: '/letter/bgguaranteeplatform/enable',
        stopURL: '/letter/bgguaranteeplatform/stop',
        deleteIsBatch: true
      },
      statusoptions: [
        {
          value: 0,
          label: '未提交'
        },
        {
          value: 1,
          label: '待审核'
        },
        {
          value: 2,
          label: '已认证'
        }
      ],
      dataForm: {
        name: '',
        orgType: '',
        secretType: '',
        status: '',
        isPlatform: 1
      },
      orderField: 'code',
      order: 'asc',
      uploadVisible: false,
      addOrSetVisible: false,
      orgTypeOptions: '',
      orgTypemaps: '',
      getDicListURL: '/sys/dict/type/',
      secretTypeOptions: '',
      secretTypemaps: '',
      detailVisible: false
    }
  },
  components: {
    AddOrUpdate,
    Upload,
    AddOrSet,
    platformDetail
  },
  activated () {
    this.getOrgTypeInfo()
    this.getSecretTypeInfo()
  },
  methods: {
    detail (id) {
      this.detailVisible = true
      this.$nextTick(() => {
        this.$refs.platformDetail.dataForm.id = id
        this.$refs.platformDetail.init()
      })
    },
    setHandle (id) {
      this.addOrSetVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpSet.platFormId = id
        this.$refs.addOrUpSet.init()
      })
    },
    // 导入
    importHandle () {
      this.uploadVisible = true
      this.$nextTick(() => {
        this.$refs.upload.init()
      })
    },
    // 状态启用具体代码需微调格式去除空格
    enableHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(
        this.$t('prompt.info', { handle: '启用' }),
        this.$t('prompt.title'),
        {
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          this.$http
            .put(
              this.mixinViewModuleOptions.enableURL,
              id ? [id] : this.dataListSelections.map(item => item.id)
            )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getDataList()
                }
              })
            })
            .catch(() => {})
        })
        .catch(() => {})
    },
    // 状态停用
    stopHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(
        this.$t('prompt.info', { handle: '停用' }),
        this.$t('prompt.title'),
        {
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          // 具体代码需微调格式去除空格
          this.$http
            .put(
              this.mixinViewModuleOptions.stopURL,
              id ? [id] : this.dataListSelections.map(item => item.id)
            )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getDataList()
                }
              })
            })
            .catch(() => {})
        })
        .catch(() => {})
    },
    // 获取机构类型
    getOrgTypeInfo () {
      this.$http
        .get(this.getDicListURL + 'platformType')
        .then(({ data: res }) => {
          this.orgTypeOptions = {
            ...this.orgTypeOptions,
            ...res.data.list
          }
          this.orgTypemaps = {
            ...this.orgTypemaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    },
    fomatMethod (value) {
      return this.orgTypemaps[value]
    },
    // 获取机构类型
    getSecretTypeInfo () {
      this.$http
        .get(this.getDicListURL + 'secretType')
        .then(({ data: res }) => {
          this.secretTypeOptions = {
            ...this.secretTypeOptions,
            ...res.data.list
          }
          this.secretTypemaps = {
            ...this.secretTypemaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    },
    fomatMethodSecretType (value) {
      return this.secretTypemaps[value]
    },
    // 查看用户
    userInfoHandle (deptId) {
      if (!deptId || deptId === 0) {
        return this.$message({
          message: this.$t('prompt.selectBatch'),
          type: 'warning',
          duration: 500
        })
      }

      this.$router.push({ name: 'user', params: { deptId: deptId } })
    },
    // 出具机构管理
    issueInfo (platformId) {
      if (!platformId || platformId === 0) {
        return this.$message({
          message: this.$t('prompt.selectBatch'),
          type: 'warning',
          duration: 500
        })
      }
      this.$router.push({
        name: 'platformIssue',
        params: { platformId: platformId }
      })
    }
  }
}
</script>
