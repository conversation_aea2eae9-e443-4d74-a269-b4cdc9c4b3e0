<template>
  <el-dialog :visible.sync="visible" :title="`退保`" :close-on-click-modal="false" :close-on-press-escape="false" width="600px">
    <!-- {{startDate}} -->
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"  :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="colConfig">
          <!-- <el-form-item label="id" prop="id" hidden>
            <el-input v-model="dataForm.id" placeholder="id"></el-input>
          </el-form-item> -->
          <!-- <el-form-item label="退保生效期" prop="validDate">
            <el-date-picker v-model="dataForm.validDate" :picker-options="pickerOptions" value-format="yyyy-MM-dd HH:mm:ss" type="date" placeholder="选择日期">
            </el-date-picker>
          </el-form-item> -->
          <el-form-item label="退保原因" prop="endorseText">
            <el-input type="textarea" :rows="7" v-model="dataForm.endorseText" placeholder="退保原因"></el-input>
          </el-form-item>

        </el-col>
      </el-row>
    </el-form>
    <template slot="footer">
        <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" :loading="btnLoading" @click="dataFormSubmitHandle()">退保</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import moment from 'moment'
export default {
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      btnLoading: false,
      startDate: '',
      dataForm: {
        letterId: '',
        endorseText: '',
        validDate: ''
      },
      pickerOptions: {

      }
    }
  },
  computed: {
    dataRule () {
      return {
        // code: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        endorseText: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    moment,
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.pickerOptions.disabledDate = this.disabledDate
      })
    },
    disabledDate (time) {
      return time.getTime() > moment(this.startDate).subtract(1, 'days') || time.getTime() < Date.now() - 8.64e7
    },
    // 退保
    surrender () {
      var params = this.dataForm
      this.btnLoading = true
      this.$http
        .post(`letter/bgguaranteeletter/surrenderApply`, params)
        .then(({ data: res }) => {
          this.btnLoading = false
          if (!res.data.flag) {
            return this.$message.error(res.data.msg)
          }
          this.visible = false
          this.$emit('refreshDataList')
          this.$message.success('退保申请成功！')
        })
        .catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) {
            return false
          }
          this.surrender()
        })
      },
      1000,
      { leading: true, trailing: false }
    )
  }
}
</script>
