<template>
  <el-row :gutter="20" style="margin:15px 20px 0;">
    <el-card class="box-card" shadow='never'>
      <div slot="header" class="clearfix">
        <span class="title">保函信息</span>
      </div>
      <div>
        <el-col :xs="24" :lg="24">
          <div class="formCon">
            <template>
              <!-- {{paramsForm.paramsRules}} -->
              <el-form :model="dataForm" ref="dataForm" label-position="left" style="margin-bottom:15px;" :rules="dataRule" label-width="140px">
                <el-col :xs="24" :lg="12">
                  <!-- <el-form-item prop="guaranteeAmount" label="保全金额">
                    <el-input v-model="dataForm.guaranteeAmount" class="wd180" size="mini"></el-input>
                  </el-form-item> -->
                  <el-form-item label="保全金额" prop="guaranteeAmount">
                    <!-- {{dataForm.guaranteeAmount}} 元 -->
                    <!-- {{guaranteeAmount}} -->
                    <el-input-number placeholder="请输入保全金额" size="small" style='width:180px;' v-model="guaranteeAmount" @change='change' controls-position="right" :precision="6" :step="0.000001"
                      :min="0" :max="10000"> <template slot="append">万元</template></el-input-number> 万元
                    <!-- <el-tooltip content="保全金额最大80万元，超过80万元按80万元计算。" placement="bottom">
                      <i class="el-icon-question" style="font-size:16px;color:#409EFF;"></i>
                    </el-tooltip> -->
                    <!-- <div style="color:#F56C6C;font-size:12px;">注：保全金额最大80万元，超过80万元按80万元计算。</div> -->
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg="12">
                  <!-- <el-form-item prop="guaranteePrice" label="保费">
                    <el-input v-model="dataForm.guaranteePrice" class="wd180" size="mini"></el-input>
                  </el-form-item> -->
                  <el-form-item label="保费" prop="guaranteePrice">
                    <!-- <span style="font-size:20px;color:#e6a23c;font-weight: bold;letter-spacing: 2px;">{{guaranteeAmount?serPrice:'--'}}</span> 元 -->
                    <!-- <el-tooltip v-if="tip!==''" :content="tip" placement="bottom">
                      <i class="el-icon-info"></i>
                    </el-tooltip> -->
                    <!--              {{dataForm.guaranteePrice}}-->
                    <el-input v-model.number="dataForm.guaranteePrice"  size="small" class="wd180" style="width:100%;" placeholder="保函金额"></el-input>&nbsp;元
                  </el-form-item>

                </el-col>
                <el-col :xs="24" :lg="24">
                  <!-- {{$store.state.user.roleType}} -->
                  <el-form-item label="业务承办人" prop="scUserId">
                    <div v-if="$store.state.user.roleType==5">{{$store.state.user.realName}} / {{$store.state.user.mobile}}</div>
                    <el-select v-else v-model="dataForm.scUserId"  filterable class="wd180" size="mini" placeholder="请选择业务承办人" clearable>
                      <el-option v-for="item in scUserList" :key="item.id" :label="item.userName" :value="item.id">
                        <span style="float: left">{{ item.userName }}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.userTel }}</span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-form>
            </template>
          </div>
        </el-col>
      </div>
    </el-card>
    <!-- propertyPreservationType //财产保全类型 1诉前，2诉中
    preservationAmount //保全金额
    charge //收费
    causeOfAction //案由
    code //编号
    court //受理法院 -->
  </el-row>
</template>
<script>
export default {
  data () {
    return {
      scUserList: [],
      guaranteeAmount: null,
      insuranceCon: {},
      tip: '',
      discountInfo: '',
      serPriceId: ''
    }
  },
  computed: {
    dataRule () {
      return {
        guaranteeAmount: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        guaranteePrice: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ]
      }
    },
    serPrice () {
      var price = null
      if (this.insuranceCon.pricelist) {
        //
        this.insuranceCon.pricelist.map((item, index, arr) => {
          console.log(item)
          let len = this.insuranceCon.pricelist.length - 1
          // eslint-disable-next-line vue/no-side-effects-in-computed-properties
          this.insuranceCon.pricelist[len].guaranteeAmount =
            Number.POSITIVE_INFINITY
          if (Number(item.guaranteeAmount) === 0) {
            item.guaranteeAmount = Number.POSITIVE_INFINITY
          }
          if (index === 0) {
            // eslint-disable-next-line vue/no-side-effects-in-computed-properties
            this.serPriceId = item.id
            if (
              Number(this.dataForm.guaranteeAmount) <=
              Number(item.guaranteeAmount)
            ) {
              if (item.priceType === '1') {
                price = Number(item.letterPrice) + Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，${item.letterPrice}（定额） ${
                  this.discountInfo
                }${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              } else if (item.priceType === '2') {
                price =
                  (Number(this.dataForm.guaranteeAmount) *
                    Number(Math.min(item.percentValue, 1) * 1000)) /
                  1000
                console.log(price)
                price =
                  Math.round(Number(price) * 100) / 100 +
                  Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，费率：${Number(
                  Math.min(item.percentValue, 1) * 1000
                )}（千分比）${item.letterPrice}（定额） ${this.discountInfo}${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              }
            }
          } else if (index + 1 === arr.length) {
            if (
              Number(this.dataForm.guaranteeAmount) >
              Number(item.guaranteeAmount)
            ) {
              // eslint-disable-next-line vue/no-side-effects-in-computed-properties
              this.serPriceId = item.id
              if (item.priceType === '1') {
                price = Number(item.letterPrice) + Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，${item.letterPrice}（定额） ${
                  this.discountInfo
                }${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              } else if (item.priceType === '2') {
                price =
                  (Number(this.dataForm.guaranteeAmount) *
                    Number(Math.min(item.percentValue, 1) * 1000)) /
                  1000
                console.log(price)
                price =
                  Math.round(Number(price) * 100) / 100 +
                  Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，费率：${Number(
                  Math.min(item.percentValue, 1) * 1000
                )}（千分比）${item.letterPrice}（定额） ${this.discountInfo}${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              }
            } else if (
              Number(this.dataForm.guaranteeAmount) >
                Number(arr[index - 1].guaranteeAmount) &&
              Number(this.dataForm.guaranteeAmount) <=
                Number(item.guaranteeAmount)
            ) {
              // '500000 - 800000'
              // eslint-disable-next-line vue/no-side-effects-in-computed-properties
              this.serPriceId = item.id
              if (item.priceType === '1') {
                price = Number(item.letterPrice) + Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，${item.letterPrice}（定额） ${
                  this.discountInfo
                }${item.letterPrice}（定额）${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              } else if (item.priceType === '2') {
                price =
                  (Number(this.dataForm.guaranteeAmount) *
                    Number(Math.min(item.percentValue, 1) * 1000)) /
                  1000
                console.log(price, Number(item.platformPrice))
                price =
                  Math.round(Number(price) * 100) / 100 +
                  Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，费率：${Number(
                  Math.min(item.percentValue, 1) * 1000
                )}（千分比）${item.letterPrice}（定额） ${this.discountInfo}${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              }
            }
          } else {
            if (
              Number(this.dataForm.guaranteeAmount) >
                Number(arr[index - 1].guaranteeAmount) &&
              Number(this.dataForm.guaranteeAmount) <=
                Number(item.guaranteeAmount)
            ) {
              // eslint-disable-next-line vue/no-side-effects-in-computed-properties
              this.serPriceId = item.id
              if (item.priceType === '1') {
                price = Number(item.letterPrice) + Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，${item.letterPrice}（定额） ${
                  this.discountInfo
                }${item.letterPrice}（定额）${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              } else if (item.priceType === '2') {
                price =
                  (Number(this.dataForm.guaranteeAmount) *
                    Number(Math.min(item.percentValue, 1) * 1000)) /
                  1000
                console.log(
                  price,
                  (Number(this.dataForm.guaranteeAmount) *
                    Math.min(item.percentValue, 1) *
                    1000) /
                    1000
                )
                price =
                  Math.round(Number(price) * 100) / 100 +
                  Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，费率：${Number(
                  Math.min(item.percentValue, 1) * 1000
                )}（千分比）${item.letterPrice}（定额） ${this.discountInfo}${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              }
            }
          }
        })
      }
      //
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.dataForm.guaranteePrice = price

      return price || '--'
    }
  },
  props: ['dataForm'],
  created () {
    this.getYwUser()
    this.getInsurance()
    if (this.dataForm.guaranteeAmount) {
      this.guaranteeAmount = this.dataForm.guaranteeAmount / 10000
    }
  },
  watch: {
    dataForm: {
      handler (a) {
        if (a.guaranteeAmount) {
          this.guaranteeAmount = a.guaranteeAmount / 10000
        }
      },
      deep: true
    }
  },
  methods: {
    getYwUser () {
      // letter/scUser/findBusinessContractor
      this.$http
        .get(`letter/scUser/findBusinessContractor`)
        .then(({ data: res }) => {
          this.scUserList = res.data
        })
        .catch(() => {})
    },
    getInsurance () {
      this.$http
        .get(
          `/letter/bgguaranteeissue/getIssueByCode/${this.$route.params.insuranceCode}&${this.$route.params.type}`
        )
        .then(({ data: res }) => {
          this.insuranceCon = res.data
        })
        .catch(() => {})
    },
    change (val) {
      if (val) {
        this.$set(this.dataForm, 'guaranteeAmount', val * 10000)
      } else {
        this.$set(this.dataForm, 'guaranteeAmount', 0)
      }
    },
    push () {
      return new Promise((resolve, reject) => {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            // let params = {
            //   cjfChargeId: this.serPriceId,
            //   guaranteeAmount: this.dataForm.guaranteeAmount,
            //   letterPrice: this.serPrice
            // }
            // this.dataForm.guaranteePrice = this.serPrice
            if (this.$store.state.user.roleType === 5) {
              this.dataForm.scUserId = this.$store.state.user.scUserId
            }
            // this.$set(this.dataForm, 'guaranteePrice', this.serPrice)
            resolve(true)
            // this.$http
            //   .post('/letter/bgguaranteeprice/JSletterPrice', params)
            //   .then(({ data: res }) => {
            //     if (res.code !== 0) {
            //       resolve(false)
            //       return this.$message.error('支付金额出错')
            //     }
            //   })
            //   .catch(() => {})
          } else {
            resolve(false)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.wd180 {
  width: 260px !important;
}
.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
</style>
