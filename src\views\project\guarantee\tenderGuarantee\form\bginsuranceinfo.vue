<template>
  <el-row :gutter="20" style="margin:15px 20px 0;">
    <el-form :model="dataForm" label-position="left" ref="dataForm" :rules="dataRule" label-width="140px">
      <el-card class="box-card" shadow='never'>
        <div slot="header" class="clearfix">
          <span class="title">保函信息</span>
          <!-- <el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button> -->
        </div>
        <div>
          <el-col :xs="24" :lg="24">
            <div class="formCon">
              <el-col :xs="24" :lg="12">
                <el-form-item label="保函类型" prop="insuranceType">
                  <!-- {{dataForm.insuranceType}} -->
                  <el-select v-model="dataForm.insuranceType" disabled size="small" class="wd180" style="width:100%;" placeholder="保函类型">
                    <el-option v-for="item in options.insuranceTypeoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="出具方" >
                  {{$route.params.insuranceName}}
                </el-form-item>
                <el-form-item label="担保金额" prop="guaranteeAmount">
                  <!-- {{dataForm.guaranteeAmount}} 元 -->
                  <!-- {{guaranteeAmount}} -->
                  <el-input-number placeholder="请输入担保金额" size="small" style='width:180px;' v-model="guaranteeAmount" @change='change' controls-position="right" :precision="6" :step="0.000001" :min="0"
                    :max="1000"> <template slot="append">万元</template></el-input-number> 万元
                  <el-tooltip content="建设类担保金额最大80万元，交通类担保金额最大1000万元。" placement="bottom">
                    <i class="el-icon-question" style="font-size:16px;color:#409EFF;"></i>
                  </el-tooltip>
                  <div style="color:#F56C6C;font-size:12px;">注：建设类担保金额最大80万元，交通类担保金额最大1000万元。</div>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :lg="24" v-if="this.$route.params.insuranceCode!=='ddbx'">
                <el-form-item label="保函开始/结束日期" prop="startDate" >
                  <!-- {{this.time}}{{new Date(tbDateA)}} -->
                  <el-date-picker v-model="time" size="small" :picker-options="pickerOptions" @change='dateChange' style='width:350px;' unlink-panels type="daterange" value-format="yyyy-MM-dd"
                    range-separator="至" align="right">
                  </el-date-picker>

                </el-form-item>
              </el-col>
                <el-col :xs="24" :lg="24" v-else>
                <el-form-item  label="保函开始/结束日期" prop="startDate">
                  <!-- {{dataForm.startDate}} - {{dataForm.endDate}} -->
                  <span v-if="bidOpenDate&&ddEndDate!=='Invalid date'">{{bidOpenDate}} - {{ddEndDate}}</span>
                  <span v-else> -- </span>
                  <!-- <el-button @click="push"></el-button> -->
                  <span style="margin-left:25px;color:rgba(0,0,0,.4);"
                      class="el-upload__tip"><span style="color:#F56C6C;">注：</span>保函开始日期 = 投标截止日期,保函结束日期 = 投标截止日期 + 招标有效期</span>
                </el-form-item>
                </el-col>
                <el-col :xs="24" :lg="24" >
                <!-- {{guaranteeAmount}}{{serPrice}} -->
                <el-form-item label="保费" prop="guaranteePrice">
                  <span style="font-size:20px;color:#e6a23c;font-weight: bold;letter-spacing: 2px;">{{guaranteeAmount?serPrice:'--'}}</span> 元
                  <el-tooltip v-if="tip!==''" :content="tip" placement="bottom">
                    <i class="el-icon-info"></i>
                  </el-tooltip>
                  &emsp;
                  <!-- <span class="el-upload__tip" v-show="$route.params.insuranceCode === 'hybx'">新机构入驻限时<span style="color:#F56C6C;">八</span>折！</span> -->
                  <!--              {{dataForm.guaranteePrice}}-->
                  <!-- <el-input v-model="dataForm.guaranteePrice"  size="small" class="wd180" style="width:100%;" placeholder="保函金额"></el-input> -->
                </el-form-item>

              </el-col>
            </div>
          </el-col>
        </div>
      </el-card>

    </el-form>
  </el-row>
</template>
<script>
import moment from 'moment'
var that = null
export default {
  data () {
    return {
      guaranteeAmount: null,
      radio: 0,
      time: [],
      tbDateA: '',
      insuranceList: [],
      insuranceCon: [],
      tip: '',
      serPriceId: '',
      pickerOptions: this.pickerOptionsFun(),
      discountInfo: ''
    }
  },
  props: {
    dataForm: {
      type: Object
    },
    options: {
      type: Object
    },
    tbDate: String,
    tenderValid: String,
    bidOpenDate: String
  },
  created () {
    that = this
    // this.getinList()
    this.getInsurance(this.$route.params.insuranceCode, 0)
    this.tbDateA = this.tbDate
    if (this.dataForm.guaranteeAmount) {
      this.guaranteeAmount = this.dataForm.guaranteeAmount / 10000
    }
  },
  beforeMount () {
    // var data = JSON.parse(this.$route.query.secretParams)
    this.$set(this.dataForm, 'insuranceType', this.$route.params.type)
    if (this.dataForm.startDate) {
      this.time = [this.dataForm.startDate, this.dataForm.endDate]
    } else {
      this.initDate()
    }
    // this.initDate()
  },
  computed: {
    ddEndDate () {
      let date = new Date(this.bidOpenDate).setTime(new Date(this.bidOpenDate).getTime() + 3600 * 1000 * 24 * Number(this.tenderValid))
      return this.moment(date).format('YYYY-MM-DD') === 'Invalid date' ? '' : this.moment(date).format('YYYY-MM-DD')
    },
    serPrice () {
      var price = null
      if (this.insuranceCon.pricelist) {
        //
        this.insuranceCon.pricelist.map((item, index, arr) => {
          console.log(item)
          let len = this.insuranceCon.pricelist.length - 1
          // eslint-disable-next-line vue/no-side-effects-in-computed-properties
          this.insuranceCon.pricelist[len].guaranteeAmount =
            Number.POSITIVE_INFINITY
          if (Number(item.guaranteeAmount) === 0) {
            item.guaranteeAmount = Number.POSITIVE_INFINITY
          }
          if (index === 0) {
            // eslint-disable-next-line vue/no-side-effects-in-computed-properties
            this.serPriceId = item.id
            if (
              Number(this.dataForm.guaranteeAmount) <=
              Number(item.guaranteeAmount)
            ) {
              if (item.priceType === '1') {
                price = Number(item.letterPrice) + Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，${
                  item.letterPrice
                }（定额） ${this.discountInfo}${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              } else if (item.priceType === '2') {
                price =
                  (Number(this.dataForm.guaranteeAmount) *
                    Number(Math.min(item.percentValue, 1) * 1000)) /
                  1000
                console.log(price)
                price =
                  Math.round(Number(price) * 100) / 100 +
                  Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，费率：${Number(
                  Math.min(item.percentValue, 1) * 1000
                )}（千分比）${item.letterPrice}（定额） ${this.discountInfo}${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              }
            }
          } else if (index + 1 === arr.length) {
            if (
              Number(this.dataForm.guaranteeAmount) >
              Number(item.guaranteeAmount)
            ) {
              // eslint-disable-next-line vue/no-side-effects-in-computed-properties
              this.serPriceId = item.id
              if (item.priceType === '1') {
                price = Number(item.letterPrice) + Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，${
                  item.letterPrice
                }（定额） ${this.discountInfo}${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              } else if (item.priceType === '2') {
                price =
                  (Number(this.dataForm.guaranteeAmount) *
                    Number(Math.min(item.percentValue, 1) * 1000)) /
                  1000
                console.log(price)
                price =
                  Math.round(Number(price) * 100) / 100 +
                  Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，费率：${Number(
                  Math.min(item.percentValue, 1) * 1000
                )}（千分比）${item.letterPrice}（定额） ${this.discountInfo}${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              }
            } else if (
              Number(this.dataForm.guaranteeAmount) >
                Number(arr[index - 1].guaranteeAmount) &&
              Number(this.dataForm.guaranteeAmount) <=
                Number(item.guaranteeAmount)
            ) {
              // '500000 - 800000'
              // eslint-disable-next-line vue/no-side-effects-in-computed-properties
              this.serPriceId = item.id
              if (item.priceType === '1') {
                price = Number(item.letterPrice) + Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，${
                  item.letterPrice
                }（定额） ${this.discountInfo}${item.letterPrice}（定额）${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              } else if (item.priceType === '2') {
                price =
                  (Number(this.dataForm.guaranteeAmount) *
                    Number(Math.min(item.percentValue, 1) * 1000)) /
                  1000
                console.log(price, Number(item.platformPrice))
                price =
                  Math.round(Number(price) * 100) / 100 +
                  Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，费率：${Number(
                  Math.min(item.percentValue, 1) * 1000
                )}（千分比）${item.letterPrice}（定额） ${this.discountInfo}${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              }
            }
          } else {
            if (
              Number(this.dataForm.guaranteeAmount) >
                Number(arr[index - 1].guaranteeAmount) &&
              Number(this.dataForm.guaranteeAmount) <=
                Number(item.guaranteeAmount)
            ) {
              // eslint-disable-next-line vue/no-side-effects-in-computed-properties
              this.serPriceId = item.id
              if (item.priceType === '1') {
                price = Number(item.letterPrice) + Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，${
                  item.letterPrice
                }（定额） ${this.discountInfo}${item.letterPrice}（定额）${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              } else if (item.priceType === '2') {
                price =
                  (Number(this.dataForm.guaranteeAmount) *
                    Number(Math.min(item.percentValue, 1) * 1000)) /
                  1000
                console.log(
                  price,
                  (Number(this.dataForm.guaranteeAmount) *
                    Math.min(item.percentValue, 1) *
                    1000) /
                    1000
                )
                price =
                  Math.round(Number(price) * 100) / 100 +
                  Number(item.platformPrice)
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.tip = `${item.name}，费率：${Number(
                  Math.min(item.percentValue, 1) * 1000
                )}（千分比）${item.letterPrice}（定额） ${this.discountInfo}${
                  item.platformPrice > 0
                    ? '+' + item.platformPrice + '（平台服务费）'
                    : ''
                }`
              }
            }
          }
        })
      }
      //
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.dataForm.guaranteePrice = price

      return price || '--'
    },
    dataRule () {
      var checkAge = (rule, value, callback) => {
        if (value === 0) {
          return callback(new Error('担保金额不能为0'))
        } else {
          callback()
        }
      }
      return {
        insuranceType: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        guaranteeAmount: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          },
          { validator: checkAge, trigger: 'change' }
        ],
        startDate: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  watch: {
    serPriceId (a, b) {
      this.$emit('getSerPriceId', a)
    },
    tbDate (a, b) {
      this.tbDateA = a
      console.log(this.tbDateA)
      this.initDate()
      this.pickerOptionsFun(a)
    }
  },
  methods: {
    moment,
    pickerOptionsFun (a) {
      return {
        disabledDate (time) {
          return time.getTime() < Date.now()
        },
        shortcuts: [
          {
            text: '30天',
            onClick (picker) {
              console.log(that.tbDateA)
              const end =
                that.tbDateA === '' ? new Date() : new Date(that.tbDateA)
              const start =
                that.tbDateA === '' ? new Date() : new Date(that.tbDateA)
              start.setTime(start.getTime() + (that.tbDateA === '' ? 3600 * 1000 * 24 : 0))
              end.setTime(end.getTime() + 3600 * 1000 * 24 * (that.tbDateA === '' ? 31 : 30))
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '60天',
            onClick (picker) {
              const end =
                that.tbDateA === '' ? new Date() : new Date(that.tbDateA)
              const start =
                that.tbDateA === '' ? new Date() : new Date(that.tbDateA)
              start.setTime(start.getTime() + (that.tbDateA === '' ? 3600 * 1000 * 24 : 0))
              end.setTime(end.getTime() + 3600 * 1000 * 24 * (that.tbDateA === '' ? 61 : 60))
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '90天',
            onClick (picker) {
              const end =
                that.tbDateA === '' ? new Date() : new Date(that.tbDateA)
              const start =
                that.tbDateA === '' ? new Date() : new Date(that.tbDateA)
              start.setTime(start.getTime() + (that.tbDateA === '' ? 3600 * 1000 * 24 : 0))
              end.setTime(end.getTime() + 3600 * 1000 * 24 * (that.tbDateA === '' ? 91 : 90))
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '180天',
            onClick (picker) {
              const end =
                that.tbDateA === '' ? new Date() : new Date(that.tbDateA)
              const start =
                that.tbDateA === '' ? new Date() : new Date(that.tbDateA)
              start.setTime(start.getTime() + (that.tbDateA === '' ? 3600 * 1000 * 24 : 0))
              end.setTime(end.getTime() + 3600 * 1000 * 24 * (that.tbDateA === '' ? 181 : 180))
              picker.$emit('pick', [start, end])
            }
          }
        ]
      }
    },
    initDate () {
      this.time = []

      this.time.push(
        this.moment(this.tbDateA ? this.tbDateA : new Date())
          .add(this.tbDateA ? 0 : 1, 'days')
          .hour(0)
          .minute(0)
          .second(0)
          .format('YYYY-MM-DD HH:mm:ss'),
        this.moment(this.tbDateA ? this.tbDateA : new Date())
          .add(3, 'month')
          .hour(23)
          .minute(59)
          .second(59)
          .format('YYYY-MM-DD HH:mm:ss')
      )
      this.$set(this.dataForm, 'startDate', this.time[0])
      this.$set(this.dataForm, 'endDate', this.time[1])
    },
    change (val) {
      if (val) {
        this.$set(this.dataForm, 'guaranteeAmount', val * 10000)
      } else {
        this.$set(this.dataForm, 'guaranteeAmount', 0)
      }
      // this.dataForm.guaranteeAmount = val * 10000
      // console.log(this.dataForm.guaranteeAmount)
    },

    BlurText (e) {
      // let boolean = new RegExp('^[1-9][0-9]*$').test(e.target.value)
    },
    push () {
      return new Promise((resolve, reject) => {
        this.$refs['dataForm'].validate((valid) => {
          console.log(valid)
          if (valid) {
            console.log(2222)
            let params = {
              cjfChargeId: this.serPriceId,
              guaranteeAmount: this.dataForm.guaranteeAmount,
              letterPrice: this.serPrice
            }

            this.$http
              .post('/letter/bgguaranteeprice/JSletterPrice', params)
              .then(({ data: res }) => {
                if (res.code !== 0) {
                  resolve(false)
                  return this.$message.error('支付金额出错')
                }
                if (this.$route.params.insuranceCode === 'ddbx') {
                  this.$set(this.dataForm, 'startDate', this.bidOpenDate + ' 00:00:00')
                  this.$set(this.dataForm, 'endDate', this.ddEndDate + ' 23:59:59')
                }
                this.dataForm.guaranteePrice = this.serPrice
                // this.$set(this.dataForm, 'guaranteePrice', this.serPrice)
                resolve(true)
              })
              .catch(() => {})
          } else {
            console.log(1111)
            resolve(false)
          }
        })
      })
    },
    // getinList () {
    //   this.$http
    //     .get(`/letter/bgguaranteeissue/getIssue/${this.$route.params.type}&1`)
    //     .then(({ data: res }) => {
    //       if (res.code !== 0) {
    //         return
    //       }
    //       //
    //       this.insuranceList = res.data
    //       // console.log(this.insuranceList)
    //       this.$emit('insuranceInfo', this.insuranceList[0])
    //       this.getInsurance(this.insuranceList[0].code, 0)
    //     })
    //     .catch(() => {})
    // },

    getInsurance (code, index) {
      this.$http
        .get(
          `/letter/bgguaranteeissue/getIssueByCode/${code}&${this.$route.params.type}`
        )
        .then(({ data: res }) => {
          this.disLoading = false

          this.insuranceCon = res.data
        })
        .catch(() => {})
    },
    diff (t1, t2) {
      let m1 = moment(t1 + ' 00:00:00')
      let m2 = moment(t2 + ' 23:59:59')
      console.log(m2.diff(m1, 'days'))
      return m2.diff(m1, 'days')
    },
    dateChange (val) {
      if (val) {
        if (this.diff(val[0], val[1]) < 30) {
          this.$message.error('保函有效日期必须大于30天')
          this.initDate()
        } else {
          this.$set(this.dataForm, 'startDate', val[0] + ' 00:00:00')
          this.$set(this.dataForm, 'endDate', val[1] + ' 23:59:59')
        }
      } else {
        this.$set(this.dataForm, 'startDate', '')
        this.$set(this.dataForm, 'endDate', '')
      }

      // console.log(this.dataForm)
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
.el-icon-info {
  font-size: 15px !important;
  cursor: pointer;
  position: relative;
  left: 10px;
  color: #e6a23c;
}
</style>
