<template>
  <el-dialog :visible.sync="visible" title="出具方功能配置" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" ref="dataForm" :label-width="$i18n.locale === 'en-US' ? '120px' : '200px'">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="colConfig">
          <el-form-item label="保函类型" prop="alias">
            <el-select v-model="dataForm.guaranteeTypeCode" placeholder="请选择保函类型" @change='change'>
              <el-option v-for="item in guaranteeTypenamesOptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <div v-if="dataForm.guaranteeTypeCode">
        <el-row :gutter="20" v-if="dataForm.guaranteeTypeCode == 'tbdbbh'">
          <el-col :xs="24" :lg="colConfig">
            <el-form-item label="" prop="id" hidden>
              <el-input v-model="dataForm.id" placeholder=""></el-input>
            </el-form-item>
            <el-form-item label="出具方别名" prop="alias">
              <el-input v-model="dataForm.alias" placeholder="出具方别名"></el-input>
            </el-form-item>
            <el-form-item label="开标延后日" prop="delayBidOpening">
              <el-input v-model.number="dataForm.delayBidOpening" placeholder="开标延后日"></el-input>
            </el-form-item>
            <el-form-item label="是否需要邮寄信息" prop="isMail">
              <el-switch v-model="dataForm.isMail" :active-value='1' :inactive-value='0'>
              </el-switch>
            </el-form-item>
            <el-form-item label="支付方式" prop="paymentMethod" v-if="dataForm.paymentMethod">
              <el-checkbox-group v-model="dataForm.paymentMethod">
                <el-checkbox label="offline" disabled>线下支付</el-checkbox>
                <el-checkbox label="WXPAY">微信</el-checkbox>
                <el-checkbox label="ALIPAY">支付宝</el-checkbox>
                <el-checkbox label="FUIOU_WECHAT">富友微信</el-checkbox>
                <el-checkbox label="FUIOU_ALIPAY">富友支付宝</el-checkbox>
                <el-checkbox label="FUIOU_UNIONPAY">富友银联</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="是否支持批改" prop="isCorrection">
              <el-switch v-model="dataForm.isCorrection" :active-value='1' :inactive-value='0'>
              </el-switch>
            </el-form-item>
            <el-form-item label="批改配置" prop="isCorrectionDate" v-show="dataForm.isCorrection=='1'">
              <el-checkbox v-model="dataForm.isCorrectionDate" true-label='01' false-label='0'>批改截止日期</el-checkbox><br>
              <el-checkbox v-model="dataForm.isCorrectionPayMethod" true-label='14' false-label='0'>批改支付方式</el-checkbox><br>
              <el-checkbox v-model="dataForm.isCorrectionProjectName" true-label='08' false-label='0'>批改项目名称</el-checkbox><br>
              <el-checkbox v-model="dataForm.isCorrectionTbr" true-label='60' false-label='0'>批改投保人信息</el-checkbox><br>
              <el-checkbox v-model="dataForm.isCorrectionBbr" true-label='04' false-label='0'>批改被保人信息</el-checkbox><br>
              <el-checkbox v-model="dataForm.isCorrectionAmount" true-label='15' false-label='0'>批改担保金额</el-checkbox><br>
              <el-checkbox v-model="dataForm.isUploadCorrectionFile" true-label='1' false-label='0'>批改说明文件</el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :lg="colConfig">

            <el-form-item label="是否需要备注字段" prop="isRemarks">
              <el-switch v-model="dataForm.isRemarks" :active-value='1' :inactive-value='0'>
              </el-switch>
            </el-form-item>
            <el-form-item label="责任类型" prop="commonlyOrRelated">
              <!-- <el-input v-model="dataForm.commonlyOrRelated" placeholder="可开具一般责任和连带责任保函，1一般责任，2连带责任，3全部"></el-input> -->
              <el-select v-model="dataForm.commonlyOrRelated" placeholder="请选择">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="是否支持退保" prop="isSurrender">
              <el-switch v-model="dataForm.isSurrender" :active-value='1' :inactive-value='0'>
              </el-switch>
            </el-form-item>
             <el-form-item label="表单内嵌发票" prop="isInvoiceFrom">
              <el-switch v-model="dataForm.isInvoiceFrom" :active-value='1' :inactive-value='0'>
              </el-switch>
            </el-form-item>
            <el-form-item label="是否支持开具发票" prop="isApplyInvoice">
              <el-switch v-model="dataForm.isApplyInvoice" :active-value='1' :inactive-value='0'>
              </el-switch>
            </el-form-item>
            <el-form-item label="是否支持下载发票" prop="isApplyInvoice">
              <el-switch v-model="dataForm.isDownloadInvoice" :active-value='1' :inactive-value='0'>
              </el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if=" ['lydbbh', 'lybhyh', 'zfdbbh', 'yfkdbbh'].includes(dataForm.guaranteeTypeCode)">
          <el-col :span="24">
            <el-form-item label="可支持的保函模板类型" prop="guaranteeTemplateTypes" >
              <!-- {{guaranteeTemplateTypes}} -->
              <div>
                <el-button type="primary" @click="aopen" size="small">新增</el-button>
              </div>
              <!-- <el-checkbox-group  v-model="dataForm.guaranteeTemplateTypes">
                <el-checkbox  v-for="(item,index) in guaranteeTemplateTypes" :key="index" :label="item.dictCode">{{item.dictName}}</el-checkbox>
              </el-checkbox-group> -->
              <div>
                <el-tag
                  style="margin-right: 10px;"
                  v-for="(tag,index) in guaranteeTemplateTypes"
                  :key="index"
                  closable
                  @close="guaranteeTemplateTypes.splice(index, 1)"
                  >
                  {{tag.name}}({{tag.value}})
                </el-tag>
              </div>

            </el-form-item>
            <el-form-item label="支付方式" prop="paymentMethod" v-if="dataForm.paymentMethod">
              <el-checkbox-group v-model="dataForm.paymentMethod">
                <el-checkbox label="offline" disabled>线下支付</el-checkbox>
                <el-checkbox label="WXPAY">微信</el-checkbox>
                <el-checkbox label="ALIPAY">支付宝</el-checkbox>
                <el-checkbox label="FUIOU_WECHAT">富友微信</el-checkbox>
                <el-checkbox label="FUIOU_ALIPAY">富友支付宝</el-checkbox>
                <el-checkbox label="FUIOU_UNIONPAY">富友银联</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="是否支持开具发票" prop="isApplyInvoice">
              <el-switch v-model="dataForm.isApplyInvoice" :active-value='1' :inactive-value='0'>
              </el-switch>
            </el-form-item>
            <el-form-item label="是否支持下载发票" prop="isApplyInvoice">
              <el-switch v-model="dataForm.isDownloadInvoice" :active-value='1' :inactive-value='0'>
              </el-switch>
            </el-form-item>
            <el-dialog append-to-body :visible.sync="avisible" title="新增" :close-on-click-modal="false" :close-on-press-escape="false">
              <el-form :model="aform" :rules="dataRule" ref="aform" label-width="120px">
                <el-form-item prop="name" :label="$t('dict.dictName')">
                  <el-input v-model="aform.name" :placeholder="$t('dict.dictName')"></el-input>
                </el-form-item>
                <el-form-item  prop="value" :label="$t('dict.dictType')">
                  <el-input v-model="aform.value" :placeholder="$t('dict.dictType')"></el-input>
                </el-form-item>
              </el-form>
              <template slot="footer">
                <el-button @click="avisible = false">{{ $t('cancel') }}</el-button>
                <el-button type="primary" @click="adda">新增</el-button>
              </template>
            </el-dialog>
          </el-col>
          <el-col :span="24">
            <el-form-item label="是否需要邮寄信息" prop="isMail">
              <el-switch v-model="dataForm.isMail" :active-value='1' :inactive-value='0'>
              </el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input v-model="dataForm.contactPerson" placeholder="联系人"></el-input>
            </el-form-item>
            <el-form-item label="联系人电话" prop="contactPhone">
              <el-input v-model="dataForm.contactPhone" placeholder="联系人电话"></el-input>
            </el-form-item>
            <el-form-item label="邮寄地址" prop="mailingAddress">
              <el-input v-model="dataForm.mailingAddress" placeholder="邮寄地址"></el-input>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row class="czz">
          <el-col :span="24">
            <el-form-item label="文件配置">
              <el-button size="mini" @click="add" type="primary">新增文件</el-button>
              <el-tree :data="menuListTree" show-checkbox :highlight-current="true" :default-checked-keys='keys' :props="defaultProps" node-key="id" accordion ref="menuListTree" default-expand-all
                :expand-on-click-node="false" :render-content="renderContent">
              </el-tree>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
    <filePageDia v-if="addOrUpdateVisible" ref="filePageDia" @refreshDataList='change'></filePageDia>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import filePageDia from './filePage-dia'
import { getDict } from '@/utils/index'

export default {
  data () {
    return {
      colConfig: 2, // 列配置 1 2列
      visible: false,
      addOrUpdateVisible: false,
      guaranteeTypecodes: '',
      keys: [],
      options: [
        {
          value: '3',
          label: '全部'
        },
        {
          value: '1',
          label: '一般责任'
        },
        {
          value: '2',
          label: '连带责任'
        }
      ],
      avisible: false,
      aform: {
        name: '',
        value: ''
      },
      guaranteeTypenamesOptions: [],
      guaranteeTemplateTypes: [],
      menuListTree: [], // 文件树
      defaultProps: {
        children: 'children',
        label: 'fileName'
      },
      dataForm: {
        id: '',
        code: '',
        name: '',
        issueCode: '',
        alias: '',
        delayBidOpening: '',
        isMail: '',
        isRemarks: '',
        commonlyOrRelated: '',
        guaranteeTypeCode: '',
        isSurrender: '',
        isCorrection: '',
        isApplyInvoice: '',
        isCorrectionDate: '',
        isCorrectionPayMethod: '',
        isCorrectionProjectName: '',
        paymentMethod: '',
        creator: '',
        isInvoiceFrom: '',
        fileIdList: '',
        createDate: '',
        updater: '',
        updateDate: '',
        status: 1,
        remarks: '',
        isCorrectionAmount: '', // 是否支持批改担保金额，1是 0否
        isJumpPayPath: '', // 是否跳转第三方支付平台，1是 0否
        isDownloadInvoice: '', // 是否下载发票，1是 0否
        isUploadCorrectionFile: '', // 批改说明文件
        guaranteeTemplateTypes: [], //   可支持的保函模板类型
        contactPerson: '',
        contactPhone: '',
        mailingAddress: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        value: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' }
        ]
      }
    }
  },
  components: {
    filePageDia
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    getDict,
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.guaranteeTemplateTypes = []
        this.dataForm.guaranteeTypeCode = ''
        this.guaranteeTypeInfo()
      })
    },
    aopen () {
      this.avisible = true
      this.$nextTick(() => {
        this.$refs.aform.resetFields()
      })
    },
    adda () {
      this.guaranteeTemplateTypes.push(JSON.parse(JSON.stringify(this.aform)))
      this.avisible = false
    },
    // 获取信息
    getInfo () {
      this.$http
        .get(
          `/letter/bgissueconfigure/getInfoByIssueCode?issueCode=${this.dataForm.issueCode}&guaranteeTypeCode=${this.dataForm.guaranteeTypeCode}`
        )
        .then(async ({ data: res }) => {
          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
          if (this.dataForm.paymentMethod) {
            this.dataForm.paymentMethod = this.dataForm.paymentMethod.split(',')
          }
          if (this.dataForm.guaranteeTemplateTypes) {
            this.guaranteeTemplateTypes = JSON.parse(this.dataForm.guaranteeTemplateTypes)
          } else {
            this.guaranteeTemplateTypes = []
          }
          this.dataForm.fileIdList.forEach((item) => {
            this.$refs.menuListTree.setChecked(item, true)
          })
        })
    },
    // 获取文件配置树
    getFileTree () {
      return this.$http
        .get(
          `/letter/bgIssueDocument/findByIssueCodeAndType/?issueCode=${this.dataForm.issueCode}&guaranteeTypeCode=${this.dataForm.guaranteeTypeCode}`
        )
        .then(async ({ data: res }) => {
          this.menuListTree = res.data
        })
    },
    change (val) {
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.$refs.menuListTree.setCheckedKeys([])
        Promise.all([this.getFileTree()]).then(() => {
          this.getInfo()
        })
      })
    },
    renderContent (h, { node, data, store }) {
      //  <el-button size="mini" type="text" on-click={ () => this.append(data) }>Append</el-button>
      //       <el-button size="mini" type="text" on-click={ () => this.remove(node, data) }>Delete</el-button>
      return (
        <span class="custom-tree-node">
          <span>{node.label}</span>
          <span class="cz">
            <el-button size="mini" type="text" on-click={() => this.add(node)}>
              配置
            </el-button>
            <el-popconfirm
              title="确认删除此文件吗？"
              on-confirm={() => this.deleteFile(data)}
            >
              <el-button
                slot="reference"
                size="mini"
                type="text"
                class="delete"
              >
                &emsp;删除
              </el-button>
            </el-popconfirm>
          </span>
        </span>
      )
    },

    // 获取保函类型
    guaranteeTypeInfo () {
      this.guaranteeTypenamesOptions = []
      this.$http
        .get('/sys/dict/type/guaranteeType')
        .then(({ data: res }) => {
          let op = [...this.guaranteeTypenamesOptions, ...res.data.list]
          console.log(this.guaranteeTypecodes)
          let arr = this.guaranteeTypecodes.split(',')
          console.log(arr)
          op.map((a) => {
            arr.map((b) => {
              if (b === a.dictCode) {
                this.guaranteeTypenamesOptions.push(a)
              }
            })
          })
        })
        .catch(() => {})
    },
    add (data) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.filePageDia.dataForm.id = data.key
        this.$refs.filePageDia.dataForm.guaranteeTypeCodes =
          this.dataForm.guaranteeTypeCode
        this.$refs.filePageDia.dataForm.issueCode = this.dataForm.issueCode
        this.$refs.filePageDia.dataForm.level = `${data.level - 1}`
        this.$refs.filePageDia.menuList = this.menuListTree
        this.$refs.filePageDia.init()
      })
    },
    deleteFile (data) {
      console.log(data)
      this.$http
        .delete('/letter/bgIssueDocument', {
          data: [data.id]
        })
        .then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.getFileTree()
            }
          })
        })
        .catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) {
            return false
          }
          let arr = []
          this.$refs.menuListTree.getCheckedNodes(false, true).map((a) => {
            arr.push(a.id)
          })
          let fileIdList = arr
          // console.log(fileIdList)
          if (this.dataForm.paymentMethod) {
            this.dataForm.paymentMethod = this.dataForm.paymentMethod.join(',')
          }
          if (this.guaranteeTemplateTypes) {
            this.dataForm.guaranteeTemplateTypes = JSON.stringify(this.guaranteeTemplateTypes)
          }
          this.dataForm.fileIdList = fileIdList
          this.$http[!this.dataForm.id ? 'post' : 'put'](
            '/letter/bgissueconfigure/',
            this.dataForm
          )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            })
            .catch(() => {})
        })
      },
      1000,
      { leading: true, trailing: false }
    )
  }
}
</script>
<style lang="scss" scoped>
</style>
