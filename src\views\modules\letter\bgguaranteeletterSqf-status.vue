<template>
  <el-dialog :visible.sync="visible" :title="`保函变更`" :before-close='close' :close-on-click-modal="false" :close-on-press-escape="false" width="600px">
    <!-- {{startDate}} -->
    <el-form :model="dataForm" v-loading='loading' ref="dataForm"  :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="colConfig">
          <el-form-item label="id" prop="id" hidden >
            <el-input v-model="dataForm.id"  placeholder="id"></el-input>
          </el-form-item>
          <el-form-item label="保函状态" prop="letterStatus">
            <el-select v-model="dataForm.letterStatus" clearable placeholder="保函状态">
              <el-option
                      v-for="item in letterStatusoptions"
                      :key="item.dictCode"
                      :label="item.dictName"
                      :value="item.dictCode"
              >
              </el-option>
            </el-select>

          </el-form-item>
          <el-form-item label="变更原因" prop="endorseText">
            <el-input type="textarea" :rows="7" v-model="dataForm.endorseText" placeholder="变更原因"></el-input>
          </el-form-item>

        </el-col>
      </el-row>
    </el-form>
    <template slot="footer">
        <el-button @click="close">{{ $t('cancel') }}</el-button>
      <el-button type="primary" :loading='btnloading'  @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import moment from 'moment'
export default {
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      letterStatusoptions: '',
      getDicListURL: '/sys/dict/type/',
      id: '',
      loading: false,
      btnloading: false,
      dataForm: {
        id: '',
        endorseText: '',
        letterStatus: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        // code: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        // name: [
        //   {
        //     required: true,
        //     message: this.$t('validate.required'),
        //     trigger: 'blur'
        //   }
        // ]
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    moment,
    init () {
      this.visible = true
      this.getStatusType()
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.id) {
          this.getInfo()
        }
      })
    },
    close () {
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.visible = false
      })
    },
    getStatusType () {
      this.$http.get(this.getDicListURL + 'letterStatus').then(({ data: res }) => {
        this.letterStatusoptions = {
          ...this.letterStatusoptions,
          ...res.data.list
        }
      }).catch(() => { })
    },

    // 获取信息
    getInfo () {
      this.loading = true
      this.$http.get(`/letter/bgguaranteeletter/${this.id}`).then(({ data: res }) => {
        this.loading = false

        this.dataForm.id = res.data.id
        this.dataForm.letterStatus = res.data.letterStatus
        this.dataForm.endorseText = res.data.endorseText
      }).catch(() => {})
    },
    // 状态变更
    ststusbg () {
      this.btnLoading = true
      this.$http
        .post(`letter/bgguaranteeletter/ststusbg`, this.dataForm)
        .then(({ data: res }) => {
          this.btnLoading = false
          this.surrenderVisible = false

          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        })
        .catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.ststusbg()
      },
      1000,
      { leading: true, trailing: false }
    )
  }
}
</script>
