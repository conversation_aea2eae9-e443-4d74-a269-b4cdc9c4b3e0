<template>
    <div>
        <el-card class="box-card" >
            <div class="temp-content">
                <el-form :model="bgGuaranteeApply" :rules="dataRule" ref="bgGuaranteeApply" label-width="140px"  v-loading='pageLoading'>
                    <el-row :gutter="20">
                        <div class="temp-subtitle">投保人信息（投保方）</div>
                        <el-col :xs="24" :lg="colConfig">
                            <el-form-item label="名称" prop="name">
                                <el-input v-model="bgGuaranteeApply.name" disabled  placeholder="名称"></el-input>
                            </el-form-item>
                            <el-form-item label="证件类型" prop="certType">
                                <el-select v-model="bgGuaranteeApply.certType" disabled placeholder="证件类型">
                                    <el-option
                                            v-for="item in certTypeoptions"
                                            :key="item.dictCode"
                                            :label="item.dictName"
                                            :value="item.dictCode"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="证件号码" prop="certCode">
                                <el-input v-model="bgGuaranteeApply.certCode" disabled  placeholder="证件号码"></el-input>
                            </el-form-item>
                            <el-form-item label="证件有效期" prop="certTerm">
                                <el-date-picker v-model="bgGuaranteeApply.certTerm" disabled  type="date" value-format="yyyy-MM-dd" placeholder="证件有效期"></el-date-picker>
                            </el-form-item>
                            <el-form-item label="证件有效期类型" prop="certTermType">
                                <el-select v-model="bgGuaranteeApply.certTermType" disabled  placeholder="证件有效期类型">
                                    <el-option
                                            v-for="item in certTermTypeoptions"
                                            :key="item.dictCode"
                                            :label="item.dictName"
                                            :value="item.dictCode"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="联系人" prop="linkman">
                                <el-input v-model="bgGuaranteeApply.linkman" disabled  placeholder="联系人"></el-input>
                            </el-form-item>
                            <el-form-item label="联系人电话" prop="linkmanTel">
                                <el-input v-model="bgGuaranteeApply.linkmanTel" disabled  placeholder="联系人电话"></el-input>
                            </el-form-item>
                            <el-form-item label="公司电话" prop="phoneNumber">
                                <el-input v-model="bgGuaranteeApply.phoneNumber" disabled  placeholder="公司电话"></el-input>
                            </el-form-item>
                            <el-form-item label="地址" prop="address">
                                <el-input v-model="bgGuaranteeApply.address"  disabled placeholder="地址"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <el-form :model="bginsurancebbr" :rules="dataRule2" ref="bginsurancebbr" label-width="140px">
                    <el-row :gutter="20">
                        <div class="temp-subtitle">被保人（招标方）</div>
                        <el-col :xs="24" :lg="colConfig">
                            <el-form-item label="名称" prop="name">
                                <el-input v-model="bginsurancebbr.name"  disabled placeholder="名称"></el-input>
                            </el-form-item>
                            <el-form-item label="证件类型" prop="certType">
                                <el-select v-model="bginsurancebbr.certType" disabled  placeholder="证件类型">
                                    <el-option
                                            v-for="item in certTypeoptions"
                                            :key="item.dictCode"
                                            :label="item.dictName"
                                            :value="item.dictCode"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="证件号码" prop="certCode">
                                <el-input v-model="bginsurancebbr.certCode" disabled  placeholder="证件号码"></el-input>
                            </el-form-item>
                            <el-form-item label="证件有效期" prop="certTerm">
                                <el-date-picker v-model="bginsurancebbr.certTerm" disabled  type="date" value-format="yyyy-MM-dd" placeholder="证件有效期"></el-date-picker>
                            </el-form-item>
                            <el-form-item label="证件有效期类型" prop="certTermType" >
                                <el-select v-model="bginsurancebbr.certTermType" disabled  placeholder="证件有效期类型">
                                    <el-option
                                            v-for="item in certTermTypeoptions"
                                            :key="item.dictCode"
                                            :label="item.dictName"
                                            :value="item.dictCode"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="联系人" prop="linkman">
                                <el-input v-model="bginsurancebbr.linkman" disabled placeholder="联系人"></el-input>
                            </el-form-item>
                            <el-form-item label="联系人电话" prop="linkmanTel">
                                <el-input v-model="bginsurancebbr.linkmanTel" disabled placeholder="联系人电话"></el-input>
                            </el-form-item>
                            <el-form-item label="公司电话" prop="phoneNumber">
                                <el-input v-model="bginsurancebbr.phoneNumber" disabled placeholder="公司电话"></el-input>
                            </el-form-item>
                            <el-form-item label="地址" prop="address">
                                <el-input v-model="bginsurancebbr.address" disabled placeholder="地址"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>

                <el-form :model="bgbiddingproject" :rules="dataRule3" ref="bgbiddingproject" label-width="140px">
                    <el-row :gutter="20">
                        <el-col :xs="24" :lg="colConfig">
                            <div class="temp-subtitle">项目信息</div>
                            <el-form-item label="标段名称" prop="name">
                                <el-input v-model="bgbiddingproject.name" disabled placeholder="标段名称"></el-input>
                            </el-form-item>
                            <el-form-item label="投标工程名" prop="biddingName">
                                <el-input v-model="bgbiddingproject.biddingName" disabled placeholder="投标工程名"></el-input>
                            </el-form-item>
                            <el-form-item label="标段开始日期" prop="bidStartDate">
                                <el-date-picker v-model="bgbiddingproject.bidStartDate" disabled type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="标段开始日期"></el-date-picker>
                            </el-form-item>
                            <el-form-item label="标段结束日期" prop="bidEndDate">
                                <el-date-picker v-model="bgbiddingproject.bidEndDate" disabled type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="标段结束日期"></el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <el-form :model="bginsuranceinfo" :rules="dataRule4" ref="bginsuranceinfo" label-width="140px">
                    <el-row :gutter="20">
                        <el-col :xs="24" :lg="colConfig">
                            <div class="temp-subtitle">保险信息</div>
                            <el-form-item label="名称" prop="name">
                                <el-input v-model="bginsuranceinfo.name" disabled placeholder="名称"></el-input>
                            </el-form-item>
                            <el-form-item label="保险类型" prop="insuranceType">
                                <el-select v-model="bginsuranceinfo.insuranceType" disabled placeholder="保险类型">
                                    <el-option
                                            v-for="item in insuranceTypeoptions"
                                            :key="item.dictCode"
                                            :label="item.dictName"
                                            :value="item.dictCode"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="保险开始时间" prop="startDate">
                                <el-date-picker v-model="bginsuranceinfo.startDate" disabled type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="保险开始时间"></el-date-picker>
                            </el-form-item>
                            <el-form-item label="保险截止时间" prop="endDate">
                                <el-date-picker v-model="bginsuranceinfo.endDate" disabled type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="保险截止时间"></el-date-picker>
                            </el-form-item>
                            <el-form-item label="担保金额" prop="guaranteeAmount">
                                <el-input v-model="bginsuranceinfo.guaranteeAmount" disabled placeholder="担保金额"></el-input>
                            </el-form-item>
                            <el-form-item label="保险金额" prop="guaranteePrice">
                                <el-input v-model="bginsuranceinfo.guaranteePrice" disabled  placeholder="保险金额"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>

                <el-form :model="bgguaranteeinvoice" :rules="dataRule5" ref="bgguaranteeinvoice" label-width="140px">
                    <el-row :gutter="20">
                        <el-col :xs="24" :lg="colConfig">
                            <div class="temp-subtitle">发票信息</div>
                            <el-form-item label="开票对象" prop="invoiceObject">
                                <el-select v-model="bgguaranteeinvoice.invoiceObject" disabled placeholder="开票对象">
                                    <el-option
                                            v-for="item in invoiceObjectoptions"
                                            :key="item.dictCode"
                                            :label="item.dictName"
                                            :value="item.dictCode"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="发票类型" prop="invoiceType">
                                <el-select v-model="bgguaranteeinvoice.invoiceType" disabled placeholder="发票类型">
                                    <el-option
                                            v-for="item in invoiceTypeoptions"
                                            :key="item.dictCode"
                                            :label="item.dictName"
                                            :value="item.dictCode"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="纳税人类型" prop="taxpayerType">
                                <el-select v-model="bgguaranteeinvoice.taxpayerType" disabled placeholder="纳税人类型">
                                    <el-option
                                            v-for="item in taxpayerTypeoptions"
                                            :key="item.dictCode"
                                            :label="item.dictName"
                                            :value="item.dictCode"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="纳税人识别号" prop="taxpayerNumber">
                                <el-input v-model="bgguaranteeinvoice.taxpayerNumber" disabled placeholder="纳税人识别号"></el-input>
                            </el-form-item>
                            <el-form-item label="电话" prop="phoneNumber">
                                <el-input v-model="bgguaranteeinvoice.phoneNumber" disabled placeholder="电话"></el-input>
                            </el-form-item>
                            <el-form-item label="地址" prop="address">
                                <el-input v-model="bgguaranteeinvoice.address" disabled placeholder="地址"></el-input>
                            </el-form-item>
                            <el-form-item label="开户行名称" prop="bankAccountName">
                                <el-input v-model="bgguaranteeinvoice.bankAccountName" disabled placeholder="开户行名称"></el-input>
                            </el-form-item>
                            <el-form-item label="银行账户号码" prop="bankAccountNo">
                                <el-input v-model="bgguaranteeinvoice.bankAccountNo" disabled placeholder="银行账户号码"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>

            </div>
        </el-card>

        <select-project v-if="selectVisible" ref="selectProject"  @refreshDataList="getDataList()"></select-project>

    </div>
</template>

<script>
import Bus from '@/api/bus.js'
export default {
  data () {
    return {
      letterId: '',
      cjfChargeId: '',

      colConfig: 1, // 列配置 1 2列
      visible: false,
      selectVisible: false,
      pageLoading: false,
      getDicListURL: '/sys/dict/type/',
      certTermTypeoptions: [],
      insuranceTypeoptions: [],
      invoiceTypeoptions: [],
      taxpayerTypeoptions: [],
      invoiceObjectoptions: [],
      certTypeoptions: [],
      retJson: [],
      appCode: '',
      dataFrom: {
      },
      bgGuaranteeApply: {
        id: '',
        code: '',
        name: '',
        certType: '',
        certCode: '',
        certTerm: '',
        certTermType: '',
        linkman: '',
        linkmanTel: '',
        corporation: '',
        email: '',
        phoneNumber: '',
        fax: '',
        postcode: '',
        platformCode: '',
        address: '',
        description: '',
        registerAddress: '',
        registerCapital: '',
        registerTime: '',
        isVip: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      bginsurancebbr: {
        code: '',
        name: '',
        certType: '',
        certCode: '',
        certTerm: '',
        certTermType: '',
        linkman: '',
        linkmanTel: '',
        corporation: '',
        email: '',
        phoneNumber: '',
        fax: '',
        postcode: '',
        platformCode: '',
        address: '',
        description: '',
        registerAddress: '',
        registerCapital: '',
        registerTime: '',
        isVip: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      bgbiddingproject: {
        code: '',
        name: '',
        bidOpenDate: '',
        biddingid: '',
        guaranteeAmount: '',
        tenderee: '',
        tendereeAgent: '',
        biddingName: '',
        biddingToOffces: '',
        remars: '',
        bidStartDate: '',
        bidEndDate: '',
        platformCode: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      bginsuranceinfo: {
        code: '',
        name: '',
        insuranceType: '',
        startDate: '',
        endDate: '',
        insuranceOrgid: '',
        insuranceOrg: '',
        insuranceCode: '',
        insuranceUrl: '',
        insuranceAddr: '',
        insuranceChange: '',
        guaranteeAmount: '',
        guaranteePrice: '',
        platformCode: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      bgguaranteeinvoice: {
        code: '',
        name: '',
        invoiceType: '',
        invoiceObject: '',
        taxpayerType: '',
        taxpayerNumber: '',
        phoneNumber: '',
        address: '',
        bankAccountName: '',
        bankAccountNo: '',
        mailingCode: '',
        mailingUser: '',
        mailingTel: '',
        mailingAddress: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certTerm: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certTermType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        linkman: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        linkmanTel: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        phoneNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        address: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    },
    dataRule2 () {
      return {
        name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certTerm: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        certTermType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        linkman: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        linkmanTel: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        phoneNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        address: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    },
    dataRule3 () {
      return {
        name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        biddingName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        bidStartDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        bidEndDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]

      }
    },
    dataRule4 () {
      return {
        name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        insuranceType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        startDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        endDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        guaranteeAmount: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        guaranteePrice: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]

      }
    },
    dataRule5 () {
      return {
        invoiceType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        invoiceObject: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        taxpayerType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        taxpayerNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        phoneNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        address: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        bankAccountName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        bankAccountNo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.getCertTermTypeInfo()
    this.getCertTypeTTInfo()
    this.getInvoiceObjectInfo()
    this.getInsuranceTypeInfo()
    this.getInvoiceTypeInfo()
    this.getTaxpayerTypeInfo()
    this.init()
    this.colConfig = 24 / this.colConfig
  },
  mounted: function () {
    // 用$on事件来接收参数
    Bus.$on('val', (data) => {
      if (data != null) {
        var strs = data.split(',') // 字符分割
        this.dataForm.projectId = strs[0]
        this.dataForm.projectCode = strs[1]
        this.dataForm.projectName = strs[2]
      }
    })
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['bgGuaranteeApply'].resetFields()
        this.$refs['bginsurancebbr'].resetFields()
        this.$refs['bgbiddingproject'].resetFields()
        this.$refs['bginsuranceinfo'].resetFields()
        this.$refs['bgguaranteeinvoice'].resetFields()
        this.getInfo()
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/letter/bgguaranteeletter/getInfoById/${this.$route.query.id}`).then(({ data: res }) => {
        this.bgGuaranteeApply = {
          ...this.dataForm,
          ...res.data.bgGuaranteeApply
        }
        this.bgbiddingproject = {
          ...this.dataForm,
          ...res.data.bgbiddingproject
        }
        this.bginsurancebbr = {
          ...this.dataForm,
          ...res.data.bginsurancebbr
        }
        this.bginsuranceinfo = {
          ...this.dataForm,
          ...res.data.bginsuranceinfo
        }
        this.bgguaranteeinvoice = {
          ...this.dataForm,
          ...res.data.bgguaranteeinvoice
        }
        this.bginsuranceinfo.guaranteeAmount = res.data.bgbiddingproject.guaranteeAmount
        this.pageLoading = false
      }).catch(() => {})
    },
    // 纳税人类型信息
    getTaxpayerTypeInfo (status) {
      this.$http.get(this.getDicListURL + 'taxpayerType').then(({ data: res }) => {
        this.taxpayerTypeoptions = {
          ...this.taxpayerTypeoptions,
          ...res.data.list
        }
      }).catch(() => {})
    },
    // 发票类型信息
    getInvoiceTypeInfo () {
      this.$http.get(this.getDicListURL + 'invoiceType').then(({ data: res }) => {
        this.invoiceTypeoptions = {
          ...this.invoiceTypeoptions,
          ...res.data.list
        }
      }).catch(() => {})
    },
    // 开票对象信息
    getInvoiceObjectInfo () {
      this.$http.get(this.getDicListURL + 'invoiceObject').then(({ data: res }) => {
        this.invoiceObjectoptions = {
          ...this.invoiceObjectoptions,
          ...res.data.list
        }
      }).catch(() => {})
    },
    // 保险类型信息
    getInsuranceTypeInfo () {
      this.$http.get(this.getDicListURL + 'insuranceType').then(({ data: res }) => {
        this.insuranceTypeoptions = {
          ...this.insuranceTypeoptions,
          ...res.data.list
        }
      }).catch(() => {})
    },
    // 证件有效期信息
    getCertTermTypeInfo () {
      this.$http.get(this.getDicListURL + 'certTermType').then(({ data: res }) => {
        this.certTermTypeoptions = {
          ...this.certTermTypeoptions,
          ...res.data.list
        }
      }).catch(() => {})
    },
    // 证件信息
    getCertTypeTTInfo () {
      this.$http.get(this.getDicListURL + 'certTypeTT').then(({ data: res }) => {
        this.certTypeoptions = {
          ...this.certTypeoptions,
          ...res.data.list
        }
      }).catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
    .el-card{
        border: 1px solid #ddd !important;
        margin-bottom: 20px;
    }
    .temp-content {
        // padding: 24px;
        width: 800px;
        margin: 0 auto;
        .titlename {
            width: 100%;
            height: 60px;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-pack: center;
            -ms-flex-pack: center;
            justify-content: center;
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;
            font-weight: bold;
            border-bottom: 1px solid #e6e6e6;
            font-size: 20px;
            p{
                margin: 10px 0;
            }
        }
        .temp-line {
            padding-bottom: 32px;
        }
        .temp-line:after {
            position: absolute;
            right: 0;
            bottom: 0;
            content: "";
            display: block;
            clear: both;
            width: 100%;
            min-width: 100%;
            height: 1px;
            margin: 8px 0 24px;
            background: #e8e8e8;
        }
        .temp-subtitle {
            font-size: 26px;

            color: #333;
            line-height: 29px;
            margin-bottom: 30px;
            font-weight: bold;
            text-align: center;
        }
        .temp-form {
            position: relative;

            .el-date-editor.el-input{
                width: auto !important;
            }
            .el-date-editor{
                max-width: 280px;
            }
            .el-input {
                max-width: 280px;
            }
            .el-date-editor--daterange{
                max-width: 240px;
            }
        }
    }
    .foot{
        width: 100%;
        background: #fff;
        position: fixed;
        bottom: 0;
        padding: 10px 0;
        border-top: 1px solid #ddd;
        background: #f9f9f9;
        text-align: center;
        left: 80px;
        z-index: 999;
        span{
            text-align: center;
        }
    }
    .btn{
        .el-input-group__append{
            color: #fff;
            background-color: #409eff;
            border-color: #409eff;
            padding: 0 8px;
        }
    }
    .el-loading-mask{
        z-index: 998 !important;
    }

</style>
