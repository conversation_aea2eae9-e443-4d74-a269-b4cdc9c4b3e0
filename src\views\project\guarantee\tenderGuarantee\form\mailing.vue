<!--
 * @Descripttion:
 * @Author: kong<PERSON><PERSON>ang
 * @Date: 2020-07-29 09:13:44
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-09-28 17:21:58
-->
<template>
  <el-row :gutter="20" class="mailing" style="margin:15px 20px 0;">

    <el-form v-if="dataForm" label-position="left" :model="dataForm" ref="dataForm" :rules="dataRule" label-width="140px">
      <el-card class="box-card" shadow='never'>
        <div slot="header" class="clearfix">
          <span class="title">保函收件信息</span>
          <!-- <el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button> -->
        </div>
        <div>
          <el-col :xs="24" :lg="24" style="text-align:center;margin-bottom:15px;">
            <!-- mailingUser 收件人 mailingTel 收件电话 mailingAddress 收件地址 -->
            <el-button icon='el-icon-circle-plus' @click="showDia()" size="small" type="primary">选择收件信息</el-button>
          </el-col>
          <div >
            <el-col :xs="24" :lg="span">
              <el-form-item label="收件人" prop="mailingUser">
                <!-- {{this.dataForm.mailingUser}} -->
                <el-input v-model="dataForm.mailingUser"  size="small" class="wd180" style="width:100%;" placeholder="请输入收件人"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :lg="span">
              <el-form-item label="收件电话" prop="mailingTel">
                <!-- {{this.dataForm.mailingTel}} -->
                <el-input v-model="dataForm.mailingTel"  size="small" class="wd180" style="width:100%;" placeholder="请输入收件电话"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :lg="24">
              <el-form-item label="收件地址" prop="mailingAddress">
                <!-- {{this.dataForm.mailingAddress}} -->
                <el-input v-model="dataForm.mailingAddress"  type="textarea" :rows="2"  size="small" class="wd180" style="width:100%;" placeholder="请输入收件地址"></el-input>
              </el-form-item>
            </el-col>
          </div>
        </div>
      </el-card>

      <choseMailing v-if="visible" ref="choseMailing" @getMailingData='getMailingData'></choseMailing>
    </el-form>
  </el-row>

</template>
<script>
import choseMailing from './compoents/choseMailing'
export default {
  components: {
    choseMailing
  },
  data () {
    return {
      visible: false
    }
  },
  props: {
    dataForm: {
      type: Object
    },
    options: {
      type: Object
    },
    span: {
      type: Number,
      default: 12
    }
  },
  computed: {
    dataRule () {
      return {
        mailingUser: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        mailingTel: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        mailingAddress: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ]
      }
    }
  },
  methods: {
    showDia () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['choseMailing'].init()
      })
    },
    getMailingData (row) {
      this.$set(this.dataForm, 'id', row.id)
      this.$set(this.dataForm, 'mailingUser', row.mailingUser)
      this.$set(this.dataForm, 'mailingTel', row.mailingTel)
      this.$set(this.dataForm, 'mailingAddress', row.mailingAddress)
    },
    push () {
      return new Promise((resolve, reject) => {
        this.$refs['dataForm'].validate((valid) => {
          console.log(valid)
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    }
  }
}
</script>
<style  scoped>
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  /* padding: 16px 0; */
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
</style>
