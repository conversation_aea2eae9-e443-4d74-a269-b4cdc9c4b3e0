<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"  :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
        <el-row :gutter="20">
            <el-col :xs="24" :lg="colConfig">
          <el-form-item label="编码" prop="code">
     <el-input v-model="dataForm.code" placeholder="编码"></el-input>
</el-form-item>
                <el-form-item label="名称" prop="name">
     <el-input v-model="dataForm.name" placeholder="名称"></el-input>
</el-form-item>
                <el-form-item label="保函id" prop="letterId">
     <el-input v-model="dataForm.letterId" placeholder="保函id"></el-input>
</el-form-item>
                <el-form-item label="保函编码" prop="letterCode">
     <el-input v-model="dataForm.letterCode" placeholder="保函编码"></el-input>
</el-form-item>
                <el-form-item label="保函名称" prop="letterName">
     <el-input v-model="dataForm.letterName" placeholder="保函名称"></el-input>
</el-form-item>
                <el-form-item label="支付时间" prop="payTime">
     <el-input v-model="dataForm.payTime" placeholder="支付时间"></el-input>
</el-form-item>
                <el-form-item label="支付方式" prop="paymentMethod">
     <el-input v-model="dataForm.paymentMethod" placeholder="支付方式"></el-input>
</el-form-item>
                <el-form-item label="支付状态" prop="paymentStatus">
     <el-input v-model="dataForm.paymentStatus" placeholder="支付状态"></el-input>
</el-form-item>
                <el-form-item label="交易账号" prop="tradeAccount">
     <el-input v-model="dataForm.tradeAccount" placeholder="交易账号"></el-input>
</el-form-item>
                <el-form-item label="交易账户" prop="tradeAccountName">
     <el-input v-model="dataForm.tradeAccountName" placeholder="交易账户"></el-input>
</el-form-item>
                <el-form-item label="订单号" prop="tradeNo">
     <el-input v-model="dataForm.tradeNo" placeholder="订单号"></el-input>
</el-form-item>
                <el-form-item label="状态" prop="status" size="mini">
   <el-radio-group v-model="dataForm.status">
       <el-radio :label="0">停用</el-radio>
       <el-radio :label="1">正常</el-radio>
   </el-radio-group>
</el-form-item>
                            </el-col>
        </el-row>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        letterId: '',
        letterCode: '',
        letterName: '',
        payTime: '',
        paymentMethod: '',
        paymentStatus: '',
        tradeAccount: '',
        tradeAccountName: '',
        tradeNo: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        code: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        letterId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        letterCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        letterName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        payTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        paymentMethod: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        paymentStatus: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        tradeAccount: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        tradeAccountName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        tradeNo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        status: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]

      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/letter/bgguaranteepay/${this.dataForm.id}`).then(({ data: res }) => {
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/letter/bgguaranteepay/', this.dataForm).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
