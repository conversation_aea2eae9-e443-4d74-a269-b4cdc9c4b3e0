<template>
  <div v-loading.fullscreen.lock="loading"  :element-loading-text="$t('loading')" :class="['aui-wrapper', { 'aui-sidebar--fold': $store.state.sidebarFold }]">
    <template v-if="!loading">
      <main-navbar />
      <main-sidebar />
      <div class="aui-content__wrapper">
        <main-content v-if="!$store.state.contentIsNeedRefresh" />
      </div>
      <main-theme-tools />
      <main-process/>
      <!-- <main-Serve/> -->
    </template>
  </div>
</template>

<script>
import MainNavbar from './main-navbar'
import MainSidebar from './main-sidebar'
import MainContent from './main-content'
import MainThemeTools from './main-theme-tools'
import MainProcess from './main-process'
import MainServe from './main-serve'
import debounce from 'lodash/debounce'
const { version } = require('../../../package.json')
export default {
  provide () {
    return {
      // 刷新
      refresh () {
        this.$store.state.contentIsNeedRefresh = true
        this.$nextTick(() => {
          this.$store.state.contentIsNeedRefresh = false
        })
      }
    }
  },
  data () {
    return {
      loading: true
    }
  },
  components: {
    MainNavbar,
    MainSidebar,
    MainContent,
    MainThemeTools,
    MainProcess,
    // eslint-disable-next-line vue/no-unused-components
    MainServe
  },
  watch: {
    $route: 'routeHandle'
    // $route: {
    //   handler (val, oldVal) {
    //     // console.log('更新', val)
    //     this.routeHandle(val)
    //   },
    //   // 深度观察监听
    //   deep: true
    // }
  },
  created () {
    this.windowResizeHandle()
    this.routeHandle(this.$route)
    Promise.all([
      this.getUserInfo(),
      this.getPermissions()
    ]).then(() => {
      this.loading = false
    })
  },
  methods: {
    // 窗口改变大小
    windowResizeHandle () {
      this.$store.state.sidebarFold = document.documentElement['clientWidth'] <= 992 || false
      window.addEventListener('resize', debounce(() => {
        this.$store.state.sidebarFold = document.documentElement['clientWidth'] <= 992 || false
      }, 150))
    },
    // 路由, 监听
    routeHandle (route) {
      if (!route.meta.isTab) {
        return false
      }
      var tab = this.$store.state.contentTabs.filter(item => item.name === route.name)[0]
      console.log(tab)
      if (!tab) {
        tab = {
          ...window.SITE_CONFIG['contentTabDefault'],
          ...route.meta,
          'name': route.name,
          'params': { ...route.params },
          'query': { ...route.query }
        }
        this.$store.state.contentTabs = this.$store.state.contentTabs.concat(tab)
        console.log(this.$store.state.contentTabs)
        // console.log(this.$store.state.contentTabs, window.SITE_CONFIG['contentTabDefault'])
        if (!this.$store.state.sidebarMode) {
          let len = this.$store.state.contentTabs.length
          this.$store.state.contentTabs[len - 1].navActive = this.$store.state.navbarActive
        }
      } else {
        this.$store.state.contentTabs.map((item, index) => {
          if (item.name === route.name) {
            this.$store.state.contentTabs[index] = tab
          }
        })
      }
      this.$store.state.sidebarMenuActiveName = tab.menuId
      this.$store.state.contentTabsActiveName = tab.name
    },
    // 获取当前管理员信息
    getUserInfo () {
      return this.$http.get('/sys/user/info').then(({ data: res }) => {
        this.$store.state.user.id = res.data.id
        this.$store.state.user.deptId = res.data.deptId
        this.$store.state.user.name = res.data.username
        this.$store.state.user.superAdmin = res.data.superAdmin
        this.$store.state.user.auditProcedures = res.data.auditProcedures
        window.localStorage.wmUserInfo = JSON.stringify({ userId: res.data.id, userTag: res.data.username, projectVersion: version })
      }).catch(() => {})
    },
    // 获取权限
    getPermissions () {
      return this.$http.get('/sys/menu/permissions').then(({ data: res }) => {
        window.SITE_CONFIG['permissions'] = res.data
      }).catch(() => {})
    }
  }
}
</script>
