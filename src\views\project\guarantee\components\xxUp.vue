<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-10-31 10:34:59
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-12-24 14:30:26
-->
<template>
  <div>

    <el-form-item label="线下支付" prop="xx">
      <el-timeline>
        <el-timeline-item>
          <span slot="dot" class="dot">1</span>
          <div>

            <div class="xx">
              <h3>第一步&emsp;打款</h3>

              <p><span>账户名称：</span>{{bankInfo.bankAccountName}}</p>
              <p><span>开户银行：</span>{{bankInfo.bankName}}</p>
              <p><span>账号：</span>{{bankInfo.bankAccountNo}}</p>
            </div>
          </div>
        </el-timeline-item>

        <el-timeline-item>
          <span slot="dot" class="dot">2</span>
          <h3>第二步&emsp;上传支付凭证</h3>
          <el-upload style="width:500px;" ref="upload" class="upload-demo" drag :on-remove='handleRemove' :action="url+'/letter/guarantee/bhFileupload'" :data='zbData' :headers="myHeaders"
            :before-remove="BeforeRemove" :on-success='successHandle' :before-upload="beforeUpload" :limit="1" :on-exceed="handleExceed" :file-list="fileList">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传.jpg,.JPG,.jpeg,.JPEG,.png,.PNG文件</div>
          </el-upload>
        </el-timeline-item>
        <el-timeline-item>
          <span slot="dot" class="dot">2</span>
          <h3>第三步&emsp;填写支付信息</h3>
          <el-row>
            <el-col :span="24">
              <el-form-item label="打款账号" prop="bankAccount">
                <el-input size="small" v-model="dataForm.bankAccount" placeholder="打款账号"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="打款金额" prop="moneyAmount">
                <el-input size="small" v-model="dataForm.moneyAmount" placeholder="打款金额"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="打款日期" prop="payTime">
                <el-date-picker size="small" value-format="yyyy-MM-dd" v-model="dataForm.payTime" type="date" placeholder="打款日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="联系人姓名" prop="contacts">
                <el-input size="small" v-model="dataForm.contacts" placeholder="联系人姓名"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="联系人电话" prop="mobile">
                <el-input size="small" v-model="dataForm.mobile" placeholder="联系人电话"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-timeline-item>
      </el-timeline>
    </el-form-item>
  </div>

</template>
<script>
import Cookies from 'js-cookie'
export default {
  props: ['dataForm'],
  data () {
    return {
      url: '',
      zbData: {},
      myHeaders: {
        token: Cookies.get('token') || ''
      },
      // dataForm: {
      //   bankAccount: '',
      //   moneyAmount: '',
      //   payTime: '',
      //   contacts: '',
      //   mobile: ''
      // },
      bankInfo: {},
      fileList: []
    }
  },
  created () {
    this.url = window.SITE_CONFIG['apiURL']
    this.zbData.letterId = this.$route.query.id
    this.zbData.type = '7'
    this.getOfflinePayBankName()
  },
  methods: {
    submit () {
      this.$refs.upload.submit()
    },
    beforeUpload (file) {
      var extension = file.name.substring(file.name.lastIndexOf('.') + 1)
      var lastName =
        extension !== 'jpg' &&
        extension !== 'JPG' &&
        extension !== 'jpeg' &&
        extension !== 'JPEG' &&
        extension !== 'png' &&
        extension !== 'PNG'

      if (lastName) {
        this.$message.warning('文件要求格式为：.jpg,.JPG,.jpeg,.JPEG,.png,.PNG')
      }

      return !lastName
    },
    handleExceed (files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件`
      )
    },
    successHandle (res, file, fileList) {
      if (res.data.flag !== true) {
        this.fileList = []
        return this.$message.error(res.data.msg)
      }
      // this.getInfo()
      this.$message.success('上传成功')
    },
    async getOfflinePayBankName () {
      let { data } = await this.$http.get(
        'letter/bgguaranteeissue/getOfflinePayBankName?code=' +
          this.$route.query.key
      )
      this.bankInfo = data.data
    },
    BeforeRemove (file, fileList) {
      console.log(file, fileList)
      let a = true
      if (file && file.status === 'success') {
        a = this.$confirm(`确定移除 ${file.name}？`)
      }
      return a
    },
    handleRemove (file, fileList) {
      if (file && file.status === 'success') {
        // 移除方法
        var deleteId = file.url ? file.url : file.response.data.ossId
        return this.deleteFile(deleteId, 1)
      }
    },
    deleteFile (id, type) {
      return new Promise(async (resolve, reject) => {
        let { data } = await this.$http.get(
          'letter/bgguaranteeletter/deletePayVoucherFile?ossId=' +
            id +
            '&letterId=' +
            this.letterId
        )
        if (data.code !== 0) {
          this.$message.error(data.msg)
          resolve(false)
        }
        // this.getInfo()
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
        resolve(true)
      })
    }
  }
}
</script>
<style lang="scss">
.dot {
  display: inline-block;
  width: 25px;
  height: 25px;
  /* padding: 5px; */
  left: -8px;
  top: 0;
  position: absolute;
  background-color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #f59600;
  border: 1px solid rgb(241, 130, 65);
}
</style>
