<template>
    <el-dialog :visible.sync="visible" title="信息详情" :close-on-click-modal="false" :close-on-press-escape="false"
               width="70%">
        <el-form :model="dataForm" v-loading='loading' ref="dataForm"
                 :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
            <el-row :gutter="20">
                <el-col :xs="24" :lg="colConfig">
                    <el-form-item label="模板编码" prop="code" label-width="190px">
                        <span>{{dataForm.code}}</span>
                    </el-form-item>
                    <el-form-item label="模板名称" prop="name" label-width="190px">
                        <span>{{dataForm.name}}</span>
                    </el-form-item>
                    <el-form-item label="是否默认" prop="isDefault" label-width="190px">
                        <span v-if="dataForm.isDefault == 0">否</span>
                        <span v-if="dataForm.isDefault == 1">是</span>
                    </el-form-item>
                    <el-form-item label="状态" prop="status" label-width="190px">
                        <span v-if="dataForm.status == 0">停用</span>
                        <span v-if="dataForm.status == 1">正常</span>
                    </el-form-item>
                    <el-form-item label="保函类型" prop="guaranteeType" label-width="190px">
                        <span>{{dataForm.guaranteeType}}</span>
                    </el-form-item>
                    <el-form-item label="模板正文" prop="content" label-width="190px">
                        <div class="content" ref='box'>
                        </div>
                    </el-form-item>
                    <el-form-item label="申请人盖章关键字" prop="acSignKeyword" label-width="190px">
                        <span>{{dataForm.acSignKeyword}}</span>
                    </el-form-item>
                    <el-form-item label="出具机构盖章关键字" prop="gcSignKeyword" label-width="190px">
                        <span>{{dataForm.gcSignKeyword}}</span>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template slot="footer">
            <el-button type="primary" @click="visible = false">确定</el-button>
        </template>
    </el-dialog>
</template>

<script>
import 'quill/dist/quill.snow.css'
export default {
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      loading: false,
      dataForm: {
        id: '',
        acSignKeyword: '',
        companyId: '',
        content: '',
        gcSignKeyword: '',
        guaranteeType: '',
        isDefault: '',
        name: '',
        code: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.loading = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/letter/bgguaranteetemplate/${this.dataForm.id}`).then(({ data: res }) => {
        this.loading = false

        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        this.$refs.box.innerHTML = res.data.content
      }).catch(() => {
      })
    }
  }
}
</script>
