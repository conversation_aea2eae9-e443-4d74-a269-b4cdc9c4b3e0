<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-letter__bginsuranceinfo}">
      <el-form :inline="true" :model="dataForm" >
        <el-form-item>
          <el-input v-model="dataForm.name" clearable   placeholder="名称"></el-input>
        </el-form-item>
          <el-form-item>
              <el-input v-model="dataForm.propsalNo" clearable   placeholder="投保单号"></el-input>
          </el-form-item>
          <el-form-item>
              <el-input v-model="dataForm.insuranceCode" clearable   placeholder="保单号"></el-input>
          </el-form-item>
        <el-form-item>
          <el-button type="danger"   @click="getDataList(1)">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
<!--        <el-form-item>-->
<!--           <el-button type="primary" @click="importHandle()">{{ $t('import') }}</el-button>-->
<!--         </el-form-item>-->
<!--        <el-form-item>-->
<!--          <el-button v-if="$hasPermission('letter:bginsuranceinfo:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>-->
<!--        </el-form-item>-->
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bginsuranceinfo:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
<!--        <el-form-item>-->
<!--          <el-button v-if="$hasPermission('letter:bginsuranceinfo:update')" type="danger" @click="enableHandle()">{{ $t('enable') }}</el-button>-->
<!--        </el-form-item>-->
<!--        <el-form-item>-->
<!--          <el-button v-if="$hasPermission('letter:bginsuranceinfo:update')" type="danger" @click="stopHandle()">{{ $t('stop') }}</el-button>-->
<!--        </el-form-item>-->
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle"   style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
            <el-table-column prop="name" label="名称" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
            <el-table-column prop="insuranceType" label="保险类型" sortable="custom" header-align="center" align="center" width="200">
                <template slot-scope="scope">
                    <el-tag size="mini" type="danger"><span>{{fomatMethod(scope.row.insuranceType,1)}}</span>
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="startDate" label="保险开始时间" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
            <el-table-column prop="endDate" label="保险截止时间" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
            <!-- <el-table-column prop="insuranceOrgid" label="保险机构ID" sortable="custom" header-align="center" align="center"></el-table-column> -->
            <el-table-column prop="insuranceOrg" label="保险机构" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
            <el-table-column prop="propsalNo" label="投保单号" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
            <el-table-column prop="insuranceCode" label="保函单号" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
            <el-table-column prop="insuranceUrl" label="保函下载url" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
            <el-table-column prop="guaranteeAmount" label="担保金额" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
            <el-table-column prop="guaranteePrice" label="保险金额" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
            <el-table-column prop="platformCode" label="所属平台编码" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
              <el-table-column prop="createDate" label="创建时间" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
              <el-table-column prop="updateDate" label="更新时间" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
              <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button v-if="$hasPermission('letter:bginsuranceinfo:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
            <el-dropdown>
              <span class="el-dropdown-link">
                <el-button type="text" size="mini">更多操作<i class="el-icon-arrow-down el-icon--right"></i></el-button>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item> <el-button v-if="$hasPermission('letter:bginsuranceinfo:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button></el-dropdown-item>
                  <el-dropdown-item> <el-button v-if="$hasPermission('letter:bginsuranceinfo:update')&&scope.row.status==0" type="text" size="small" @click="enableHandle(scope.row.id)">{{ $t('enable') }}</el-button></el-dropdown-item>
                    <el-dropdown-item> <el-button v-if="$hasPermission('letter:bginsuranceinfo:update')&&scope.row.status==1" type="text" size="small" @click="stopHandle(scope.row.id)">{{ $t('stop') }}</el-button></el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
        <!-- 弹窗, 上传文件 -->
      <upload v-if="uploadVisible" ref="upload" @refreshDataList="getDataList"></upload>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './bginsuranceinfo-add-or-update'
import Upload from './bginsuranceinfo-upload'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bginsuranceinfo/page',
        getDataListIsPage: true,
        exportURL: '/letter/bginsuranceinfo/export',
        deleteURL: '/letter/bginsuranceinfo',
        enableURL: '/letter/bginsuranceinfo/enable',
        stopURL: '/letter/bginsuranceinfo/stop',
        deleteIsBatch: true
      },
      statusoptions: [{
        value: 1,
        label: '正常'
      }, {
        value: 0,
        label: '停用'
      }],
      dataForm: {
        code: '',
        name: '',
        propsalNo: '',
        insuranceCode: '',
        status: 1
      },
      getDicListURL: '/sys/dict/type/',
      guaranteeTypemaps: '',
      guaranteeTypeoptions: [],
      orderField: 'code',
      order: 'asc',
      uploadVisible: false
    }
  },
  components: {
    AddOrUpdate,
    Upload
  },
  activated () {
    this.getGuaranteeTypeInfo()
  },
  methods: {
    fomatMethod (value, i) {
      if (i === 1) {
        return this.guaranteeTypemaps[value]
      }
      if (i === 2) {
        return this.letterStatusmaps[value]
      }
    },
    // 获取保函类型信息
    getGuaranteeTypeInfo () {
      this.$http
        .get(this.getDicListURL + 'guaranteeType')
        .then(({ data: res }) => {
          this.guaranteeTypeoptions = {
            ...this.guaranteeTypeoptions,
            ...res.data.list
          }
          this.guaranteeTypemaps = {
            ...this.guaranteeTypemaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    },
    // 导入
    importHandle () {
      this.uploadVisible = true
      this.$nextTick(() => {
        this.$refs.upload.init()
      })
    },
    // 状态启用具体代码需微调格式去除空格
    enableHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(this.$t('prompt.info', { 'handle': '启用' }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.$http.put(this.mixinViewModuleOptions.enableURL, id ? [id] : this.dataListSelections.map(item => item.id)).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.getDataList()
            }
          })
        }).catch(() => {})
      }).catch(() => {})
    },
    // 状态停用
    stopHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(this.$t('prompt.info', { 'handle': '停用' }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        // 具体代码需微调格式去除空格
        this.$http.put(this.mixinViewModuleOptions.stopURL, id ? [id] : this.dataListSelections.map(item => item.id)).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.getDataList()
            }
          })
        }).catch(() => {})
      }).catch(() => {})
    }
  }
}

</script>
