<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2021-02-01 11:08:20
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-03-17 10:56:41
-->
<template>
  <div>
    <div class="el-drawer__header">审核记录</div>
    <el-timeline>
      <el-timeline-item v-for="(activity, index) in activities" :color="setColor(activity.auditStatus)" :key="index" :timestamp="activity.auditDatetime">
        <div>
          <div :style="`color:${setColor(activity.auditStatus)};`">
            {{activity.nodeName}}
          </div>
          <div class="itemBox" v-if="activity.nodeNo=='1'&&activity.auditStatus=='1'">
            <div class="item">
              初审意见:
              <span>{{detail.fwbFirstInstance}}</span>
            </div>
            <div class="item">
              初审签字:
              <span>{{detail.fwbFirstName}}</span>
            </div>
            <div class="item">
              初审日期:
              <span>{{detail.fwbFirstDate}}</span>
            </div>
          </div>
          <div class="itemBox" v-if="activity.nodeNo=='2'&&activity.auditStatus=='1'">
            <div class="item">
              复审意见:
              <span>{{detail.fwbRepeatInstance}}</span>
            </div>
            <div class="item">
              复审签字:
              <span>{{detail.fwbRepeatName}}</span>
            </div>
            <div class="item">
              复审日期:
              <span>{{detail.fwbRepeatDate}}</span>
            </div>
          </div>
          <div class="itemBox" v-if="activity.nodeNo=='3'&&activity.auditStatus=='1'">
             <div class="item">
              主管审核:
              <span>{{detail.zgFirstInstance}}</span>
            </div>
             <div class="item">
              主管签字:
              <span>{{detail.zgFirstName}}</span>
            </div>
             <div class="item">
              主管审核日期:
              <span>{{detail.zgFirstDate}}</span>
            </div>
          </div>
          <!-- <div class="itemBox" v-if="activity.nodeNo=='4'&&activity.auditStatus=='4'">
             <div class="item">
              总经理签字:
              <span>{{detail.zjlFirstName}}</span>
            </div>
             <div class="item">
              总经理审核日期:
              <span>{{detail.zjlFirstDate}}</span>
            </div>
          </div> -->
          <div class="auditStatus" v-if="activity.auditStatus=='2'">
            不通过原因：{{activity.auditOpinion}}
          </div>
        </div>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>
<script>
export default {
  data () {
    return {
      activities: [],
      detail: {}
    }
  },
  props: {
    dataForm: {
      type: Object
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    setColor (val) {
      if (val === '0') {
        return '#E6A23C'
      }
      if (val === '1') {
        return '#67C23A'
      }
      if (val === '2') {
        return '#F56C6C'
      }
    },
    getList () {
      this.$http
        .get(
          `/letter/bgletterlitigation/NodeResultList?businessId=${this.dataForm.auditId}`
        )
        .then(({ data: res }) => {
          this.loading = false

          this.activities = res.data.auditList
          this.detail = res.data
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.auditStatus {
  font-size: 12px;
  margin-top: 15px;
}
.itemBox{
  .item{
    line-height: 30px;
  }
}
</style>
