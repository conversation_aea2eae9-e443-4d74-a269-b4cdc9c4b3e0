<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"  :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
        <el-row :gutter="20">
            <el-col :xs="24" :lg="colConfig">
          <el-form-item label="实例编码" prop="code">
     <el-input v-model="dataForm.code" placeholder="实例编码"></el-input>
</el-form-item>
                <el-form-item label="实例名称" prop="name">
     <el-input v-model="dataForm.name" placeholder="实例名称"></el-input>
</el-form-item>
                <el-form-item label="业务表id" prop="tableId">
     <el-input v-model="dataForm.tableId" placeholder="业务表id"></el-input>
</el-form-item>
                <el-form-item label="业务表编码" prop="tableCode">
     <el-input v-model="dataForm.tableCode" placeholder="业务表编码"></el-input>
</el-form-item>
                <el-form-item label="业务表名称" prop="tableName">
     <el-input v-model="dataForm.tableName" placeholder="业务表名称"></el-input>
</el-form-item>
                <el-form-item label="审核类型" prop="auditType">
     <el-input v-model="dataForm.auditType" placeholder="审核类型"></el-input>
</el-form-item>
                <el-form-item label="审核节点" prop="auditNodeId">
     <el-input v-model="dataForm.auditNodeId" placeholder="审核节点"></el-input>
</el-form-item>
                <el-form-item label="审核流程类型" prop="auditNodeType">
     <el-input v-model="dataForm.auditNodeType" placeholder="审核流程类型"></el-input>
</el-form-item>
                <el-form-item label="节点审核名称" prop="auditNodeName">
     <el-input v-model="dataForm.auditNodeName" placeholder="节点审核名称"></el-input>
</el-form-item>
                <el-form-item label="审核节点状态" prop="auditNodeStatus">
     <el-input v-model="dataForm.auditNodeStatus" placeholder="审核节点状态"></el-input>
</el-form-item>
                <el-form-item label="备注" prop="remarks">
     <el-input v-model="dataForm.remarks" placeholder="备注"></el-input>
</el-form-item>
                  <el-form-item label="状态" prop="status" size="mini">
   <el-radio-group v-model="dataForm.status">
       <el-radio :label="0">停用</el-radio>
       <el-radio :label="1">正常</el-radio>
   </el-radio-group>
</el-form-item>
                            </el-col>
        </el-row>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        tableId: '',
        tableCode: '',
        tableName: '',
        auditType: '',
        auditNodeId: '',
        auditNodeType: '',
        auditNodeName: '',
        auditNodeStatus: '',
        remarks: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        code: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        auditType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        auditNodeId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        auditNodeType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        auditNodeName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        auditNodeStatus: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        status: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]

      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/demo/tbauditinstance/${this.dataForm.id}`).then(({ data: res }) => {
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/demo/tbauditinstance/', this.dataForm).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
