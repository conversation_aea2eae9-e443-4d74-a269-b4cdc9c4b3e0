<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-letter__bgguaranteepay}">
        <el-form :inline="true" :model="dataForm" >
            <el-form-item >
                <el-input v-model="dataForm.tradeNo"  clearable  placeholder="订单号"></el-input>
            </el-form-item>
            <el-form-item>
                <el-input v-model="dataForm.letterName" clearable   placeholder="订单信息"></el-input>
            </el-form-item>
            <el-form-item>
                <el-select v-model="dataForm.paymentStatus" clearable placeholder="请选择支付状态">
                    <el-option
                            v-for="item in paymentStatusoptions"
                            :key="item.dictCode"
                            :label="item.dictName"
                            :value="item.dictCode"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-input v-model="dataForm.policyNo" clearable placeholder="保单号"></el-input>
            </el-form-item>
            <el-form-item>
                <el-select v-model="dataForm.paymentMethod" clearable placeholder="请选择支付方式">
                    <el-option
                            v-for="item in paymentMethodoptions"
                            :key="item.dictCode"
                            :label="item.dictName"
                            :value="item.dictCode"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="danger"   @click="getDataList(1)">{{ $t('query') }}</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="danger" @click="resetHandle()">重置</el-button>
            </el-form-item>
        <el-form-item>
          <el-button type="success" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle"   style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
          <!--  <el-table-column prop="name" label="名称" sortable="custom" header-align="center" align="center" width="270"></el-table-column>
          -->
          <el-table-column prop="letterName" label="订单信息" sortable="custom" header-align="center" align="center" width="500"></el-table-column>
          <el-table-column prop="tradeNo" label="订单号" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
            <el-table-column prop="paymentMethod" label="支付方式" sortable="custom" header-align="center" align="center" width="270">
                <template slot-scope="scope">
                    <el-tag size="mini" type="danger"><span>{{fomatMethod(scope.row.paymentMethod,1)}}</span>
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="paymentStatus" label="支付状态" sortable="custom" header-align="center" align="center">
                <template slot-scope="scope">
                    <el-tag size="mini" type="danger"><span>{{fomatMethod(scope.row.paymentStatus,2)}}</span>
                    </el-tag>
                </template>
            </el-table-column>
          <el-table-column prop="policyNo" label="保单号" sortable="custom" header-align="center" align="center" width="180"></el-table-column>
          <el-table-column prop="payTime" label="支付时间" sortable="custom" header-align="center" align="center" width="200"></el-table-column>
          <el-table-column prop="createDate" label="创建时间" sortable="custom" header-align="center" align="center"></el-table-column>
              <el-table-column prop="updateDate" label="更新时间" sortable="custom" header-align="center" align="center"></el-table-column>
              <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button  type="text" size="small" @click="seeInfo(scope.row.letterId)">查看保函</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
        <!-- 弹窗, 上传文件 -->
      <upload v-if="uploadVisible" ref="upload" @refreshDataList="getDataList"></upload>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './bgguaranteepay-add-or-update'
import Upload from './bgguaranteepay-upload'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgguaranteepay/page',
        getDataListIsPage: true,
        exportURL: '/letter/bgguaranteepay/export',
        deleteURL: '/letter/bgguaranteepay',
        enableURL: '/letter/bgguaranteepay/enable',
        stopURL: '/letter/bgguaranteepay/stop',
        deleteIsBatch: true
      },
      statusoptions: [{
        value: 1,
        label: '正常'
      }, {
        value: 0,
        label: '停用'
      }],
      dataForm: {
        tradeNo: '',
        letterName: '',
        paymentStatus: '',
        policyNo: '',
        paymentMethod: ''
      },
      orderField: 'code',
      order: 'asc',
      uploadVisible: false,
      getDicListURL: '/sys/dict/type/',
      paymentMethodmaps: '',
      paymentMethodoptions: [],
      paymentStatusmaps: '',
      paymentStatusoptions: []
    }
  },
  components: {
    AddOrUpdate,
    Upload
  },
  activated () {
    this.getPaymentMethodInfo()
    this.getPaymentStatusInfo()
  },
  methods: {
    seeInfo (id, type) {
      this.$router.push({
        name: 'bgguaranteeletterDetail',
        query: {
          seeLetterId: `${id}`,
          type: type,
          JumpName: this.$route.name
        }
      })
    },
    fomatMethod (value, i) {
      if (i === 1) {
        if (value == null) {
          return '未知'
        } else {
          return this.paymentMethodmaps[value]
        }
      } else if (i === 2) {
        return this.paymentStatusmaps[value]
      }
    },
    // 获取支付方式信息
    getPaymentMethodInfo () {
      this.$http.get(this.getDicListURL + 'paymentMethod').then(({ data: res }) => {
        this.paymentMethodoptions = {
          ...this.paymentMethodoptions,
          ...res.data.list
        }
        this.paymentMethodmaps = {
          ...this.paymentMethodmaps,
          ...res.data.map
        }
      }).catch(() => {})
    },
    // 获取支付状态信息
    getPaymentStatusInfo () {
      this.$http.get(this.getDicListURL + 'paymentStatus').then(({ data: res }) => {
        this.paymentStatusoptions = {
          ...this.paymentStatusoptions,
          ...res.data.list
        }
        this.paymentStatusmaps = {
          ...this.paymentStatusmaps,
          ...res.data.map
        }
      }).catch(() => {})
    },
    // 导入
    importHandle () {
      this.uploadVisible = true
      this.$nextTick(() => {
        this.$refs.upload.init()
      })
    },
    // 状态启用具体代码需微调格式去除空格
    enableHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(this.$t('prompt.info', { 'handle': '启用' }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.$http.put(this.mixinViewModuleOptions.enableURL, id ? [id] : this.dataListSelections.map(item => item.id)).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.getDataList()
            }
          })
        }).catch(() => {})
      }).catch(() => {})
    },
    // 状态停用
    stopHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(this.$t('prompt.info', { 'handle': '停用' }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        // 具体代码需微调格式去除空格
        this.$http.put(this.mixinViewModuleOptions.stopURL, id ? [id] : this.dataListSelections.map(item => item.id)).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.getDataList()
            }
          })
        }).catch(() => {})
      }).catch(() => {})
    }
  }
}

</script>
