<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-03-10 18:54:25
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-03-03 10:17:26
-->
<template>
  <el-table size='mini' stripe :cell-style="TableCellStyle"  v-loading="dataListLoading" :data="dataList" border style="width: 100%;zoom:0.9">
    <el-table-column
        type="index"
        width="50">
        <template slot-scope="scope">
          <span v-show="dataList.length !=scope.$index+1">{{scope.$index+1}}</span>
        </template>
      </el-table-column>
    <el-table-column prop="issueName" label="出具机构名称" header-align="center" align="center" width="300">
      <template slot-scope="scope">
        <el-link v-if="type&&(scope.row.issueName!=='合计'&&scope.row.issueName.indexOf('累计')==-1)" type="primary" @click="go(scope.row.issueCode)" :underline="false">{{scope.row.issueName}}</el-link>
        <span v-else>{{scope.row.issueName}}</span>
      </template>
    </el-table-column>
    <el-table-column prop="settlementMonth" label="结算日期" header-align="center" align="center" ></el-table-column>
    <el-table-column prop="guaranteePrice" label="保函金额（元）" header-align="center" align="center" ></el-table-column>
    <el-table-column prop="invoicingRevenue" label="宏筑收入（开票）（元）" header-align="center" align="center" ></el-table-column>
    <el-table-column v-if="type === 'CJ'"  prop="profitMargin" label="宏筑财务确认" fixed="right" header-align="center" align="center" width="145">
      <template slot-scope="scope">
        <span v-if="scope.row.financeConfirm&&((scope.row.issueName!=='合计'&&scope.row.issueName.indexOf('累计')==-1)||scope.row.issueName.indexOf('累计')==-1)"><i style="color:#67C23A;" class="el-icon-success"></i>&nbsp;宏筑财务已确认</span>
      </template>
    </el-table-column>
    <el-table-column v-if="type"  prop="profitMargin" label="出具机构确认" fixed="right" header-align="center" align="center" width="145">
      <template slot-scope="scope">
        <el-button v-if="type=='CJ'&&(scope.row.issueName!=='合计'&&scope.row.issueName.indexOf('累计')==-1)&&!scope.row.issueConfirm" type="primary" @click="confirm(scope.row,2)" size="small" icon="el-icon-circle-check">本月账单确认</el-button>
        <span v-if="scope.row.issueConfirm&&(scope.row.issueName!=='合计'&&scope.row.issueName.indexOf('累计')==-1)"><i style="color:#67C23A;" class="el-icon-success"></i>&nbsp;出具机构已确认</span>
      </template>
    </el-table-column>

    <el-table-column  v-if="type == 'CW'" :label="$t('handle')" fixed="right" header-align="center" align="center" width="145">
      <template slot-scope="scope">
        <el-button v-if="type=='CW'&&(scope.row.issueName!=='合计'&&scope.row.issueName.indexOf('累计')==-1)&&!scope.row.financeConfirm" type="primary" @click="confirm(scope.row,1)" size="mini" icon="el-icon-circle-check">本月账单确认</el-button>
        <span v-if="scope.row.financeConfirm&&(scope.row.issueName!=='合计'&&scope.row.issueName.indexOf('累计')==-1)"><i style="color:#67C23A;" class="el-icon-success"></i>&nbsp;已确认</span>
      </template>
    </el-table-column>
  </el-table>
</template>
<script>
import moment from 'moment'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins: [ mixinViewModule ],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgSettlement/revenueDetails',
        getDataListIsPage: false,
        activatedIsNeed: false,
        exportURL: '/letter/bgguaranteeletter/accountDataExport',
        deleteIsBatch: true
      }
    }
  },
  props: {
    type: String
  },
  mounted () {
    if (this.type) {
      this.dataForm.startDate = moment().subtract(1, 'month').format('YYYY-MM')
      this.dataForm.endDate = moment().subtract(1, 'month').format('YYYY-MM')
    }
    this.getDataList()
  },
  computed: {
    fomate () {
      var str = ''
      if (this.guaranteeType === 'tbdbbh') {
        str = '投标有效期'
      }
      if (this.guaranteeType === 'lydbbh') {
        str = '担保期间'
      }
      if (this.guaranteeType === 'yfkdbbh') {
        str = '有效期'
      }
      return str
    }
  },
  methods: {
    TableCellStyle ({ rowIndex }) {
      if (rowIndex === this.dataList.length - 1 || rowIndex === this.dataList.length - 2) {
        return 'background-color:#f1824126'
      }
    },
    go (issueCode) {
      let { startDate, endDate } = this.dataForm
      this.$router.push({
        path: '/reconciliation',
        query: {
          issueCode,
          startDate,
          endDate
        }
      })
    },
    confirm ({ issueCode, issueName }, type) {
      this.$confirm(`请确认 ${issueName} ${moment().subtract(1, 'month').format('YYYY-MM')} 账单是否无误？`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = {
          settlementMonth: moment().subtract(1, 'month').format('YYYY-MM'),
          isConfirm: '1'
        }
        if (type === 1) {
          params.issueName = issueName
          params.issueCode = issueCode
        }
        this.$http
          .post('letter/bgSettlementConfirmation', params)
          .then(({ data: res }) => {
            if (res.code !== 500) {
              this.$message.success('操作成功！')
              this.getDataList()
            }
          })
          .catch(() => {})
      })
    }
  }
}
</script>
