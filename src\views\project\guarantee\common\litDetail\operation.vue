<!--
 * @Descripttion:
 * @Author: kong<PERSON>qiang
 * @Date: 2021-02-01 09:22:09
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-12-21 16:25:51
-->
<template>
  <div>
    <div class="operation">
      <el-button type="danger" size="small"  plain @click="defineGuarantee(dataForm.letterId)">电子保函预览</el-button>
      <el-button type="danger" size="small"  plain @click="defineGuaranteeHT(dataForm.letterId)">{{dataForm.contractId?'委托合同预览':'生成委托合同'}}</el-button>
      <el-button v-if="Number(dataForm.auditSort)>1" @click="showSP">审批表预览</el-button>
      <!-- <el-button >附件预览</el-button> -->
      <el-button type="primary" v-if="Number(dataForm.letterStatus) === 50"  @click="downloadLetter">下载保函</el-button>
      <el-button type="primary" v-if="Number(dataForm.auditSort)>=$store.state.user.auditProcedures" @click="showDia('litExamine')">
        {{Number(dataForm.auditSort)==$store.state.user.auditProcedures?'审核':'审核记录'}}</el-button>
      <el-button type="primary" v-if="Number(dataForm.auditSort)==4&&$store.state.user.auditProcedures==4" @click="showDia('uploadGee')">上传保函</el-button>
      <el-button type="primary" v-if="Number(dataForm.auditSort)==5" @click="showDia('uploadGee')">重新上传保函</el-button>
      <el-button type="primary" plain @click="showDia('updateDia')" v-if="dataForm.auditSort =='1'&&$store.state.user.auditProcedures===1">修改</el-button>
      <el-button type="primary" plain @click="showDia('updateDia')" v-if="dataForm.auditSort =='3'&&$store.state.user.auditProcedures===3">修改</el-button>

    </div>
    <updateDia v-if="visible" ref="updateDia" @refresh='refresh'></updateDia>
    <litExamine v-if="visible" ref="litExamine" @refresh='refresh'></litExamine>
    <preview v-if="visible" ref="preview" @refresh='refresh' :letterId='dataForm.letterId'></preview>
    <uploadGee  v-if="visible" ref="uploadGee" @refresh='refresh'></uploadGee>
  </div>
</template>
<script>
import updateDia from './updateDia'
import uploadGee from './uploadGee'
import litExamine from './litExamine'
import preview from '@/views/project/guarantee/common/detail/preview'
import { newWin } from '@/utils'
export default {
  components: { updateDia, litExamine, preview, uploadGee },
  data () {
    return {
      visible: false,
      drawer: false
    }
  },
  props: {
    dataForm: {
      type: Object
    }
  },
  mounted () {
    this.$route.meta.title = '案件审核详情'
  },
  methods: {
    refresh () {
      this.$emit('refresh')
    },
    defineGuarantee (id) {
      this.$http
        .get(
          '/letter/bgletterlitigation/generationGuarantee?letterId=' +
            id +
            '&tempId=1355517205537329153'
        )
        .then(({ data: res }) => {
          this.showDia('preview', res.data.id, id)
        })
    },
    defineGuaranteeHT (id) {
      this.$http
        .get(
          '/letter/guarantee/generateModel?letterId=' +
            id +
            '&fileType=322'
        )
        .then(({ data: res }) => {
          this.showDia('preview', res.data.fileId.id, id)
        })
    },
    drawerOpen (type) {
      this.drawer = !this.drawer
      this.$emit('drawer', this.drawer, type)
    },
    downloadLetter (id, type, key) {
      this.$http
        .get(
          `/letter/bgguaranteeletter/obtainDownloadLetter/` +
            this.dataForm.letterId +
            '?type=01' +
            '&key=' +
            this.dataForm.issueCode
        )
        .then(({ data: res }) => {
          this.updateLoading = false
          if (res.data) {
            newWin(res.data)
            // setTimeout(window.open(res.data, '_blank'), 500)
          }
        })
        .catch((rej) => {})
    },
    showSP () {
      this.$http
        .get(
          `/letter/bgletterlitigation/approvalForm/?letterId=` +
            this.$route.query.seeLetterId
        )
        .then(({ data: res }) => {
          this.loading = false

          console.log(res.data)
          this.showDia('preview', res.data)
        })
    },
    showDia (name, id) {
      this.visible = true
      this.$nextTick(() => {
        if (name === 'preview') {
          this.$refs[name].id = id
        } else {
          this.$refs[name].dataForm = this.dataForm
        }
        this.$refs[name].init()
      })
    }
  }
}
</script>
<style lang="scss">
.operation {
  margin-bottom: 15px;
  text-align: right;
  padding-right: 30px;
}
</style>
