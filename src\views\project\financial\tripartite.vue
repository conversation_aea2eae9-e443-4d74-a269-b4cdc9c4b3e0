<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2021-03-10 16:43:21
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-02-16 16:50:21
-->
<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="block">
        <el-date-picker
          v-model="time"
          type="month"
          size="small"
          @change="change"
          value-format="yyyy-MM"
          placeholder="选择月">
        </el-date-picker>
        &nbsp;
        <el-button @click="search()" size="small" type="primary">{{ $t('query') }}</el-button>
        <el-button type="success" size="small" @click="exportHandle()">{{ $t('export') }}</el-button>

        <el-divider></el-divider>
    </div>
    <keep-alive>
      <income type="CJ" ref="income"></income>
    </keep-alive>
  </el-card>
</template>
<script>
// import mixinViewModule from '@/mixins/view-module'
import income from './statements/income.vue'
import Cookies from 'js-cookie'
import moment from 'moment'
import qs from 'qs'
export default {
  // mixins: [mixinViewModule],
  components: {
    income
  },
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgguaranteeletter/pageAccountLetter',
        getDataListIsPage: true,
        exportURL: '/letter/bgSettlement/issueExportMonth',
        deleteIsBatch: true
      },
      currentRole: 'guarantee',
      dataList: {},
      typeList: [],
      time: moment().subtract(1, 'month').format('YYYY-MM'),
      dataForm: {
        startDate: moment().subtract(1, 'month').format('YYYY-MM'),
        endDate: moment().subtract(1, 'month').format('YYYY-MM')
      },
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick (picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick (picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  props: {
    iscompoent: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    search () {
      this.$nextTick(() => {
        this.$refs.income.dataForm.startDate = this.time
        this.$refs.income.dataForm.endDate = this.time
        this.$refs.income.getDataList()
      })
    },
    exportHandle () {
      var params = qs.stringify({
        'token': Cookies.get('token'),
        ...this.dataForm
      })
      window.location.href = `${window.SITE_CONFIG['apiURL']}${this.mixinViewModuleOptions.exportURL}?${params}`
    },
    change (val) {
      if (val) {
        this.$set(this.dataForm, 'startDate', val)
        this.$set(this.dataForm, 'endDate', val)
      } else {
        this.$set(this.dataForm, 'startDate', '')
        this.$set(this.dataForm, 'endDate', '')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.cati{
position: relative;
top:6px;
}
</style>
