<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-07-29 10:19:28
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-09-28 16:35:12
-->
<template>
  <el-dialog title="选择收件信息" :close-on-click-modal="false" append-to-body :close-on-press-escape="false" :visible.sync="visible" width="70%">
    <span>
      <div class="mod-letter__bgguaranteemailing}">
        <el-form :inline="true" :model="dataForm">
          <el-form-item>
            <el-input v-model="dataForm.mailingUser" clearable placeholder="收件人"></el-input>
          </el-form-item>
          <el-form-item>
            <el-input v-model="dataForm.mailingTel" clearable style="width:400px;" placeholder="收件电话"></el-input>
          </el-form-item>

          <el-form-item>
            <el-button type="danger" @click="getDataList(1)">{{ $t('query') }}</el-button>
          </el-form-item>

          <!-- <el-form-item>
            <el-button v-if="$hasPermission('letter:bgguaranteemailing:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
          </el-form-item> -->
          <el-form-item>
            <el-button type="danger" @click="resetHandle()">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle" style="width: 100%;">
          <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
          <!--<el-table-column prop="code" label="编码" sortable="custom" header-align="center" align="center"></el-table-column>-->
          <el-table-column prop="mailingUser" label="收件人" sortable="custom" header-align="center" align="center"></el-table-column>
          <el-table-column prop="mailingTel" label="收件电话" sortable="custom" header-align="center" align="center"></el-table-column>
          <el-table-column prop="mailingAddress" label="收件地址" sortable="custom" header-align="center" align="center"></el-table-column>

          <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="choice(scope.row)">选择</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle"
          @current-change="pageCurrentChangeHandle">
        </el-pagination>
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
        <!-- 浏览 -->
        <display v-if="displayVisible" ref="display" @refreshDataList="refreshList"></display>
      </div>
    </span>

  </el-dialog>
</template>
<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from '@/views/modules/letter/bgguaranteemailing-add-or-update'
import Display from '@/views/modules/letter/bgguaranteemailing-display'
export default {
  mixins: [mixinViewModule],
  components: {
    AddOrUpdate,
    Display
  },
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgguaranteemailing/page',
        getDataListIsPage: true,
        exportURL: '/letter/bgguaranteemailing/export',
        deleteURL: '/letter/bgguaranteemailing',
        enableURL: '/letter/bgguaranteemailing/enable',
        stopURL: '/letter/bgguaranteemailing/stop',
        deleteIsBatch: true
      },
      statusoptions: [
        {
          value: 1,
          label: '正常'
        },
        {
          value: 0,
          label: '停用'
        }
      ],
      dataForm: {
        code: '',
        mailingUser: '',
        mailingTel: '',
        status: ''
      },
      orderField: 'code',
      order: 'asc',
      uploadVisible: false,
      visible: false
    }
  },
  methods: {
    init () {
      this.visible = true
      this.getDataList()
    },
    // 状态启用具体代码需微调格式去除空格
    choice (row) {
      this.$message.success('选择成功！')
      this.visible = false
      this.$emit('getMailingData', {
        id: row.id,
        mailingUser: row.mailingUser,
        mailingTel: row.mailingTel,
        mailingAddress: row.mailingAddress
      })
    }
  }
}
</script>
