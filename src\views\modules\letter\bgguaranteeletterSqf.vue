<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2020-07-22 08:53:59
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-05-19 17:33:00
-->
<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="listTab">
      <el-tabs v-model="activeName" size='small' type="card" @tab-click="handleClick">
        <el-tab-pane label="全部" name="all"></el-tab-pane>
        <el-tab-pane label="未提交" name="10"></el-tab-pane>
        <el-tab-pane label="待审核" name="20"></el-tab-pane>
        <el-tab-pane label="待支付" name="40"></el-tab-pane>
        <el-tab-pane label="已出具" name="50"></el-tab-pane>
        <el-tab-pane label="已退款" name="90"></el-tab-pane>
      </el-tabs>
    </div>

    <div class="mod-letter__bgguaranteeletter}">
      <el-form :inline="true" :model="dataForm">
        <el-form-item>
          <el-input v-model="dataForm.applyName" size="small" clearable placeholder="投保人名称"></el-input>
        </el-form-item>
        <el-form-item style="display:none;">
          <el-date-picker v-model="bidOpenDate" size="small" @change="bidOpenChange" unlink-panels type="daterange" value-format="yyyy-MM-dd" :range-separator="$t('datePicker.range')"
            :start-placeholder="'开标日期查询范围'" :end-placeholder="'开标日期查询范围'">
          </el-date-picker>
        </el-form-item>
        <template v-if="searchShow">
          <el-form-item>
            <el-date-picker v-model="daterange" size="small" @change="change" unlink-panels type="daterange" value-format="yyyy-MM-dd" :range-separator="$t('datePicker.range')"
              :start-placeholder="'开始日期'" :end-placeholder="'结束日期'">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-input v-model="dataForm.bbrName" size="small" clearable placeholder="被保人名称"></el-input>
          </el-form-item>
          <el-form-item>
            <el-input v-model="dataForm.issueName" size="small" clearable placeholder="出具方名称"></el-input>
          </el-form-item>
          <!-- <el-form-item>
          <el-input v-model="dataForm.platformName" clearable placeholder="使用方名称"></el-input>
        </el-form-item> -->
          <el-form-item>
            <el-input v-model="dataForm.policyNo" size="small" clearable placeholder="保单号"></el-input>
          </el-form-item>
          <el-form-item>
            <el-select v-model="dataForm.guaranteeType" size="small" clearable placeholder="请选择保函类型">
              <el-option v-for="item in guaranteeTypeoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select @change="getDataList(1)" size="small" v-if="this.$route.name === 'letter-bgguaranteeletterSqf'" v-model="dataForm.letterStatus" clearable placeholder="请选择保函状态">
              <el-option v-for="item in letterStatusoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item>
        </template>

        <el-form-item>
          <el-button @click="getDataList(1)" size="small" type="primary">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="danger" size="small" @click="resetHandle(resetData())">重置</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" size="small" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
         <el-form-item>
          <el-button type="success" size="small" v-if="this.$hasPermission('letter:bgguaranteeletter:exportContacts')" @click="exportHandleUser()">导出联系人</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="text" size="small" @click="searchShow = !searchShow">{{searchShow?'收起':'更多筛选'}}<i v-if="!searchShow" class="el-icon-arrow-down"></i><i v-else class="el-icon-arrow-up"></i>
          </el-button>
        </el-form-item>
      </el-form>
      <!-- show-summary :summary-method="getSummaries" -->
      <el-table size='small' :header-row-style="{padding:'5px',height:'10px'}" :header-cell-style="{padding:'8px'}" v-loading="dataListLoading" show-summary :summary-method="getSummaries"
        :data="dataList" @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle" style="width: 100%;">
        <el-table-column type="selection" fixed='left' header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="applyName" label="申请方名称" sortable="custom" header-align="center" align="left" min-width="240">
          <template slot-scope="scope">
            <div class="title1">{{scope.row.applyName}}</div>
            <div class="title2"><span class="t_span">出具方：</span>{{scope.row.issueName}}</div>
            <div class="title2"><span class="t_span">保函类型：</span>
              <el-tag size="mini"><span>{{fomatMethod(scope.row.guaranteeType,1)}}</span>
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="项目名称" header-align="center" align="center" width="180">
          <el-table-column label="被保人名称" header-align="center" align="center" width="250">
            <template slot-scope="scope">
              <div>{{scope.row.projectName?scope.row.projectName:'-'}}</div>
              <div class="line"></div>
              <div>{{scope.row.bbrName?scope.row.bbrName:'-'}}</div>
            </template>
          </el-table-column>
        </el-table-column>
        <!-- <el-table-column prop="regoinName" label="所属区域" sortable="custom" header-align="center" align="center" width="180"></el-table-column> -->
        <!-- <el-table-column prop="issueName" label="出具方" sortable="custom" header-align="center" align="center" width="180"></el-table-column> -->
        <!-- <el-table-column prop="platformName" label="使用方名称" sortable="custom" header-align="center" align="center" width="180"></el-table-column> -->
        <el-table-column prop="letterStatus" label="当前状态" sortable="custom" header-align="center" align="center" width="125">
          <template slot-scope="scope">
            <el-tag size="mini" :type="statusColor(scope.row.letterStatus)"><span>{{fomatMethod(scope.row.letterStatus,2)}}</span>
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="letterStatus" label="支付状态" sortable="custom" header-align="center" align="center" width="125">
          <template slot-scope="scope">
            <!-- <el-tag size="mini" v-if="scope.row.payStatus === '10'" type="info"><span>未支付</span>
            </el-tag>
            <el-tag size="mini" v-if="scope.row.payStatus === '20'" type="warning"><span>支付审核中</span>
            </el-tag>
            <el-tag size="mini" v-if="scope.row.payStatus === '30'" type="success"><span>已支付</span>
            </el-tag>
            <el-tag size="mini" v-if="scope.row.payStatus === '40'" type="danger"><span>支付失败</span>
            </el-tag>
            <el-tag size="mini" v-if="scope.row.payStatus === '50'" type="danger"><span>已退款</span>
            </el-tag> -->
            <el-tag size="mini" :type="typePay(scope.row.payStatus)"><span>{{getDict('支付状态',scope.row.payStatus)}}</span>
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="policyNo" label="保单号" sortable="custom" header-align="center" align="center" width="185"></el-table-column>
        <!-- <el-table-column prop="guaranteeType" label="保函类型" sortable="custom" header-align="center" align="center" width="170">
          <template slot-scope="scope">
            <el-tag size="mini" type="danger"><span>{{fomatMethod(scope.row.guaranteeType,1)}}</span>
            </el-tag>
          </template>
        </el-table-column> -->
        <el-table-column prop="guaranteeAmount" label="担保金额（元）" header-align="right" align="right" width="120">
          <template slot-scope="scope">
            <div>{{scope.row.guaranteeAmount}}</div>
          </template>
        </el-table-column>
        <el-table-column prop="guaranteePrice" label="保函费用（元）" header-align="right" align="right" width="120">
          <template slot-scope="scope">
            <div>{{scope.row.guaranteePrice}}</div>
          </template>
        </el-table-column>

        <el-table-column prop="createDate" label="创建时间" header-align="center" align="center" width="180">
          <el-table-column prop="openDate" label="开具时间" header-align="center" align="center" width="180">
            <template slot-scope="scope">
              <div>{{scope.row.createDate?scope.row.createDate:'-'}}</div>
              <div class="line"></div>
              <div>{{scope.row.openDate?scope.row.openDate:'-'}}</div>
            </template>
          </el-table-column>
        </el-table-column>
        <!-- <el-table-column prop="updateDate" label="更新时间" sortable="custom" header-align="center" align="center" width="180"></el-table-column> -->
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="160">
          <template slot-scope="scope">
            <div>
              <el-button v-if="scope.row.letterStatus >= 10" type="text" size="small" @click="seeInfo(scope.row)">查看</el-button>
              <el-button v-if="(Number(scope.row.letterStatus) === 10 ||Number(scope.row.letterStatus) === 21 ||Number(scope.row.letterStatus) === 41)" type="text" size="small" class="Warning"
                @click="modifyJump(scope.row.id,scope.row.guaranteeType,scope.row.issueCode,scope.row.issueName,scope.row.guaranteeTemplateType)">修改</el-button>
                <!-- 投标担保保函 -->
               <el-button v-if="(Number(scope.row.letterStatus) === 20&& Number(scope.row.payStatus) !== 20) &&
            (Number(scope.row.payStatus) === 40 ||
              Number(scope.row.payStatus) === 10)&& scope.row.guaranteeType === 'tbdbbh'" type="text"
                size="small" @click="pay(scope.row.id, scope.row.issueCode)">支付</el-button>
                <!-- 其他类型保函 -->
                <template v-if="(Number(scope.row.letterStatus) === 40 || Number(scope.row.letterStatus) === 49)&&
            (Number(scope.row.payStatus) === 40 ||
              Number(scope.row.payStatus) === 10)">
                  <!-- 投标银行保函，保险 -->
                  <el-button v-if=" (scope.row.guaranteeType == 'tbbxbh' || scope.row.guaranteeType == 'tbbhyh')" type="text"
                size="small" @click="pay(scope.row.id, scope.row.issueCode)">支付</el-button>
                <!-- 履约，支付，预付款 -->
                <!-- &&(scope.row.contractConfirmation == 1||issueCode) -->
                <el-button v-if="isLv(scope.row.guaranteeType)&&scope.row.contractConfirmation == 1&&(scope.row.issueCode == 'YFDDB'||scope.row.issueCode == 'HZDB')" type="text"
                size="small" @click="pay(scope.row.id, scope.row.issueCode)">支付</el-button>
                <el-button v-if="isLv(scope.row.guaranteeType)&&(scope.row.issueCode != 'YFDDB')" type="text"
                size="small" @click="pay(scope.row.id, scope.row.issueCode)">支付</el-button>
                <!-- {{scope.row.guaranteeType==lybhyh}} -->
                <el-button v-if="isLv(scope.row.guaranteeType)&&(scope.row.issueCode != 'YFDDB'&&scope.row.issueCode != 'HZDB')&&scope.row.guaranteeType=='lybhyh'" type="text"
                size="small" @click="lvShow(scope.row.bgIssueConfigureDTO)">履约资料补充</el-button>
                </template>
              <span v-for="(item,index) in operations(scope)" @click="operationsCilck(item.actionName,item.actionParams)" :key="index">
                <span v-if="item.insurance.length===0||item.insurance.includes(scope.row.issueCode)">
                  <el-button v-if="item.isShow" class="opera" type="text" size="small" >{{item.operationsName}}</el-button>
                </span>
              </span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->

      <HTmodel v-if="addOrUpdateVisible" ref="HTmodel"></HTmodel>
      <!-- <lvDia v-if="addOrUpdateVisible" ref="lvDia"/> -->
      <!-- performingbankfile -->
      <el-dialog
        title="提示"
        :visible.sync="performingVis"
        width="1200px"
        class="adia"
        >
        <performingbankfile ref="performingbankfile" iscom/>

        <span slot="footer" class="dialog-footer">
          <el-button @click="performingVis = false">取 消</el-button>
          <el-button type="primary" @click="performingVis = false">确 定</el-button>
        </span>
      </el-dialog>
      <InvoiceAndMail v-if="addOrUpdateVisible" ref="InvoiceAndMail" />
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
      <bgguaranteeletterSqf-surrender v-if="surrenderVisible" ref="bgguaranteeletterSqfSurrender" @refreshDataList="getDataList"></bgguaranteeletterSqf-surrender>
      <bgguaranteeletterSqf-status v-if="statusVisible" ref="bgguaranteeletterSqfStatus" @refreshDataList="getDataList"></bgguaranteeletterSqf-status>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './bgguaranteeletter-add-or-update'
import bgguaranteeletterSqfSurrender from './bgguaranteeletterSqf-surrender'
import bgguaranteeletterSqfStatus from './bgguaranteeletterSqf-status'
import moment from 'moment'
import InvoiceAndMail from '@/views/project/guarantee/common/InvoiceAndMail'
import { newWin } from '@/utils'
import { getDict } from '@/utils/index'
import Cookies from 'js-cookie'
import qs from 'qs'
import HTmodel from '@/views/project/guarantee/components/HTmodel'
import performingbankfile from '../../project/performingbankfile/index'

export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgguaranteeletter/page',
        getDataListIsPage: true,
        exportURL: '/letter/bgguaranteeletter/export',
        deleteURL: '/letter/bgguaranteeletter',
        enableURL: '/letter/bgguaranteeletter/enable',
        stopURL: '/letter/bgguaranteeletter/stop',
        deleteIsBatch: true,
        activatedIsNeed: false
      },
      searchShow: false,
      activeName: 'all',
      pdfType: '',
      statusoptions: [
        {
          value: 1,
          label: '正常'
        },
        {
          value: 0,
          label: '停用'
        }
      ],
      daterange: null,
      bidOpenDate: null,
      dataForm: {
        code: '',
        bbrName: '',
        applyName: '',
        issueName: '',
        platformName: '',
        insuranceName: '',
        letterStatus: '',
        guaranteeType: '',
        policyNo: '',
        startDate: '',
        endDate: '',
        bidOpenStartDate: '',
        bidOpenEndDate: '',
        type: '1' // 类型1：查询申请方保函信息列表、2：查询出具方保函信息列表、3：查询使用方保函信息列表
      },
      getDicListURL: '/sys/dict/type/',
      guaranteeTypemaps: '',
      guaranteeTypeoptions: [],
      letterStatusmaps: '',
      letterStatusoptions: [],
      orderField: 'create_date',
      order: 'desc',
      uploadVisible: false,
      surrenderVisible: false,
      performingVis: false,
      statusVisible: false,
      moreActions: []
    }
  },
  watch: {
    dataForm: {
      handler (a) {
        if (!a.startDate && !a.endDate) {
          this.daterange = []
        }
        if (
          a.letterStatus === '10' ||
          a.letterStatus === '20' ||
          a.letterStatus === '40' ||
          a.letterStatus === '50' ||
          a.letterStatus === '90'
        ) {
          this.activeName = a.letterStatus
        } else {
          this.activeName = 'all'
        }
      },
      deep: true
    }
  },
  components: {
    AddOrUpdate,
    bgguaranteeletterSqfSurrender,
    bgguaranteeletterSqfStatus,
    InvoiceAndMail,
    HTmodel,
    performingbankfile
  },
  created () {
    this.dataForm.code = this.$route.query.secretParams || ''
  },

  activated () {
    this.getGuaranteeTypeInfo()
    // this.getDict()
    this.getLetterStatusInfo()
    this.getPdfInfo()
    this.init().then((a) => {
      if (a) {
        this.getDataList()
      }
    })
    if (this.$route.query.letterStatus) {
      this.activeName = this.$route.query.letterStatus
      this.handleClick()
    }
  },
  methods: {
    moment,
    init () {
      return new Promise((resolve, reject) => {
        if (this.$route.name !== 'letter-bgguaranteeletterSqf') {
          if (this.$route.name === 'bgguaranteeletterSqfReviewed') {
            this.dataForm.letterStatus = '20'
          }
          if (this.$route.name === 'bgguaranteeletterSqfDefray') {
            this.dataForm.letterStatus = '40'
          }
          if (this.$route.name === 'bgguaranteeletterSqfPayed') {
            this.dataForm.letterStatus = '50'
          }
          if (this.$route.name === 'bgguaranteeletterSqfRefunded') {
            this.dataForm.letterStatus = '90'
          }
          if (this.$route.name === 'bgguaranteeletterSqfNotSubmitted') {
            this.dataForm.letterStatus = '10'
          }
        }
        resolve(true)
      })
    },
    lvShow (pakeage) {
      this.performingVis = true
      this.$nextTick(() => {
        this.$refs.performingbankfile.pakeage = pakeage
      })
    },
    isLv (val) {
      const lv = ['lydbbh', 'lybhyh', 'zfdbbh', 'yfkdbbh']
      return lv.includes(val)
    },
    // 导出
    exportHandleUser () {
      var params = qs.stringify({
        'token': Cookies.get('token'),
        ...this.dataForm
      })
      window.location.href = `${window.SITE_CONFIG['apiURL']}/letter/bgguaranteeletter/exportContacts?${params}`
    },
    typePay (val) {
      if (Number(val) === 10) {
        return 'info'
      } else if (Number(val) === 20) {
        return 'warning'
      } else if (Number(val) === 30) {
        return 'success'
      } else {
        return 'danger'
      }
    },
    getDict (name, val) {
      let arr = getDict(name)
      if (arr.filter((a) => a.dictCode === val).length > 0) {
        return arr.filter((a) => a.dictCode === val)[0].dictName
      }
    },
    change (val) {
      console.log(val)
      if (val) {
        this.$set(this.dataForm, 'startDate', val[0] + ' 00:00:00')
        this.$set(this.dataForm, 'endDate', val[1] + ' 23:59:59')
      } else {
        this.$set(this.dataForm, 'startDate', '')
        this.$set(this.dataForm, 'endDate', '')
      }
    },
    bidOpenChange (val) {
      if (val) {
        this.$set(this.dataForm, 'bidOpenStartDate', val[0] + ' 00:00:00')
        this.$set(this.dataForm, 'bidOpenEndDate', val[1] + ' 23:59:59')
      } else {
        this.$set(this.dataForm, 'bidOpenStartDate', '')
        this.$set(this.dataForm, 'bidOpenEndDate', '')
      }
    },
    handleClick () {
      if (this.activeName === 'all') {
        this.dataForm.letterStatus = ''
      } else {
        this.dataForm.letterStatus = this.activeName
      }
      this.getDataList()
    },
    statusColor (code) {
      let dcode = Number(code)
      if (dcode < 30) {
        return 'info'
      } else if (dcode < 50) {
        return 'warning'
      } else if (dcode === 50) {
        return 'success'
      } else if (dcode < 100) {
        return 'danger'
      }
    },
    resetData () {
      if (this.$route.name !== 'letter-bgguaranteeletterSqf') {
        if (this.$route.name === 'bgguaranteeletterSqfReviewed') {
          // this.dataForm.letterStatus = '20'
          return {
            letterStatus: '20'
          }
        }
        if (this.$route.name === 'bgguaranteeletterSqfDefray') {
          return {
            letterStatus: '40'
          }
        }
        if (this.$route.name === 'bgguaranteeletterSqfPayed') {
          return {
            letterStatus: '50'
          }
        }
        if (this.$route.name === 'bgguaranteeletterSqfRefunded') {
          return {
            letterStatus: '90'
          }
        }
        if (this.$route.name === 'bgguaranteeletterSqfNotSubmitted') {
          return {
            letterStatus: '10'
          }
        }
      }
    },
    /**
     * @name:更多操作
     * @msg:
     * @param {*} route 当前路由
     * @param {*} pageRoutes 页面路由
     */
    operations (scope) {
      // 保险公司：tabx,ddbx,hybx,ygbx
      let arr = [
        {
          insurance: [],
          isShow: this.$hasPermission('letter:bgguaranteeletter:statusbg'),
          actionName: 'bhStatusChange',
          actionParams: [scope.row.id],
          operationsName: '保函状态变更'
        },
        {
          insurance: [],
          isShow: Number(scope.row.letterStatus) === 30,
          actionName: 'seeStatus',
          actionParams: [scope.row.id],
          operationsName: '查看审核状态'
        },
        {
          insurance: [],
          isShow: scope.row.contractConfirmation === '0' && this.isLv(scope.row.guaranteeType),
          actionName: 'showHt',
          actionParams: [scope.row],
          operationsName: '委托合同签章'
        },
        {
          insurance: ['tabx', 'ygbx', 'hybx'],
          isShow:
            Number(scope.row.letterStatus) === 50 &&
            (Number(scope.row.invoiceStatus) === 0 ||
              scope.row.invoiceStatus == null),
          actionName: 'invoiceQuery',
          actionParams: [
            scope.row.id,
            scope.row.issueCode,
            scope.row.guaranteeType
          ],
          operationsName: '申请发票'
        },
        {
          insurance: [],
          isShow:
            Number(scope.row.letterStatus) === 50 &&
            Number(scope.row.invoiceStatus) === 0 && scope.row.bgIssueConfigureDTO.isApplyInvoice,
          actionName: 'InvoiceAndMailShow',
          actionParams: [
            scope.row.id,
            scope.row.issueCode,
            scope.row.guaranteeType,
            scope.row.applyName
          ],
          operationsName: '申请发票'
        },
        {
          insurance: [],
          isShow:
            Number(scope.row.letterStatus) === 50 &&
            Number(scope.row.invoiceStatus) === 1 && scope.row.bgIssueConfigureDTO.isDownloadInvoice,
          actionName: 'downloadInvoice',
          actionParams: [scope.row.id],
          operationsName: '下载发票'
        },
        {
          insurance: ['ygbx', 'hybx'],
          isShow: Number(scope.row.letterStatus) === 50,
          actionName: 'downloadLetter',
          actionParams: [scope.row.id, '01', scope.row.issueCode],
          operationsName: '下载保函/保单'
        },
        {
          insurance: ['ygbx'],
          isShow: Number(scope.row.letterStatus) === 50,
          actionName: 'downloadLetter',
          actionParams: [scope.row.id, '00', scope.row.issueCode],
          operationsName: '下载证明文件'
        },
        {
          insurance: ['ygbx', 'hybx'],
          isShow: Number(scope.row.letterStatus) === 50,
          actionName: 'goCorrection',
          actionParams: [
            scope.row.id,
            scope.row.issueCode,
            scope.row.guaranteeType
          ],
          operationsName: '批改'
        },
        {
          insurance: [],
          isShow:
            Number(scope.row.letterStatus) === 50 &&
            Number(scope.row.correctStatus) !== 10 && scope.row.bgIssueConfigureDTO.isCorrection,
          actionName: 'goCorrection',
          actionParams: [
            scope.row.id,
            scope.row.issueCode,
            scope.row.guaranteeType
          ],
          operationsName: '批改'
        },
        {
          insurance: [],
          isShow:
            Number(scope.row.payStatus) === 30 &&
            Number(scope.row.surrenderStatus) !== 30 &&
            Number(scope.row.surrenderStatus) !== 10 &&
            this.isOpen(scope.row.validStartDate) && scope.row.bgIssueConfigureDTO.isSurrender,
          actionName: 'surrender',
          actionParams: [
            scope.row.id,
            scope.row.issueCode,
            scope.row.guaranteeType
          ],
          operationsName: '退保'
        },
        {
          insurance: [],
          isShow: Number(scope.row.letterStatus) === 50,
          actionName: 'downloadLetter',
          actionParams: [scope.row.id, '01', scope.row.issueCode],
          operationsName: '下载保函'
        },
        {
          insurance: ['tabx', 'ddbx'],
          isShow: Number(scope.row.letterStatus) === 50,
          actionName: 'downloadLetter',
          actionParams: [scope.row.id, '00', scope.row.issueCode],
          operationsName: '下载保单'
        },
        {
          insurance: [],
          isShow:
            this.$hasPermission('letter:bgguaranteeletter:delete') &&
            (Number(scope.row.letterStatus) === 10 ||
              Number(scope.row.letterStatus) === 41),
          actionName: 'deleteHandle',
          actionParams: [scope.row.id],
          operationsName: '删除'
        }
      ]

      return arr
    },
    operationsCilck (name, params) {
      this[name].apply({}, params)
      console.log(params)
    },
    isOpen (time) {
      let date = moment(time).subtract('days', 2).format('YYYY-MM-DD')
      // console.log(date)
      return !moment(date).isBefore(new Date())
    },
    // 下载发票
    getDownloadInvoiceInfo (id) {
      // 获取信息
      return new Promise((resolve, reject) => {
        this.$http
          .get(`/letter/bgguaranteeletter/ObtainInvoiceDownloadInfo/` + id)
          .then(({ data: res }) => {
            resolve(res.data)
          })
          .catch((rej) => {
            reject(rej.msg)
          })
      })
    },
    // 合计行
    getSummaries (param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        const values = data.map((item) => Number(item[column.property]))
        // console.log(values)
        if (
          !values.every((value) => isNaN(value)) &&
          (index === 6 || index === 7)
        ) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return Math.add(prev, curr)
            } else {
              return Math.add(prev)
            }
          }, 0)
          sums[index] += ' 元'
        } else {
          sums[index] = '-'
        }
      })
      return sums
    },
    showHt (row) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        let obj = { letterId: row.id }
        this.$refs.HTmodel.dataForm = { ...row, ...obj }
        this.$refs.HTmodel.init()
      })
    },

    async downloadInvoice (id) {
      this.$loading({
        lock: true,
        text: `下载请求中`
      })
      var data = await this.getDownloadInvoiceInfo(id)
      this.$loading().close()
      if (data) {
        newWin(data)
      } else {
        this.$message.error('发票生成中，请稍后再试！')
      }
    },
    InvoiceAndMailShow (id, key, guaranteeType, applyName) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        if (guaranteeType === 'tbbhyh' && key === 'zcdb') {
          this.$refs.InvoiceAndMail.isZC = true
          this.$refs.InvoiceAndMail.key = key
        } else {
          this.$refs.InvoiceAndMail.isZC = false
          this.$refs.InvoiceAndMail.key = key
        }
        this.$refs.InvoiceAndMail.invoiceTitle = applyName
        this.$refs.InvoiceAndMail.dataForm.letterId = id
        this.$refs.InvoiceAndMail.init()
      })
    },
    // 申请发票
    invoiceQuery (id, key, guaranteeType) {
      let url = '/letter/bgguaranteeletter/letterInvoiceQuery'
      this.$http
        .get(url + '/' + id + `?key=${key}`)
        .then(({ data: res }) => {
          this.updateLoading = false

          this.getDataList()
          this.$message.success(res.data)
        })
        .catch((rej) => {})
    },
    downloadLetter (id, type, key) {
      this.$http
        .get(
          `/letter/bgguaranteeletter/obtainDownloadLetter/` +
            id +
            '?type=' +
            type +
            '&key=' +
            key
        )
        .then(({ data: res }) => {
          this.updateLoading = false

          if (res.data) {
            newWin(res.data)
            // setTimeout(window.open(res.data, '_blank'), 500)
          } else {
            this.$confirm('正在生成文件，请稍后再试！', '提示', {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'warning'
            }).then(() => {})
          }
        })
        .catch((rej) => {})
    },
    async pay (letterId, key) {
      console.log(letterId, key)
      this.$confirm(
        '警告：支付时请根据招标文件要求慎重选择支付方式，如因支付方式选择错误导致保函失效，不予退款！',
        '提示',
        {
          confirmButtonText: '支付',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(async () => {
          this.$loading({
            lock: true,
            text: `请求支付中`
          })
          var data = await this.getPayMentInfo(letterId, key)
          console.log(data)
          if (data.code !== 0) {
            this.$message.error(data.msg)
            this.$loading().close()
            return
          }
          var payInfo = data.data
          this.$loading().close()
          newWin(encodeURI(payInfo))
        })
        .catch(() => {})
    },
    seeStatus (id) {
      this.$router.push({
        name: 'awaitingAudit',
        query: {
          id: id
        }
      })
    },
    goCorrection (id, key, type) {
      this.$router.push({
        name: 'Correction',
        query: {
          id: id,
          key: key,
          type: type
        }
      })
    },
    bhStatusChange (id) {
      this.statusVisible = true
      this.$nextTick(() => {
        this.$refs.bgguaranteeletterSqfStatus.id = id
        this.$refs.bgguaranteeletterSqfStatus.init()
      })
    },
    getPayMentInfo (letterId, key) {
      // 获取信息
      return new Promise((resolve, reject) => {
        this.$http
          .get(
            `/letter/bgguaranteeletter/getPayUrl/` + letterId + `?key=${key}`
          )
          .then(({ data: res }) => {
            this.updateLoading = false
            resolve(res)
          })
          .catch((rej) => {
            reject(rej.msg)
          })
      })
    },
    surrender (id, insuranceId) {
      this.surrenderVisible = true
      this.$nextTick(() => {
        this.$refs.bgguaranteeletterSqfSurrender.dataForm.letterId = id
        this.$refs.bgguaranteeletterSqfSurrender.init()
      })
    },
    fomatMethod (value, i) {
      if (i === 1) {
        return this.guaranteeTypemaps[value]
      }
      if (i === 2) {
        return this.letterStatusmaps[value]
      }
    },
    // 获取保函类型信息
    getGuaranteeTypeInfo () {
      this.$http
        .get(this.getDicListURL + 'guaranteeType')
        .then(({ data: res }) => {
          this.guaranteeTypeoptions = {
            ...this.guaranteeTypeoptions,
            ...res.data.list
          }
          this.guaranteeTypemaps = {
            ...this.guaranteeTypemaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    },
    // 获取当前状态信息
    getLetterStatusInfo () {
      this.$http
        .get(this.getDicListURL + 'letterStatus')
        .then(({ data: res }) => {
          this.letterStatusoptions = {
            ...this.letterStatusoptions,
            ...res.data.list
          }
          this.letterStatusmaps = {
            ...this.letterStatusmaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    },
    seeInfo (obj, type) {
      this.$router.push({
        name: 'bgguaranteeletterDetail',
        query: {
          seeLetterId: `${obj.id}`,
          type: type,
          JumpName: this.$route.name
        }
      })
    },
    getPdfInfo () {
      this.$http
        .get(`/sys/params/getValue/${'isSign'}`)
        .then(({ data: res }) => {
          this.pdfType = res.data
        })
        .catch(() => {})
    },
    modifyJump (id, type, insuranceCode, insuranceName, guaranteeTemplateType) {
      // eslint-disable-next-line no-unused-vars
      var name = ''
      if (type === 'tbdbbh') {
        name = 'warrantGuaranteeUpdate'
      } else if (type === 'tbbhyh') {
        name = 'bankGuaranteeUpdate'
      } else if (type === 'tbbxbh') {
        name = 'bgguaranteeletterUpdate'
      } else {
        name = 'performance'
      }
      let params = {
        id: id,
        type: type,
        insuranceCode,
        insuranceName,
        pdfType: this.pdfType,
        projectId: '0'
      }
      if (type === 'lybhyh' || type === 'lydbbh') {
        params.templateType = guaranteeTemplateType || '0'
      }
      this.$router.push({
        name,
        params
      })
    },
    // 状态停用
    stopHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(
        this.$t('prompt.info', { handle: '停用' }),
        this.$t('prompt.title'),
        {
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          // 具体代码需微调格式去除空格
          this.$http
            .put(
              this.mixinViewModuleOptions.stopURL,
              id ? [id] : this.dataListSelections.map((item) => item.id)
            )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getDataList()
                }
              })
            })
            .catch(() => {})
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.title1 {
  font-weight: bold;
}
.opera {
  display: inline-block;
  margin-left: 10px;
}
.line {
  display: block;
  height: 1px;
  width: 100%;
  margin: 8px 0;
  background-color: #dcdfe6;
}
.title2 {
  font-size: 13px;
  color: #999;
  .t_span {
    display: inline-block;
    width: 70px;
  }
}
.adia{
  /deep/.el-dialog__body{
    max-height:none;
  }
}
</style>
