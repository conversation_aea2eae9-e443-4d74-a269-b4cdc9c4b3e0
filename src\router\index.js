import Vue from 'vue'
import Router from 'vue-router'
import http from '@/utils/request'
import { isURL } from '@/utils/validate'
import Cookies from 'js-cookie'
import module from './moduleRoutesArr'
Vue.use(Router)

// 页面路由(独立页面)
export const pageRoutes = [
  {
    path: '/layout',
    name: 'layout',
    component: () => import('@/views/modules/page/layout'),
    redirect: { name: 'homePage' },
    children: [
      // { path: '/bgguaranteeletterSqf-surrender', component: () => import('@/views/modules/letter/bgguaranteeletterSqf-surrender'), name: 'payResult', meta: { title: '订单支付' } },
      // { path: '/applyBgGuaranteeApplyFrom', component: () => import('@/views/modules/letter/bgGuaranteeApplyFrom'), name: 'applyBgGuaranteeApplyFrom', meta: { title: '投标保函申请', isTab: true } },
      // { path: '/applyGuaranteePayment', component: () => import('@/views/modules/payment/applyGuaranteePayment'), name: 'applyGuaranteePayment', meta: { title: '电子保函服务费支付', isTab: true } },
      { path: '/login', component: () => import('@/views/pages/login'), name: 'login', meta: { title: '登录', hIndex: 6, hasFoot: false } },
      { path: '/register', component: () => import('@/views/pages/register'), name: 'register', meta: { title: '注册', hIndex: 6, hasFoot: false } },
      ...module.pageRoutesArr
    ]
  },
  {
    path: '/404',
    component: () => import('@/views/pages/404'),
    name: '404',
    meta: { title: '404未找到' },
    beforeEnter (to, from, next) {
      // 拦截处理特殊业务场景
      // 如果, 重定向路由包含__双下划线, 为临时添加路由
      if (/__.*/.test(to.redirectedFrom)) {
        return next(to.redirectedFrom.replace(/__.*/, ''))
      }
      next()
    }
  },
  {
    path: '/proposal',
    component: () => import('@/views/pages/proposal'),
    name: 'proposal',
    meta: { title: '建议' }
  },
  { path: '/applyGuarantee', component: () => import('@/views/pages/base'), name: 'applyGuarantee', meta: { title: '电子保函' } },
  { path: '/lock', component: () => import('@/views/pages/lock'), name: 'lock', meta: { title: '锁屏' } },
  // { path: '/lddp', component: () => import('@/views/modules/chart/index'), name: 'login', meta: { title: '数据分析' } },
  { path: '/news/:id', component: () => import('@/views/pages/news'), name: 'news', meta: { title: '新闻详情' } }

]

// 模块路由(基于主入口布局页面)
export const moduleRoutes = {
  path: '/',
  component: () => import('@/views/layout/main'),
  name: 'main',
  redirect: { name: 'home' },
  meta: { title: '主入口布局' },
  children: [
    { path: '/home', component: () => import('@/views/modules/homeNew'), name: 'home', meta: { title: '首页', isTab: true } },
    { path: '/homeNew', component: () => import('@/views/modules/homeNew'), name: 'homeNew', meta: { title: '仪表盘', isTab: true } },

    ...module.moduleRoutesArr
  ]
}

const router = new Router({
  mode: 'hash',
  scrollBehavior: () => ({ y: 0 }),
  routes: pageRoutes.concat(moduleRoutes)
})

router.beforeEach((to, from, next) => {
  // if (from.name === 'lock' && window.SITE_CONFIG['setLock']) {
  //   Vue.prototype.$message.error('屏幕处于锁定状态，请先解锁')
  //   return next({ name: 'lock' })
  // }
  console.log(Cookies.get('token'), "Cookies.get('token')")
  // if (to.name === 'login' && Cookies.get('token')) {
  //   console.log(Cookies.get('token'), "Cookies.get('token')")
  //   next({
  //     name: 'home'
  //   })
  // }
  // if (!BrowserType() && to.name !== 'proposal') {
  //   return next({ name: 'proposal' })
  // } else if (BrowserType() && to.name === 'proposal') {
  //   return next({ name: 'logim' })
  // }
  // 添加动态(菜单)路由
  // 已添加或者当前路由为页面路由, 可直接访问
  if (window.SITE_CONFIG['dynamicMenuRoutesHasAdded'] || fnCurrentRouteIsPageRoute(to, pageRoutes)) {
    // let whiteList = ['applyBxGuarantee', 'applyBgGuaranteeApplyFrom']
    // 定义个无用的路由防止重复校验
    let whiteList = ['applyGuarantee', 'pdfIe', 'signatureIe', 'otherLogin']
    if (!whiteList.includes(to.name)) {
      console.log(whiteList.includes(to.name), to.name)
      return next()
    } else {
      if (to.query.secretParams && to.query.type) {
        console.log(to.query.secretParams)
        var parm = encodeURIComponent(to.query.secretParams)
        Vue.prototype.$http.get(`${window.SITE_CONFIG['apiURL']}/interface?type=${to.query.type}&secretParams=${parm}`
        ).then(({ data: res }) => {
          if (res.code !== 0) {
            Vue.prototype.$message.error(res.msg)
            return next({ name: 'login' })
          } else {
            if (to.query.type != null) {
              Cookies.set('token', res.data.token)
              return next({
                name: 'home',
                query: {
                  secretParams: res.data.secretParams,
                  type: res.data.type
                }
              })
            } else {
              return next({
                name: res.data.name,
                query: {
                  secretParams: res.data.secretParams,
                  type: res.data.type
                }
              })
            }
          }
        }).catch(() => {
          return next()
        })
      } else {
        return next()
      }
    }
    return next()
  } else {
    console.log(to, 'to.query')
    // oa跳转
    if (to.query.appCode === 'HZOA' && to.query.loginId) {
      console.log(111111111111111, '进入aaa')
      Vue.prototype.$http.get(`${window.SITE_CONFIG['apiURL']}/oALogin?loginId=${to.query.loginId}`
      ).then(({ data: res }) => {
        if (res.code !== 0) {
          Vue.prototype.$message.error(res.msg)
          return next({ name: 'login' })
        } else {
          Cookies.set('token', res.data.token)
          return next({
            name: 'home'
          })
        }
      }).catch(() => {
        return next()
      })
    } else {
      // 获取菜单列表, 添加并全局变量保存
      http.get('/sys/menu/nav').then(({ data: res }) => {
        console.log(Cookies.get('token'), "Cookies.get('token')")
        if (res.code !== 0) {
        // Vue.prototype.$message.error(res.msg)
          return next({ name: 'login' })
        }
        window.SITE_CONFIG['menuList'] = res.data
        fnAddDynamicMenuRoutes(window.SITE_CONFIG['menuList'])
        if (!sessionStorage.getItem('dict')) {
          http
            .get(`sys/dict/type/getAllDict`)
            .then(({ data: res }) => {
              sessionStorage.setItem('dict', JSON.stringify(res.data))
            })
            .catch(() => {})
        }

        next({ ...to, replace: true })
      }).catch((rej) => {
        console.log(rej)
        return next()
      })
    }
  }
})
/**
 * 判断当前页面ie
 */
// eslint-disable-next-line no-unused-vars
function BrowserType () {
  var userAgent = navigator.userAgent // 取得浏览器的userAgent字符串
  // var isOpera = userAgent.indexOf('Opera') > -1 // 判断是否Opera浏览器
  // var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera; //判断是否IE浏览器
  var isIE = window.ActiveXObject || 'ActiveXObject' in window
  // var isEdge = userAgent.indexOf("Windows NT 6.1; Trident/7.0;") > -1 && !isIE; //判断是否IE的Edge浏览器
  // var isEdge = userAgent.indexOf('Edge') > -1 // 判断是否IE的Edge浏览器
  // var isFF = userAgent.indexOf('Firefox') > -1 // 判断是否Firefox浏览器
  // // eslint-disable-next-line eqeqeq
  // var isSafari = userAgent.indexOf('Safari') > -1 && userAgent.indexOf('Chrome') == -1 // 判断是否Safari浏览器
  // var isChrome = userAgent.indexOf('Chrome') > -1 && userAgent.indexOf('Safari') > -1 && !isEdge // 判断Chrome浏览器

  if (isIE) {
    var reIE = new RegExp('MSIE (\\d+\\.\\d+);')
    reIE.test(userAgent)
    var fIEVersion = parseFloat(RegExp['$1'])
    // eslint-disable-next-line eqeqeq
    if (userAgent.indexOf('MSIE 6.0') != -1) {
      return false
    // eslint-disable-next-line eqeqeq
    } else if (fIEVersion == 7) { return false } else if (fIEVersion == 8) { return false } else if (fIEVersion == 9) { return false } else if (fIEVersion == 10) { return true } else if (userAgent.toLowerCase().match(/rv:([\d.]+)\) like gecko/)) {
      return true
    } else { return false }// IE版本过低
  } else {
    return true
  }// isIE end
}
/**
 * 判断当前路由是否为页面路由
 * @param {*} route 当前路由
 * @param {*} pageRoutes 页面路由
 */
function fnCurrentRouteIsPageRoute (route, pageRoutes = []) {
  var temp = []
  for (var i = 0; i < pageRoutes.length; i++) {
    if (route.path === pageRoutes[i].path) {
      return true
    }
    if (pageRoutes[i].children && pageRoutes[i].children.length >= 1) {
      temp = temp.concat(pageRoutes[i].children)
    }
  }
  return temp.length >= 1 ? fnCurrentRouteIsPageRoute(route, temp) : false
}

/**
 * 添加动态(菜单)路由
 * @param {*} menuList 菜单列表
 * @param {*} routes 递归创建的动态(菜单)路由
 */
function fnAddDynamicMenuRoutes (menuList = [], routes = []) {
  var temp = []
  for (var i = 0; i < menuList.length; i++) {
    if (menuList[i].children && menuList[i].children.length >= 1) {
      temp = temp.concat(menuList[i].children)
      continue
    }
    // 组装路由
    var route = {
      path: '',
      component: null,
      name: '',
      meta: {
        ...window.SITE_CONFIG['contentTabDefault'],
        menuId: menuList[i].id,
        title: menuList[i].name
      }
    }
    // eslint-disable-next-line
    let URL = (menuList[i].url || '').replace(/{{([^}}]+)?}}/g, (s1, s2) => eval(s2)) // URL支持{{ window.xxx }}占位符变量
    if (isURL(URL)) {
      route['path'] = route['name'] = `i-${menuList[i].id}`
      route['meta']['iframeURL'] = URL
    } else {
      URL = URL.replace(/^\//, '').replace(/_/g, '-')
      route['path'] = route['name'] = URL.replace(/\//g, '-')
      if (route['path'].split('-')[0] === 'project') {
        route['component'] = () => import(`@/views/${URL}`)
      } else {
        route['component'] = () => import(`@/views/modules/${URL}`)
      }
    }
    routes.push(route)
  }
  if (temp.length >= 1) {
    return fnAddDynamicMenuRoutes(temp, routes)
  }
  // 添加路由
  router.addRoutes([
    {
      ...moduleRoutes,
      name: 'main-dynamic-menu',
      children: routes
    },
    { path: '*', redirect: { name: '404' } }
  ])
  window.SITE_CONFIG['dynamicMenuRoutes'] = routes
  window.SITE_CONFIG['dynamicMenuRoutesHasAdded'] = true
}

export default router
