/*
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-08-14 10:58:10
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-12-21 16:43:57
 */
import Cookies from 'js-cookie'
import store from '@/store'
import { Message } from 'element-ui'

/**
 * 权限
 * @param {*} key
 */
export function hasPermission (key) {
  return window.SITE_CONFIG['permissions'].indexOf(key) !== -1 || false
}
/**
 * 获取字典
 * @param {*} dictName
 */
export function getDict (dictName) {
  let dictList = JSON.parse(sessionStorage.getItem('dict'))
  return dictList[dictName] ? dictList[dictName] : []
}
/**
 * 跳转防浏览器
 * @param {*} key
 */
export function newWin (url) {
  var a = document.createElement('a')
  a.setAttribute('href', url)
  a.setAttribute('target', '_blank')
  a.click()
  document.body.removeChild(a)
}
/**
 * 清除登录信息
 */
export function clearLoginInfo () {
  // router.push('login')
  store.commit('resetStore')
  Cookies.remove('token')
  window.SITE_CONFIG['dynamicMenuRoutesHasAdded'] = false
  Message.close()
}

/**
 * 获取uuid
 */
export function getUUID () {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
    return (c === 'x' ? (Math.random() * 16 | 0) : ('r&0x3' | '0x8')).toString(16)
  })
}

/**
 * 获取svg图标(id)列表
 */
export function getIconList () {
  var res = []
  document.querySelectorAll('svg symbol').forEach(item => {
    res.push(item.id)
  })
  return res
}
export function banca () {
  if (!!window.ActiveXObject || 'ActiveXObject' in window) { window.open('http://hebcaonline.hebca.com:9001/Hebca/certbussconduct/entry.action?onlineType=4&projectType=zbzbt&cztype=1', '_blank') } else { this.$message.error('请使用IE浏览器进行CA办理业务操作!') }
}
/**
 * 树形数据转换
 * @param {*} data
 * @param {*} id
 * @param {*} pid
 */
export function treeDataTranslate (data, id = 'id', pid = 'pid') {
  var res = []
  var temp = {}
  for (var i = 0; i < data.length; i++) {
    temp[data[i][id]] = data[i]
  }
  for (var k = 0; k < data.length; k++) {
    if (!temp[data[k][pid]] || data[k][id] === data[k][pid]) {
      res.push(data[k])
      continue
    }
    if (!temp[data[k][pid]]['children']) {
      temp[data[k][pid]]['children'] = []
    }
    temp[data[k][pid]]['children'].push(data[k])
    data[k]['_level'] = (temp[data[k][pid]]._level || 0) + 1
  }
  return res
}
