<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2020-08-06 18:47:13
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-12-21 16:50:42
-->
<template>
  <el-dialog :visible.sync="visible" class="dia" title="发票信息维护" :close-on-click-modal="false" :close-on-press-escape="false" width="1100px">
    <div v-if="dataForm.bgguaranteeinvoice" v-loading='loading'>
      <bgguaranteeinvoice :isZC='isZC' :dataForm='dataForm.bgguaranteeinvoice' :options='options' ref="bgguaranteeinvoice" />
      <!-- <bgGuaranteeMailing :dataForm='dataForm.bgGuaranteeMailing' :options='options' ref="bgGuaranteeMailing"/> -->
    </div>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" :loading='btnLoading' @click="dataFormSubmitHandle()">提交</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import dictionaries from '@/views/project/guarantee/components/dictionaries'
import bgguaranteeinvoice from '@/views/project/guarantee/common/InvoiceAndMail/form' // 发票信息
// import bgGuaranteeMailing from '@/views/project/guarantee/tenderGuarantee/form/mailing'
export default {
  mixins: [dictionaries],
  data () {
    return {
      visible: false,
      loading: false,
      btnLoading: false,
      isZC: false,
      key: '',
      invoiceTitle: '',
      dataForm: {
        letterId: ''
      },
      formReset: [
        'bgguaranteeinvoice'
        // 'bgGuaranteeMailing'
      ]
    }
  },
  components: { bgguaranteeinvoice },
  computed: {
    dataRule () {
      return {
        reason: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  mounted () {
  },
  created () {},
  methods: {
    init () {
      this.visible = true
      this.setObj()
      this.$nextTick(() => {
        // this.$refs['dataForm'].resetFields()
        if (this.dataForm.letterId) {
          this.getInfo(this.dataForm.letterId)
          console.log(this.invoiceTitle)
        }
      })
    },
    setObj () {
      this.formReset.map((a) => {
        if (!this.dataForm[a]) this.$set(this.dataForm, a, {})
      })
    },
    Verification () {
      return new Promise((resolve, reject) => {
        var arr = []
        this.formReset.map((a) => {
          if (this.$refs[`${a}`] !== undefined) {
            this.$refs[`${a}`].push().then((res) => {
              arr.push(res)
            })
          }
        })
        setTimeout(() => {
          resolve(arr)
        }, 300)
      })
    },
    getInfo (id) {
      console.log(id)
      this.loading = true
      this.$http
        .get('letter/bgguaranteeletter/getInvoiceMail?letterId=' + id)
        .then(({ data: res }) => {
          this.loading = false

          console.log(res.data)
          this.dataForm = Object.assign({}, this.dataForm, {
            bgguaranteeinvoice: res.data.bgguaranteeinvoice
              ? res.data.bgguaranteeinvoice
              : {
                invoiceObject: '1',
                invoiceType: '1',
                taxpayerType: '1',
                invoiceTitle: this.invoiceTitle
              },
            letterId: id,
            key: this.key
          })
          console.log(this.dataForm)
        })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function () {
        this.Verification().then((res) => {
          if (!res.includes(false)) {
            this.submit()
          } else {
            this.$message.error('表单提交有误，请检查提交信息')
          }
        })
      },
      1000,
      { leading: true, trailing: false }
    ),

    submit () {
      this.btnLoading = true
      this.$http
        .post('letter/bgguaranteeletter/saveInvoiceMail', this.dataForm)
        .then(({ data: res }) => {
          this.btnLoading = false

          if (this.isZC) {
            this.$http
              .get(
                '/letter/bank/bankInvoiceQuery/' +
                  this.dataForm.letterId +
                  `?key=${this.key}`
              )
              .then(({ data: res }) => {
              })
              .catch((rej) => {})
          }
          this.$message.success('申请发票成功！')
          this.visible = false
        })
    }
  }
}
</script>
<style lang="scss" scoped>
 .dia /deep/ .el-dialog__body{
    max-height: none;
  }
</style>
