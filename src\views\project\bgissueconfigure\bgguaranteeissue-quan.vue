<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2022-01-18 09:08:45
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2022-01-18 16:49:54
-->
<template>
  <el-dialog class="dialog" :visible.sync="visible" title="权重" :close-on-click-modal="false" :close-on-press-escape="false" width="800">
    <el-form :model="dataForm" ref="dataForm" label-width="120px">
      <el-row :gutter="20">
        <el-col :xs="12" :lg="12">
          <el-form-item label="保函类型" prop="guaranteeTypeCode">
            <el-select v-model="dataForm.guaranteeTypeCode" placeholder="请选择保函类型" @change='change'>
              <el-option v-for="item in dict" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" v-if="dataForm.guaranteeTypeCode">
        <el-col :xs="12" :lg="12">
          <el-form-item label="权重值" prop="alias">
            <el-input v-model.number="qZTotal" readonly placeholder="请输入权重值"></el-input>
          </el-form-item>
        </el-col>
        <el-col :xs="6" :lg="6">
          <el-form-item label="已使用权重" prop="alias">
            <span :style="total>qZTotal?'color:red':''"> {{total}}</span>
          </el-form-item>
        </el-col>
        <el-col :xs="6" :lg="6">
          <el-form-item label="剩余权重" prop="alias">
            <span :style="qZTotal - total<0?'color:red':''"> {{qZTotal - total}}</span>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :lg="24" v-show="qZTotal - total<0">
          <el-form-item>
            <el-alert :closable="false" effect="dark" title="已使用权重超出权重值！" show-icon type="error">
            </el-alert>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :lg="24">
          <el-form-item label="权重配置" prop="alias">
            <div v-for="(item,index) in quanListTree" :key="index">
              <div> <el-switch v-model="item.status"  active-color="#13ce66" :active-value="1" :inactive-value="0"></el-switch>&emsp;{{item.issueName}}：
                <el-slider v-model="item.issuedProportion" :disabled='item.status == 0' :step="100" show-stops :max='qZTotal' show-input>
                </el-slider>
              </div>
            </div>
          </el-form-item>
        </el-col>

        <!-- {{quanListTree}} -->
      </el-row>
    </el-form>
    <template slot="footer">
      <el-button type="primary" :disabled='qZTotal - total<0' @click="submit()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { getDict } from '@/utils/index'

export default {
  data () {
    return {
      visible: false,
      dict: getDict('电子保函类型'),
      dataForm: {
        guaranteeTypeCode: '',
        issueCode: ''
      },
      quanListTree: [],
      qZTotal: ''
    }
  },
  computed: {
    total () {
      let total = 0
      this.quanListTree.map((a) => {
        if (total >= this.qZTotal) {
          // this.$message.error('总计不能超过权重值')
          return false
        } else {
          total += a.issuedProportion
        }
      })
      return total
    }
  },
  created () {},
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
      })
    },
    // fomateIss (val) {
    //   // getDict('出具机构')
    //   var aa = getDict('出具机构').filter((a) => a.dictCode === val)
    //   return aa.length > 0 ? aa[0].dictName : '-'
    // },
    change (val) {
      this.getQuanList()
    },
    submit () {
      this.$http
        .post(
          `/letter/bgissueconfigure/updateProportion`, {
            list: this.quanListTree
          }
        )
        .then(async ({ data: res }) => {
          this.visible = false
          this.$message.success('提交成功！')
        })
    },
    getQuanList () {
      this.$http
        .get(
          `/letter/bgissueconfigure/qZConfigure?issueCode=${this.dataForm.issueCode}&guaranteeTypeCode=${this.dataForm.guaranteeTypeCode}`
        )
        .then(async ({ data: res }) => {
          this.quanListTree = res.data.proportionList
          this.qZTotal = res.data.qZTotal
        })
    }
    // 获取信息
  }
}
</script>
<style lang="scss" scoped>
.dialog {
  /deep/.el-dialog__body {
    max-height: none;
  }
}
</style>
