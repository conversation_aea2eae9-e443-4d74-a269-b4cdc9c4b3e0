<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019-12-17 17:20:41
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-03-29 14:14:29
 -->
<template>
  <div>
    <el-dialog title="保函项目选择" :visible.sync="dialogVisible" width="85%" append-to-body :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :inline="true" :model="dataForm" >
        <el-form-item>
          <el-input v-model="dataForm.name" style="width: 500px" clearable placeholder="项目名称"></el-input>
        </el-form-item>
        <el-form-item >
          <el-input hidden v-model="dataForm.regionId" clearable placeholder="所属区域"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getDataList(1)">{{ $t('query') }}</el-button>
        </el-form-item>
        <!-- <el-form-item>
          <el-button type="danger" @click="resetHandle()">重置</el-button>
        </el-form-item> -->

      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @row-dblclick="selectHandle" @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle"
        style="width: 100%;">
            <el-table-column prop="name" label="项目标段名称"  header-align="center" align="center" ></el-table-column>
            <el-table-column prop="region" label="项目所属区域"  header-align="center" align="center" width="200"></el-table-column>
            <el-table-column prop="guaranteeAmount" label="担保金额"  header-align="center" align="center" width="100"></el-table-column>
            <!-- <el-table-column prop="tbDate" label="投标日期"  header-align="center" align="center" width="100"></el-table-column> -->
            <el-table-column prop="platformCode" label="交易平台"  header-align="center" align="center"  width="100">
              <template slot-scope="scope">
                <span v-if="scope.row.platformCode === 'zbt'">招标通</span>
                <span v-else>其他平台</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="choice(scope.row)">选择</el-button>
              </template>
            </el-table-column>
      </el-table>
      <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
// import Bus from '@/api/bus.js'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgbiddingproject/page',
        getDataListIsPage: true,
        exportURL: '/letter/bgbiddingproject/export',
        deleteURL: '/letter/bgbiddingproject',
        enableURL: '/letter/bgbiddingproject/enable',
        stopURL: '/letter/bgbiddingproject/stop',
        deleteIsBatch: true
      },
      dialogVisible: false,
      statusoptions: [
        {
          value: 1,
          label: '正常'
        },
        {
          value: 0,
          label: '停用'
        }
      ],
      dataForm: {
        code: '',
        name: '',
        regionId: '',
        status: 1
      },
      orderField: 'id',
      order: 'desc',
      uploadVisible: false
    }
  },
  methods: {
    init () {
      this.dialogVisible = true
      this.getDataList()
    },
    choice (row) {
      this.$message.success('选择成功！')
      this.dialogVisible = false
      this.$emit('getProjectData', {
        name: row.name,
        platformCode: row.platformCode,
        guaranteeAmount: row.guaranteeAmount,
        bbrId: row.bbrId,
        id: row.id
      })
    },
    db () {
      console.log(1111)
    },
    selectHandle (row) {
      // console.log(row)
      if (row) {
        this.$emit('pushData', row)
        this.dialogVisible = false
      } else {
        if (!row && this.dataListSelections.length !== 1) {
          return this.$message({
            message: '请选择且只选择一个操作项',
            type: 'warning',
            duration: 500
          })
        }
        this.$emit('pushData', this.dataListSelections[0])
        this.dialogVisible = false
      }
    }
  }
}
</script>
