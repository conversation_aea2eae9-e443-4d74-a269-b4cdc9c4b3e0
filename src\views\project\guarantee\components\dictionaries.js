/*
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-10-12 17:28:40
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-10-13 09:26:54
 */
import { getDict } from '@/utils/index'
export default {
  data () {
    /* eslint-disable */
    return {
        options:{
          certTermTypeoptions: [...getDict('证件有效期类型')],
          insuranceTypeoptions: [...getDict('电子保函类型')],
          invoiceTypeoptions: [...getDict('发票类型')],
          taxpayerTypeoptions: [...getDict('纳税人类型')],
          invoiceObjectoptions: [...getDict('开票对象')],
          certTypeoptions: [...getDict('证件类型（团体）')],
          platformOptions: [...getDict('使用方类型')],
          letterStatusoptions:[...getDict('保函状态')]
        },
        pickerOptions: {
          shortcuts: [
            {
              text: '今天',
              onClick (picker) {
                picker.$emit('pick', new Date())
              }
            },
            {
              text: '昨天',
              onClick (picker) {
                const date = new Date()
                date.setTime(date.getTime() - 3600 * 1000 * 24)
                picker.$emit('pick', date)
              }
            },
            {
              text: '一周前',
              onClick (picker) {
                const date = new Date()
                date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
                picker.$emit('pick', date)
              }
            }
          ]
        },
    }
  },
  created () {
   
  },
  methods: {


  },
}
