<!--
 * @Description:
 * @Autor: kongweiqiang
 * @Date: 2020-02-25 14:59:27
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-08-26 17:48:47
 -->
<template>
  <el-dialog :close-on-click-modal='false'  title="签章" class='opening cadia'  append-to-body :visible.sync="visible" width="70%" :fullscreen="fullscreen" >
    <div slot='title'>
      <h3>签章</h3>
      <!-- <el-button @click="fullscreen=!fullscreen" class="el-dialog__headerbtn diaFull" type="text"><i class="el-icon-full-screen"></i></el-button> -->
    </div>
    <div class='side'>
      <!-- {{fileList}} -->
      <div class="side-nav">
        <!-- 内容 -->
        <div class="nav-group">
          <div class="nav-group__title">文件列表</div>
          <ul class="pure-menu-list" style="height: auto;">
            <li class="nav-item" @click="acChange(index)" v-for="(item,index) in fileList" :key="index">
              <img src="@/assets/img/pdf.png" alt="">
              <img class="qian" v-if="item.isSign === 1" src="@/assets/img/qian.png" alt="">
              <a class="router-link-exact-active " :class="active===index?'active':''">{{item.type==1?'申请书':'担保合同'}}</a>
            </li>
          </ul>
        </div>
      </div>
      <el-divider direction="vertical"></el-divider>
      <div style="width:100%">
        <!-- <iframe style="width:100%;height:100%" :src="fileSrc" frameborder="0"></iframe> -->
        <object ref="webPDFObj" v-show="show" classid="CLSID:6EE0E832-A96C-480C-AE0A-EB35A9BB9651" width="100%" style="height:100%;float: left">
        </object>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
           <el-button size="medium" type="success" v-if='fileList[0].isSign===1&&fileList[1].isSign===1' @click="close()">完成签章</el-button>
      <el-button size="medium" type="primary" v-else @click="sing(fileList[active].type==1?'【授权企业盖章位置】':'【委托企业盖章位置】')">签章并保存文件</el-button>
    </span>

  </el-dialog>
</template>
<script>
export default {
  data () {
    return {
      webPDFObj: '',
      visible: false,
      fullscreen: true,
      dialogVisible: false,
      btnLoaidng: false,
      show: true,
      form: {},
      fileList: [],
      fileSrc:
        `${window.SITE_CONFIG['apiURL']}` +
        '/letter/guarantee/generate',
      savesrc: `${window.SITE_CONFIG['apiURL']}` + '/letter/guarantee/savePDF',
      active: 0
    }
  },
  created () {
    this.getData()
  },
  mounted () {
    this.visible = true
  },
  methods: {
    sing (name) {
      try {
        // 关键字签章
        // 参数1：关键字
        // 参数2：可选参数，时间戳使用方式，0表示在配置时间戳试使用时间戳，1表示不使用时间戳
        this.webPDFObj.SealKeyWord(name, 1)
        this.$message.success('签章成功')
        this.submitHandle()
        // this.TestGetSeal()
      } catch (e) {
        this.ShowExceptionError(e)
      }
      // this.TestGetSeal()
    },
    submitHandle () {
      console.log(this.savesrc + `?ossId=` + this.fileList[this.active].id)
      try {
        let result = this.webPDFObj.SaveNetFile(this.savesrc + `?ossId=` + this.fileList[this.active].id)
        if (result === null || result === '') {
          this.show = false
        }
      } catch (e) {
        this.ShowExceptionError(e)
        return false
      }
      this.getData()
      return true
    },
    initQian (fileList) {
      try {
        this.$nextTick(() => {
          this.webPDFObj = this.$refs['webPDFObj']
          console.log(this.fileSrc + `?ossId=` + fileList.id)
          this.webPDFObj.OpenFlags = 8
          this.webPDFObj.OpenNetFile(
            this.fileSrc + `?ossId=` + fileList.id // fileList.signatureFile.fileByte
          )
          this.webPDFObj.ShowToolbar(true)
        })
      } catch (e) {
        alert(e)
        // console.log(e)
      }
      // this.$http.get(this.fileSrc + fileList.signatureFile.id).then(({ data: res }) => {
      //   console.log(res)
      // })
    },
    ShowExceptionError (e) {
      if (e.name === 'TypeError') {
        alert('JS错误：' + e.message)
      } else {
        try {
          alert('WebPDF错误：' + this.webPDFObj.GetErrMsg())
        } catch (ee) {
          alert('JS内部错误：' + ee.message)
        }
      }
    },
    close () {
      window.open('', '_self')
      window.close()
    },

    closePdf () {
      try {
        // 关闭打开的PDF文件
        this.webPDFObj.CloseFile()
      } catch (e) {
        alert('错误：' + this.webPDFObj.GetErrMsg())
      }
    },
    acChange (idx) {
      try {
        // 关闭打开的PDF文件
        this.webPDFObj.CloseFile()
      } catch (e) {
        alert('错误：' + this.webPDFObj.GetErrMsg())
      }

      this.active = idx
      this.initQian(this.fileList[idx])
    },
    getData () {
      this.fileList = []
      this.$http.get(`letter/guarantee/getOssById`, { params: {
        ossId: this.$route.query.sqs
      } }).then(({ data: res }) => {
        this.fileList.push(res.data)
        this.initQian(this.fileList[0])
      }).catch((e) => {
      })
      this.$http.get(`letter/guarantee/getOssById`, { params: {
        ossId: this.$route.query.wthtId
      } }).then(({ data: res }) => {
        this.fileList.push(res.data)
      }).catch((e) => {
      })
      console.log(this.fileList)
    }
  }
}
</script>
<style  scoped>
/* .cadia .el-dialog__body{
  height: calc(100vh - 50px - 38px - 30px - 150px) !important
} */

.side {
  display: flex;
  height: 100%;
}
.side-nav {
  width: 150px;
}
.side-nav li {
  list-style: none;
  text-align: center ;
}
.side-nav .nav-group__title {
  font-size: 12px;
  color: #999;
  line-height: 26px;
}
.side-nav ul {
  padding: 0;
  margin: 0;
  overflow: hidden;
}
.side-nav .nav-item {
  position: relative;
}
.side-nav .nav-item .qian {
  position: absolute;
  top: 16px;
}
.side-nav .nav-item a {
  display: block;
  height: 40px;
  color: #444;
  line-height: 40px;
  font-size: 14px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-weight: 400;
}
.side-nav .nav-item img {
  width: 40px;
}
.side-nav .nav-item a.active,
.side-nav .nav-item a:hover {
  color: #409eff !important;
}
</style>
