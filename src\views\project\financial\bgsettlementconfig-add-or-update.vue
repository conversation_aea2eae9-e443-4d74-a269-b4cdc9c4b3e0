<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false"
    :close-on-press-escape="false" width="70%">
    <!-- {{this.$route.query}}
    {{this.dataForm.guaranteeType}} -->
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"
      :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="colConfig">
          <!-- <el-form-item label="结算编码" prop="code">
            <el-input v-model="dataForm.code" placeholder="结算编码"></el-input>
          </el-form-item> -->
          <el-form-item label="结算名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="结算名称"></el-input>
          </el-form-item>
          <el-form-item label="保额条件" prop="amountConditions">
            <!-- <el-input v-model="dataForm.amountRange" placeholder="保额区间"></el-input> -->
            <el-radio-group v-model="dataForm.amountConditions">
              <el-radio label="小于">小于</el-radio>
              <el-radio label="小于等于">小于等于</el-radio>
              <el-radio label="大于">大于</el-radio>
              <el-radio label="大于等于">大于等于</el-radio>
            </el-radio-group>
            <el-input v-model="dataForm.amountRange" placeholder="保额区间"></el-input>
          </el-form-item>
          <el-form-item label="保函类型" prop="guaranteeType">
            <!-- <el-checkbox-group v-model="dataForm.guaranteeType">
                <el-checkbox :label="item" v-for="(item,index) in guaranteeTypeList" :key="index">{{item}}</el-checkbox>
              </el-checkbox-group> -->
              <el-radio-group v-model="dataForm.guaranteeType">
                <el-radio :label="item" v-for="(item,index) in guaranteeTypeList" :key="index">{{item}}</el-radio>
              </el-radio-group>
          </el-form-item>
          <!-- amountConditions保额条件agentPercent代理百分比fixedCost固定成本 -->
          <!-- 1 定额 2按比例 -->
          <el-form-item label="结算类型" prop="settlementType">
            <el-radio-group v-model="dataForm.settlementType">
              <el-radio label="1">定额</el-radio>
              <el-radio label="2">按比例</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="金额" prop="price" v-if="dataForm.settlementType == '1'">
            <el-input v-model="dataForm.price" placeholder="金额"></el-input>
          </el-form-item>
          <el-form-item label="百分比" prop="percent" v-if="dataForm.settlementType == '2'">
            <el-input v-model="dataForm.percent" placeholder="百分比">
             <template slot="append">%</template></el-input>
          </el-form-item>

          <el-form-item label="代理百分比" prop="agentPercent">
            <el-input v-model="dataForm.agentPercent" placeholder="代理百分比">
             <template slot="append">%</template></el-input>
          </el-form-item>
          <el-form-item label="固定成本" prop="fixedCost">
            <el-input v-model="dataForm.fixedCost" placeholder="固定成本"></el-input>
          </el-form-item>
          <el-form-item label="区域奖金" prop="regionalBonus">
            <el-input v-model="dataForm.regionalBonus" placeholder="区域奖金">
             <template slot="append">%</template></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      guaranteeTypeList: this.$route.query.guaranteeType.split(','),
      dataForm: {
        id: '',
        code: '',
        name: '',
        issueId: this.$route.query.id,
        issueCode: this.$route.query.code,
        issueName: this.$route.query.name,
        guaranteeType: '',
        amountRange: '',
        amountConditions: '小于',
        settlementType: '1',
        percent: '',
        price: '',
        agentInvoicingPercent: '',
        agentPercent: '',
        regionalBonus: '5',
        description: '',
        orderCode: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        fixedCost: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        code: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        fixedCost: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        amountConditions: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        issueId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        issueCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        issueName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        guaranteeType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        amountRange: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        settlementType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        percent: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        price: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        agentInvoicingPercent: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        agentPercent: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        regionalBonus: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        description: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        orderCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        status: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]

      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/letter/bgsettlementconfig/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        this.dataForm.percent = res.data.percent * 100
        this.dataForm.agentPercent = res.data.agentPercent * 100
        this.dataForm.regionalBonus = res.data.regionalBonus * 100
        // this.dataForm.guaranteeType = this.dataForm.guaranteeType
        // console.log(this.dataForm.guaranteeType)
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        console.log(this.dataForm.guaranteeType)
        let params = {
          ...this.dataForm,
          percent: this.dataForm.percent / 100,
          agentPercent: this.dataForm.agentPercent / 100,
          regionalBonus: this.dataForm.regionalBonus / 100,
          guaranteeType: this.dataForm.guaranteeType
        }

        this.$http[!this.dataForm.id ? 'post' : 'put']('/letter/bgsettlementconfig/', params).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
