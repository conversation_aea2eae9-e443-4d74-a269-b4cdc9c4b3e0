<template>
  <el-dialog
    :visible.sync="visible"
    :title="'设置锁屏密码'"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"  label-width="90px">
      <el-form-item :label="$t('updatePassword.username')">
        <span>{{ $store.state.user.name }}</span>
      </el-form-item>
      <el-form-item prop="password" :label="'锁屏密码'">
        <el-input v-model="dataForm.password" type="password" :placeholder="'锁屏密码'"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="handleSetLock()">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'mian-navbar-lock',
  data () {
    return {
      visible: false,
      dataForm: {
        password: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        password: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
      })
    },
    // 表单提交
    handleSetLock () {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$store.state.setLockPasswd = this.dataForm.password
          this.handleLock()
        }
      })
    },
    handleLock () {
      setTimeout(() => {
        this.$router.push({ path: '/lock' })
        window.SITE_CONFIG['setLock'] = true
      }, 100)
    }
  }
}
</script>
