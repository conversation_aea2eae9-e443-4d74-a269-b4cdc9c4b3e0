<!--
 * @Descripttion:
 * @Author: k<PERSON>weiqiang
 * @Date: 2020-10-13 09:38:40
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-05-19 11:12:15
-->
<template>
  <el-col :span="20">
    <el-col :span="24" class="con-title">
      保函信息
    </el-col>
    <el-col :span="24">
      <el-form-item label="出具方信息">
        {{dataForm.issueName}}
      </el-form-item>
    </el-col>
    <el-col :span="24">
      <el-form-item label="保单号(验真)">
        {{fomate('bginsuranceinfo','insuranceCode')}}
      </el-form-item>
    </el-col>
    <el-col :span="24" v-if="dataForm.guaranteeType == 'lydbbh'||dataForm.guaranteeType == 'lybhyh'">
      <el-form-item label="模板类型">
        {{JSON.parse(pakeage.guaranteeTemplateTypes).find(a=>a.value === dataForm.guaranteeTemplateType ).name}}
       <!-- {{dataForm.guaranteeType == 'lydbbh'?db.find(a=>a.dictCode==dataForm.guaranteeTemplateType).dictName:yh.find(a=>a.dictCode==dataForm.guaranteeTemplateType).dictName}} -->
      </el-form-item>
    </el-col>
  </el-col>

</template>
<script>
import { getDict } from '@/utils/index'

export default {
  props: {
    dataForm: Object,
    options: Object
  },
  data () {
    return {
      pakeage: {},
      yh: getDict('履约银行保函模板类型'),
      db: getDict('履约担保保函模板类型')
    }
  },
  watch: {
    dataForm: {
      handler (a) {
        this.getpakeage()
      },
      deep: true
    }
  },
  methods: {
    fomate (key, val) {
      return this.dataForm[key][val] ? this.dataForm[key][val] : '-'
    },
    certType (val, name) {
      // console.log(this.options)
      var aa = this.options[name].filter((a) => a.dictCode === val)
      if (name === 'letterStatusoptions') {
        console.log(aa)
      }
      return aa.length > 0 ? aa[0].dictName : '-'
    },
    getpakeage () {
      console.log(this.dataForm)
      this.$http
        .get(`/letter/bgissueconfigure/getInfoByIssueCode?issueCode=${this.dataForm.key}&guaranteeTypeCode=${this.dataForm.guaranteeType}`)
        .then(async ({ data: res }) => {
          console.log('配置', res)
          this.pakeage = res.data
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.con-title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  // border-left: 6px solid rgb(241, 130, 65);
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-bottom: 10px;
}
</style>
