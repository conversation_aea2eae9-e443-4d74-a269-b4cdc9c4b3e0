// eslint-disable-next-line no-unused-vars
import request from '@/utils/request'
// eslint-disable-next-line no-unused-vars
import Cookies from 'js-cookie'

export function transactionList (query) {
  // return request({
  //   url: `${window.SITE_CONFIG['apiURL']}/dashboard/check/userInfo?token=${Cookies.get('token')}`,
  //   method: 'get',
  //   params: query
  // })
}
export function checkSum (query) {
  // return request({
  //   url: `${window.SITE_CONFIG['apiURL']}/dashboard/check/checkSum`,
  //   method: 'get',
  //   params: query
  // })
}

export function checkMonth (query) {
  // return request({
  //   url: `${window.SITE_CONFIG['apiURL']}/dashboard/check/checkMonth`,
  //   method: 'get',
  //   params: query
  // })
}

export function checkTaskResultMonth (query) {
  // return request({
  //   url: `${window.SITE_CONFIG['apiURL']}/dashboard/check/checkTaskResultMonth`,
  //   method: 'get',
  //   params: query
  // })
}

export function checkTaskYear (query) {
  // return request({
  //   url: `${window.SITE_CONFIG['apiURL']}/dashboard/check/checkTaskYear`,
  //   method: 'get',
  //   params: query
  // })
}

export function checkTaskResultYear (query) {
  // return request({
  //   url: `${window.SITE_CONFIG['apiURL']}/dashboard/check/checkTaskResultYear`,
  //   method: 'get',
  //   params: query
  // })
}

export function checkProportional (query) {
  // return request({
  //   url: `${window.SITE_CONFIG['apiURL']}/dashboard/check/checkProportional`,
  //   method: 'get',
  //   params: query
  // })
}
