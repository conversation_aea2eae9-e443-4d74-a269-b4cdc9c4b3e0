/*
 * @Descripttion:
 * @Author: k<PERSON><PERSON>qi<PERSON>
 * @Date: 2019-12-18 15:41:53
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2024-01-17 10:28:08
 */
const moduleRoutesArr = [
  /**
   * @name:投标保险保函
   * @msg:表单、修改、详情
   */
  { path: '/bgguaranteeletterDetail', component: () => import('@/views/project/guarantee/common/bgguaranteeletterDetail'), name: 'bgguaranteeletterDetail', meta: { title: '保函查看页面', isTab: true } },
  // { path: '/bgguaranteeletterDetail1', component: () => import('@/views/project/guarantee/common/bgguaranteeletterDetail1'), name: 'bgguaranteeletterDetail1', meta: { title: '保函查看页面', isTab: true } },
  { path: '/openbidtimeDetail', component: () => import('@/views/project/guarantee/common/bgguaranteeletterDetail'), name: 'openbidtimeDetail', meta: { title: '保函查看页面', isTab: true } },
  { path: '/bgguaranteeletterUpdate/:id/:type/:insuranceCode/:insuranceName/:pdfType/:projectId', component: () => import('@/views/project/guarantee/tenderGuarantee/bgGuaranteeApplyFrom'), name: 'bgguaranteeletterUpdate', meta: { title: '保函修改页面', isTab: false } },
  { path: '/applyBgGuaranteeApplyFromFLogin/:type/:insuranceCode/:insuranceName/:pdfType/:projectId', component: () => import('@/views/project/guarantee/tenderGuarantee/bgGuaranteeApplyFrom'), name: 'applyBgGuaranteeApplyFromFLogin', meta: { title: '投标保函申请', isTab: false } },
  { path: '/applyBgGuaranteeApplyFromFLoginHy/:type/:insuranceCode/:insuranceName', component: () => import('@/views/project/guarantee/tenderGuarantee/bgGuaranteeApplyFromHy'), name: 'applyBgGuaranteeApplyFromFLoginHy', meta: { title: '投标保函申请', isTab: false } },
  /**
   * @name:担保保函
   * @msg:表单、修改、详情
   */
  { path: '/warrantGuarantee/:type/:insuranceCode/:insuranceName/:pdfType/:projectId', component: () => import('@/views/project/guarantee/warrantGuarantee/index'), name: 'warrantGuarantee', meta: { title: '投标保函申请', isTab: false } },
  { path: '/warrantGuaranteeUpdate/:id/:type/:insuranceCode/:insuranceName/:pdfType/:projectId', component: () => import('@/views/project/guarantee/warrantGuarantee/index'), name: 'warrantGuaranteeUpdate', meta: { title: '保函修改页面', isTab: false } },
  /**
   *   /**
   * @name:担保保函
   * @msg:表单、修改、详情
   */
  { path: '/performance/:type/:insuranceCode/:insuranceName/:pdfType/:projectId/:templateType', component: () => import('@/views/project/guarantee/performance/index'), name: 'performance', meta: { title: '保函申请', isTab: false } },
  { path: '/performanceUpdate/:id/:type/:insuranceCode/:insuranceName/:pdfType/:projectId', component: () => import('@/views/project/guarantee/performance/index'), name: 'performanceUpdate', meta: { title: '保函修改页面', isTab: false } },
  /**
   * @name:银行保函
   * @msg:表单、修改、详情
   */
  { path: '/bankGuarantee/:type/:insuranceCode/:insuranceName/:pdfType/:projectId', component: () => import('@/views/project/guarantee/bankGuarantee/index'), name: 'bankGuarantee', meta: { title: '投标保函申请', isTab: false } },
  { path: '/bankGuaranteeUpdate/:id/:type/:insuranceCode/:insuranceName/:pdfType/:projectId', component: () => import('@/views/project/guarantee/bankGuarantee/index'), name: 'bankGuaranteeUpdate', meta: { title: '保函修改页面', isTab: false } },
  /**
   * @name:诉讼保函
   * @msg:表单、修改、详情
   */
  { path: '/litigationGuarantee/:type/:insuranceCode/:insuranceName', component: () => import('@/views/project/guarantee/litigationGuarantee/index'), name: 'litigationGuarantee', meta: { title: '保函申请', isTab: false } },
  { path: '/litigationGuaranteeUpdate/:id/:type/:insuranceCode/:insuranceName/:pdfType', component: () => import('@/views/project/guarantee/litigationGuarantee/index'), name: 'litigationGuaranteeUpdate', meta: { title: '保函修改页面', isTab: false } },
  { path: '/litigationGuaranteeDetail', component: () => import('@/views/project/guarantee/common/litigationGuaranteeDetail'), name: 'litigationGuaranteeDetail', meta: { title: '保函查看页面', isTab: true } },
  { path: '/thirdParty', component: () => import('@/views/project/guarantee/litigationGuarantee/thirdParty'), name: 'thirdParty', meta: { title: '第三方诉讼申请', isTab: false } },
  { path: '/litDetail', component: () => import('@/views/project/letter/litigation/litDetail'), name: 'litDetail', meta: { title: '诉讼详情', isTab: false } },
  // 盖章
  { path: '/Correction', component: () => import('@/views/project/guarantee/common/applicant/Correction/index'), name: 'Correction', meta: { title: '批改', isTab: true } },
  { path: '/homeChart', component: () => import('@/views/modules/demo/echarts'), name: 'charts', meta: { title: '仪表盘', isTab: true } },
  { path: '/bgguaranteeletterSqf', component: () => import('@/views/modules/letter/bgguaranteeletterSqf'), name: 'bgguaranteeletterSqf', meta: { title: '电子保函', isTab: true } },
  { path: '/bgguaranteeletterSyf', component: () => import('@/views/modules/letter/bgguaranteeletterSyf'), name: 'bgguaranteeletterSyf', meta: { title: '电子保函', isTab: true } },
  { path: '/bgguaranteeletterCjf', component: () => import('@/views/modules/letter/bgguaranteeletterCjf'), name: 'bgguaranteeletterCjf', meta: { title: '电子保函', isTab: true } },
  { path: '/bgguaranteeletterSqfUpdate/:letterId', component: () => import('@/views/modules/letter/bgguaranteeletterSQF-add-or-update'), name: 'bgguaranteeletterSqfUpdate', meta: { title: '保函修改页面', isTab: true } },
  { path: '/guaranteePayment/:guaranteeId', component: () => import('@/views/modules/payment/applyGuaranteePayment'), name: 'guaranteePayment', meta: { title: '电子保函服务费支付', isTab: true } },
  { path: '/user/:deptId', component: () => import('@/views/modules/letter/issue-user'), name: 'user', meta: { title: '用户管理', isTab: true } },
  { path: '/price/:issueId', component: () => import('@/views/modules/letter/issue-bgguaranteeprice'), name: 'price', meta: { title: '服务定价管理', isTab: true } },
  { path: '/platformIssue/:platformId', component: () => import('@/views/modules/letter/bgguaranteeplatform-issue'), name: 'platformIssue', meta: { title: '出具机构管理', isTab: true } },
  // 统计
  { path: '/cost', component: () => import('@/views/project/report/cost'), name: 'cost', meta: { title: '统计', isTab: false } },
  // 财务结算配置
  { path: '/bgsettlementconfig', component: () => import('@/views/project/financial/bgsettlementconfig'), name: 'bgsettlementconfig', meta: { title: '财务结算配置', isTab: false } },
  { path: '/reconciliation', component: () => import('@/views/project/financial/reconciliation/index'), name: 'reconciliation', meta: { title: '保单详情', isTab: false } }
]
const pageRoutesArr = [// 登录前投保
  // 保函详情
  { path: '/pdfIe', component: () => import('@/views/project/guarantee/tenderGuarantee/pdfIe'), name: 'pdfIe', meta: { title: 'PDF盖章', isTab: true } },
  { path: '/signatureIe', component: () => import('@/views/project/guarantee/warrantGuarantee/signatureIe'), name: 'signatureIe', meta: { title: '签章', isTab: false } },
  { path: '/examineSignatureIe', component: () => import('@/views/project/guarantee/common/detail/examineSignatureIe'), name: 'examineSignatureIe', meta: { title: '签章', isTab: false } },
  { path: '/bgguaranteeletterDetailZbt', component: () => import('@/views/project/guarantee/common/bgguaranteeletterDetail'), name: 'bgguaranteeletterDetailZbt', meta: { title: '保函详情', isTab: true } },
  // 第一步
  // { path: '/applyBxGuarantee', component: () => import('@/views/project/guarantee/tenderGuarantee/applyBxGuarantee'), name: 'applyBxGuarantee', meta: { title: '投标保险保函申请' } },
  // 第二步
  // { path: '/applyBgGuaranteeApplyFrom', component: () => import('@/views/project/guarantee/tenderGuarantee/bgGuaranteeApplyFromBeFore'), name: 'applyBgGuaranteeApplyFrom', meta: { title: '投标保函申请', isTab: true } },
  { path: '/awaitingAudit', component: () => import('@/views/project/guarantee/tenderGuarantee/awaitingAudit'), name: 'awaitingAudit', meta: { title: '等待审核' } },
  { path: '/payResult', component: () => import('@/views/project/guarantee/tenderGuarantee/payResult'), name: 'payResult', meta: { title: '订单支付' } },
  // { path: '/applyPaySus', component: () => import('@/views/modules/payment/paySus'), name: 'applyPaySus', meta: { title: '支付成功', isTab: true } },
  { path: '/pay', component: () => import('@/views/project/guarantee/common/pay'), name: 'pay', meta: { title: '支付', isTab: true } },
  { path: '/otherLogin', component: () => import('@/views/project/otherLogin/index'), name: 'otherLogin', meta: { title: '支付', isTab: true } }

]
export default { moduleRoutesArr, pageRoutesArr }
