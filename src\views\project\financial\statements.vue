<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2021-03-10 16:43:21
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-05-04 16:21:41
-->
<template>
  <el-card shadow="never" class="aui-card--fill">
    <el-tabs type="card" v-if="typeList.length>0" v-model="guaranteeType" @tab-click="handleClick">
      <el-tab-pane v-for="(item,index) in typeList" :key="index" :label="item.name" :name="item.value"></el-tab-pane>
    </el-tabs>
    <div>
      <el-form :inline="true" :model="dataForm" v-if="!iscompoent">
        <el-form-item>
          <el-cascader placeholder="请选择区域" :options="regionIds" v-model.trim="regionId" ref='region' @change='regionIdChange'
                size="small" clearable class="wd180" style="width:250px;" :props="props"></el-cascader>
        </el-form-item>
        <el-form-item>
          <div class="block">
            <el-input v-model="dataForm.projectName" size="small" placeholder="项目名称"></el-input>
          </div>
        </el-form-item>
        <el-form-item>
          <div class="block">
            <el-input v-model="dataForm.applyName" size="small" placeholder="申请方名称"></el-input>
          </div>
        </el-form-item>
        <el-form-item>
          <div class="block">
            <el-input v-model="dataForm.policyNo" size="small" placeholder="保函编号"></el-input>
          </div>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="date"
            type="monthrange"
            size="small"
            align="right"
            unlink-panels
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            @change="change"
            value-format="yyyy-MM"
            :picker-options="pickerOptions">
          </el-date-picker>
        </el-form-item>

        <el-form-item>
          <el-button @click="search()" size="small" type="primary">{{ $t('query') }}</el-button>
        </el-form-item>
         <el-form-item>
          <el-button type="success" size="small" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
      </el-form>
      <div>
      <keep-alive>
        <component :is="currentRole" :ref="currentRole" />
      </keep-alive>
      </div>
    </div>
  </el-card>
</template>
<script>
// import mixinViewModule from '@/mixins/view-module'
import guarantee from './statements/guarantee.vue'
import income from './statements/income.vue'
import issue from './statements/issue.vue'
import agent from './statements/agent.vue'
import Cookies from 'js-cookie'
import moment from 'moment'
import qs from 'qs'
export default {
  // mixins: [mixinViewModule],
  components: { guarantee,
    income,
    issue,
    agent },
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgguaranteeletter/pageAccountLetter',
        getDataListIsPage: true,
        exportURL: '/letter/bgSettlement/exportMonth',
        deleteIsBatch: true
      },
      regionIds: [],
      currentRole: 'guarantee',
      dataList: {},
      typeList: [],
      activeName: '',
      date: [moment().subtract(1, 'month').format('YYYY-MM'), moment().subtract(1, 'month').format('YYYY-MM')],
      guaranteeType: '0',
      props: {
        value: 'code',
        label: 'name',
        children: 'child',
        isLeaf: 'leaf',
        lazy: true,
        lazyLoad: this.lazyLoad,
        collapseTags: true
      },
      dataForm: {
        projectName: '',
        applyName: '',
        policyNo: '',
        regionCode: '',
        startDate: moment().subtract(1, 'month').format('YYYY-MM'),
        endDate: moment().subtract(1, 'month').format('YYYY-MM')
      },
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick (picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick (picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created () {
    this.init()
  },
  props: {
    iscompoent: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    init () {
      // let arr = []
      this.setarr()
      this.$nextTick(() => {
        this.$refs[this.currentRole].dataForm = this.dataForm
        this.$refs[this.currentRole].getDataList(1)
      })
    },
    exportHandle () {
      var params = qs.stringify({
        'token': Cookies.get('token'),
        ...this.dataForm
      })
      window.location.href = `${window.SITE_CONFIG['apiURL']}${this.mixinViewModuleOptions.exportURL}?${params}`
    },
    search () {
      this.$nextTick(() => {
        this.$refs[this.currentRole].dataForm = this.dataForm
        this.$refs[this.currentRole].getDataList(1)
      })
    },
    change (val) {
      if (val) {
        this.$set(this.dataForm, 'startDate', val[0])
        this.$set(this.dataForm, 'endDate', val[1])
      } else {
        this.$set(this.dataForm, 'startDate', '')
        this.$set(this.dataForm, 'endDate', '')
      }
    },
    handleClick ({ index }) {
      this.currentRole = this.typeList[index].com
      this.search()
    },
    async lazyLoad (node, resolve) {
      // 点击节点
      const { level } = node
      let nodes = await this.lazyloadFn(level === 0 ? 100000 : node.value)
      let nodes2 = nodes.map((item) => ({
        code: item.code,
        name: item.name,
        leaf: level > 0
      }))

      resolve(nodes2)
    },
    lazyloadFn (parentCode) {
      // 获取node
      return new Promise((resolve, reject) => {
        this.$http
          .get(`/demo/tbregion/regionTree?code=${parentCode}`)
          .then(({ data: res }) => {
            resolve(res.data)
            // this.zongingList = res.data
          })
          .catch(() => { })
      })
    },
    regionIdChange (val) {
      this.$set(this.dataForm, 'regionCode', val ? val[1] : '')
    },
    setarr () {
      this.typeList = [
        { name: '保函明细', value: '0', com: 'guarantee' },
        { name: '出具机构收入明细', value: '1', com: 'income' },
        { name: '各区域收入明细', value: '2', com: 'issue' },
        { name: '代理机构收入明细', value: '3', com: 'agent' }
      ]
    }
  }
}
</script>
<style lang="scss" scoped>
.cati{
position: relative;
top:6px;
}
</style>
