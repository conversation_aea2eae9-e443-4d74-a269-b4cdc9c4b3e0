<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-04-21 16:14:25
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-04-21 16:55:59
-->
<template>
  <el-dialog :visible.sync="visible" title="信息详情" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" v-loading='loading' ref="dataForm" :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="colConfig">
          <el-form-item label="企业名称" prop="companyName" label-width="190px">
            <span>{{dataForm.companyName}}</span>
          </el-form-item>
          <el-form-item label="证件号码" prop="certCode" label-width="190px">
            <span>{{dataForm.certCode}}</span>
          </el-form-item>
          <el-form-item label="备注" prop="remark" label-width="190px">
            <span>{{dataForm.remark}}</span>
          </el-form-item>
          <el-form-item label="状态" prop="status" label-width="190px">
            <span v-if="dataForm.status == 0">停用</span>
            <span v-if="dataForm.status == 1">正常</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template slot="footer">
      <el-button type="primary" @click="visible = false">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      loading: false,
      dataForm: {
        id: '',
        companyName: '',
        certCode: '',
        remark: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.loading = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http
        .get(`/letter/tbBlacklist/${this.dataForm.id}`)
        .then(({ data: res }) => {
          this.loading = false

          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
        })
        .catch(() => {})
    }
  }
}
</script>
