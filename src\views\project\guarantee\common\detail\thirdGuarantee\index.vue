<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2020-10-13 09:38:40
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2022-01-20 15:04:36
-->
<template>
  <el-col :span="20">
    <el-col :span="24" class="con-title">
      反担保信息
    </el-col>
    <el-col :span="24">
      <el-form-item label="保证金" prop="bond">
        {{fomate('thirdGuaranteeDTO','bond','元')}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg='24'>
      <el-form-item label="保证金比例" prop="bondPercent">
        {{fomate('thirdGuaranteeDTO','bondPercent','%')}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg='24'>
      <el-form-item label="第三方反担保" prop="thirdGuarantee">
        {{fomate('thirdGuaranteeDTO','thirdGuarantee','')}}
      </el-form-item>
    </el-col>
    <el-col :xs="24" :lg='24'>
      <el-form-item label="第三方担保金额" prop="thirdGuaranteeAmount">
        {{fomate('thirdGuaranteeDTO','thirdGuaranteeAmount','元')}}

      </el-form-item>
    </el-col>
  </el-col>

</template>
<script>
export default {
  props: {
    dataForm: Object,
    options: Object
  },
  methods: {
    fomate (key, val, type) {
      return this.dataForm[key][val] ? this.dataForm[key][val] + type : '-'
    },
    certType (val, name) {
      // console.log(this.options)
      var aa = this.options[name].filter((a) => a.dictCode === val)
      if (name === 'letterStatusoptions') {
        console.log(aa)
      }
      return aa.length > 0 ? aa[0].dictName : '-'
    }
  }
}
</script>
<style lang="scss" scoped>
.con-title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  // border-left: 6px solid rgb(241, 130, 65);
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-bottom: 10px;
}
</style>
