<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019-12-18 16:52:10
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-11-04 14:36:22
 -->
<template>
  <div class="inSteps">
     <el-steps :active="active"  align-center ref='steps'>
       <!-- {{this.$store.state.pdfType}} -->
        <el-step title="选择保函">1</el-step>
        <el-step title="提交申请"></el-step>
        <el-step title="盖章" v-if="this.$route.params.pdfType==='0'"></el-step>
        <el-step title='等待审核'></el-step>
      </el-steps>
  </div>
</template>
<script>
export default {
  data () {
    return {

    }
  },
  mounted () {
    // this.$refs.steps.$children.map((item, index) => {
    //   item.$el.getElementsByClassName('el-step__head')[0].lastChild.innerHTML =
    //     index + 1
    // })
  },
  props: {
    active: {
      type: Number
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
