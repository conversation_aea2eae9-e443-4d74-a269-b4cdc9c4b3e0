<template>
  <div class="card" v-loading='loading' >
    <!-- {{$route.query}} -->
    <el-card class="box-card" style="min-height:550px;">
      <!-- <steps :active='1' :pdfType='pdfType'></steps> -->
      <el-card class="box-card  maxcard proDia" shadow="never">
        <el-row :gutter="20" style=" max-width: 1000px;margin: 15px auto;">
          <!-- 111 -->
          <h3 class="temp-subtitle">保函类型</h3>
          <div class="guaranteeList">
            <div class="guaranteeList-item" :class="guaranteeAc==index?'active':''" v-for="(item,index) in guaranteeList" @click="guaranteeClick(index,item.status)" :key="index">
              <span class="icon"><img :src="iconL(item.code)" alt=""></span>
              <!-- <svg viewBox="-9 -9 43 35.191" width="43" height="35">
                <path id="check-icon" d=" M 0 9 Q 8 17 8 17 Q 8 17 25 0" fill="none" stroke-width="6" stroke="rgb(0,0,0)" stroke-linecap="round"></path>
              </svg> -->
              <span class="name">{{item.name}}</span>
              <i class="choice" v-if="guaranteeAc==index"><img src="@/assets/img/choice.png" alt=""></i>
              <!-- <el-radio :label="item.code"> <span class="name">{{item.name}}</span></el-radio> -->

            </div>
          </div>
          <h3 class="temp-subtitle">出具机构</h3>
          <div class="guaranteeList">
            <span v-for="(item,index) in mechanismList" :key="index">
              <div class="guaranteeList-item" :class="mechanismAc==index?'active':''" @click="mechanismClick(index,item.status)" v-if="isShow(item.jurisdiction)">
                <span class="icon"><img :src="iconL(item.code)" alt=""></span>
                <span class="name">{{item.name}}</span>
                <i class="choice" v-if="mechanismAc==index"><img src="@/assets/img/choice.png" alt=""></i>
                <!-- <el-radio :label="item.code"> <span class="name">{{item.name}}</span></el-radio> -->
              </div>
            </span>
          </div>
          <el-divider></el-divider>
          <div class="tip">
            <div>当前申请保函：<span class="bttip">{{guaranteeAc!==null?guaranteeList[guaranteeAc].name:'-'}}-{{mechanismAc!==null?mechanismList[mechanismAc].name:'-'}}</span></div>
            <template v-if="guaranteeAc!==null&&guaranteeList[guaranteeAc].code ==='tb'">
              <div>预计出函时间：<span class="bttip">{{mechanismAc!==null?mechanismList[mechanismAc].time:'-'}}</span>&nbsp;个小时以内</div>
              <div v-if="mechanismAc!==null&&mechanismList[mechanismAc].tips">出函说明：<span class="bttip2">{{mechanismAc!==null?mechanismList[mechanismAc].tips:'-'}}</span>&nbsp;
                <!-- <el-alert  title="目前银行节前暂停出单，请选择其他保函！" :closable='false' type="error" effect="dark">
                </el-alert> -->
              </div>
            </template>
          </div>
          <!-- <el-divider></el-divider> -->
          <!-- <el-divider></el-divider> -->
          <!-- <el-col :xs="8" :sm="6" :md="4" :lg="4" :xl="4" v-for="(item,index) in projectStatusoptions" :key="index">
            <div @click="getProject(item.dictCode,'1')" v-if="item.status == 1">
              <el-card shadow="hover" :body-style="{ padding: '0px' }">
                <img src="@/assets/img/icon-guaranteea.png" class="image">
                <div style="padding: 14px;min-height: 60px;margin-bottom: 20px;">
                  <span>{{item.dictName}}</span>
                </div>
              </el-card>
            </div>
            <div v-else>
              <el-card shadow="hover" :body-style="{ padding: '0px' }">
                <img src="@/assets/img/icon-guarantee.png" class="image">
                <div style="padding: 14px;min-height: 60px;margin-bottom: 20px;">
                  <span>{{item.dictName}} <br> <span style="color:#aaa;font-size:12px;">暂未开放</span> </span>
                </div>
              </el-card>
            </div>
          </el-col> -->
          <div style="text-align:center;">
            <!-- {{guaranteeAc}}{{mechanismAc}} -->
            <el-button type="primary" @click="getProject(guaranteeList[guaranteeAc].code+mechanismList[mechanismAc].code,guaranteeList[guaranteeAc].name+'-'+mechanismList[mechanismAc].name)"
              v-if='guaranteeAc!==null&&mechanismAc!==null'>保函申请</el-button>
          </div>
        </el-row>
      </el-card>
    </el-card>
    <chooiseDia ref="chooiseDia" @isSure='choseGuarantee' v-if="visible" />
    <lvDia ref="lvDia" @submit='goC' v-if="visible" />
    <div class="foot">
      <span>

      </span>
    </div>
  </div>
</template>
<script>
import steps from '@/views/project/guarantee/components/step'
// import '@/views/project/guarantee/components/insurance'
// import { guaranteeList, mechanismList } from './js/guarantee'
import { findList } from '@/api/apiList/bgApplyConfigure'
import chooiseDia from './chooiseDia'
import lvDia from './lvDia'

export default {
  data () {
    return {
      projectStatusoptions: [],
      pdfType: '',
      guaranteeList: {},
      mechanismList: {},
      iconList: [
        { name: 'tb', icon: require('@/assets/img/1-tbbh.png') },
        { name: 'ly', icon: require('@/assets/img/1-lybh.png') },
        { name: 'yfk', icon: require('@/assets/img/1-yfk.png') },
        { name: 'zf', icon: require('@/assets/img/1-zf.png') },
        { name: 'nmgbh', icon: require('@/assets/img/1-nmgbh.png') },
        { name: 'zlbh', icon: require('@/assets/img/1-zlbh.png') },
        { name: 'bhyh', icon: require('@/assets/img/2-yh.png') },
        { name: 'dbbh', icon: require('@/assets/img/2-db.png') },
        { name: 'bxbh', icon: require('@/assets/img/2-bx.png') },
        { name: 'other', icon: require('@/assets/img/1-tbbh.png') }
      ],
      //   icon: require('@/assets/img/1-lybh.png'),
      //   icon: require('@/assets/img/1-yfk.png'),
      //   icon: require('@/assets/img/1-zf.png'),
      //   icon: require('@/assets/img/1-nmgbh.png'),
      //   icon: require('@/assets/img/1-zlbh.png'),
      //   icon: require('@/assets/img/2-yh.png'),
      //   icon: require('@/assets/img/2-db.png'),
      //   icon: require('@/assets/img/2-bx.png')

      // },
      guaranteeAc: null,
      mechanismAc: null,
      visible: false,
      loading: false
    }
  },
  components: {
    // eslint-disable-next-line vue/no-unused-components
    steps,
    lvDia,
    chooiseDia
  },
  created () {},
  activated () {
    this.getProjectStatusInfo()
    this.getPdfInfo()
    this.BrowserType()
    this.getGuaranteeList()
  },
  methods: {
    getGuaranteeList () {
      this.loading = true
      findList().then(({ data: res }) => {
        this.loading = false
        res.data.guaranteeList.forEach((a) => {
          console.log(`../../../${a.icon}`)
          a.backgroundDiv = {
            // backgroundImage: require('@/' + a.icon),
            backgroundRepeat: 'no-repeat',
            backgroundSize: '100% 100%'
          }
        })
        console.log(res.data.guaranteeList)
        this.guaranteeList = res.data.guaranteeList
        this.mechanismList = res.data.issueTypeList
      })
    },
    iconL (code) {
      if (code) {
        // console.log
        if (this.iconList.filter(a => a.name === code).length > 0) {
          return this.iconList.filter(a => a.name === code)[0].icon
        } else {
          return this.iconList.filter(a => a.name === 'other')[0].icon
        }
      }
    },
    guaranteeClick (idx, status) {
      if (!status) {
        const h = this.$createElement
        // this.$message.warning('目前支持线下开具，如有需要，请拨打400-0311-616！')
        this.$notify({
          title: '提示',
          type: 'warning',
          message: h('span', null, [
            h('p', null, '目前支持线下开具，如有需要，请拨打'),
            h('p', { style: 'color: #409EFF' }, '400-0311-616')
          ])
        })
        this.guaranteeAc = null
        this.mechanismAc = null
      } else {
        this.guaranteeAc = idx
      }
    },
    mechanismClick (idx, status) {
      // this.mechanismAc = idx
      if (!status) {
        const h = this.$createElement
        // this.$message.warning('目前支持线下开具，如有需要，请拨打400-0311-616！')
        this.$notify({
          title: '提示',
          type: 'warning',
          message: h('span', null, [
            h('span', null, '因政策调整该类型保函申请暂时关闭!'),
            h('span', null, '！')
          ])
        })
        // this.guaranteeAc = null
        this.mechanismAc = null
      } else {
        this.mechanismAc = idx
      }
    },
    isShow (arr) {
      if (this.guaranteeAc !== null) {
        return arr.includes(this.guaranteeList[this.guaranteeAc].code)
      } else {
        return false
      }
    },
    BrowserType () {
      var userAgent = navigator.userAgent // 取得浏览器的userAgent字符串
      var isOpera = userAgent.indexOf('Opera') > -1 // 判断是否Opera浏览器
      // var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera; //判断是否IE浏览器
      var isIE = window.ActiveXObject || 'ActiveXObject' in window
      // var isEdge = userAgent.indexOf("Windows NT 6.1; Trident/7.0;") > -1 && !isIE; //判断是否IE的Edge浏览器
      var isEdge = userAgent.indexOf('Edge') > -1 // 判断是否IE的Edge浏览器
      var isFF = userAgent.indexOf('Firefox') > -1 // 判断是否Firefox浏览器
      // eslint-disable-next-line eqeqeq
      var isSafari =
        // eslint-disable-next-line eqeqeq
        userAgent.indexOf('Safari') > -1 && userAgent.indexOf('Chrome') == -1 // 判断是否Safari浏览器
      var isChrome =
        userAgent.indexOf('Chrome') > -1 &&
        userAgent.indexOf('Safari') > -1 &&
        !isEdge // 判断Chrome浏览器

      if (isIE) {
        this.$notify.closeAll()
        const h = this.$createElement
        var hDom = h(
          'span',
          {},
          '检测到当前浏览器为IE，推荐使用360浏览器、谷歌浏览器、IE11及以上版本进行申请保函！'
        )
        this.$notify({
          title: '警告',
          message: hDom,
          type: 'warning',
          duration: 60000
        })
        var reIE = new RegExp('MSIE (\\d+\\.\\d+);')
        reIE.test(userAgent)
        var fIEVersion = parseFloat(RegExp['$1'])
        // eslint-disable-next-line eqeqeq
        if (userAgent.indexOf('MSIE 6.0') != -1) {
          return 'IE6'
          // eslint-disable-next-line eqeqeq
        } else if (fIEVersion == 7) {
          return 'IE7'
          // eslint-disable-next-line eqeqeq
        } else if (fIEVersion == 8) {
          return 'IE8'
          // eslint-disable-next-line eqeqeq
        } else if (fIEVersion == 9) {
          return 'IE9'
          // eslint-disable-next-line eqeqeq
        } else if (fIEVersion == 10) {
          return 'IE10'
        } else if (userAgent.toLowerCase().match(/rv:([\d.]+)\) like gecko/)) {
          return 'IE11'
        } else {
          return '0'
        } // IE版本过低
      } // isIE end

      if (isFF) {
        return 'FF'
      }
      if (isOpera) {
        return 'Opera'
      }
      if (isSafari) {
        return 'Safari'
      }
      if (isChrome) {
        return 'Chrome'
      }
      if (isEdge) {
        return 'Edge'
      }
    },

    getProjectStatusInfo () {
      this.loading = true
      this.$http
        .get('/sys/dict/type/guaranteeType')
        .then(({ data: res }) => {
          this.loading = false

          this.projectStatusoptions = {
            ...this.projectStatusoptions,
            ...res.data.list
          }
        })
        .catch(() => {})
    },
    getPdfInfo () {
      this.$http
        .get(`/sys/params/getValue/${'isSign'}`)
        .then(({ data: res }) => {
          this.loading = false

          this.pdfType = res.data
        })
        .catch(() => {})
    },
    choseGuarantee (code, data, projectId, templateType) {
      let obj = this.guaranteeList[this.guaranteeAc].path
        ? this.guaranteeList[this.guaranteeAc]
        : this.mechanismList[this.mechanismAc]

      let params = {
        type: code,
        insuranceCode: data.code,
        insuranceName: data.name,
        pdfType: this.pdfType,
        projectId: projectId || '0'
      }
      if (obj.code === 'ly') {
        console.log('ly')
        params.templateType = templateType || '0'
      }
      console.log('obj.params', params)
      this.$router.push({
        name: obj.path,
        params: params
      })
    },
    // choseGuarantee (code, data, projectId) {
    //   this.$router.push({
    //     name: this.guaranteeList[this.guaranteeAc].path
    //       ? this.guaranteeList[this.guaranteeAc].path
    //       : this.mechanismList[this.mechanismAc].path,
    //     params: {
    //       type: code,
    //       insuranceCode: data.code,
    //       insuranceName: data.name,
    //       pdfType: this.pdfType,
    //       projectId: projectId || '0'
    //     }
    //   })
    // },

    async getProject (code, name) {
      // this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
      //   (item) => item.name !== 'project-insuranceAfterLogin-chooiseIn'
      // )
      if (code === 'tbdbbh' || code === 'tbbhyh') {
        this.visible = true
        this.$nextTick(() => {
          this.$refs.chooiseDia.init()
          this.$refs.chooiseDia.code = code
          this.$refs.chooiseDia.title = name
        })
      } else if (code === 'lybhyh') {
        this.visible = true
        this.$nextTick(() => {
          this.$refs.lvDia.init()
          this.$refs.lvDia.code = code
          this.$refs.lvDia.title = name
        })
      } else {
        const h = this.$createElement
        const mssage = code !== 'ssdbbh' ? h('p', null, [
          h('span', null, '请根据'),
          h('span', { style: 'color: #E6A23C' }, ' 招标文件 '),
          h('span', null, '要求选择对应保函类型，以免造成其他影响！')
        ]) : h('p', null, [
          h('span', null, '诉讼保函目前只支持'),
          h('span', { style: 'color: #E6A23C' }, ' 邯郸、邢台 '),
          h('span', null, '地区申请，是否继续？')
        ])

        this.$confirm(
          mssage,
          '提示',
          {
            confirmButtonText: '继续申请',
            cancelButtonText: '重新选择',
            type: 'warning'
          }
        )
          .then(async () => {
            this.goC(code)
          })
          .catch(() => {})
      }
    },
    async goC (code, projectId, guaranteeTemplateType) {
      console.log(projectId)
      var { data } = await this.$http.get(
        `/letter/bgguaranteeissue/getIssue?guaranteeTypecode=${code}&pCode=1&projectId=${
          projectId || ''
        }&guaranteeTemplateType=${
          guaranteeTemplateType || ''
        }`
      )
      if (data.data[0]) {
        this.choseGuarantee(code, data.data[0], projectId, guaranteeTemplateType)
      } else {
        this.$message.error('暂无出具机构！')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.el-upload__tip {
  color: #f56c6c;
}
.guaranteeList {
  display: flex;
  flex-wrap: wrap;
  // margin: 20px auto;
  .guaranteeList-item {
    width: 200px;
    box-shadow: 0 0 30px #e7e7e7;
    padding: 15px;
    margin: 20px;
    text-align: center;
    transition: all 0.3s ease-in-out;
    border: 1px solid transparent;
    cursor: pointer;
    position: relative;
    span {
      display: block;
      margin-top: 12px;
    }
    .icon {
      transition: all 0.3s ease-in-out;
      display: inline-block;
      width: 40px;
      height: 40px;
      line-height: 40px;
      img {
        transition: all 0.3s ease-in-out;
        width: 40px;
      }
    }
  }
  .guaranteeList-item:hover {
    transform: scale(1.1);
    border: 1px solid #f18241;
    .icon {
      background-color: rgba(241, 130, 65, 0.1);
      transform: scale(1.4);
      border-radius: 50%;
      img {
        width: 25px;
      }
    }
  }
  .active {
    transform: scale(1.1);
    border: 1px solid rgb(241, 130, 65);
    color: #f18241;
    .icon {
      background-color: rgba(241, 130, 65, 0.1);
      transform: scale(1.4);
      border-radius: 50%;
      img {
        width: 25px;
      }
    }
  }
  .choice {
    position: absolute;
    top: -2px;
    right: -2px;
    img {
      width: 18px;
    }
  }
}
.bttip {
  font-size: 16px;
  color: rgb(241, 130, 65);
  font-weight: bold;
}
.bttip2 {
  color: #f56c6c;
  font-size: 16px;
  line-height: 1.5em;
  margin-top: 5px;
  display: inline-block;
  text-indent: 32px;
}
.tip {
  margin-bottom: 20px;
  div {
    margin: 10px 0;
  }
}
.card {
  min-width: 900px;
  // max-width: 1200px;
  margin: 15px auto;
  margin-bottom: 65px;
}
.foot {
  width: 100%;
  background: #fff;
  position: fixed;
  bottom: 0;
  padding: 10px 0;
  border-top: 1px solid #ddd;
  background: #f9f9f9;
  text-align: center;
  left: 0px !important;
  z-index: 999;
  span {
    text-align: center;
  }
}
.temp-subtitle {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  margin: 0;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.maxcard {
  // margin-top: 20px;
  margin-bottom: 10px;
  // min-height: calc(100vh - 70px - 38px - 30px) !important;
  // height: calc(00vh - 70px - 38px - 30px) !important;
  background: rgb(253, 253, 253);
  // text-align: center;
  h4 {
    font-size: 22px;
  }
}
.image {
  width: 100%;
  box-sizing: border-box;
  border: 20px solid white;
}
</style>
