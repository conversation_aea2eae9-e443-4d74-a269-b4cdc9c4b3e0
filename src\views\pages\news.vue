<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-08-26 09:43:10
 * @LastEditors: k<PERSON><PERSON>qiang
 * @LastEditTime: 2020-09-22 09:15:15
-->
<template >
  <div>
    <div class="wrap">
      <div class="inner">
        <h2 class="title">{{data.title}}</h2>
        <div class="desc"><a class="user">{{data.newType == '1' ?'通知公告':'新闻动态'}}</a> <span class="data">{{data.pubDate}}</span> </div>
        <div class="content" ref='box'>

        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data () {
    return {
      data: {}
    }
  },
  created () {
    this.id = this.$route.params.id
    this.getInfo()
  },
  methods: {
    getInfo () {
      this.$http.get(`/demo/news/${this.id}`).then(({ data: res }) => {
        //
        this.data = res.data
        this.$refs.box.innerHTML = res.data.content
      })
    }
  }
}
</script>
<style type="text/css" scoped>
.error .title {
  font-size: 100px;
  text-align: center;
}
.error p {
  text-align: center;
  font-size: 20px;
}
.wrap {
  font-size: 15px;
  padding: 32px 16px 12px;
}
.wrap .inner {
    max-width: 677px;
    margin-left: auto;
    margin-right: auto;
}
.wrap .inner .title {
      font-size: 22px;
      line-height: 1.4;
      margin-bottom: 14px;
}
.wrap .inner .desc .user {
      color: #576b95;
      text-decoration: none;
      margin-right: 10px;
}
.wrap .inner .desc .data {
      color: rgba(0, 0, 0, 0.3);
}
.wrap .inner .desc .pageView {
      color: #f00;
      float: right;
}
.wrap .inner .content {
      padding-top: 20px;
}
</style>
