<!--
 * @Descripttion:
 * @Author: kong<PERSON>qiang
 * @Date: 2020-08-19 15:14:36
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-05-19 09:48:53
-->
<template>
  <el-card class="box-card box" shadow='never'>
    <!-- <el-button size="mini" @click="zoom+0.1" round>放大</el-button>
    <el-button size="mini" @click="zoom-0.1" round>缩小</el-button> -->
    <!-- {{dataForm.guaranteeAmount}} -->
    <el-scrollbar class="height"  v-loading="!model"  element-loading-text="加载模板中...">
      <div class="model" id="tenModel">
        <bh  v-if="model&&dataForm" style="zoom:0.85;" :html="model" :dataForm='{...dataForm,...InsuranceAllocation}'></bh>
      </div>
    </el-scrollbar>

  </el-card>
</template>
<script>
import './bh'
import { concatData } from './InsuranceAllocation/data.js'

export default {
  data () {
    return {
      model: '',
      zoom: 1,
      insurancecode: '',
      src: '',
      guaranteeAmountDefault: '',
      guaranteeAmountChange: '',
      InsuranceAllocation: {}
    }
  },
  props: {
    dataForm: Object,
    code: String,
    type: String,
    height: String,
    bgType: {
      type: String,
      default: '0'
    }
  },
  components: {},
  created () {
    this.src = process.env.BASE_URL
    this.insurancecode = this.$route.params.insuranceCode
    // this.initImportFile()
  },
  watch: {
    bgType (val) {
      // console.log(val)
      this.getModel()
    },
    dataForm: {
      handler (a) {
        this.InsuranceAllocation = { ...concatData(this.dataForm) }
        // console.log(a.guaranteeAmount && !this.model, a.guaranteeAmount, this.model)
        let type = this.type ? this.type : this.$route.params.type
        if (type === 'tbdbbh') {
          if (this.guaranteeAmountDefault !== this.guaranteeAmountChange) {
            this.guaranteeAmountChange = this.guaranteeAmountDefault
            if (a.guaranteeAmount) {
              this.getModel()
            }
          }
          this.guaranteeAmountDefault = a.guaranteeAmount > 800000 ? '1' : '2'
          if (a.guaranteeAmount && !this.model) {
            this.getModel()
          }
        } else {
          // console.log()
          console.log(this.model)
          console.log(this.model)
          if (!this.model) {
            a.guaranteeAmount = a.guaranteeAmount || 0
            this.getModel()
          }
          // if (a.guaranteeAmount && !this.model) {
          //   this.getModel()
          // } else {
          //   a.guaranteeAmount = a.guaranteeAmount || 0
          //   // this.getModel()
          // }
        }
      },
      deep: true
    }
  },
  methods: {
    // initImportFile () {
    //   return require(`./InsuranceAllocation/${this.insurancecode}.js`)
    // },
    async getModel () {
      let { data } = await this.$http.get(
        'letter/bgguaranteetemplate/getInfoByIssueCodeAndType/' +
          (this.code ? this.code : this.$route.params.insuranceCode) +
          `/${this.bgType}/` +
          (this.type ? this.type : this.$route.params.type) +
          `/${this.dataForm.guaranteeAmount || 0}`
      )

      data.data.content.match(/\{\{(.+?)\}\}/g).map((a) => {
        data.data.content = data.data.content.replace(
          a,
          `<span style='color:#409EFF;'>${a}</span>`
        )
      })
      this.model = data.data.content
      this.InsuranceAllocation = { ...concatData(this.dataForm) }
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  margin-bottom: 15px;
}
.box {
  height: calc(100vh - 205px);
}
.height {
  height: 100%;
}
.model {
  margin: 2px;
  padding: 1.5em  1.2em;
  // height: 100%;
  // overflow-y: auto;
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.2);
}
</style>
