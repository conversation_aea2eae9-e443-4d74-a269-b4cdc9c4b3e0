﻿/* eslint-disable */
function HebcaClient () {
  this.clientCtrl = null
}
// 谷歌浏览器部分
var isIE = window.ActiveXObject !== undefined
var CertMgr = null
var recipientCerts = new Array()
var cert = null

var cryptCert = null
var util = null
var pkcs7 = null
// 非IE浏览器下
var socket = null
var host = 'ws://localhost:17212/test'
window.onload = function () {
  if (isIE) { // 创建WebSeal对象说说
    CertMgr = new HebcaP11Object()
  } else {
    connect()
  }
}

function HebcaP11Object () {
  var p11Ctrl = null
  // 一个页面只创建一次
  if (CertMgr != null) {
    return CertMgr
  }
  if (isIE) {
    // IE 浏览器创建插件
    try {
      var plugin_embed = document.createElement('OBJECT')
      plugin_embed.setAttribute('id', 'npHebcaP11Plugin')
      plugin_embed.setAttribute('classid', 'CLSID:59B3BFD5-6CC5-4FFA-90E8-C1E5AFCB42E9')
      document.body.appendChild(plugin_embed)
	    			p11Ctrl = document.getElementById('npHebcaP11Plugin')

	    			npHebcaP11Plugin.Licence = 'amViY56Xmp2cnpeanZyel5qdnJ6Xmp2cYWhlYoQsftgudcLw21NcfvO8eN13ICbS'

	    			util = npHebcaP11Plugin.Util
	    			pkcs7 = npHebcaP11Plugin.CreatePkcs7()
    } catch (e) {
      throw Error('没有安装客户端软件或IE阻止其运行.')
    }
    		CertMgr = p11Ctrl
  }
  return p11Ctrl
}

// 连接WebSocket
function connect () {
  try {
    socket = new WebSocket(host)
    socket.onopen = function () {
      socket.send('0|' + new Date().getTime())
    }
    socket.onclose = function () {
      console.log('证书服务无法连接，请启动证书服务。')
    }
  } catch (exception) {
    alert('Error:' + exception)
  }
}

function Sign (content, callback) {
  if (isIE) {
    try {
      var signature = signCert.SignText(content, 1)
      callback(signature)
    } catch (e) {
      alert(e.message)
	  	}
  } else {
    var msgSend = '1|SignText|{"signText":"' + content + '"}'
    socket.send(msgSend)
    socket.onmessage = function (msg) {
      var args = msg.data.split('|')
      if (args[1] == 'SignText') {
        var json = eval('(' + args[2] + ')')
        if (json.ret == 0) {
          var signature = json.signature
          if (signature != '') {
            callback(signature)
          }
        } else {
          alert(json.msg)
        }
      }
    }
  }
}

function GetCertB64 (callback) {
  if (isIE) {
    try {
      var content = cert.GetCertB64()
      callback(content)
    } catch (e) {
      alert(e.message)
	  	}
  } else {
    var msgSend = '1|GetCertB64'
    socket.send(msgSend)
    socket.onmessage = function (msg) {
      var args = msg.data.split('|')
      if (args[1] == 'GetCertB64') {
        var json = eval('(' + args[2] + ')')
        if (json.ret == 0) {
          var cert = json.cert
          if (cert != '') {
            callback(cert)
          }
        } else {
          alert(json.msg)
        }
      }
    }
  }
}
// 谷歌浏览器部分结束

HebcaClient.prototype = {
  // 获取证书管理对象
  _GetClientCtrl: function () {
    if (this.clientCtrl) { return this.clientCtrl } else {
      var certMgrObj
      try {
        certMgrObj = new ActiveXObject('HebcaP11X.CertMgr')
      } catch (e) {
        throw Error('系统检查到您没有安装河北CA助手(请从www.hebca.com下载)或使用非IE内核浏览器(请使用IE浏览器)！')
      }
      certMgrObj.Licence = 'amViY55oZWKcZmhlnXxhaGViY2GXGmNjYWhcYgsECgYDTykqNzEiIilJKiEyJVchIk0gACAGCDo2IyAgRC0HIQQBNi9RIilJKgYDCwQxFgMrJDAwIJUggOgu64BMSVYIW7qucuBpjkZu'
      this.clientCtrl = certMgrObj
      return this.clientCtrl
    }
  },

  // 用签名证书对字符串source进行签名，source为待签名的文本，1为算法索引
  Sign: function (source) {
    var c = this._GetClientCtrl().SelectSignCert()
    return c.SignText(source, 1)
  },

  // 验证签名信息source为需要签名的文本数据 signature为签名后的文本数据 SignAlg 为签名算法索引
  VerifySign: function (source, signature, SignAlg) {
    	this._GetClientCtrl().SelectSignCert().VerifySignatureText(source, signature, SignAlg)
  },

  // 获取签名证书Base64格式字符串
  GetSignCert: function () {
    return this._GetClientCtrl().SelectSignCert().GetCertB64()
  },

  // 获取加密证书Base64格式字符串
  GetCryptCert: function () {
    return this._GetClientCtrl().SelectEncryptCert().GetCertB64()
  },

  // 向Key中写入文件
  // dataName：Key中数据的名称，应用自己命名，注意避免和其它应用重复就可以了
  // fileName:文件路径
  // isPrivate: 是否私有。如果设置为true，表示私有，则读取此数据时需要先登录Key。如设为false,则不需登录就可以读取
  WriteData: function (dataName, fileName, isPrivate) {
    	  // 读取文件,并转化为base64格式
    	  var mgr = this._GetClientCtrl()
    	  var fileDataB64 = mgr.Util.ReadFileBase64(fileName)

    	  var device = mgr.SelectDevice()
    	  device.WriteDataB64(dataName, fileDataB64, isPrivate)
  },

  // 从Key中读取数据
  ReadData: function (dataName) {
    	var mgr = this._GetClientCtrl()
    	var device = mgr.SelectDevice()
    	return device.ReadDataB64(dataName)
  },
  // 从Key中删除数据
  DeleteData: function (dataName) {
    	var mgr = this._GetClientCtrl()
    	var device = mgr.SelectDevice()
    	device.DeleteData(dataName)
  },

  // 存储临时文件
  WriteTempFile: function (fileName, base64Data) {
    var mgr = this._GetClientCtrl()
    // 获取系统临时文件夹
    var tmpPath = mgr.Util.GetFolderPath(5)
    var file = tmpPath + fileName
    mgr.Util.WriteFileBase64(file, base64Data)
    return file
  },
  /**
     * 遍历页面上的signed=true的元素,组织xml字符串，并签名，将签名结果以hidden形式写入提交表单
     * zyq 2015/05/20
     * @param arguments 支持传入1个及其以上参数，参数为页面元素id
     * 1个参数时：参数为提交表单的id，此参数必填
     * 1个及以上参数时：后续参数为待遍历元素的内的signed=true的id
     * @returns {Boolean} 签名是否成功
     */
  HebcaAutoSignDataForm: function () {
    	if (arguments.length == 0) {
    		throw Error('请指定提交的form表单的id！')
    		return false
    	}
    	var formid = arguments[0]
    	var arg = ','
    	for (var i = 1; i < arguments.length; i++) {
    		arg = arguments[i] + arg
    	}
    	arg = arg.substr(0, arg.length - 1)
    	var _Hebca_Original_Text = ''
    	var _Hebca_SignCert = ''
    	var _Hebca_SignData = ''
    	try {
    		// _Hebca_Original_Text = getOriginalText(getPageSignDataObj(arg));
    		_Hebca_Original_Text = $('#bk').text()
    		_Hebca_SignCert = _Hebca_certMgrObj.SelectSignCert()
    		_Hebca_SignData = _Hebca_SignCert.SignText(_Hebca_Original_Text, 1)
    		_Hebca_Original_Text = encodeURI(_Hebca_Original_Text)
    	} catch (e) {
    		throw Error(e.message)
    		return false
    	}
    	$('#' + formid).append("<input type = \"hidden\" id=\"_Hebca_Original_Text\" name = \"_Hebca_Original_Text\" value='" + _Hebca_Original_Text.replace(/\+/g, '%2B') + "'>")
    	$('#' + formid).append("<input type = \"hidden\" id=\"_Hebca_SignCert\" name = \"_Hebca_SignCert\" value='" + _Hebca_SignCert.GetCertB64() + "'>")
    	$('#' + formid).append("<input type = \"hidden\" id=\"_Hebca_SignData\" name = \"_Hebca_SignData\" value='" + _Hebca_SignData + "'>")
    	$('#' + formid).append('<input type = "hidden" id="_HebcaSignFlag" name = "_HebcaSignFlag" value="P1">')
    	return true
  },
  /**
     * 在父页面调用，对frame子页面的数据进行签名
     * zyq 2015/05/20
     * @param 必须指定frame的ID(参数1)及其frame页面form表单的id(参数2)
     */
  HebcaAutoSignDataFrameForm: function () {
    	if (arguments.length == 0) {
    		throw Error('请指定父页面IFrame的id！')
    		return false
    	}
    	if (arguments.length == 1) {
    		throw Error('请指定提交的form表单的id！')
    		return false
    	}
    	var frameid = arguments[0]
    	var formid = arguments[1]
    	var arg = ','
    	for (var i = 2; i < arguments.length; i++) {
    		arg = arguments[i] + arg
    	}
    	arg = arg.substr(0, arg.length - 1)
    	var _Hebca_Original_Text = ''
    	var _Hebca_SignCert = ''
    	var _Hebca_SignData = ''
    	try {
    		_Hebca_Original_Text = getOriginalText(getFrameSignDataObj(frameid, arg))
    		_Hebca_SignCert = _Hebca_certMgrObj.SelectSignCert()
    		_Hebca_SignData = _Hebca_SignCert.SignText(_Hebca_Original_Text, 1)
    	} catch (e) {
    		throw Error(e.message)
    		return false
    	}
    	$('#' + frameid).contents().find('#' + formid).append("<input type = \"hidden\" id=\"_Hebca_Original_Text\" name = \"_Hebca_Original_Text\" value='" + _Hebca_Original_Text.replace(/\+/g, '%2B') + "'>")
    	$('#' + frameid).contents().find('#' + formid).append("<input type = \"hidden\" id=\"_Hebca_SignCert\" name = \"_Hebca_SignCert\" value='" + _Hebca_SignCert.GetCertB64() + "'>")
    	$('#' + frameid).contents().find('#' + formid).append("<input type = \"hidden\" id=\"_Hebca_SignData\" name = \"_Hebca_SignData\" value='" + _Hebca_SignData + "'>")
    	$('#' + frameid).contents().find('#' + formid).append('<input type = "hidden" id="_HebcaSignFlag" name = "_HebcaSignFlag" value="P1">')
    	return true
  },
  /**
     * 此接口返回键值=值&键值=值形式字符串，用于Ajax等形式提交数据
     * zyq 2015/05/20
     * @param arguments 支持传入0个及其以上参数，参数为页面元素id
     * 0个参数时：遍历整个页面上的signed=true的元素
     * 1个及以上参数时：遍历传入元素的内的signed=true的元素
     * @returns {String} 返回签名原文、签名证书、签名值
     */
  HebcaAutoSignDataStr: function () {
    	try {
    		var _Hebca_Original_Text = getOriginalText(getPageSignDataObj(arguments))
    		var _Hebca_SignCert = _Hebca_certMgrObj.SelectSignCert()
    		var _Hebca_SignData = _Hebca_SignCert.SignText(xml, 1)
    		return '_Hebca_Original_Text=' + _Hebca_Original_Text.replace(/\+/g, '%2B') + '&_Hebca_SignCert=' + _Hebca_SignCert.GetCertB64() + '&_Hebca_SignData=' + _Hebca_SignData
    	} catch (e) {
    		throw Error(e.message)
    		return ''
    	}
  }
}

/**
 * 根据页面表单参数获取签名元素对象内容,
 * 支持遍历当前页面signed=true的元素，获取原文对象
 * zyq 2015/05/25
 * @returns 签名元素
 */
function getPageSignDataObj () {
  var baseSelector = '[signed=true],'
  // 默认选择器为基本选择器，选择所有的包含signed=true的元素
  var selector = arguments.length > 0 ? '' : baseSelector
  // 指定选择器时，选取选择器所有的包含signed=true的元素
  for (var i = 0; i < arguments.length; i++) {
    selector += arguments[i] + ' ' + baseSelector
  }
  selector = selector.substr(0, selector.length - 1)
  return $(selector)
}

/**
 * 根据页面表单参数获取签名元素对象内容,
 * 支持遍历IFrame等嵌套格式内容，获取原文对象
 * zyq 2015/05/25
 * @returns 签名元素
 */
function getFrameSignDataObj () {
  var baseSelector = '[signed=true],'
  // 默认选择器为基本选择器，选择所有的包含signed=true的元素
  var selector = arguments.length > 0 ? '' : baseSelector
  // 指定选择器时，选取选择器所有的包含signed=true的元素
  for (var i = 1; i < arguments.length; i++) {
    selector += arguments[i] + ' ' + baseSelector
  }
  var frameid = arguments[0]
  selector = selector.substr(0, selector.length - 1)

  return $('#' + frameid).contents().find('[signed=true]')
}

/**
 * 根据传入参数组织选择器，遍历符合条件元素返回xml字符串
 * 支持text、textarea、div、span、td、password、checkbox、radio、select
 * zyq 2015/05/20
 * @returns {String} 组织后原文xml字符串
 */
function getOriginalText (signDataObj) {
  var xml = ''
  // var info ="";
  // var info = "<chkInfo>";
  var dl = signDataObj.length
  if (dl < 1) {
    throw Error('没有监测到[signed=true]标记元素数据,请确认！')
    return ''
  }
  xml += '<dataList>'
  for (var i = 0; i < dl; i++) {
    var tag = _getTagData(signDataObj[i])
    if (tag == '') { continue }
    xml += tag
  }
  xml += '</dataList>'
  xml = '<?xml version="1.0" encoding="gb2312"?>' + xml
  return xml
}
/**
 * 根据获取到的签名数据jquery对象组织xml实体
 * 支持从input、div、span、select等获取带签名数据值
 * zyq 2015/05/20
 * @param o 符合signed=true的页面元素对象
 * @returns {String} xml字符串中的<data>对象实体
 */
function _getTagData (o) {
  // 判断提交表单中是否存在复选框或者单选框，如果存在时，且尚未选中时不组织数据。
  if (($(o)[0].type == 'checkbox' || $(o)[0].type == 'radio') && $(o)[0].checked == false) {
    return ''
  } else if ($(o)[0].type == undefined) {
    var tag = '<data>'
    // tag += "<value>"+HebcaXMLTrunChar(encode64(utf16to8($(o).text())))+"</value>";
    // tag += "<desc>"+HebcaXMLTrunChar(encode64(utf16to8($(o).attr("desc"))))+"</desc>";
    tag += '<desc>' + HebcaXMLTrunChar($(o).attr('desc')) + '</desc>'
    tag += '<name>' + $(o).attr('dataname') + '</name>'
    tag += '</data>'
    return tag
  } else if ($(o)[0].localName == 'input') {
    var tag = '<data>'
    tag += '<value>' + HebcaXMLTrunChar(HebcaXMLTrunChar(encode64(utf16to8($(o).val())))) + '</value>'
    // tag += "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
    tag += '<desc>' + encode64(utf16to8($(o).attr('desc'))) + '</desc>'
    tag += '<name>' + $(o).attr('name') + '</name>'
    tag += '</data>'
    return tag
  } else if ($(o)[0].type == 'textarea') {
    var tag = '<data>'
    tag += '<value>' + HebcaXMLTrunChar(encode64(utf16to8($(o).val()))) + '</value>'
    tag += '<desc>' + HebcaXMLTrunChar(encode64(utf16to8($(o).attr('desc')))) + '</desc>'
    tag += '<name>' + $(o).attr('name') + '</name>'
    tag += '</data>'
    return tag
  }
}

/**
 * 排序算法，用于对xml数据排序，默认为安装ASCII码排序
 * zyq 2015/05/20
 */
function sortAlgorithm () {

}

/**
 * 字符串转义，转义字符串为XML保留字符串包括：&、<、>、'、"五种
 * zyq 2015/05/20
 * @param str 字符串
 * @returns 转移后字符串
 */
function HebcaXMLTrunChar (str) {
  return str.replace(/\&/g, '&amp;').replace(/\>/g, '&gt;').replace(/\</g, '&lt;')
    .replace(/\'/g, '&apos;').replace(/\"/g, '&quot;')
}

/**
 * 将Ansi编码的字符串进行Base64编码
 * zyq 2015/05/20
 * @param input Base64编码前字符串
 * @returns {String} Base64编码后字符串
 */
function encode64 (input) {
  var keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='
  var output = ''
  var chr1; var chr2; var chr3 = ''
  var enc1; var enc2; var enc3; var enc4 = ''
  var i = 0
  do {
    chr1 = input.charCodeAt(i++)
    chr2 = input.charCodeAt(i++)
    chr3 = input.charCodeAt(i++)
    enc1 = chr1 >> 2
    enc2 = ((chr1 & 3) << 4) | (chr2 >> 4)
    enc3 = ((chr2 & 15) << 2) | (chr3 >> 6)
    enc4 = chr3 & 63
    if (isNaN(chr2)) {
      enc3 = enc4 = 64
    } else if (isNaN(chr3)) {
      enc4 = 64
    }
    output = output + keyStr.charAt(enc1) + keyStr.charAt(enc2) +
keyStr.charAt(enc3) + keyStr.charAt(enc4)
    chr1 = chr2 = chr3 = ''
    enc1 = enc2 = enc3 = enc4 = ''
  } while (i < input.length)
  return output
}
/**
 * 将Base64编码字符串转换成Ansi编码的字符串
 * zyq 2015/05/20
 * @param input Base64编码字符串
 * @returns {String} 字符串明文
 */
function decode64 (input) {
  var keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='
  var output = ''
  var chr1; var chr2; var chr3 = ''
  var enc1; var enc2; var enc3; var enc4 = ''
  var i = 0

  if (input.length % 4 != 0) {
    return ''
  }
  var base64test = /[^A-Za-z0-9\+\/\=]/g
  if (base64test.exec(input)) {
    return ''
  }
  do {
    enc1 = keyStr.indexOf(input.charAt(i++))
    enc2 = keyStr.indexOf(input.charAt(i++))
    enc3 = keyStr.indexOf(input.charAt(i++))
    enc4 = keyStr.indexOf(input.charAt(i++))
    chr1 = (enc1 << 2) | (enc2 >> 4)
    chr2 = ((enc2 & 15) << 4) | (enc3 >> 2)
    chr3 = ((enc3 & 3) << 6) | enc4

    output = output + String.fromCharCode(chr1)
    if (enc3 != 64) {
      output += String.fromCharCode(chr2)
    }
    if (enc4 != 64) {
      output += String.fromCharCode(chr3)
    }
    chr1 = chr2 = chr3 = ''
    enc1 = enc2 = enc3 = enc4 = ''
  } while (i < input.length)
  return output
}

/**
 * uft-16转为utf-8
 * zyq 2015/05/20
 * @param str utf-16编码字符串
 * @returns utf-8编码字符串
 */
function utf16to8 (str) {
  var out, i, len, c

  out = ''
  len = str.length
  for (i = 0; i < len; i++) {
    c = str.charCodeAt(i)
    if ((c >= 0x0001) && (c <= 0x007F)) {
      out += str.charAt(i)
    } else if (c > 0x07FF) {
      out += String.fromCharCode(0xE0 | ((c >> 12) & 0x0F))
      out += String.fromCharCode(0x80 | ((c >> 6) & 0x3F))
      out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F))
    } else {
      out += String.fromCharCode(0xC0 | ((c >> 6) & 0x1F))
      out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F))
    }
  }
  return out
}

/**
 * uft-8转为utf-16
 * zyq 2015/05/20
 * @param str utf-8编码字符串
 * @returns utf-16编码字符串
 */
function utf8to16 (str) {
  var out, i, len, c
  var char2, char3

  out = ''
  len = str.length
  i = 0
  while (i < len) {
    c = str.charCodeAt(i++)
    switch (c >> 4) {
      case 0: case 1: case 2: case 3: case 4: case 5: case 6: case 7:
        // 0xxxxxxx
        out += str.charAt(i - 1)
        break
      case 12: case 13:
        // 110x xxxx   10xx xxxx
        char2 = str.charCodeAt(i++)
        out += String.fromCharCode(((c & 0x1F) << 6) | (char2 & 0x3F))
        break
      case 14:
        // 1110 xxxx  10xx xxxx  10xx xxxx
        char2 = str.charCodeAt(i++)
        char3 = str.charCodeAt(i++)
        out += String.fromCharCode(((c & 0x0F) << 12) |
        ((char2 & 0x3F) << 6) |
        ((char3 & 0x3F) << 0))
        break
    }
  }
  return out
}

export default HebcaClient