<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-08-06 18:47:13
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-08-06 18:56:41
-->
<template>
  <el-dialog :visible.sync="visible" title="提示" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
  <div>

  </div>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      visible: false
    }
  },

  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {

      })
    }

  }
}
</script>
