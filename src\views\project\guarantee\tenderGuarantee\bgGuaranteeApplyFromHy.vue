<template>
  <div class="card">
    <!-- {{$route.query}} -->
    <el-card class="box-card">
      <!-- <steps :active='2'></steps> -->
      <el-card class="box-card" shadow="never">
        <!-- {{dataForm}} -->
        <hyForm ref="hyForm"></hyForm>
      </el-card>
    </el-card>
    <div class="foot">
      <div style="margin-bottom:10px;">
         <el-checkbox v-model="checked">我已阅读并同意 <el-button type="text" @click="showDia('xieyi')">服务协议</el-button></el-checkbox>
      </div>
      <span>
        <el-button type="primary" :disabled="!checked" @click="backStep()" plain>上一步</el-button>
        <!-- <el-button type="primary" :disabled="!checked" @click="dataFormSubmitHandle(0,dataForm)">保存</el-button> -->
        <!-- <el-button type="primary" :disabled="!checked" :loading="nextLoading" @click="applyPDF()" v-if="letterId !== ''">下一步</el-button> -->
        <el-button type="primary" :disabled="!checked" :loading="nextLoading" @click="dataFormSubmitHandle()" >提交</el-button>
      </span>
    </div>
    <xieyi v-if="visible" ref="xieyi"></xieyi>
    <tip v-if="visible" ref="tip"></tip>
  </div>
</template>
<script>
import steps from '@/views/project/guarantee/components/step'
import debounce from 'lodash/debounce'
import xieyi from '@/views/project/guarantee/components/xieyi'
import hyForm from '@/views/project/guarantee/tenderGuarantee/form/hyForm'

import tip from '@/views/project/guarantee/components/tip'
export default {
  data () {
    return {
      showSyrCom: false,
      checked: false,
      visible: false,
      nextLoading: false,
      formArr: [],
      insuranceInfo: {},
      // eslint-disable-next-line standard/array-bracket-even-spacing
      formReset: [
        'bgGuaranteeApply',
        'bginsurancebbr',
        'bginsurancesyr',
        'bgbiddingproject',
        'bginsuranceinfo',
        'bgguaranteeinvoice'
      ],
      dataForm: {},
      tbDate: '',
      payAmount: '', // 金额
      projectId: '', // 项目id
      serPriceId: '', // 服务定价id
      letterId: '', // 保函id
      guaranteeType: '', // 保函类型
      wtsStatus: 0 // 委托书状态 0：需要 1：不需要
    }
  },
  components: {
    // eslint-disable-next-line vue/no-unused-components
    steps,
    xieyi,
    hyForm,
    tip
  },
  created () {
  },
  activated () {

  },
  methods: {

    showDia (name) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs[name].init()
        this.$refs[name].letterId = this.letterId
      })
    },
    backStep () {
      this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
        item => item.name !== 'applyBgGuaranteeApplyFromFLoginHy'
      )
      this.$router.push({
        name: 'project-insuranceAfterLogin-chooiseIn'
      })
    },
    // 获取支付信息

    dataFormSubmitHandle: debounce(
      function () {
        this.$nextTick(async () => {
          var p = await this.$refs.hyForm.push()
          if (p) {
            this.nextLoading = true
            var { data } = await this.$http.post('letter/bgguaranteeletter/getHYApiParam', this.$refs.hyForm.dataForm)
            if (data.code !== 0) {
              return this.$message.error(data.msg)
            }
            this.nextLoading = false
            this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
              item => item.name !== 'applyBgGuaranteeApplyFromFLoginHy'
            )
            this.$router.push({
              name: 'bgguaranteeletterSqf'
            })
            window.open(data.data)
          }
        })
      },
      1000,
      { leading: true, trailing: false }
    )
  }
}
</script>
<style lang="scss" scoped>
.card {
  min-width: 900px;
  max-width: 1200px;
  margin: 15px auto;
  margin-bottom: 85px;
}
.foot {
  width: 100%;
  background: #fff;
  position: fixed;
  bottom: 0;
  padding: 10px 0;
  border-top: 1px solid #ddd;
  background: #f9f9f9;
  text-align: center;
  left: 0px !important;
  z-index: 999;
  padding-left: 210px;

  span {
    text-align: center;
  }
}
</style>
