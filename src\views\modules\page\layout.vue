<!--
 * @Descripttion:
 * @Author: kong<PERSON><PERSON>ang
 * @Date: 2021-07-07 11:24:02
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-10-27 10:36:57
-->
<template>
       <div  class="layout">
         <el-scrollbar
         class="hei"

            >
           <!-- <dzbh-header :active='activeIndex'></dzbh-header> -->
              <router-view></router-view>
          <!-- <router-view></router-view> -->
          <!-- <dzbh-foot v-if="$route.meta.hasFoot"></dzbh-foot> -->
          </el-scrollbar>
       </div>
</template>
<script>
// import dzbhHeader from './components/header'
// import dzbhFoot from './components/foot'
export default {
  // eslint-disable-next-line vue/no-unused-components
  // components: { dzbhHeader, dzbhFoot },
  data () {
    return {
      activeIndex: ''
    }
  },

  watch: {
    '$route' (to, from) {
      this.activeIndex = to.name
    }

  },
  mounted () {

  },
  activated () {
    this.activeIndex = this.$route.name
  },
  created () {
    //
    this.activeIndex = this.$route.name
  },
  methods: {

  }
}
</script>
<style lang="scss" scoped>
@media screen and (max-width: 1360px) {
  .hei{
    height: 125vh !important;
  }
}
  .hei{
    height: 100vh;
  }
.el-scrollbar__wrap {
    overflow-x: auto !important;
    margin-bottom: 0 !important;
}
</style>
