/*
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-02-01 14:33:36
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-02-03 09:35:59
 */
const rule = {
  contractNo: [
    {
      required: true,
      message: '必填项不能为空',
      trigger: 'blur'
    }
  ],
  guaranteeNo: [
    {
      required: true,
      message: '必填项不能为空',
      trigger: 'blur'
    }
  ],
  approvalNo: [
    {
      required: true,
      message: '必填项不能为空',
      trigger: 'blur'
    }
  ],
  auditOpinion: [
    {
      required: true,
      message: '必填项不能为空',
      trigger: 'blur'
    }
  ],
  fwbFirstInstance: [
    {
      required: true,
      message: '必填项不能为空',
      trigger: 'blur'
    }
  ],
  fwbFirstName: [
    {
      required: true,
      message: '必填项不能为空',
      trigger: 'blur'
    }
  ],
  fwbFirstDate: [
    {
      required: true,
      message: '必填项不能为空',
      trigger: 'blur'
    }
  ],
  fwbRepeatInstance: [
    {
      required: true,
      message: '必填项不能为空',
      trigger: 'blur'
    }
  ],
  fwbRepeatName: [
    {
      required: true,
      message: '必填项不能为空',
      trigger: 'blur'
    }
  ],
  fwbRepeatDate: [
    {
      required: true,
      message: '必填项不能为空',
      trigger: 'blur'
    }
  ],
  zgFirstInstance: [
    {
      required: true,
      message: '必填项不能为空',
      trigger: 'blur'
    }
  ],
  zgFirstName: [
    {
      required: true,
      message: '必填项不能为空',
      trigger: 'blur'
    }
  ],
  zgFirstDate: [
    {
      required: true,
      message: '必填项不能为空',
      trigger: 'blur'
    }
  ],
  zjlFirstName: [
    {
      required: true,
      message: '必填项不能为空',
      trigger: 'blur'
    }
  ],
  zjlFirstDate: [
    {
      required: true,
      message: '必填项不能为空',
      trigger: 'blur'
    }
  ],
  tempId: [
    {
      required: true,
      message: '必填项不能为空',
      trigger: 'blur'
    }
  ]
}
export { rule }
