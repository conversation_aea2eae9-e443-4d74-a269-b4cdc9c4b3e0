<template>
  <div>
    <el-dialog :visible.sync="visible" title="流程实例审核" class="finaDia" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
      <el-row :gutter="20">
        <el-form :model="dataForm" :rules="dataRule" ref="dataForm" :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
          <el-tabs type="border-card">
            <el-tab-pane label="审核意见">
              <el-form-item label="实例名称" prop="name">
                <el-input v-model="dataForm.name" disabled="disabled" placeholder="实例名称"></el-input>
              </el-form-item>
              <el-form-item label="审核类型" prop="auditType">
                <el-input v-model="dataForm.auditType" disabled="disabled" placeholder="审核类型"></el-input>
              </el-form-item>
              <el-form-item label="当前节点" prop="auditNodeName">
                <el-input v-model="dataForm.auditNodeName" disabled="disabled" placeholder="节点审核名称"></el-input>
              </el-form-item>
              <el-form-item label="审核结果" prop="auditStatus" size="mini">
                <el-radio-group @change="raChange" v-model="dataForm.auditStatus">
                  <el-radio :label="'1'">审核通过</el-radio>
                  <el-radio :label="'2'">审核退回</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="审核意见" prop="auditOpinion">
                <el-input v-model="dataForm.auditOpinion" placeholder="审核意见"></el-input>
              </el-form-item>
            </el-tab-pane>
            <el-tab-pane label="审核流程图">
              <div class="view">
                <div class="viewBox" v-for="(item,index) in auditNodeIdoptions" :key="item.id">
                  <div>
                    <div :class="acIndex>=index?'active viewItem':'viewItem'">
                      {{item.name}}
                    </div>
                    <div v-if="index !== auditNodeIdoptions.length-1"><img style="width: 60px;height: 40px;" src="../../../assets/img/j.png" alt=""></div>
                  </div>
                  <div class="dec">
                    <div>
                      审核人：{{item.auditRoleName}}

                    </div>
                  </div>

                </div>

              </div>
            </el-tab-pane>
            <el-tab-pane label="保函申请方注册信息" v-if="this.dataForm.tableName === 'bg_guarantee_apply'">
              <el-col :xs="24" :lg="24">
                <el-form-item label="机构名称" prop="name">
                  <!-- <el-input v-model="dataForm.instancesqf.name" disabled="disabled"  placeholder="名称"></el-input> -->
                  <span>{{dataForm.instancesqf.name}}</span>
                  <el-button type="text" size="mini" v-if="dataForm.instancesqf.annexId != null" @click="downloadHandle(dataForm.instancesqf.annexId)">营业执照</el-button>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :lg="colConfig">
                <!--                                <el-form-item label="编码" prop="code">-->
                <!--                                    &lt;!&ndash; <el-input v-model="dataForm.instancesqf.code"  disabled="disabled"  placeholder="编码"></el-input> &ndash;&gt;-->
                <!--                                    <span>{{dataForm.instancesqf.code}}</span>-->
                <!--                                </el-form-item>-->

                <el-form-item label="机构证件类型" prop="certType">
                  <!-- <el-input v-model="dataForm.instancesqf.certType" disabled="disabled"  placeholder="证件类型"></el-input> -->
                  <span>{{certTypeTTInfo(dataForm.instancesqf.certType)}}</span>
                </el-form-item>

                <el-form-item label="证件有效期类型" prop="certTermType">
                  <!-- <el-input v-model="dataForm.instancesqf.certTermType" disabled="disabled"  placeholder="证件有效期类型"></el-input> -->
                  <span>{{certTermTypeInfo(dataForm.instancesqf.certTermType)}}</span>
                </el-form-item>
                 <el-form-item label="法定代表人" prop="corporation">
                  <!-- <el-input v-model="dataForm.instancesqf.corporation" disabled="disabled"  placeholder="法定代表人"></el-input> -->
                  <span>{{dataForm.instancesqf.corporation}}</span>
                </el-form-item>
                <el-form-item label="联系人" prop="linkman">
                  <!-- <el-input v-model="dataForm.instancesqf.linkman" disabled="disabled"  placeholder="联系人"></el-input> -->
                  <span>{{dataForm.instancesqf.linkman}}</span>
                </el-form-item>

                 <el-form-item label="注册时间" prop="registerTime">
                  <!-- <el-input v-model="dataForm.instancesqf.registerTime" disabled="disabled"  placeholder="注册时间"></el-input> -->
                  <span>{{dataForm.instancesqf.registerTime}}</span>
                </el-form-item>
                 <el-form-item label="邮箱" prop="email">
                  <!-- <el-input v-model="dataForm.instancesqf.email" disabled="disabled"  placeholder="邮箱"></el-input> -->
                  <span>{{dataForm.instancesqf.email}}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :lg="colConfig">
                 <el-form-item label="机构证件号码" prop="certCode">
                  <!-- <el-input v-model="dataForm.instancesqf.certCode" disabled="disabled"  placeholder="证件号码"></el-input> -->
                  <span>{{dataForm.instancesqf.certCode}}</span>
                </el-form-item>
                 <el-form-item label="机构证件有效期" prop="certTerm">
                  <!-- <el-input v-model="dataForm.instancesqf.certTerm" disabled="disabled"  placeholder="证件有效期"></el-input> -->
                  <span>{{dataForm.instancesqf.certTerm}}</span>
                </el-form-item>
                 <el-form-item label="公司电话" prop="phoneNumber">
                  <!-- <el-input v-model="dataForm.instancesqf.phoneNumber" disabled="disabled"  placeholder="公司电话"></el-input> -->
                  <span>{{dataForm.instancesqf.phoneNumber}}</span>
                </el-form-item>
                <el-form-item label="联系人电话" prop="linkmanTel">
                  <!-- <el-input v-model="dataForm.instancesqf.linkmanTel" disabled="disabled"  placeholder="联系人电话"></el-input> -->
                  <span>{{dataForm.instancesqf.linkmanTel}}</span>
                </el-form-item>
                  <el-form-item label="注册资金" prop="registerCapital">
                  <!-- <el-input v-model="dataForm.instancesqf.registerCapital" disabled="disabled"  placeholder="注册资金"></el-input> -->
                  <span>{{dataForm.instancesqf.registerCapital}}</span>
                </el-form-item>

                <!--                                <el-form-item label="传真" prop="fax">-->
                <!--                                    &lt;!&ndash; <el-input v-model="dataForm.instancesqf.fax" disabled="disabled"  placeholder="传真"></el-input> &ndash;&gt;-->
                <!--                                    <span>{{dataForm.instancesqf.fax}}</span>-->
                <!--                                </el-form-item>-->
                <!--                                <el-form-item label="邮编" prop="postcode">-->
                <!--                                    &lt;!&ndash; <el-input v-model="dataForm.instancesqf.postcode" disabled="disabled"  placeholder="邮编"></el-input> &ndash;&gt;-->
                <!--                                    <span>{{dataForm.instancesqf.postcode}}</span>-->
                <!--                                </el-form-item>-->
                <!--                                <el-form-item label="所属平台编码" prop="platformCode">-->
                <!--                                    &lt;!&ndash; <el-input v-model="dataForm.instancesqf.platformCode" disabled="disabled"  placeholder="所属平台编码"></el-input> &ndash;&gt;-->
                <!--                                    <span>{{dataForm.instancesqf.platformCode}}</span>-->
                <!--                                </el-form-item>-->

                <!--                                <el-form-item label="注册地址" prop="registerAddress">-->
                <!--                                    &lt;!&ndash; <el-input v-model="dataForm.instancesqf.registerAddress" disabled="disabled"  placeholder="注册地址"></el-input> &ndash;&gt;-->
                <!--                                    <span>{{dataForm.instancesqf.registerAddress}}</span>-->
                <!--                                </el-form-item>-->

                <!--                                <el-form-item label="是否VIP" prop="isVip">-->
                <!--                                    &lt;!&ndash; <el-input v-model="dataForm.instancesqf.isVip" disabled="disabled"  placeholder=""></el-input> &ndash;&gt;-->
                <!--                                    <span>{{dataForm.instancesqf.isVip}}</span>-->
                <!--                                </el-form-item>-->
                <!-- <el-form-item label="经营范围" prop="description">
                                    <div class="des" v-html="dataForm.instancesqf.description">
                                    </div>
                                    &lt;!&ndash; <el-input v-model="dataForm.instancesqf.description" disabled="disabled"  placeholder="描述"></el-input> &ndash;&gt;
                                    &lt;!&ndash;                                    <span>{{dataForm.instancesqf.description}}</span>&ndash;&gt;
                                </el-form-item>-->
                                 <el-form-item label="地址" prop="address">
                  <!-- <el-input v-model="dataForm.instancesqf.address" disabled="disabled"  placeholder="地址"></el-input> -->
                  <span>{{dataForm.instancesqf.address}}</span>
                </el-form-item>
              </el-col>

            </el-tab-pane>
            <el-tab-pane label="保函出具方注册信息" v-if="this.dataForm.tableName === 'bg_guarantee_issue'">

              <el-col :xs="24" :lg="colConfig">
                <el-form-item label="机构名称" prop="name">
                  <!-- <el-input v-model="dataForm.name" placeholder="机构名称" ></el-input> -->
                  <span>{{dataForm.name}}</span>
                </el-form-item>
                <el-form-item label="法定代表人" prop="corporation">
                  <!-- <el-input v-model="dataForm.corporation" placeholder="法定代表人"></el-input> -->
                  <span>{{dataForm.instancecjf.corporation}}</span>
                </el-form-item>
                <el-form-item label="机构网站" prop="website">
                  <!-- <el-input v-model="dataForm.website" placeholder="机构网站"></el-input> -->
                  <span>{{dataForm.instancecjf.website}}</span>
                </el-form-item>
                <el-form-item label="邮箱" prop="email">
                  <!-- <el-input v-model="dataForm.instancecjf.email" placeholder="邮箱"></el-input> -->
                  <span>{{dataForm.instancecjf.email}}</span>
                </el-form-item>
                <el-form-item label="联系电话" prop="phoneNumber">
                  <!-- <el-input v-model="dataForm.instancecjf.phoneNumber" placeholder="联系电话"></el-input> -->
                  <span>{{dataForm.instancecjf.phoneNumber}}</span>
                </el-form-item>
                <el-form-item label="传真" prop="fax">
                  <!-- <el-input v-model="dataForm.instancecjf.fax" placeholder="传真"></el-input> -->
                  <span>{{dataForm.instancecjf.fax}}</span>
                </el-form-item>
                <!--                            <el-form-item label="LOGO地址" prop="logoUrl">-->
                <!--                                &lt;!&ndash; <el-input v-model="dataForm.instancecjf.logoUrl" placeholder="LOGO地址"></el-input> &ndash;&gt;-->
                <!--                                <span>{{dataForm.instancecjf.logoUrl}}</span>-->
                <!--                            </el-form-item>-->
                <el-form-item label="邮编" prop="postcode">
                  <!-- <el-input v-model="dataForm.instancecjf.postcode" placeholder="邮编"></el-input> -->
                  <span>{{dataForm.instancecjf.postcode}}</span>
                </el-form-item>
                <el-form-item label="地址" prop="address">
                  <!-- <el-input v-model="dataForm.instancecjf.address" placeholder="地址"></el-input> -->
                  <span>{{dataForm.instancecjf.address}}</span>
                </el-form-item>
                <el-form-item label="联系人" prop="linkman">
                  <!-- <el-input v-model="dataForm.instancecjf.linkman" placeholder="联系人"></el-input> -->
                  <span>{{dataForm.instancecjf.linkman}}</span>
                </el-form-item>
                <el-form-item label="联系人电话" prop="linkmanTel">
                  <!-- <el-input v-model="dataForm.instancecjf.linkmanTel" placeholder="联系人电话"></el-input> -->
                  <span>{{dataForm.instancecjf.linkmanTel}}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :lg="colConfig">
                <el-form-item label="担保金限制额" prop="guaranteeLimitAmount">
                  <!-- <el-input v-model="dataForm.instancecjf.guaranteeLimitAmount" placeholder="担保金限制额"></el-input> -->
                  <span>{{dataForm.instancecjf.guaranteeLimitAmount}}</span>
                </el-form-item>
                <el-form-item label="保函审批时长" prop="auditingHour">
                  <!-- <el-input v-model="dataForm.instancecjf.auditingHour" placeholder="保函审批时长"></el-input> -->
                  <span>{{dataForm.instancecjf.auditingHour}}</span>
                </el-form-item>
                <!--                            <el-form-item label="可开具保函编码" prop="guaranteeTypecodes">-->
                <!--                                &lt;!&ndash; <el-input v-model="dataForm.instancecjf.guaranteeTypecodes" placeholder="可开具保函编码"></el-input> &ndash;&gt;-->
                <!--                                <span>{{dataForm.instancecjf.guaranteeTypecodes}}</span>-->
                <!--                            </el-form-item>-->
                <el-form-item label="可开具保函" prop="guaranteeTypenames">
                  <!-- <el-input v-model="dataForm.instancecjf.guaranteeTypenames" placeholder="可开具保函"></el-input> -->
                  <span>{{dataForm.instancecjf.guaranteeTypenames}}</span>
                </el-form-item>
                <el-form-item label="开户行名称" prop="bankAccountName">
                  <!-- <el-input v-model="dataForm.instancecjf.bankAccountName" placeholder="开户行名称"></el-input> -->
                  <span>{{dataForm.instancecjf.bankAccountName}}</span>
                </el-form-item>
                <el-form-item label="银行账户号码" prop="bankAccountNo">
                  <!-- <el-input v-model="dataForm.instancecjf.bankAccountNo" placeholder="银行账户号码"></el-input> -->
                  <span>{{dataForm.instancecjf.bankAccountNo}}</span>
                </el-form-item>
                <el-form-item label="银行名称" prop="bankName">
                  <!-- <el-input v-model="dataForm.instancecjf.bankName" placeholder="银行名称"></el-input> -->
                  <span>{{dataForm.instancecjf.bankName}}</span>
                </el-form-item>
                <el-form-item label="银行编码" prop="bankNo">
                  <!-- <el-input v-model="dataForm.instancecjf.bankNo" placeholder="银行编码"></el-input> -->
                  <span>{{dataForm.instancecjf.bankNo}}</span>
                </el-form-item>

                <el-form-item label="排序" prop="orders">
                  <!-- <el-input v-model="dataForm.instancecjf.orders" placeholder="排序"></el-input> -->
                  <span>{{dataForm.instancecjf.orders}}</span>
                </el-form-item>
                <el-form-item label="是否收取平台使用费" prop="isIncludePlatformFee">
                  <!-- <el-input v-model="dataForm.instancecjf.isIncludePlatformFee" placeholder="是否收取平台使用费"></el-input> -->
                  <!-- <el-radio-group v-model="dataForm.instancecjf.isIncludePlatformFee" placeholder="是否收取平台使用费">
                                    <el-radio :label="1">是</el-radio>
                                    <el-radio :label="0">否</el-radio>
                                </el-radio-group> -->
                  <span>{{dataForm.instancecjf.isIncludePlatformFee == 1?"是":"否"}}</span>
                </el-form-item>
                <el-form-item label="机构描述" prop="description">
                  <div class="des" v-html="dataForm.instancecjf.description">
                  </div>
                  <!-- <el-input v-model="dataForm.instancecjf.description" placeholder="机构描述"></el-input> -->
                  <!--                                <span>{{dataForm.instancecjf.description}}</span>-->
                </el-form-item>
              </el-col>
            </el-tab-pane>
            <el-tab-pane label="保函使用方注册信息" v-if="this.dataForm.tableName === 'bg_guarantee_platform'">

              <el-col :xs="24" :lg="colConfig">
                <el-form-item label="名称" prop="name">
                  <!-- <el-input v-model="dataForm.instancesyf.name" placeholder="名称"></el-input> -->
                  <span>{{dataForm.instancesyf.name}}</span>
                </el-form-item>
                <el-form-item label="加密秘钥" prop="secretKey">
                  <!-- <el-input v-model="dataForm.instancesyf.secretKey" placeholder="加密秘钥"></el-input> -->
                  <span>{{dataForm.instancesyf.secretKey}}</span>
                </el-form-item>
                <el-form-item label="RSA私钥" prop="rsaPrikey">
                  <!-- <el-input v-model="dataForm.instancesyf.rsaPrikey" placeholder="RSA私钥"></el-input> -->
                  <span>{{dataForm.instancesyf.rsaPrikey}}</span>
                </el-form-item>
                <el-form-item label="RSA公钥" prop="rsaPubkey">
                  <!-- <el-input v-model="dataForm.instancesyf.rsaPubkey" placeholder="RSA公钥"></el-input> -->
                  <span>{{dataForm.instancesyf.rsaPubkey}}</span>
                </el-form-item>
                <el-form-item label="加密方式" prop="secretType">
                  <!-- <el-input v-model="dataForm.instancesyf.secretType" placeholder="加密方式"></el-input> -->
                  <span>{{secretTypeInfo(dataForm.instancesyf.secretType)}}</span>
                </el-form-item>
                <el-form-item label="平台网站" prop="website">
                  <!-- <el-input v-model="dataForm.instancesyf.website" placeholder="平台网站"></el-input> -->
                  <span>{{dataForm.instancesyf.website}}</span>
                </el-form-item>
                <el-form-item label="机构类型" prop="orgType">
                  <!-- <el-input v-model="dataForm.instancesyf.orgType" placeholder="机构类型 1 招投标交易平台 2其他"></el-input> -->
                  <span>{{platformTypeInfo(dataForm.instancesyf.orgType)}}</span>
                </el-form-item>
                <el-form-item label="法定代表人" prop="corporation">
                  <!-- <el-input v-model="dataForm.instancesyf.corporation" placeholder="法定代表人"></el-input> -->
                  <span>{{dataForm.instancesyf.corporation}}</span>
                </el-form-item>
                <el-form-item label="邮箱" prop="email">
                  <!-- <el-input v-model="dataForm.instancesyf.email" placeholder="邮箱"></el-input> -->
                  <span>{{dataForm.instancesyf.email}}</span>
                </el-form-item>
                <el-form-item label="联系电话" prop="phoneNumber">
                  <!-- <el-input v-model="dataForm.instancesyf.phoneNumber" placeholder="联系电话"></el-input> -->
                  <span>{{dataForm.instancesyf.phoneNumber}}</span>
                </el-form-item>
                <el-form-item label="传真" prop="fax">
                  <!-- <el-input v-model="dataForm.instancesyf.fax" placeholder="传真"></el-input> -->
                  <span>{{dataForm.instancesyf.fax}}</span>
                </el-form-item>
                <!--                            <el-form-item label="LOGO地址" prop="logoUrl">-->
                <!--                                &lt;!&ndash; <el-input v-model="dataForm.instancesyf.logoUrl" placeholder="LOGO地址"></el-input> &ndash;&gt;-->
                <!--                                <span>{{dataForm.instancesyf.logoUrl}}</span>-->
                <!--                            </el-form-item>-->
                <el-form-item label="邮编" prop="postcode">
                  <!-- <el-input v-model="dataForm.instancesyf.postcode" placeholder="邮编"></el-input> -->
                  <span>{{dataForm.instancesyf.postcode}}</span>
                </el-form-item>
                <el-form-item label="地址" prop="address">
                  <!-- <el-input v-model="dataForm.instancesyf.address" placeholder="地址"></el-input> -->
                  <span>{{dataForm.instancesyf.address}}</span>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :lg="colConfig">
                <el-form-item label="联系人" prop="linkman">
                  <!-- <el-input v-model="dataForm.instancesyf.linkman" placeholder="联系人"></el-input> -->
                  <span>{{dataForm.instancesyf.linkman}}</span>
                </el-form-item>
                <el-form-item label="联系人电话" prop="linkmanTel">
                  <!-- <el-input v-model="dataForm.instancesyf.linkmanTel" placeholder="联系人电话"></el-input> -->
                  <span>{{dataForm.instancesyf.linkmanTel}}</span>
                </el-form-item>
                <el-form-item label="担保金限制额" prop="guaranteeLimitAmount">
                  <!-- <el-input v-model="dataForm.instancesyf.guaranteeLimitAmount" placeholder="担保金限制额"></el-input> -->
                  <span>{{dataForm.instancesyf.guaranteeLimitAmount}}</span>
                </el-form-item>
                <!--                            <el-form-item label="可开具保函编码" prop="guaranteeTypecodes">-->
                <!--                                &lt;!&ndash; <el-input v-model="dataForm.instancesyf.guaranteeTypecodes" placeholder="可开具保函编码"></el-input> &ndash;&gt;-->
                <!--                                <span>{{dataForm.instancesyf.guaranteeTypecodes}}</span>-->
                <!--                            </el-form-item>-->
                <!--                            <el-form-item label="可开具保函" prop="guaranteeTypenames">-->
                <!--                                &lt;!&ndash; <el-input v-model="dataForm.instancesyf.guaranteeTypenames" placeholder="可开具保函"></el-input> &ndash;&gt;-->
                <!--                                <span>{{dataForm.instancesyf.guaranteeTypenames}}</span>-->
                <!--                            </el-form-item>-->
                <el-form-item label="开户行名称" prop="bankAccountName">
                  <!-- <el-input v-model="dataForm.instancesyf.bankAccountName" placeholder="开户行名称"></el-input> -->
                  <span>{{dataForm.instancesyf.bankAccountName}}</span>
                </el-form-item>
                <el-form-item label="银行账户号码" prop="bankAccountNo">
                  <!-- <el-input v-model="dataForm.instancesyf.bankAccountNo" placeholder="银行账户号码"></el-input> -->
                  <span>{{dataForm.instancesyf.bankAccountNo}}</span>
                </el-form-item>
                <el-form-item label="银行名称" prop="bankName">
                  <!-- <el-input v-model="dataForm.instancesyf.bankName" placeholder="银行名称"></el-input> -->
                  <span>{{dataForm.instancesyf.bankName}}</span>
                </el-form-item>
                <el-form-item label="银行编码" prop="bankNo">
                  <!-- <el-input v-model="dataForm.instancesyf.bankNo" placeholder="银行编码"></el-input> -->
                  <span>{{dataForm.instancesyf.bankNo}}</span>
                </el-form-item>
                <el-form-item label="是否收取平台使用费" prop="isIncludePlatformFee">
                  <!-- <el-input v-model="dataForm.instancesyf.isIncludePlatformFee" placeholder="是否收取平台使用费"></el-input> -->
                  <span>{{dataForm.instancecjf.isIncludePlatformFee == 1?"是":"否"}}</span>
                </el-form-item>

                <el-form-item label="机构描述" prop="description">
                  <div class="des" v-html="dataForm.instancesyf.description">
                  </div>
                </el-form-item>
              </el-col>
            </el-tab-pane>
            <el-tab-pane label="审核历史">
              <el-table :data="auditNodeResultoptions" border>
                <el-table-column prop="contractName" label="审核人" sortable="custom" header-align="center" align="center"></el-table-column>
                <el-table-column prop="auditDatetime" label="审核时间" sortable="custom" header-align="center" align="center"></el-table-column>
                <el-table-column prop="auditOpinion" label="审核意见" sortable="custom" header-align="center" align="center"></el-table-column>
                <el-table-column prop="auditStatus" label="节点审核状态" sortable="custom" header-align="center" align="center">
                  <template slot-scope="scope">
                    <el-tag v-if="scope.row.auditStatus == 0" size="mini" type="danger">未审核</el-tag>
                    <el-tag v-if="scope.row.auditStatus == 1" size="mini" type="danger">审核通过</el-tag>
                    <el-tag v-if="scope.row.auditStatus == 2" size="mini" type="danger">审核退回</el-tag>
                  </template>
                </el-table-column>
              </el-table>

            </el-tab-pane>
          </el-tabs>
        </el-form>
      </el-row>
      <template slot="footer">
        <el-button @click="visible=false">{{ $t('cancel') }}</el-button>
        <el-button type="primary" @click="dataFormPassHandle()">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      colConfig: 2, // 列配置 1 2列
      acIndex: null,
      visible: false,
      activeName: '基本信息', // 默认展开第一个
      selectVisible: false,
      mixinViewModuleOptions: {
        activatedIsNeed: true,
        getDataListURL: '/contract/bimauditinfo/page',
        getDataListIsPage: true
      },
      getDicListURL: '/sys/dict/type/',
      dataForm: {
        id: '',
        code: '',
        name: '',
        tableId: '',
        tableCode: '',
        tableName: '',
        auditType: '',
        auditNodeType: '',
        auditNodeName: '',
        auditNodeStatus: '',
        remarks: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        instancesqf: {
          id: '',
          code: '',
          name: '',
          certType: '',
          certCode: '',
          certTerm: '',
          certTermType: '',
          linkman: '',
          linkmanTel: '',
          corporation: '',
          email: '',
          phoneNumber: '',
          fax: '',
          postcode: '',
          platformCode: '',
          address: '',
          description: '',
          registerAddress: '',
          registerCapital: '',
          registerTime: '',
          isVip: '',
          deptId: '',
          status: 0,
          creator: '',
          createDate: '',
          updater: '',
          annexId: '',
          annexName: '',
          updateDate: ''
        },
        instancecjf: {
          id: '',
          code: '',
          name: '',
          corporation: '',
          website: '',
          orgType: '',
          email: '',
          phoneNumber: '',
          fax: '',
          logoUrl: '',
          postcode: '',
          address: '',
          linkman: '',
          linkmanTel: '',
          guaranteeLimitAmount: '',
          description: '',
          auditingHour: '',
          guaranteeTypecodes: '',
          guaranteeTypenames: '',
          bankAccountName: '',
          bankAccountNo: '',
          bankName: '',
          bankNo: '',
          isIncludePlatformFee: '',
          orders: '',
          deptId: '',
          status: 0,
          auditId: '',
          creator: '',
          createDate: '',
          updater: '',
          updateDate: '',
          isIssue: '0'
        },
        instancesyf: {
          id: '',
          code: '',
          name: '',
          secretKey: '',
          rsaPrikey: '',
          rsaPubkey: '',
          secretType: '',
          website: '',
          orgType: '',
          corporation: '',
          email: '',
          phoneNumber: '',
          fax: '',
          logoUrl: '',
          postcode: '',
          address: '',
          linkman: '',
          linkmanTel: '',
          guaranteeLimitAmount: '',
          description: '',
          guaranteeTypecodes: '',
          guaranteeTypenames: '',
          bankAccountName: '',
          bankAccountNo: '',
          bankName: '',
          bankNo: '',
          auditId: '',
          isIncludePlatformFee: '',
          orders: '',
          deptId: '',
          status: 1,
          creator: '',
          createDate: '',
          updater: '',
          updateDate: ''
        },
        certTermTypeOptions: '',
        certTermTypemaps: '',
        platformTypeOptions: '',
        platformTypemaps: [],
        secretTypeOptions: '',
        secretTypemaps: [],
        certTypeOptions: '',
        certTypemaps: '',
        auditStatus: '',
        auditOpinion: ''
      },
      getNodeListURL: '/demo/tbprocessnode/type/',
      getNodeResultListURL: '/demo/tbauditinfo/NodeResultList',
      getBgguaranteeapplyURL: '/letter/bgguaranteeapply/',
      getBgguaranteeissueURL: '/letter/bgguaranteeissue/',
      getBgguaranteeplatformURL: '/letter/bgguaranteeplatform/',
      auditNodeStatusoptions: [
        {
          value: '0',
          label: '申请未提交'
        },
        {
          value: '1',
          label: '未审核'
        },
        {
          value: '2',
          label: '审核完成'
        }
      ],
      auditNodeIdoptions: [],
      auditNodeResultoptions: []
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
    this.getCertTermTypeInfo()
    this.getCertTypeTTInfo()
    this.getPlatformTypeInfo()
    this.getSecretTypeInfo()
  },
  activated () {
    this.getDataList()
  },
  computed: {
    dataRule () {
      return {
        auditStatus: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ],
        auditOpinion: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        // this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
          if (this.dataForm.tableName === 'bg_guarantee_apply') {
            this.getInstance()
          } else if (this.dataForm.tableName === 'bg_guarantee_issue') {
            this.getInstance1()
          } else if (this.dataForm.tableName === 'bg_guarantee_platform') {
            this.getInstance2()
          }
          this.auditNodeIdoptions = []
          this.auditNodeResultoptions = []
          this.getAuditNodeResultInfo()
        }
      })
    },
    raChange (val) {
      // console.log(val)
      if (val === '1') {
        this.dataForm.auditOpinion = '通过'
      }
      if (val === '2') {
        this.dataForm.auditOpinion = ''
      }
    },
    certTermTypeInfo (value) {
      return this.certTermTypemaps[value]
    },
    certTypeTTInfo (value) {
      return this.certTypemaps[value]
    },
    platformTypeInfo (value) {
      return this.platformTypemaps[value]
    },
    secretTypeInfo (value) {
      return this.secretTypemaps[value]
    },
    // 获取使用方类型
    getPlatformTypeInfo () {
      this.$http
        .get(this.getDicListURL + 'platformType')
        .then(({ data: res }) => {
          this.platformTypeOptions = {
            ...this.platformTypeOptions,
            ...res.data.list
          }
          this.platformTypemaps = {
            ...this.platformTypemaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    },
    // 获取加密方式
    getSecretTypeInfo () {
      this.$http
        .get(this.getDicListURL + 'secretType')
        .then(({ data: res }) => {
          this.secretTypeOptions = {
            ...this.secretTypeOptions,
            ...res.data.list
          }
          this.secretTypemaps = {
            ...this.secretTypemaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    },
    // 证件有效期类型
    getCertTermTypeInfo () {
      this.$http
        .get(this.getDicListURL + 'certTermType')
        .then(({ data: res }) => {
          this.certTermTypeOptions = {
            ...this.certTermTypeOptions,
            ...res.data.list
          }
          this.certTermTypemaps = {
            ...this.certTermTypemaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    },
    // 证件类型
    getCertTypeTTInfo () {
      this.$http
        .get(this.getDicListURL + 'certTypeTT')
        .then(({ data: res }) => {
          this.certTypeOptions = {
            ...this.certTypeOptions,
            ...res.data.list
          }
          this.certTypemaps = {
            ...this.certTypemaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    },
    // 获取信息
    getInfo () {
      this.$http
        .get(`/demo/tbauditinstance/${this.dataForm.id}`)
        .then(({ data: res }) => {
          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
          this.getAuditNodeIdInfo()
        })
        .catch(() => {})
    },
    // 获取审核记录信息
    getAuditNodeResultInfo () {
      this.$http
        .get(this.getNodeResultListURL, {
          params: {
            businessCode: '',
            businessId: this.dataForm.id
          }
        })
        .then(({ data: res }) => {
          this.auditNodeResultoptions = [
            ...this.auditNodeResultoptions,
            ...res.data
          ]
        })
        .catch(() => {})
    },
    // 获取流程节点
    getAuditNodeIdInfo () {
      this.$http
        .get(this.getNodeListURL + 'register')
        .then(({ data: res }) => {
          this.auditNodeIdoptions = [
            ...this.auditNodeIdoptions,
            ...res.data.list
          ]
          this.auditNodeIdoptions.map((item, index) => {
            if (item.code === this.dataForm.auditNodeId) {
              this.acIndex = index
            }
          })
        })
        .catch(() => {})
    },
    // 获取流程实例信息 保函申请方信息
    getInstance () {
      this.$http
        .get(this.getBgguaranteeapplyURL + this.dataForm.tableId)
        .then(({ data: res }) => {
          this.dataForm.instancesqf = {
            ...this.dataForm.instancesqf,
            ...res.data
          }
          console.log(this.dataForm.instancesqf.annexName)
          console.log(this.dataForm.instancesqf.annexId)
        })
        .catch(() => {})
    },
    downloadHandle (id) {
      var url = `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${id}`
      window.location.href = url
    },
    // 获取流程实例信息保函出具方信息
    getInstance1 () {
      this.$http
        .get(this.getBgguaranteeissueURL + this.dataForm.tableId)
        .then(({ data: res }) => {
          this.dataForm.instancecjf = {
            ...this.dataForm.instancecjf,
            ...res.data
          }
        })
        .catch(() => {})
    },

    // 获取流程实例信息保函使用方信息
    getInstance2 () {
      this.$http
        .get(this.getBgguaranteeplatformURL + this.dataForm.tableId)
        .then(({ data: res }) => {
          this.dataForm.instancesyf = {
            ...this.dataForm.instancesyf,
            ...res.data
          }
        })
        .catch(() => {})
    },
    // 表单提交
    dataFormPassHandle: debounce(
      function () {
        this.$refs['dataForm'].validate(valid => {
          if (!valid) {
            return false
          }
          this.$http['post']('/demo/tbauditinstance/audit/', this.dataForm)
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            })
            .catch(() => {})
        })
      },
      1000,
      { leading: true, trailing: false }
    )
  }
}
</script>
<style scoped>
.finaDia .el-dialog .el-dialog__body {
  max-height: 60vh;
  height: auto !important;
  overflow-y: auto;
}

.view {
  display: flex;
  padding: 20px;
  overflow: auto;
}

.view .viewItem {
  width: 170px;
  min-width: 170px;
  height: 60px;
  padding: 10px;
  text-align: center;
  vertical-align: middle;
  display: table-cell !important;
  background: #f9f9f9;
  border-radius: 5px;
  border: 1px solid #e2e2e2;
}

.viewItem ~ div {
  vertical-align: middle;
  display: table-cell !important;
  height: 60px;
  padding: 10px 0;
}

.view .viewBox:first-child .viewItem,
.view .viewBox:last-child .viewItem {
  /* border-radius: 50%;
        width:60px ;
        min-width: 60px;
        height: 60px ; */
}

/* .view .viewBox:first-child .viewItem{
      border: 1px solid #67C23A;
    }
    .view .viewBox:last-child .viewItem{
       border: 1px solid #F56C6C;
    } */
.active {
  border: 1px solid #67c23a !important;
}

.dec div {
  padding: 15px 0;
}
</style>
