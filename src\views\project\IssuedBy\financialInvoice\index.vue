<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-letter__bgguaranteeletter}">
      <el-form :inline="true" :model="dataForm">
        <el-form-item>
          <el-input v-model="dataForm.applyName" clearable placeholder="申请方名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="dataForm.policyNo" clearable placeholder="保单号"></el-input>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="daterange"
            unlink-panels
            type="daterange"
            value-format="yyyy-MM-dd"
            :range-separator="$t('datePicker.range')"
            :start-placeholder="'发票申请开始日期'"
            :end-placeholder="'发票申请结束日期'"
            @change="change">
          </el-date-picker>
        </el-form-item>
        <!--   <el-form-item>
          <el-select v-model="dataForm.invoiceStatus" placeholder="请选择">
            <el-option  label="未出具" value="0">
            </el-option>
            <el-option  label="已出具" value="1">
            </el-option>
          </el-select>
        </el-form-item>-->

        <template v-if="searchShow">
          <el-form-item>
            <el-input v-model="dataForm.bbrName" clearable placeholder="被保人名称"></el-input>
          </el-form-item>
          <el-form-item>
            <el-input v-model="dataForm.issueName" clearable placeholder="出具方名称"></el-input>
          </el-form-item>
          <el-form-item>
            <el-input v-model="dataForm.platformName" clearable placeholder="使用方名称"></el-input>
          </el-form-item>
          <el-form-item>
            <el-select v-if="this.$route.name === 'letter-bgguaranteeletterCjf'" v-model="dataForm.letterStatus" clearable placeholder="请选择保函状态">
              <el-option v-for="item in letterStatusoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item>
            <el-select @click="getDataList(1)" v-model="dataForm.guaranteeType" clearable placeholder="请选择保函类型">
              <el-option v-for="item in guaranteeTypeoptions" :key="item.dictCode" :label="item.dictName" :value="item.dictCode">
              </el-option>
            </el-select>
          </el-form-item> -->
        </template>
        <el-form-item>
          <el-select v-model="dataForm.invoiceStatus" clearable placeholder="发票出具状态">
            <el-option label="未申请" value="0">
            </el-option>
            <el-option label="已出具" value="1">
            </el-option>
            <el-option label="已申请" value="2">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getDataList(1)">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="danger" @click="resetHandle()">重置</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('letter:bgguaranteeletter:export')" type="success" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <!-- <el-form-item>
           <el-button type="primary" @click="importHandle()">{{ $t('import') }}</el-button>
         </el-form-item> -->
      </el-form>
      <el-table
        v-loading="dataListLoading"
        :summary-method="getSummaries"
        :data="dataList"
        border
        style="width: 100%;"
        @selection-change="dataListSelectionChangeHandle"
        @sort-change="dataListSortChangeHandle">
        <el-table-column type="selection" fixed="left" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="applyName" label="申请方名称" sortable="custom" header-align="center" align="left" min-width="240">
          <template slot-scope="scope">
            <div class="title1">{{ scope.row.applyName }}</div>
            <div class="title2"><span class="t_span">出具方：</span>{{ scope.row.issueCode=='zcdb'&&scope.row.guaranteeType=='tbbhyh'?"建设银行":scope.row.issueName }}</div>
            <div class="title2"><span class="t_span">保函类型：</span>
              <el-tag size="mini"><span>{{ fomatMethod(scope.row.guaranteeType,1) }}</span>
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="项目名称" header-align="center" align="center" width="180">
          <el-table-column label="被保人名称" header-align="center" align="center" width="250">
            <template slot-scope="scope">
              <div>{{scope.row.projectName?scope.row.projectName:'-'}}</div>
              <div class="line"></div>
              <div>{{scope.row.bbrName?scope.row.bbrName:'-'}}</div>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column prop="letterStatus" label="保函状态" sortable="custom" header-align="center" align="center" width="125">
          <template slot-scope="scope">
            <el-tag size="mini" :type="statusColor(scope.row.letterStatus)"><span>{{fomatMethod(scope.row.letterStatus,2)}}</span>
            </el-tag>
          </template>
        </el-table-column> -->

        <el-table-column prop="letterStatus" label="发票状态" sortable="custom" header-align="center" align="center" width="125">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.invoiceStatus=='0'" size="mini" type="info">未申请</el-tag>
            <el-tag v-if="scope.row.invoiceStatus=='1'" size="mini" type="success">已出具</el-tag>
            <el-tag v-if="scope.row.invoiceStatus=='2'" size="mini" type="info">已申请</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="policyNo" label="保单号" sortable="custom" header-align="center" align="center" width="185"></el-table-column>
        <el-table-column prop="guaranteeAmount" label="担保金额（元）" header-align="right" align="right" width="120">
          <template slot-scope="scope">
            <div>{{ scope.row.guaranteeAmount }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="guaranteePrice" label="保函费用（元）" header-align="right" align="right" width="120">
          <template slot-scope="scope">
            <div>{{ scope.row.guaranteePrice }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="createDate" label="发票申请时间" sortable="custom" header-align="center" align="center" width="185"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button v-if="scope.row.letterStatus >= 10" type="text" size="small" @click="seeInfo(scope.row)">{{ scope.row.invoiceStatus=='1'?'重新出具发票':'出具发票' }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <issueInvoice v-if="uploadVisible" ref="issueInvoice" @refresh="getDataList"></issueInvoice>

    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
// import AddOrUpdate from './bgguaranteeletter-add-or-update'
import issueInvoice from '@/views/project/guarantee/common/detail/issueInvoice'

import moment from 'moment'
export default {
  components: {
    issueInvoice
    // bgguaranteeletterSqfSurrender,
    // bgguaranteeletterSqfStatus
  },
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgguaranteeinvoice/auditPage',
        getDataListIsPage: true,
        exportURL: '/letter/bgguaranteeletter/export',
        deleteURL: '/letter/bgguaranteeletter',
        enableURL: '/letter/bgguaranteeletter/enable',
        stopURL: '/letter/bgguaranteeletter/stop',
        deleteIsBatch: true
      },
      statusoptions: [
        {
          value: 1,
          label: '正常'
        },
        {
          value: 0,
          label: '停用'
        }
      ],
      searchShow: false,
      daterange: null,
      dataForm: {
        code: '',
        bbrName: '',
        applyName: '',
        letterStatus: '50',
        invoiceStatus: '',
        issueName: '',
        platformName: '',
        insuranceName: '',
        guaranteeType: '',
        policyNo: '',
        startDate: '',
        endDate: '',
        type: '2'
      },
      getDicListURL: '/sys/dict/type/',
      guaranteeTypemaps: '',
      guaranteeTypeoptions: [],
      letterStatusmaps: '',
      letterStatusoptions: [],
      orderField: 'create_date',
      order: 'desc',
      uploadVisible: false,
      moreActions: []
    }
  },
  watch: {
    daterange (val) {
      if (val[0]) {
        this.dataForm.startDate = val[0] + ' 00:00:00'
        this.dataForm.endDate = val[1] + ' 23:59:59'
      }
    }
  },
  created () {
    this.dataForm.code = this.$route.query.secretParams || ''
  },

  activated () {
    this.getGuaranteeTypeInfo()
    this.getLetterStatusInfo()
  },
  methods: {
    moment,
    change (val) {
      console.log(val)
      if (val) {
        this.$set(this.dataForm, 'startDate', val[0] + ' 00:00:00')
        this.$set(this.dataForm, 'endDate', val[1] + ' 23:59:59')
      } else {
        this.$set(this.dataForm, 'startDate', '')
        this.$set(this.dataForm, 'endDate', '')
      }
    },
    statusColor (code) {
      const dcode = Number(code)
      if (dcode < 30) {
        return 'info'
      } else if (dcode < 50) {
        return 'warning'
      } else if (dcode === 50) {
        return 'success'
      } else if (dcode < 100) {
        return 'danger'
      }
    },
    getSummaries (param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        const values = data.map((item) => Number(item[column.property]))
        if (
          !values.every((value) => isNaN(value)) &&
          (index === 6 || index === 7)
        ) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
          sums[index] += ' 元'
        } else {
          sums[index] = '-'
        }
      })
      return sums
    },

    seeStatus (id) {
      this.$router.push({
        name: 'awaitingAudit',
        query: {
          id: id
        }
      })
    },
    goCorrection (id, key, type) {
      this.$router.push({
        name: 'Correction',
        query: {
          id: id,
          key: key,
          type: type
        }
      })
    },
    bhStatusChange (id) {
      this.statusVisible = true
      this.$nextTick(() => {
        this.$refs.bgguaranteeletterSqfStatus.id = id
        this.$refs.bgguaranteeletterSqfStatus.init()
      })
    },
    getPayMentInfo (letterId, key) {
      // 获取信息
      return new Promise((resolve, reject) => {
        this.$http
          .get(
            `/letter/bgguaranteeletter/getPayUrl/` + letterId + `?key=${key}`
          )
          .then(({ data: res }) => {
            this.updateLoading = false

            resolve(res.data)
          })
          .catch((rej) => {
            reject(rej.msg)
          })
      })
    },
    surrender (id, insuranceId) {
      this.$http
        .get(`/letter/bginsuranceinfo/` + insuranceId)
        .then(({ data: res }) => {
          if (!moment(res.data.startDate).isBefore(new Date())) {
            this.surrenderVisible = true
            this.$nextTick(() => {
              this.$refs.bgguaranteeletterSqfSurrender.dataForm.id = id
              this.$refs.bgguaranteeletterSqfSurrender.startDate =
                res.data.startDate
              this.$refs.bgguaranteeletterSqfSurrender.init()
            })
          } else {
            this.$confirm('此保单已生效，不能退保', '提示', {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'warning'
            })
              .then(() => {})
              .catch(() => {})
          }
        })
        .catch((rej) => {})
    },
    fomatMethod (value, i) {
      if (i === 1) {
        return this.guaranteeTypemaps[value]
      }
      if (i === 2) {
        return this.letterStatusmaps[value]
      }
    },
    // 获取保函类型信息
    getGuaranteeTypeInfo () {
      this.$http
        .get(this.getDicListURL + 'guaranteeType')
        .then(({ data: res }) => {
          this.guaranteeTypeoptions = {
            ...this.guaranteeTypeoptions,
            ...res.data.list
          }
          this.guaranteeTypemaps = {
            ...this.guaranteeTypemaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    },
    // 获取当前状态信息
    getLetterStatusInfo () {
      this.$http
        .get(this.getDicListURL + 'letterStatus')
        .then(({ data: res }) => {
          this.letterStatusoptions = {
            ...this.letterStatusoptions,
            ...res.data.list
          }
          this.letterStatusmaps = {
            ...this.letterStatusmaps,
            ...res.data.map
          }
        })
        .catch(() => {})
    },
    seeInfo (data) {
      // this.$router.push({
      //   name: 'bgguaranteeletterDetail',
      //   query: {
      //     seeLetterId: `${id}`,
      //     type: type,
      //     JumpName: this.$route.name
      //   }
      // })
      this.uploadVisible = true
      this.$nextTick(() => {
        this.$refs.issueInvoice.dataForm = data
        this.$refs.issueInvoice.init()
      })
    },
    modifyJump (id, type, insuranceCode, insuranceName) {
      this.$router.push({
        name: 'bgguaranteeletterUpdate',
        params: {
          id: id,
          type: type,
          insuranceCode: insuranceCode,
          insuranceName: insuranceName
        }
      })
    },
    // 状态停用
    stopHandle (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: '请选择操作项',
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(
        this.$t('prompt.info', { handle: '停用' }),
        this.$t('prompt.title'),
        {
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          // 具体代码需微调格式去除空格
          this.$http
            .put(
              this.mixinViewModuleOptions.stopURL,
              id ? [id] : this.dataListSelections.map((item) => item.id)
            )
            .then(({ data: res }) => {
              this.$message({
                message: this.$t('prompt.success'),
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getDataList()
                }
              })
            })
            .catch(() => {})
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.title1 {
  font-weight: bold;
}
.line {
  display: block;
  height: 1px;
  width: 100%;
  margin: 8px 0;
  background-color: #dcdfe6;
}
.title2 {
  font-size: 13px;
  color: #999;
  .t_span {
    display: inline-block;
    width: 70px;
  }
}
</style>
