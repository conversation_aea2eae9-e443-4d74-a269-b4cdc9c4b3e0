<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-10-13 09:38:40
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-05-09 09:39:45
-->
<template>
  <span style="float:right">
    <el-button
      v-if="dataForm.letterStatus == 50&&(dataForm.bginsuranceinfo.insuranceType =='tbdbbh'||dataForm.bginsuranceinfo.insuranceType =='lydbbh'||dataForm.bginsuranceinfo.insuranceType =='zfdbbh'||dataForm.bginsuranceinfo.insuranceType =='yfkdbbh')"
      type="primary" size="small" @click="downloadLetter(dataForm.letterId,'01',dataForm.key)">下载保函</el-button>
    <el-button type="danger" size="small" @click="goPrveStep()">返回</el-button>
  </span>
</template>
<script>
export default {
  props: {
    dataForm: Object,
    options: Object,
    fileData: Array,
    draw: <PERSON>olean
  },
  data () {
    return {
      drawer: false
    }
  },
  watch: {
    draw (a) {
      this.drawer = a
    }
  },
  methods: {
    goPrveStep () {
      if (window.history.length > 1) {
        window.history.go(-1)
      } else {
        this.$router.push({
          path: 'letter-bgguaranteeletterSqf'
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.con-title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  // border-left: 6px solid rgb(241, 130, 65);
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-bottom: 10px;
}
</style>
