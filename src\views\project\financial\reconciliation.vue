<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2021-03-10 16:43:21
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-02-16 15:48:16
-->
<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="block">
        <el-date-picker
          v-model="time"
          type="month"
          size="small"
          value-format="yyyy-MM"
          placeholder="选择月">
        </el-date-picker>
        &nbsp;
        <el-button @click="search()" size="small" type="primary">{{ $t('query') }}</el-button>
        <el-divider></el-divider>
    </div>
    <keep-alive>
      <income type="CW" ref="income"></income>
    </keep-alive>
  </el-card>
</template>
<script>
// import mixinViewModule from '@/mixins/view-module'
import income from './statements/income.vue'
import moment from 'moment'

export default {
  // mixins: [mixinViewModule],
  components: {
    income
  },
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgguaranteeletter/pageAccountLetter',
        getDataListIsPage: true,
        exportURL: '/letter/bgguaranteeletter/accountDataExport',
        deleteIsBatch: true
      },
      currentRole: 'guarantee',
      dataList: {},
      typeList: [],
      time: moment().subtract(1, 'month').format('YYYY-MM'),
      dataForm: {
        date: '',
        guaranteeType: '0'
      },
      pickerOptions: {
        shortcuts: [{
          text: '本月',
          onClick (picker) {
            picker.$emit('pick', [new Date(), new Date()])
          }
        }, {
          text: '今年至今',
          onClick (picker) {
            const end = new Date()
            const start = new Date(new Date().getFullYear(), 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近六个月',
          onClick (picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  props: {
    iscompoent: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    search () {
      this.$nextTick(() => {
        this.$refs.income.dataForm.startDate = this.time
        this.$refs.income.dataForm.endDate = this.time
        this.$refs.income.getDataList()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.cati{
position: relative;
top:6px;
}
// 移动端自适应屏幕宽度：
@function pxToVw($num){
  //100vw:当前屏幕宽度
  //375:以屏幕宽度为375px为准
  //$num:需要设定的px值
  //计算：屏幕宽度100vw为375 $num:1 转换为1px（转换精度会有细微误差）
  @return 100vw / 375 * $num;
}
.class{
    width:pxToVw(100);
}
</style>
