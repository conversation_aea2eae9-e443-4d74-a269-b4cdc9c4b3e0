<template>
    <div>
        <el-dialog :visible.sync="visible" title="流程实例审核" class="finaDia"
                   :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
            <el-row :gutter="20">
                <el-form :model="dataForm" :rules="dataRule" ref="dataForm"
                         :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
                <el-tabs type="border-card">
                          <!--  <el-tab-pane label="审核意见">
                                <el-form-item label="实例名称" prop="name">
                                    <el-input v-model="dataForm.name" disabled="disabled"  placeholder="实例名称"></el-input>
                                </el-form-item>
                                <el-form-item label="审核类型" prop="auditType">
                                    <el-input v-model="dataForm.auditType" disabled="disabled" placeholder="审核类型"></el-input>
                                </el-form-item>
                                <el-form-item label="当前节点" prop="auditNodeName">
                                    <el-input v-model="dataForm.auditNodeName" disabled="disabled" placeholder="节点审核名称"></el-input>
                                </el-form-item>
                            </el-tab-pane>
                            <el-tab-pane label="审核流程图">
                                <div class="view">
                                    <div class="viewBox" v-for="(item,index) in auditNodeIdoptions" :key="item.id">
                                        <div>
                                            <div :class="acIndex>=index?'active viewItem':'viewItem'">
                                                {{item.name}}
                                            </div>
                                            <div v-if="index !== auditNodeIdoptions.length-1"><img style="width: 60px;height: 40px;" src="../../../assets/img/j.png" alt=""></div>
                                        </div>
                                        <div class="dec">
                                            <div>
                                                审核人：{{item.auditRoleName}}

                                            </div>
                                        </div>

                                    </div>

                                </div>
                            </el-tab-pane>-->
                        <el-tab-pane label="审核历史">
                            <el-table :data="auditNodeResultoptions" border>
                                <el-table-column prop="contractName" label="审核人" sortable="custom" header-align="center" align="center"></el-table-column>
                                <el-table-column prop="auditDatetime" label="审核时间" sortable="custom" header-align="center" align="center"></el-table-column>
                                <el-table-column prop="auditOpinion" label="审核意见" sortable="custom" header-align="center" align="center"></el-table-column>
                                <el-table-column prop="auditStatus" label="节点审核状态" sortable="custom" header-align="center" align="center">
                                <template slot-scope="scope">
                                    <el-tag v-if="scope.row.auditStatus == 0" size="mini" type="danger">未审核</el-tag>
                                    <el-tag v-if="scope.row.auditStatus == 1" size="mini" type="danger">审核通过</el-tag>
                                    <el-tag v-if="scope.row.auditStatus == 2" size="mini" type="danger">审核退回</el-tag>
                                </template>
                                </el-table-column>
                            </el-table>

                        </el-tab-pane>
                    </el-tabs>
                </el-form>
            </el-row>
            <template slot="footer">
                <el-button @click="visible=false">确认</el-button>
            </template>
        </el-dialog>
           </div>
</template>

<script>
export default {
  data () {
    return {
      colConfig: 2, // 列配置 1 2列
      acIndex: null,
      visible: false,
      activeName: '基本信息', // 默认展开第一个
      selectVisible: false,
      mixinViewModuleOptions: {
        activatedIsNeed: true,
        getDataListURL: '/contract/bimauditinfo/page',
        getDataListIsPage: true
      },
      dataForm: {
        id: '',
        code: '',
        name: '',
        tableId: '',
        tableCode: '',
        tableName: '',
        auditType: '',
        auditNodeType: '',
        auditNodeName: '',
        auditNodeStatus: '',
        remarks: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        auditStatus: '',
        auditOpinion: ''
      },
      getNodeListURL: '/demo/tbprocessnode/type/',
      getNodeResultListURL: '/demo/tbauditinfo/NodeResultList',
      getBgguaranteeapplyURL: '/letter/bgguaranteeapply/',
      auditNodeStatusoptions: [{
        value: '0',
        label: '申请未提交'
      }, {
        value: '1',
        label: '未审核'
      }, {
        value: '2',
        label: '审核完成'
      }],
      auditNodeIdoptions: [],
      auditNodeResultoptions: []
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  activated () {
    this.getDataList()
  },
  computed: {
    dataRule () {
      return {
        auditStatus: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        auditOpinion: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
          this.auditNodeIdoptions = []
          this.auditNodeResultoptions = []
          this.getAuditNodeResultInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/demo/tbauditinstance/${this.dataForm.id}`).then(({ data: res }) => {
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        this.getAuditNodeIdInfo()
      }).catch(() => {})
    },
    // 获取审核记录信息
    getAuditNodeResultInfo () {
      this.$http.get(
        this.getNodeResultListURL,
        {
          params: {
            'businessCode': '',
            'businessId': this.dataForm.id
          }
        }
      ).then(({ data: res }) => {
        this.auditNodeResultoptions = [
          ...this.auditNodeResultoptions,
          ...res.data
        ]
      }).catch(() => {
      })
    },
    // 获取流程节点
    getAuditNodeIdInfo () {
      this.$http.get(this.getNodeListURL + 'register').then(({ data: res }) => {
        this.auditNodeIdoptions = [
          ...this.auditNodeIdoptions,
          ...res.data.list
        ]
        this.auditNodeIdoptions.map((item, index) => {
          if (item.code === this.dataForm.auditNodeId) {
            this.acIndex = index
          }
        })
      }).catch(() => {
      })
    }
  }
}
</script>
<style scoped>
    .finaDia .el-dialog .el-dialog__body {
        max-height: 60vh;
        height: auto !important;
        overflow-y: auto;
    }

    .view {
        display: flex;
        padding: 20px;
        overflow: auto;
    }

    .view .viewItem {
        width: 170px;
        min-width: 170px;
        height: 60px;
        padding: 10px;
        text-align: center;
        vertical-align: middle;
        display: table-cell !important;
        background: #F9F9F9;
        border-radius: 5px;
        border: 1px solid #e2e2e2;
    }

    .viewItem ~ div {
        vertical-align: middle;
        display: table-cell !important;
        height: 60px;
        padding: 10px 0;
    }

    .view .viewBox:first-child .viewItem, .view .viewBox:last-child .viewItem {
        /* border-radius: 50%;
        width:60px ;
        min-width: 60px;
        height: 60px ; */
    }

    /* .view .viewBox:first-child .viewItem{
      border: 1px solid #67C23A;
    }
    .view .viewBox:last-child .viewItem{
       border: 1px solid #F56C6C;
    } */
    .active {
        border: 1px solid #67C23A !important;
    }

    .dec div {
        padding: 15px 0;
    }
</style>
