<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-02-01 14:50:27
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-08-20 15:39:09
-->
<template>
  <div>
    <div class="operation">
      <!-- <el-button @click="showSP">审批表预览</el-button> -->
      <!-- <el-button >附件预览</el-button> -->
      <el-button type="primary" v-if="dataForm.auditSort&&dataForm.auditSort!='0'" plain @click="showDia('auditRecordsDia')">审核记录</el-button>
      <el-button type="primary" v-if="Number(dataForm.letterStatus) === 50" @click="downloadLetter">下载保函</el-button>
      <el-button type="primary" v-if="Number(dataForm.letterStatus) === 50" @click="downloadLetterHT">下载委托合同</el-button>
      <!-- <el-button type="primary" plain v-if="Number(dataForm.letterStatus) === 50 &&Number(dataForm.invoiceStatus) === 0"
        @click="InvoiceAndMailShow( dataForm.letterId,dataForm.issueCode,dataForm.guaranteeTypeCode)">申请发票</el-button> -->
      <el-button type="primary" plain v-if="dataForm.defineGuarantee == 1&&((Number(dataForm.payStatus) === 40 ||
              Number(dataForm.payStatus) === 10))" @click="pay(dataForm.letterId,dataForm.issueCode)">支付</el-button>

      <!-- <el-button type="primary" plain v-if="Number(dataForm.letterStatus) === 50 &&Number(dataForm.invoiceStatus) === 1" @click="downloadInvoice( dataForm.letterId)">下载发票</el-button> -->
    </div>
    <auditRecordsDia v-if="visible" ref="auditRecordsDia" @refresh='refresh'></auditRecordsDia>
    <InvoiceAndMail v-if="addOrUpdateVisible" ref="InvoiceAndMail" @refresh='refresh' />

  </div>
</template>
<script>
import auditRecordsDia from './auditRecordsDia'
import InvoiceAndMail from '@/views/project/guarantee/common/InvoiceAndMail'
import { newWin } from '@/utils'
import { pay } from '@/views/project/guarantee/components/util'
export default {
  components: { auditRecordsDia, InvoiceAndMail },
  data () {
    return {
      visible: false,
      addOrUpdateVisible: false
    }
  },
  props: {
    dataForm: {
      type: Object
    }
  },
  mounted () {
    console.log(this.$store.state.user.auditProcedures)
  },
  methods: {
    pay (id, code) {
      pay(id, code)
    },
    refresh () {
      this.$emit('refresh')
    },
    async downloadInvoice (id) {
      this.$loading({
        lock: true,
        text: `下载请求中`
      })
      var data = await this.getDownloadInvoiceInfo(id)
      this.$loading().close()
      newWin(data)
    },
    getDownloadInvoiceInfo (id) {
      // 获取信息
      return new Promise((resolve, reject) => {
        this.$http
          .get(`/letter/bgguaranteeletter/ObtainInvoiceDownloadInfo/` + id)
          .then(({ data: res }) => {
            resolve(res.data)
          })
          .catch((rej) => {
            reject(rej.msg)
          })
      })
    },
    InvoiceAndMailShow (id, key, guaranteeType, applyName) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        if (guaranteeType === 'tbbhyh' && key === 'zcdb') {
          this.$refs.InvoiceAndMail.isZC = true
          this.$refs.InvoiceAndMail.key = key
        } else {
          this.$refs.InvoiceAndMail.isZC = false
          this.$refs.InvoiceAndMail.key = key
        }
        this.$refs.InvoiceAndMail.dataForm.guaranteeType = guaranteeType
        this.$refs.InvoiceAndMail.invoiceTitle = applyName
        this.$refs.InvoiceAndMail.dataForm.letterId = id
        this.$refs.InvoiceAndMail.init()
      })
    },
    downloadLetter (id, type, key) {
      this.$http
        .get(
          `/letter/bgguaranteeletter/obtainDownloadLetter/` +
            this.dataForm.letterId +
            '?type=01' +
            '&key=' +
            this.dataForm.issueCode
        )
        .then(({ data: res }) => {
          this.updateLoading = false
          if (res.data) {
            newWin(res.data)
            // setTimeout(window.open(res.data, '_blank'), 500)
          }
        })
        .catch((rej) => {})
    },
    downloadLetterHT () {
      this.$http
        .get(
          `/letter/bgletterlitigation/downloadContract/` +
            '?letterId=' +
            this.dataForm.letterId
        )
        .then(({ data: res }) => {
          this.updateLoading = false
          if (res.data) {
            newWin(res.data)
          }
        })
        .catch((rej) => {})
    },
    showDia (name, id) {
      this.visible = true
      this.$nextTick(() => {
        if (name === 'preview') {
          this.$refs[name].id = id
        } else {
          this.$refs[name].dataForm = this.dataForm
        }
        this.$refs[name].init()
      })
    }
  }
}
</script>
<style lang="scss">
.operation {
  margin-bottom: 15px;
  margin-top: 10px;
  text-align: right;
  padding-right: 30px;
}
</style>
