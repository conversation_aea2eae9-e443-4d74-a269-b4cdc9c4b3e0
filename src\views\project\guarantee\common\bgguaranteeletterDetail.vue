<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2020-08-24 13:56:46
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2022-05-10 11:00:13
-->
<template>
  <div class="detail" :style="draw?'width:50%;':''">
    <el-form :model="dataForm" v-if="JSON.stringify(dataForm) !== '{}'" label-position="left" ref="dataForm" label-width="145px">
      <el-card class="box-card " shadow="never" v-if="$route.query.taskId&&taskForm.taskName">
        <steps :active='taskIndex' :customList='customList'></steps>
      </el-card>
      <el-card class="box-card " shadow="never">
        <el-row :gutter="40">
          <el-col class="header" :span="24" style="margin-bottom:20px;">
            <!-- 头部 -->
            <span class="header-title">
              保函类型： {{dataForm.bginsuranceinfo.insuranceType?certType(dataForm.bginsuranceinfo.insuranceType,'insuranceTypeoptions'):''}}
            </span>
            <!-- 头部操作 -->
            <headOperationOp  v-if="$route.name=='openbidtimeDetail'" :options='this.options' @drawer='drawer' :draw='draw' :fileData='fileData' :dataForm='this.dataForm' @down='down'></headOperationOp>
            <headOperation v-else :options='this.options' @drawer='drawer' :draw='draw' :fileData='fileData' :dataForm='this.dataForm' @down='down' />
          </el-col>
          <!-- 投保人信息 -->
          <insureInfo :options='this.options' :draw='draw' :dataForm='this.dataForm' />
          <!-- 保函信息 -->
          <guaranteeInfo :options='this.options' :draw='draw' :dataForm='this.dataForm' />
          <thirdGuarantee  v-if="isLv(dataForm.guaranteeType)" :draw='draw' :dataForm='this.dataForm' />
          <!-- 状态 -->
          <statusOperation ref="statusOperation" :hisdata="hisdata" :taskForm='taskForm' :pgType='pgType()' :options='this.options' :draw='draw' :dataForm='this.dataForm' :fileData='fileData' @refresh='refresh' @showDiaSH='showDiaSH' @getGenerateGuaranteeModel='getGenerateGuaranteeModel'
            @showDia='showDia' />
        </el-row>
        <!-- {{$route.name}} -->
        <el-tabs v-model="activeName">
          <el-tab-pane label="详细信息" name="1"></el-tab-pane>
          <el-tab-pane label="附件信息" v-if="$route.name!='openbidtimeDetail'" name='2'></el-tab-pane>
        </el-tabs>
      </el-card>

      <div class="de-con" v-if="activeName==1">
        <el-card class="box-card" shadow="never">
          <div slot="header" class="clearfix">
            <span>项目信息</span>
          </div>
          <el-row>
            <el-col :span="draw?24:12">
              <el-form-item label="项目标段名称">
                {{fomate('bgbiddingproject','name')}}
              </el-form-item>
            </el-col>
            <el-col :span="draw?24:12">
              <el-form-item label="项目所属区域">
                {{fomate('bgbiddingproject','region')}}
              </el-form-item>
            </el-col>
            <el-col :span="draw?24:12">
              <el-form-item label="所属交易平台">
                招标通
              </el-form-item>
            </el-col>
            <projectInfo v-if="dataForm.bginsuranceinfo.insuranceType&&dataForm.bginsuranceinfo.insuranceType==='tbbxbh'" :draw='draw' :options='options' :dataForm='dataForm' />
            <projectinfoDB v-else ref="projectinfoDB" :hisdata='hisdata' :options='options' :dataForm='dataForm' :draw='draw' @down='down' />
            <!-- <template v-if="dataForm.bginsuranceinfo.insuranceType&&dataForm.bginsuranceinfo.insuranceType==='tbbxbh'">

            </template> -->

          </el-row>
        </el-card>

      </div>
      <div class="de-con" v-if="activeName==2">
        <fileInfo v-if="dataForm.bginsuranceinfo.insuranceType&&dataForm.bginsuranceinfo.insuranceType==='tbbxbh'" :ossId='dataForm.ossId'></fileInfo>
        <!-- <fileInfoDB v-else :fileData='fileData2' :letterStatus='dataForm.letterStatus'></fileInfoDB> -->
        <el-card shadow style="overflow:hidden;" v-else>
          <!-- dataForm.letterId == 50 -->
          <el-button v-show="dataForm.letterStatus == 50&&dataForm.key=='XTDB'" @click="downFiles" style="float:right;margin-bottom:15px;" type="primary" size="small"><i class="el-icon-download el-icon--left"></i>下载文件包</el-button>
          <uploadList  detail :type='1' :letterId='dataForm.letterId' :active='3' :dataForm='dataForm' :issueCode='dataForm.key' :guaranteeTypeCodes='dataForm.bginsuranceinfo.insuranceType'></uploadList>
        </el-card>
      </div>
    </el-form>
    <fileWin :draw="draw&&fileType==='file'" @close='close' :fileData='fileData2'></fileWin>
    <dzbh :draw="draw&&fileType==='bh'" @close='close' :dataForm='dataForm'></dzbh>
    <examineCAChrome v-if="visible" ref="examineCAChrome" @refresh='getInfo()'></examineCAChrome>
    <examine :dataForm='dataForm' v-if="visible" ref="examine" @refresh='refresh'></examine>
    <issueInvoice v-if="visible" ref="issueInvoice" @refresh='getInfo' :options='options' :dataForm='dataForm'></issueInvoice>
    <tip v-if="visible" ref="tip"></tip>
    <uploadGee  v-if="visible" ref="uploadGee" @refresh='refresh'></uploadGee>
  </div>
</template>
<script>
import dictionaries from '@/views/project/guarantee/components/dictionaries'
import projectInfo from '@/views/project/guarantee/common/detail/projectInfo/projectInfo'
import projectinfoDB from '@/views/project/guarantee/common/detail/projectInfo/projectinfoDB'
import examine from '@/views/project/guarantee/common/detail/examine'
import issueInvoice from '@/views/project/guarantee/common/detail/issueInvoice'
import examineCAChrome from '@/views/project/guarantee/common/detail/examineCAChrome'
import fileInfo from '@/views/project/guarantee/common/detail/fileInfo/fileInfo'
// import fileInfoDB from '@/views/project/guarantee/common/detail/fileInfo/fileInfoDB'
import tip from '@/views/project/guarantee/common/detail/tip'
import socketMixin from '@/mixins/socketMixin'
import fileWin from '@/views/project/guarantee/common/fileWin/index'
import dzbh from '@/views/project/guarantee/common/fileWin/dzbh'
import headOperation from '@/views/project/guarantee/common/detail/headOperation/index' // 头部操作
import headOperationOp from '@/views/project/guarantee/common/detail/headOperation/opIndex' // 头部操作
import statusOperation from '@/views/project/guarantee/common/detail/statusOperation/index'
import insureInfo from '@/views/project/guarantee/common/detail/insureInfo/index' // 投保人信息
import guaranteeInfo from '@/views/project/guarantee/common/detail/guaranteeInfo/index' // 保函信息
import thirdGuarantee from '@/views/project/guarantee/common/detail/thirdGuarantee/index' // 保函信息
import uploadList from '@/views/project/guarantee/warrantGuarantee/compoents/uploadList'
import steps from '@/views/project/guarantee/components/stepA'
import uploadGee from '@/views/project/guarantee/common/litDetail/uploadGee'

// import pdf from 'vue-pdf'
export default {
  mixins: [dictionaries, socketMixin],
  components: {
    projectInfo,
    projectinfoDB,
    examine,
    issueInvoice,
    examineCAChrome,
    fileInfo,
    uploadList,
    tip,
    fileWin,
    statusOperation,
    headOperationOp,
    headOperation,
    insureInfo,
    guaranteeInfo,
    dzbh,
    steps,
    uploadGee,
    thirdGuarantee
    // renProcessDetail
  },
  data () {
    return {
      draw: false,
      fileType: '',
      socketOptions: {
        isActive: true // 此页面是否在激活（进入）时，调用查询数据列表接口？
      },
      dataForm: {},
      customList: [],
      fileData: [],
      taskForm: {}, // 流程form
      taskIndex: null,
      fileData2: [],
      hisdata: [],
      jumpName: '',
      nullData: '',
      activeName: '1',
      tip: null,
      visible: false,
      btnLoading: false
    }
  },
  beforeRouteLeave (to, from, next) {
    // 即将跳转的路由地址
    console.log(to, from, next)
    this.$store.state.sidebarFold = false
    next()
  },
  computed: {
    isExamine () {
      return this.dataForm.signStatus === '20'
    }
  },
  created () {
    if (this.$route.name === 'bgguaranteeletterDetailZbt') {
      this.jumpName = this.$route.query.JumpName
      this.getInfo()
      this.getHis()
    }
    this.$nextTick(() => {
      console.log(this.$refs)
    })
  },
  activated () {
    this.jumpName = this.$route.query.JumpName
    this.getInfo()
    this.getHis()
    if (this.$route.query.taskId) {
      this.getTaskInfo()
    }
    this.draw = false
    this.bh = false
  },
  methods: {
    pgType () {
      // console.log(this.hisdata)
      if (this.hisdata) {
        return this.hisdata[this.hisdata.length - 1].correctType === '01' || this.hisdata[this.hisdata.length - 1].correctType === '08' || this.hisdata[this.hisdata.length - 1].correctType === '15'
      }
    },
    isLv (val) {
      const lv = ['lydbbh', 'lybhyh', 'zfdbbh', 'yfkdbbh']
      return lv.includes(val)
    },
    getTaskInfo () {
      this.$http.get(`/demo/tbYHSH/getProcessNodes?modelId=df68e1de-5bdf-11ec-9589-1263c8fdd9b5`).then(({ data: res }) => {
        let arr = []
        this.customList = res.map(a => {
          arr.push(a.name)
        })
        this.customList = arr
        this.$http.get(`/demo/tbYHSH/taskInfo/${this.$route.query.taskId}`).then(({ data: res }) => {
          this.taskForm = {
            ...this.taskForm,
            ...res.data
          }
          this.taskIndex = this.customList.findIndex(item => item === this.taskForm.taskName)
          // eslint-disable-next-line eqeqeq
          console.log(this.taskIndex, this.customList.findIndex(item => item == res.data.taskName), res.data.taskName)
        })
      })
    },
    drawer (val, type) {
      this.fileType = type
      this.draw = val
    },
    close () {
      this.draw = false
      this.$store.state.sidebarFold = false
    },
    statusColor (code) {
      let dcode = Number(code)
      if (dcode < 30) {
        return '#909399'
      } else if (dcode < 50) {
        return '#E6A23C'
      } else if (dcode < 60) {
        return '#67C23A'
      } else if (dcode < 100) {
        return '#F56C6C'
      }
    },
    websocketonopen () {
      let actions = {
        deptId: this.$store.state.user.deptId
      }
      this.websocketsend(JSON.stringify(actions))
    },
    websocketonmessage (e) {
      // 数据接收
      // '{msg:cjf}'
      const redata = JSON.parse(e.data)
      if (redata.type === 'cjf') {
        this.$store.state.navNum = redata.sum
      }
      // console.log(e.data)
    },
    open () {
      if (this.tip) {
        this.tip.close()
      }
      this.tip = this.$notify({
        // position: 'bottom-right',
        title: '消息通知',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        message: `<div>当前有新的待审核保函 <span style='color:red;'>${this.$store.state.navNum}</span> 条</div>`,
        duration: 10000
      })
    },
    down () {
      this.downloadInvoice(this.dataForm.letterId)
    },
    downFiles () {
      window.location.href = window.SITE_CONFIG['apiURL'] + `/letter/bgguaranteeletter/downloadZipFile?id=${this.$route.query.seeLetterId}`
    },
    getFileInfo () {
      this.$http
        .get(
          `letter/guarantee/getOssByLetterId?letterId=${this.$route.query.seeLetterId}`
        )
        .then(({ data: res }) => {
          this.fileData = res.data
          // eslint-disable-next-line eqeqeq
          if (this.dataForm.letterStatus !== '50') {
            // eslint-disable-next-line eqeqeq
            this.fileData2 = this.fileData.filter((a) => a.type != 5)
            console.log(this.fileData)
          } else {
            this.fileData2 = this.fileData
          }
          console.log(this.fileData)
          // this.getfile(this.fileData)
        })
    },
    showDia (name, bhid, wtht, dbht, issueCode, type) {
      this.visible = true
      this.$nextTick(() => {
        if (name === 'tip' || name === 'examineCAChrome') {
          this.$refs[name].dataForm = { ...this.dataForm, guaranteeTypeCode: this.dataForm.bginsuranceinfo.insuranceType }
          this.$refs[name].form.bhid = bhid
          this.$refs[name].form.wtht = wtht
          this.$refs[name].form.dbht = dbht
          this.$refs[name].type = type
          this.$refs[name].issueCode = issueCode
        }
        if (name === 'uploadGee') {
          this.$refs[name].dataForm = this.dataForm
        }
        this.$refs[name].init()
      })
    },
    showDiaSH (name, type) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs[name].init()
        this.$refs[name].type = type
        this.$refs[name].remark = this.dataForm.remark
        if (type === 'tb') {
          this.$refs[name].remark = this.dataForm.endorseText
        }
      })
    },
    getGenerateGuaranteeModel (name, letterId, type) {
      this.btnLoading = true
      this.$http
        .get('letter/guarantee/generateGuaranteeModel?letterId=' + letterId)
        .then(({ data: res }) => {
          this.btnLoading = false
          this.showDia(name, res.data.dzbh, type === 'pg' ? '' : res.data.wtht, res.data.dbht, this.dataForm.key, type)
        })
    },
    fomate (key, val) {
      return this.dataForm[key][val] ? this.dataForm[key][val] : '-'
    },
    refresh (type) {
      this.getInfo()
      this.getHis()
      this.getFileInfo()
      // this.initWebSocket()
      this.$nextTick(() => {
        // console.log(40, this.$route.query.taskId, !this.$route.query.taskId)
        if (type === 40 && !this.$route.query.taskId && !this.isLv(this.dataForm.guaranteeType)) {
          this.$refs['statusOperation'].getGenerateGuaranteeModel('examineCAChrome', this.dataForm.letterId)
        }
        if (this.$route.query.taskId) {
          this.$refs['projectinfoDB'].refresh()
          console.log('getTaskInfo')
          this.getTaskInfo()
        }
      })
      this.websocketonopen()
    },
    certType (val, name) {
      // console.log(this.options)
      var aa = this.options[name].filter((a) => a.dictCode === val)
      if (name === 'letterStatusoptions') {
        console.log(aa)
      }
      return aa.length > 0 ? aa[0].dictName : '-'
    },
    async downloadInvoice (id) {
      this.$loading({
        lock: true,
        text: `下载请求中`
      })
      var data = await this.getDownloadInvoiceInfo(id)
      console.log(data)
      // var downInfo = data
      this.$loading().close()
      window.open(data, '_blank')
    },
    getDownloadInvoiceInfo (id) {
      // 获取信息
      return new Promise((resolve, reject) => {
        this.$http
          .get(`/letter/bgguaranteeletter/ObtainInvoiceDownloadInfo/` + id)
          .then(({ data: res }) => {
            resolve(res.data)
          })
          .catch((rej) => {
            reject(rej.msg)
          })
      })
    },
    getHis () {
      this.dataListLoading = true
      let src = `letter/guarantee/getChangeListByLetterId?tableId=${this.$route.query.seeLetterId}`
      this.$http.get(src).then(({ data: res }) => {
        this.dataListLoading = false
        this.hisdata = res.data
      })
    },
    getInfo () {
      this.$loading({
        lock: true,
        text: `获取数据中...`
      })
      let src = `letter/bgguaranteeletter/getInfoById/${this.$route.query.seeLetterId}`
      this.$http.get(src).then(({ data: res }) => {
        this.$loading().close()

        // console.log(res)
        if (res.data) {
          this.dataForm = res.data
          console.log('dataForm:', this.dataForm)
          if (res.data.bginsuranceinfo.insuranceType !== 'tbbxbh') {
            this.getFileInfo()
          }
        } else {
          this.nullData = '暂无数据'
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.header {
  .header-title {
    display: block;
    float: left;
    margin-bottom: 0;
    padding-right: 12px;
    color: rgba(0, 0, 0, 0.75);
    border-left: 6px solid rgb(241, 130, 65);
    text-indent: 15px;
    font-weight: 600;
    font-size: 20px;
    background: rgba(241, 130, 65, 0.05);
    line-height: 45px;
  }
}
.detail {
  width: 100%;
  transition: width 0.3s ease-in-out;
  position: relative;
  .el-form-item {
    margin-bottom: 5px;
  }
  .el-tabs__header {
    padding: 0;
    position: relative;
    margin: 0 0;
    top: 19px;
  }
}

.de-con {
  margin: 15px;
}
.status {
  position: absolute;
  right: 0;
  top: 50px;
  text-align: right;
  padding-right: 30px !important;
  div:first-child {
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 10px;
  }
  .s_s {
    color: rgba(0, 0, 0, 0.85);
    font-size: 20px;
  }
}
.con-title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  // border-left: 6px solid rgb(241, 130, 65);
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-bottom: 10px;
}
</style>
