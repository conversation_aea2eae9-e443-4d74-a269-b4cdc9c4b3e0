<template >
  <div >
    <el-table size='small' :data="fileList" style="width: 100%;margin-bottom: 20px;" row-key="id" border default-expand-all :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
      <el-table-column prop="fileName" label="文件名称">
      </el-table-column>
      <el-table-column label="是否上传" width="120">
        <template slot-scope="scope">
          <div v-if="scope.row.isUploadSupported==1">
            <el-tag type="success" v-if="scope.row.ossEntity">已上传</el-tag>
            <el-tag type="danger" v-else>未上传</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="isRequired" label="是否必填" width="150">
        <template slot-scope="scope">
          <div v-if="scope.row.isUploadSupported==1">
            <span v-if="scope.row.isRequired">必填</span>
            <span type="danger" v-else>不必填</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="fileSuffix" label="文件类型" width="150">
      </el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="180">
        <template slot-scope="scope">
          <div v-if="scope.row.isUploadSupported==1&&!detail">
            <!-- ?showDia(scope.row.ossEntity.id,scope.row.fileName):miner(scope.row.ossEntity.id) -->
            <el-button type="text"  v-if="scope.row.ossEntity"  @click="drawShow(scope.row.fileType,scope.row.fileName)" >预览</el-button>
            <el-button type="text"  @click="importHandle(scope.row.fileType,scope.row)">{{(scope.row.isLegalSign == 1|| scope.row.isOfficialSign== 1 || scope.row.isWriteSign== 1)?'生成文件并上传':'上传'}}</el-button>
            <!-- {{scope.row}} -->
             <el-button type="text"  v-if="scope.row.ossEntity" @click="onPreview(scope.row.ossEntity.id)">下载</el-button>
            <!--<el-button type="text"  v-if="scope.row.ossEntity"  @click="deleteFile(scope.row.fileType)">删除</el-button> -->
          </div>
           <div v-if="detail">
            <el-button type="text"  v-if="scope.row.ossEntity"  @click="drawShow(scope.row.fileType,scope.row.fileName)" >预览</el-button>
            <el-button type="text"  v-if="scope.row.ossEntity" @click="onPreview(scope.row.ossEntity.id)">下载</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog class="preview CAmodel" title="预览" :visible.sync="imgVisible" :close-on-click-modal="false">
      <div slot='title'>
        <h3>预览</h3>
      </div>
      <div v-loading='loading'><img style="width:100%;" :src="img" alt=""></div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="imgVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
    <upload v-if="visible" :sh='sh' :letterId='letterId' ref="upload" @refreshDataList="getFileInfo" @emitCA='emitCA' @getModel='getModel'></upload>
    <caModel v-if="visible" ref="caModel" @reFresh='getFileInfo'></caModel>
    <preview v-if="visible" @refresh='visible=false' ref="preview"></preview>
    <previewFile ref="previewFile" v-if="draw"></previewFile>
  </div>

</template>
<script>
import upload from './upload'
import caModel from '@/views/project/guarantee/components/caModel'
import preview from '@/views/project/guarantee/common/detail/preview'
import previewFile from '@/views/project/guarantee/warrantGuarantee/compoents/previewFile'
import { newWin } from '@/utils'
export default {
  data () {
    return {
      fileList: [],
      fileData: [],
      visible: false,
      loading: false,
      img: '',
      draw: false,
      imgVisible: false
    }
  },
  props: {
    letterId: String,
    active: Number,
    dataForm: Object,
    serPriceId: String,
    issueCode: String,
    guaranteeTypeCodes: String,
    detail: Boolean,
    sh: {
      type: Boolean,
      default: false
    },
    type: Number
  },
  components: {
    upload,
    caModel,
    preview,
    previewFile
  },
  watch: {
    active (a) {
      console.log(a)
      if (a === 3) {
        this.getFileInfo()
      }
    },
    detail (a) {
      if (a) {
        this.getFileInfo()
      }
    }
  },
  mounted () {
    if (this.sh) {
      this.getFileInfo()
    }
    if (this.detail) {
      this.getFileInfo()
    }
  },
  // updated () {
  //   if (this.active === 3) {
  //     this.getFileInfo()
  //   }
  // },
  methods: {
    importHandle (fileType, dataForm) {
      console.log(dataForm)
      this.visible = true
      var params = { letterId: this.letterId, fileType: fileType }
      this.$nextTick(() => {
        this.$refs.upload.dataForm = dataForm
        this.$refs.upload.init(params)
      })
    },
    drawShow (fileType, name) {
      this.draw = true
      this.$nextTick(() => {
        this.$refs['previewFile'].params = {
          fileType: fileType,
          letterId: this.letterId
        }
        this.$refs['previewFile'].name = name
        this.$refs['previewFile'].init()
      })
    },
    close () {
      this.draw = false
    },
    filterType (file) {
      if (file) {
        var extension = file.substring(file.lastIndexOf('.') + 1)
        return extension === 'pdf' || extension === 'PDF'
      }
    },
    showDia (id, name) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['preview'].name = name
        this.$refs['preview'].id = id
        this.$refs['preview'].init()
      })
    },
    miner (id) {
      this.imgVisible = true
      this.loading = true
      // sys/oss/minioPreview?id=123
      this.img = `${window.SITE_CONFIG['apiURL']}/sys/oss/minioPreview?id=${id}&letterId=${this.letterId}`
      setTimeout(() => {
        this.loading = false
      }, 300)
      // this.img = res.data
    },
    emitCA (type) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['caModel'].dataForm = {}
        var obj = {
          ...this.dataForm,
          cjfChargeId: this.serPriceId,
          letterId: this.dataForm.letterId,
          guaranteeType: this.$route.params.type ? this.$route.params.type : this.dataForm.guaranteeType,
          key: this.$route.params.insuranceCode ? this.$route.params.insuranceCode : this.dataForm.issueCode
        }
        this.$refs['caModel'].dataForm = obj
        this.$refs['caModel'].type = type
        this.$refs['caModel'].init()
      })
    },
    onPreview (id) {
      window.open(
        `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${id}`
      )
    },
    getModel (type) {
      this.$http
        .get(`/letter/guarantee/generateModel`, {
          params: {
            letterId: this.dataForm.letterId,
            fileType: type
          }
        })
        .then(({ data: res }) => {
          newWin(`${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${res.data.fileId.id}`)
          // this.zongingList = res.data
        })
        .catch(() => {})
    },
    async deleteFile (type) {
      let { data } = await this.$http.get(
        'letter/guarantee/deleteBhFile?letterId=' +
          this.letterId +
          '&type=' +
          type
      )
      if (data.code !== 0) {

      }
      this.$message({
        type: 'success',
        message: '删除成功!'
      })
      this.getFileInfo()
    },
    getFileInfo () {
      this.$http
        .get(
          `/letter/bgIssueDocument/findFileIsUploaded?issueCode=${this.issueCode}&guaranteeTypeCodes=${this.guaranteeTypeCodes}&letterId=${this.letterId}&type=${this.type}`
        )
        .then(({ data: res }) => {
          this.loading = false

          this.fileList = res.data
          if (this.sh) {
            this.fileList = this.fileList.filter(a => a.fileType === '14000')
          }
          this.$emit('getFile', this.fileList)
        })
    }
  }
}
</script>
