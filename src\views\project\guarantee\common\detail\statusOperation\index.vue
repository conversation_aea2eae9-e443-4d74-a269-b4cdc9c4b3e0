<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-10-13 09:38:40
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-06-05 09:42:27
-->
<template>
<div style="display:inline-block;">
  <el-col class="status" style="width:300px;">
    <div>保函状态</div>
    <!-- {{hisdata}} -->
    <div class="s_s" :style="'color:'+statusColor(dataForm.letterStatus)" style="margin-bottom:15px;"> {{certType(dataForm.letterStatus,'letterStatusoptions')}}</div>
    <!-- <div class="s_s" v-if="dataForm.letterStatus == '41'" :style="'color:'+statusColor(dataForm.letterStatus)" style="margin-bottom:15px;"> 不通过原因：{{certType(dataForm.letterStatus,'letterStatusoptions')}}</div> -->
    <span style="padding-right:0;margin-bottom:15px;display:inline-block;"
      v-if="this.$route.query.JumpName=='project-IssuedBy-AuditGuarantee-index'&&(dataForm.letterStatus == 49||dataForm.letterStatus == 50||dataForm.letterStatus == 10001)">
      <el-button size="small" type="warning" icon="el-icon-document-delete" @click="showDiaSH('examine','th')">退回</el-button>
    </span>
    <span v-if="this.$route.query.JumpName=='project-IssuedBy-AuditGuarantee-index'&&(dataForm.letterStatus == 20||dataForm.letterStatus == 41)">
      <el-button size="small" type="warning" v-if="!$route.query.taskId" @click="showDiaSH('examine','bh')">审核</el-button>
      <el-button size="small" :loading="btnLoading" type="warning" v-if="$route.query.taskId && taskForm.taskName" icon="el-icon-s-check" @click="showDiaSH('examine','bh')">审核
      </el-button>
    </span>
    <!-- 履约，支付，预付款反担保审核前 -->
    <!-- <span v-if="this.$route.query.JumpName=='project-IssuedBy-AuditGuarantee-index'&&(dataForm.letterStatus == 20||dataForm.letterStatus == 41)&&isLv(dataForm.guaranteeType)">
      <el-button size="small" type="warning" style="margin-left:15px;" v-if="!$route.query.taskId" @click="showDiaS('thirdGuaranteeDia')">反担保</el-button>
    </span> -->
      <!-- 履约，支付，预付款反担保审核前 -->
      <span v-if="this.$route.query.JumpName=='project-IssuedBy-AuditGuarantee-index'&&(dataForm.letterStatus == 20||dataForm.letterStatus == 41)&&isLv(dataForm.guaranteeType)">
      <el-button size="small" type="warning" style="margin-left:15px;" v-if="!$route.query.taskId" @click="showDiaS('changePrice')">修改保费</el-button>
    </span>
    <!-- 审核保函 -->
    <span v-if="(this.$route.query.JumpName=='project-IssuedBy-AuditGuarantee-index')&&(dataForm.letterStatus == 49||dataForm.letterStatus == 50 ||dataForm.letterStatus == 10001)">
      <div v-if="!isExamine">
        <el-button style="margin-left:15px;" size="small" v-if="dataForm.key !='XTDB'&&dataForm.key !='ZC'" :loading="btnLoading" type="warning" icon="el-icon-s-check"
          @click="getGenerateGuaranteeModel(dataForm.guaranteeType == 'tbbhyh'?'uploadGee':'examineCAChrome', dataForm.letterId)">审核人员签章
        </el-button>
        <el-button style="margin-left:15px;" size="small" v-if="dataForm.key =='XTDB' && ($store.state.user.auditProcedures==5 || $store.state.user.auditProcedures==0)" :loading="btnLoading"
          type="warning" icon="el-icon-s-check" @click="getGenerateGuaranteeModel(dataForm.guaranteeType == 'tbbhyh'?'uploadGee':'examineCAChrome', dataForm.letterId)">审核人员签章
        </el-button>
        <el-button style="margin-left:15px;" size="small" v-if="dataForm.key =='ZC'" :loading="btnLoading"
          type="warning" icon="el-icon-s-check" @click="getGenerateGuaranteeModel(dataForm.guaranteeType == 'lybhyh'?'uploadGee':'examineCAChrome', dataForm.letterId)">{{dataForm.guaranteeType == 'lybhyh'?'保函出具':'审核人员签章'}}
        </el-button>
      </div>
      <template v-else>
        <div>
          <span style="color:#67C23A;"><i class="el-icon-success"></i>&nbsp;{{dataForm.signStatus=='20'?'审核人员已签章上传':'审核人员已手动上传'}}</span>
        </div>
        <div>
          <el-button :loading="btnLoading" @click="getGenerateGuaranteeModel(dataForm.guaranteeType == 'tbbhyh'||dataForm.guaranteeType == 'lybhyh'?'uploadGee':'examineCAChrome', dataForm.letterId)" type="text">重新签章</el-button>

        </div>
      </template>
    </span>

    <!-- 发票 -->
    <span style="float:right" v-if="this.$route.query.JumpName=='project-IssuedBy-financialInvoice-index'">
      <el-button size="small" :loading="btnLoading" type="warning" v-if="dataForm.invoiceStatus!='1'" icon="el-icon-s-check" @click="showDia('issueInvoice')">出具发票
      </el-button>
      <div v-else>
        <span style="color:#67C23A;"><i class="el-icon-success"></i>&nbsp;已出具发票</span>
        <div>
          <el-button :loading="btnLoading" @click="showDia('issueInvoice')" type="text">重新出具发票</el-button>
        </div>
      </div>

    </span>
    <!-- 线下支付审核 -->
    <span style="float:right" v-if="this.$route.query.JumpName=='project-IssuedBy-guaranteeMaintenance-index'">
      <div>线下支付状态</div>
      <div class="s_s Warning" v-if="dataForm.offlineAuditStatus=='10'">审核中</div>
      <div class="s_s Success" v-if="dataForm.offlineAuditStatus=='20'">审核通过</div>
      <div class="s_s Danger" v-if="dataForm.offlineAuditStatus=='30'">审核不通过</div>
      <el-divider></el-divider>
      <el-button size="small" :loading="btnLoading" type="warning" v-if="dataForm.offlineAuditStatus=='10'" icon="el-icon-s-check" @click="showDiaSH('examine','cw')">审核
      </el-button>
      <div v-if="dataForm.offlineAuditStatus=='20'">
        <span style="color:#67C23A;"><i class="el-icon-success"></i>&nbsp;线下支付已审核</span>
      </div>

    </span>
    <!-- 批改审核 -->
    <span style="float:right" v-if="this.$route.query.JumpName=='project-IssuedBy-correcting-index'">
      <el-button size="small" :loading="btnLoading" type="warning" v-if="dataForm.correctStatus==10" icon="el-icon-s-check" @click="showDiaSH('examine','pg')">审核
      </el-button>
      <!-- {{dataForm.correctStatus==20&&pgType&&isExamine}}{{dataForm.signStatus}} -->
      <span style="float:right" v-if="this.$route.query.JumpName=='project-IssuedBy-correcting-index'&&(dataForm.letterStatus == 40||dataForm.letterStatus == 50)">

        <el-button style="margin-left:15px;" size="small" v-if="dataForm.correctStatus==20&&pgType&&!isExamine" :loading="btnLoading" type="warning" icon="el-icon-s-check"
          @click="getGenerateGuaranteeModel('examineCAChrome', dataForm.letterId,'pg')">
          审核人员签章
        </el-button>
        <div v-if="dataForm.correctStatus==20&&pgType&&isExamine">
          <span style="color:#67C23A;"><i class="el-icon-success"></i>&nbsp;审核人员已签章</span>

        </div>
      </span>
    </span>
    <!-- 退保审核 -->
    <span style="float:right" v-if="this.$route.query.JumpName=='project-IssuedBy-surrenderAudit-index'">
      <!-- {{dataForm.surrenderStatus}} -->
      <el-button size="small" :loading="btnLoading" type="warning" v-if="dataForm.surrenderStatus==10" icon="el-icon-s-check" @click="showDiaSH('examine','tb')">审核
      </el-button>
      <!-- <span style="float:right" v-if="this.$route.query.JumpName=='project-IssuedBy-surrenderAudit-index'&&(dataForm.letterStatus == 40||dataForm.letterStatus == 50)">

         <div v-if="dataForm.refundStatus==20&&pgType">
          <span style="color:#67C23A;"><i class="el-icon-success"></i>&nbsp;审核人员已审核</span>

        </div>
      </span> -->
    </span>
  </el-col>
    <thirdGuaranteeDia v-if="visible" @refresh='refresh' ref="thirdGuaranteeDia"></thirdGuaranteeDia>
    <changePrice v-if="visible" @refresh='refresh' ref="changePrice"></changePrice>
</div>

</template>
<script>
import thirdGuaranteeDia from './thirdGuaranteeDia'
import changePrice from './changePrice'

export default {
  props: {
    dataForm: Object,
    options: Object,
    fileData: Array,
    pgType: Boolean,
    taskForm: Object,
    hisdata: Array
  },
  components: { thirdGuaranteeDia, changePrice },
  data () {
    return {
      btnLoading: false,
      visible: false,
      filePer: {
        zbwj: 1,
        wts: 1,
        cqdy: 1,
        wtht: 1,
        wtrfront: 0,
        wtrbehind: 0,
        frfront: 0,
        frbehind: 0
      }
    }
  },
  created () {
    this.filePermission()
  },
  computed: {
    isExamine () {
      return (
        this.dataForm.signStatus === '20' || this.dataForm.signStatus === '30'
      )
    }
  },
  methods: {
    filePermission () {
      this.$http
        .get(`/sys/params/getValue/${this.dataForm.key}`)
        .then(({ data: res }) => {
          this.filePer = {
            ...this.filePer,
            ...JSON.parse(res.data)
          }
          console.log(this.filePer)
        })
    },
    statusColor (code) {
      let dcode = Number(code)
      if (dcode < 30) {
        return '#909399'
      } else if (dcode < 50) {
        return '#E6A23C'
      } else if (dcode < 60) {
        return '#67C23A'
      } else if (dcode < 100) {
        return '#F56C6C'
      }
    },
    showDiaSH (a, b) {
      this.$emit('showDiaSH', a, b)
    },
    getGenerateGuaranteeModel (a, b, c) {
      this.$emit('getGenerateGuaranteeModel', a, b, c)
    },
    showDia (a, b, c) {
      this.$emit('showDia', a, b, c)
    },
    isLv (val) {
      const lv = ['lydbbh', 'lybhyh', 'zfdbbh', 'yfkdbbh']
      return lv.includes(val)
    },
    showDiaS (name) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs[name].dataForm = { ...this.dataForm.thirdGuaranteeDTO, ...{ letterId: this.dataForm.letterId } }
        this.$refs[name].init()
      })
    },
    refresh () {
      this.$emit('refresh')
    },
    certType (val, name) {
      // console.log(this.options)
      var aa = this.options[name].filter((a) => a.dictCode === val)
      if (name === 'letterStatusoptions') {
        console.log(aa)
      }
      return aa.length > 0 ? aa[0].dictName : '-'
    }
  }
}
</script>
<style lang="scss" scoped>
.status {
  position: absolute;
  right: 0;
  top: 60px;
  text-align: right;
  padding-right: 30px !important;
  div:first-child {
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 10px;
  }
  .s_s {
    color: rgba(0, 0, 0, 0.85);
    font-size: 20px;
  }
}
</style>
