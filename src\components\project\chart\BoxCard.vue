<template>
  <el-card class="box-card-component" style="margin-left:8px;">

    <div style="position:relative;">
      <div slot="header" class="clearfix">
        <span>每日进度统计</span>
      </div>
      <div slot="header" class="clearfix">
        <span></span>
      </div>
      <div style="padding-top:35px;" class="progress-item">
        <span>任务完成进度</span>
        <el-progress :percentage="BoxChartData.taskSchedule" />
      </div>
      <div class="progress-item">
        <span>代理抽取比例</span>
        <el-progress :percentage="BoxChartData.agentCheck" />
      </div>
      <div class="progress-item">
        <span>部门抽取比例</span>
        <el-progress :percentage="BoxChartData.deptCheck" />
      </div>
    </div>
  </el-card>
</template>

<script>
import { mapGetters } from 'vuex'
import { checkProportional } from '@/api/remote-search'
const BoxChartData = {
  taskSchedule: 50.78,
  agentCheck: 10,
  deptCheck: 90
}

export default {
  filters: {
    statusFilter (status) {
      const statusMap = {
        success: 'success',
        pending: 'danger'
      }
      return statusMap[status]
    }
  },
  data () {
    return {
      statisticsData: {
        article_count: 1024,
        pageviews_count: 1024
      },
      BoxChartData: BoxChartData
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.fetchData()
    })
  },
  computed: {
    ...mapGetters([
      'name',
      'avatar',
      'roles'
    ])
  },
  methods: {
    fetchData () {
      checkProportional().then(response => {
        this.BoxChartData = {
          ...this.BoxChartData,
          ...response.data.data
        }
      })
    }
  }
}
</script>

<style lang="scss" >
.box-card-component{
  .el-card__header {
    padding: 0px!important;
  }
}
</style>
<style lang="scss" scoped>
.box-card-component {
  .box-card-header {
    position: relative;
    height: 220px;
    img {
      width: 100%;
      height: 100%;
      transition: all 0.2s linear;
      &:hover {
        transform: scale(1.1, 1.1);
        filter: contrast(130%);
      }
    }
  }
  .mallki-text {
    position: absolute;
    top: 0px;
    right: 0px;
    font-size: 20px;
    font-weight: bold;
  }
  .panThumb {
    z-index: 100;
    height: 70px!important;
    width: 70px!important;
    position: absolute!important;
    top: -45px;
    left: 0px;
    border: 5px solid #ffffff;
    background-color: #fff;
    margin: auto;
    box-shadow: none!important;
    /deep/ .pan-info {
      box-shadow: none!important;
    }
  }
  .progress-item {
    margin-bottom: 10px;
    font-size: 14px;
  }
  @media only screen and (max-width: 1510px){
    .mallki-text{
      display: none;
    }
  }
}
</style>
