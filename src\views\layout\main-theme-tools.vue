<!--
 * @Descripttion:
 * @Author: k<PERSON>weiqiang
 * @Date: 2020-08-14 10:58:10
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-12-08 17:26:31
-->
<template>
  <div class="aui-theme-tools" style="font-size:15px;" :class="{ 'aui-theme-tools--open': isOpen }">
    <div class="aui-theme-tools__toggle_o newTool" @click="isOpen = !isOpen">
      <img src="@/assets/img/<EMAIL>" alt="">
      <el-tooltip content="left center" placement="left" effect="light" style="font-size:15px;">
        <!-- <div > <i class="el-icon-phone-outline"></i> </div> -->
        <div ><img src="@/assets/img/<EMAIL>" alt=""></div>

        <div slot="content" class="serve"> <i class="el-icon-phone"></i>&nbsp;<span>客服电话：</span><span>400-0311-616</span></div>
      </el-tooltip>
    </div>
    <!--  -->
    <div class="aui-theme-tools__content">
      <div class="aui-theme-tools__item">
        <h3>导航模式</h3>
        <el-checkbox v-model="$store.state.sidebarMode" @change="modeChnage" true-label="colorful">传统</el-checkbox>
      </div>
      <div class="aui-theme-tools__item">
        <h3>导航风格</h3>
        <div class="nav">
          <div class="nav-style">
            <img src="@/assets/img/topNav.png" alt="">
          </div>
          <div class="nav-style">
            <img src="@/assets/img/leftNav.png" alt="">
          </div>
        </div>

        <!-- <el-checkbox v-model="$store.state.navbarLayoutType" true-label="colorful">colorful 鲜艳</el-checkbox> -->
      </div>
      <!--<div class="aui-theme-tools__item">
        <h3>侧边导航</h3>
        <el-checkbox v-model="$store.state.sidebarLayoutSkin" true-label="dark">dark 黑色</el-checkbox>
      </div> -->
      <div class="aui-theme-tools__item">
        <h3>风格</h3>
        <el-radio-group v-model="themeColor" @change="themeColorChangeHandle">
          <el-radio v-for="item in themeList" :key="item.name" :label="item.name">{{ `${item.name} ${item.desc}` }}</el-radio>
        </el-radio-group>
      </div>
    </div>
  </div>
</template>

<script>
// import leftNav from '@/assets/img/leftNav.svg'
// import topNav from '@/assets/img/topNav.svg'

export default {
  data () {
    return {
      // leftNav: leftNav,
      // topNav: topNav,
      isOpen: false,
      themeList: require('@/element-ui/config.js'),
      themeColor: 'default'
    }
  },
  mounted () {
    this.themeColorChangeHandle(this.$store.state.navbarThemeColor)
  },
  methods: {
    modeChnage () {
      if (this.$store.state.sidebarMode) {
        this.$store.state.sidebarMenuList = window.SITE_CONFIG['menuList']
      } else {
        this.$store.state.sidebarMenuList =
          window.SITE_CONFIG['menuList'][0].children
        this.$store.state.navbarActive = 0
      }
    },
    themeColorChangeHandle (val) {
      //
      this.$store.state.navbarThemeColor = val
      var styleList = [
        {
          id: 'J_elementTheme',
          url: `${
            process.env.BASE_URL
          }element-theme/${val}/index.css?t=${new Date().getTime()}`
        },
        {
          id: 'J_auiTheme',
          url: `${
            process.env.BASE_URL
          }element-theme/${val}/aui.css?t=${new Date().getTime()}`
        }
      ]
      for (var i = 0; i < styleList.length; i++) {
        var el = document.querySelector(`#${styleList[i].id}`)
        if (el) {
          el.href = styleList[i].url
          continue
        }
        el = document.createElement('link')
        el.id = styleList[i].id
        el.href = styleList[i].url
        el.rel = 'stylesheet'
        document.querySelector('head').appendChild(el)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.nav {
  display: flex;
}
.nav-style {
  position: relative;
  margin-right: 16px;
  border-radius: 2px;
  cursor: pointer;
}
.newTool {
  width: 40px;
  height: 100px;
  top: 205px;
  background: #ffffff;
  border-radius: 6px;
  img{
    width: 20px;
    display: inline-block;
    cursor: pointer;
  }
  img:first-child{
    margin: 8px 0 12px;
  }
  img:last-child{
    margin: 12px 0 8px;
  }
}
</style>
