# hzjt-bim-ui

## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run serve
```

### Compiles and minifies for production
```
npm run build
```

### Lints and fixes files
```
npm run lint

安装
您需要提前在本地安装Node.js，使⽤Git克隆项⽬或者直接下载项⽬后，再通过 终端命令⾏ 执⾏以下命令。


如⽹络不稳定，安装时出错或进度过慢！请移步cnpm淘宝镜像进⾏安装。

cnpm i vue-highcharts -D
cnpm install echarts -D
cnpm install highcharts --save
cnpm i jsonp -S
cnpm yarn add jsonp -S
cnpm install moment --save

```
2019-08-20 淘宝源镜像切换
第一步： 删除node_modules
第二步： 执行以下命令npm配置淘宝源
npm config set registry https://registry.npm.taobao.org
第三步：执行以下命令
npm install 
npm run build:sit
