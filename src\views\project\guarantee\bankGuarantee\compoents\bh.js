/*
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-09-02 16:36:38
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-09-02 18:37:36
 */
import Vue from 'vue'
import http from '@/utils/request'
function getModel () {
  var str = window.location.href
  http.get('letter/bgguaranteetemplate/getInfoByIssueCodeAndType/' + str.match(/tbdbbh\/(\S*)\//)[1] + '/0').then(res => {
    var template = res.data.data.content
    const bh = {
      template: template,
      data () {
        return {

        }
      }
    }

    Vue.component('bh', bh)
  })
}
getModel()
