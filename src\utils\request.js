/*
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-09-27 18:16:30
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2022-05-24 15:11:35
 */
import axios from 'axios'
import Cookies from 'js-cookie'
import router from '@/router'
import qs from 'qs'
import { clearLoginInfo } from '@/utils'
import { Message } from 'element-ui'
import isPlainObject from 'lodash/isPlainObject'
const CancelToken = axios.CancelToken
let pending = {}
axios.defaults.withCredentials = true
const http = axios.create({
  baseURL: window.SITE_CONFIG['apiURL'],
  timeout: 1000 * 10,
  withCredentials: true
})

/**
 * 请求拦截
 */
function removePending (key, isRequest = false) {
  // 同一接口重复请求
  if (pending[key] && isRequest) {
    pending[key]('取消重复请求')
  }
  // 如果不是同一接口重复请求，则删除pending对象里该请求对应的key
  delete pending[key]
}

http.interceptors.request.use(config => {
  config.headers['Accept-Language'] = Cookies.get('language') || 'zh-CN'
  config.headers['token'] = Cookies.get('token') || ''
  // 默认参数
  var defaults = {}
  let key = config.url + '&' + config.method
  removePending(key, true)
  config.cancelToken = new CancelToken((c) => {
    pending[key] = c
    // 防止缓存，GET请求默认带_t参数
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        ...{ '_t': new Date().getTime() }
      }
    }
    if (isPlainObject(config.params)) {
      config.params = {
        ...defaults,
        ...config.params
      }
    }
    if (isPlainObject(config.data)) {
      config.data = {
        ...defaults,
        ...config.data
      }
      if (/^application\/x-www-form-urlencoded/.test(config.headers['content-type'])) {
        config.data = qs.stringify(config.data)
      }
    }
  })
  return config
}, error => {
  return Promise.reject(error)
})

/**
 * 响应拦截
 */
http.interceptors.response.use(response => {
  if (response.data.code === 100211) {
    Message.error('您的账号在异地登录，被迫下线了！')
    clearLoginInfo()
    router.replace({ name: 'login' })
    // return Promise.reject(response.data.msg)
  } else
  if (response.data.code === 401) {
    if (router.currentRoute.name !== 'login') {
      Message.error(response.data.msg)
    }
    clearLoginInfo()
    return router.replace({ name: 'login' })
  } else if (response.data.code === 10001) {
    Message.error(response.data.msg)
    clearLoginInfo()
    return router.replace({ name: 'login' })
  } else if (response.data.code !== 0 && response.data.code) {
    if (router.currentRoute.name !== 'login') {
      Message.error(response.data.msg)
    }
    Message.error(response.data.msg)
  }

  return response
}, error => {
  console.error(error)
  return Promise.reject(error)
})

export default http
