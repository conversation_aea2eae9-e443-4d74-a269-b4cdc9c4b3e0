<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON>qiang
 * @Date: 2020-08-26 09:43:09
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-04-25 10:36:13
-->
<template>
  <el-dialog :visible.sync="visible" class="uploadDia" append-to-body :title="`${dataForm.fileName}上传`" :close-on-click-modal="false" :close-on-press-escape="false">
    <!-- {{dataForm}} -->
    <div v-if="dataForm.isLegalSign == 1|| dataForm.isOfficialSign== 1 || dataForm.isWriteSign== 1">
      <el-form>
        <el-form-item label="上传方式">
          <el-radio-group v-model="radio">
            <el-radio v-if="showCa" :label="1">在线ca签章上传</el-radio>
            <el-radio :label="2">本地文件上传（请加盖公章）</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="CA上传" prop="wts" v-if="radio === 1&&showCa">
          <el-button size="small" @click="emitCA(dataForm.fileType)" type="primary">ca签章</el-button>
          <span class="el-upload__tip">&emsp;要求格式：{{ dataForm.fileSuffix }}</span>
          <div class="tips">
            <div class="tips-title">操作流程：</div>
            <div>1. 点击ca签章按钮</div>
            <div>2. 在弹窗内点击生成模板按钮生成对应文件模板</div>
            <div>3. 点击签章按钮完成签章后，关闭弹窗</div>
          </div>
        </el-form-item>
        <el-form-item label="文件上传" prop="wts" v-if="radio === 2">
          <el-button size="small" @click="getModel(dataForm.fileType)" type="primary">模板下载</el-button>
          <el-upload :action="url" :headers="myHeaders" :file-list="fileList" drag multiple :before-upload="beforeUploadHandle" :data='upData' :on-success="successHandle" class="text-center">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text" v-html="$t('upload.text')"></div>
            <div class="el-upload__tip" slot="tip">{{ $t('upload.tip', { 'format': dataForm.fileSuffix }) }}</div>
          </el-upload>
            <div class="tips">
            <div class="tips-title">操作流程：</div>
            <div>1. 点击模板下载按钮</div>
            <div>2. 下载好文件在打印机中打印纸质文件</div>
            <div>3. 在纸质文件上对应区域盖章</div>
            <div>4. 扫描盖章后文件上传到文件上传区域后关闭弹窗即可</div>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div v-else>
      <!-- <el-button type="primary" v-if="fileList.length>0" size="small" @click="allDown">批量下载压缩包</el-button> -->
      <el-upload :action="url" :limit='dataForm.isMultipleFilesUpload==1?10:1' :on-exceed="handleExceed" :headers="myHeaders" :on-preview='onPreview' :before-remove="beforeRemove"
        :file-list="fileList" drag multiple :before-upload="beforeUploadHandle" :data='upData' :on-success="successHandle" class="text-center">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text" v-html="$t('upload.text')"></div>
        <div class="el-upload__tip" slot="tip" v-if="dataForm.fileSuffix">{{ $t('upload.tip', { 'format': dataForm.fileSuffix }) }}</div>
      </el-upload>
    </div>

  </el-dialog>
</template>

<script>
import Cookies from 'js-cookie'
export default {
  data () {
    return {
      visible: false,
      url: '',
      num: 0,
      radio: 1,
      dataForm: {},
      fileType: '',
      upData: {},
      fileList: [],
      caList: ['1', '14000', '4', '1100'],
      showCa: true,
      myHeaders: {
        token: Cookies.get('token') || ''
      }
    }
  },
  props: {
    letterId: String,
    isCompont: {
      type: Boolean,
      default: false
    },
    sh: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dataForm: {
      handler (a) {
        // console.log(a)
        if (!this.caList.includes(a.fileType)) {
          this.showCa = false
          this.radio = 2
        } else {
          this.showCa = true
          this.radio = 1
        }
      },
      deep: true
    }
  },
  methods: {
    init (params) {
      this.upData = params
      this.visible = true
      this.url = this.isCompont
        ? window.SITE_CONFIG['apiURL'] +
          '/letter/bgguaranteeapply/registerFileupload'
        : window.SITE_CONFIG['apiURL'] + '/letter/guarantee/bhFileupload'
      // this.fileList = []
      this.getFileList()
    },
    allDown () {
      let arr = []
      this.fileList.map((a) => {
        arr.push(a.id ? a.id : a.response.data.ossId)
      })
      window.open(
        `${
          window.SITE_CONFIG['apiURL']
        }/sys/oss/downLoadZipFile?fileIds=${arr.join(',')}`
      )
    },
    getFileList () {
      console.log(this.letterId, this.dataForm.fileType)
      this.$http
        .get(
          `/letter/guarantee/getOssByFileType?letterId=${this.letterId}&fileType=${this.dataForm.fileType}`
        )
        .then(({ data: res }) => {
          console.log(res)
          this.fileList = res.data
        })
    },
    emitCA (fileType) {
      this.$emit('emitCA', fileType)
    },
    getModel (fileType) {
      this.$emit('getModel', fileType)
    },
    // 上传之前需要格式微调
    beforeUploadHandle (file) {
      if (this.dataForm.fileSuffix) {
        let types = this.dataForm.fileSuffix.split(',')
        let extension = file.name.substring(file.name.lastIndexOf('.') + 1)
        console.log(types, extension)
        if (!types.includes(extension)) {
          this.$message.error(
            this.$t('upload.tip', {
              format: this.dataForm.fileSuffix
            })
          )
          return false
        }
        this.num++
      }
    },
    beforeRemove (file, fileList) {
      console.log(file, fileList)
      if (file && file.status === 'success') {
        console.log(file)
        return this.$confirm(`确定移除 ${file.name}？`)
          .then(() => {
            this.handleRemovewt(file, fileList)
          })
          .catch(() => {
            // eslint-disable-next-line no-undef
            reject(false)
          })
      }
    },
    handleRemovewt (file, fileList) {
      console.log(file)
      if (file && file.status === 'success') {
        // 移除方法
        // var deleteId = file.id ? file.id : file.response.data.ossId
        this.deleteFilewt(
          this.letterId,
          file.type ? file.type : file.response.data.type,
          file.id ? file.id : file.response.data.ossId
        )
      }
    },
    handleExceed (files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件`
      )
    },
    deleteFilewt (id, type, annexId) {
      return new Promise(async (resolve, reject) => {
        let { data } = await this.$http.get(
          'letter/guarantee/deleteBhFile?letterId=' +
            id +
            '&type=' +
            type +
            '&annexId=' +
            annexId
        )
        if (data.code !== 0) {
          resolve(false)
        }
        this.getFileList()
        // type === 1 ? this.$emit('biddingDocumentId', '') : this.$emit('businessLicenseId', '')
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
        this.$emit('refreshDataList')
        resolve(true)
      })
    },
    onPreview (file) {
      if (file.id) {
        window.open(
          `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${file.id}`
        )
      } else {
        window.open(
          `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${file.response.data.ossId}`
        )
      }
    },
    // 上传成功需要微调格式去除空格
    successHandle (res, file, fileList) {
      if (res.code !== 0) {
        return
      }
      this.fileList = fileList
      console.log(this.fileList)
      this.$emit('refreshDataList')
      this.num--
      if (this.num === 0) {
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            // this.visible = false
            if (this.sh) {
              this.changeConfim()
              this.refresh()
              // this.$emit
            }
          }
        })
      }
    },
    async changeConfim () {
      let { data } = await this.$http.get(
        '/letter/bgLetterPerformance/updateContractConfirm?id=' + this.letterId
      )
      console.log(data)
    }
  }
}
</script>
<style lang="scss" scoped>
.tips {
  background: rgba(217, 236, 255, 0.9);
  padding: 10px 20px;
  color: #409eff;
  line-height: 24px;
  margin-top: 15px;
}
.uploadDia{
  /deep/ .el-dialog__body{
    max-height: 700px !important;
  }
}
</style>
