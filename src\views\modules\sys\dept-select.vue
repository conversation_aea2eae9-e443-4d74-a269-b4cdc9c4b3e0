<template>
  <div>
    <el-dialog
            title="机构选择"
            :visible.sync="dialogVisible"
            width="80%" :close-on-click-modal="false" :close-on-press-escape="false"
    >
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-sys__sysdept}">
      <el-form :inline="true" :model="dataForm" >
        <el-form-item>
          <el-input v-model="dataForm.name" clearable   placeholder="名称"></el-input>
        </el-form-item>
        <el-form-item >
          <el-select v-model="dataForm.deptType" clearable  placeholder="请选择机构类型">
            <el-option
                    v-for="item in deptTypeoptions"
                    :key="item.dictCode"
                    :label="item.dictName"
                    :value="item.dictCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="danger"   @click="getDataList(1)">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success"  @click="selectHandle()">选择</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" @row-dblclick="selectHandle" border @selection-change="dataListSelectionChangeHandle" @sort-change="dataListSortChangeHandle"   style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
       <!-- <el-table-column prop="code" label="编码" sortable="custom" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="name" label="名称" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="deptType" label="类型" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag  size="mini" type="danger"><span>{{fomatMethod(scope.row.deptType)}}</span></el-tag>
          </template>
        </el-table-column>
       <!-- <el-table-column prop="sort" label="排序" sortable="custom" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="status" sortable="custom" label="状态" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == 0" size="mini" type="danger">{{ $t('user.status0') }}</el-tag>
            <el-tag v-else size="mini" type="success">{{ $t('user.status1') }}</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
              :current-page="page"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="limit"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="pageSizeChangeHandle"
              @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
    </el-dialog>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './dept-add-or-update'
import Bus from '@/api/bus.js'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/sys/dept/page',
        getDataListIsPage: true,
        exportURL: '/sys/dept/export',
        deleteURL: '/sys/dept',
        enableURL: '/sys/dept/enable',
        stopURL: '/sys/dept/stop'
      },
      dialogVisible: false,
      statusoptions: [{
        value: 1,
        label: '正常'
      }, {
        value: 0,
        label: '停用'
      }],
      getDicListURL: '/sys/dict/type/',
      deptTypeoptions: [],
      deptTypemaps: '',
      dataForm: {
        code: '',
        name: '',
        status: 1
      },
      orderField: 'id',
      order: 'asc',
      uploadVisible: false
    }
  },
  activated () {
    this.getDeptTypeInfo()
  },
  components: {
    AddOrUpdate
  },
  methods: {
    init () {
      this.dialogVisible = true
      this.getDeptTypeInfo()
      this.getDataList()
    },
    selectHandle (row) {
      if (row) {
        Bus.$emit('dept', row.id + ',' + row.name)
      } else {
        if (!row && this.dataListSelections.length !== 1) {
          return this.$message({
            message: '请选择且只选择一个操作项',
            type: 'warning',
            duration: 500
          })
        }
        this.dataListSelections.map(item =>
          Bus.$emit('dept', item.id + ',' + item.name)
        )
      }
      this.dialogVisible = false
    },
    fomatMethod (value) {
      return this.deptTypemaps[value]
    },
    // 获取机构类型信息
    getDeptTypeInfo () {
      this.$http.get(this.getDicListURL + 'deptType').then(({ data: res }) => {
        this.deptTypeoptions = {
          ...this.deptTypeoptions,
          ...res.data.list
        }
        this.deptTypemaps = {
          ...this.deptTypemaps,
          ...res.data.map
        }
      }).catch(() => {})
    }
  }
}

</script>
