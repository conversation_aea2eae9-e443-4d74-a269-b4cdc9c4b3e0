<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2021-03-29 09:55:44
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-04-24 16:57:13
-->
<template>
  <el-dialog :close-on-click-modal="false" append-to-body :close-on-press-escape="false" :visible.sync="visible"
    width="800px">
    <div slot="title">
      <span class="el-dialog__title">项目信息</span>&emsp;
      <span style="color:#909399;">{{ title }}</span>
    </div>
    <el-form :model="dataForm" label-position="left" :rules="dataRule" ref="dataForm" label-width="140px">
      <el-form-item prop="id" hidden>
      </el-form-item>
      <el-form-item prop="regionId" hidden>
      </el-form-item>
      <el-form-item label="项目所属区域" prop="regionId">
        <!-- {{dataForm.regionId}} -->
        <el-cascader :options="regionIds" v-model.trim="dataForm.regionId" ref='region' @change='regionIdChange'
          size="medium" clearable class="wd180" style="width:250px;" :props="props">
        </el-cascader>&emsp;&emsp;<el-button type="text" v-if="dataForm.regionName" @click="casCilck = true">取消
        </el-button>
      </el-form-item>
      <el-form-item label="项目标段名称" v-if="dataForm.regionId" prop="name">
        <template>
          <el-button icon="el-icon-plus" size="mini" type="primary" @click="chose()">选择已有项目</el-button> <span
            style="margin-left:25px;color:rgba(0,0,0,.4);" class="el-upload__tip">选择已有项目自动带出该项目其他项目信息
            <font style="color:red;">
              (* 如项目不存在可手动填写)
            </font>
          </span>
        </template>
        <el-autocomplete :trigger-on-focus="false" class="inline-input wd180" @blur="selectBlur"
          @select="((item) => handleSelect(item))" clearable v-model.trim="dataForm.name" size="medium"
          style="width:100%;" placeholder="请输入要办理的项目及标段名称" :fetch-suggestions="querySearch">
          <template slot-scope="{ item }">
            <div class="name">{{ item.name }}</div>
            <!-- <span class="addr">{{ item.address }}</span> -->
          </template>
        </el-autocomplete>
        <!-- <el-input clearable v-model.trim="dataForm.name" @keyup.native="trimLR('name')" size="medium" class="wd180" style="width:100%;" placeholder="请输入要办理的项目及标段名称">
        </el-input> -->
        <el-col :span="24">
          <div style="color:red;font-size:13px;line-height:26px;">注：请严格按照招标文件填写项目名称！</div>
        </el-col>
      </el-form-item>
      <el-form-item label="担保金额" v-if="dataForm.regionId && code == 'tbdbbh'" prop="guaranteeAmount">
        <!-- {{dataForm.guaranteeAmountS}} -->
        <el-input-number placeholder="请输入担保金额" size="medium" style='width:180px;' v-model="guaranteeAmount"
          @change='change' controls-position="right" :precision="6" :step="0.000001" :min="0" :max="1000"> <template
            slot="append">万元</template></el-input-number> 万元
        <el-tooltip content="建设类担保金额最大80万元，交通类担保金额最大1000万元。" placement="bottom">
          <i class="el-icon-question" style="font-size:16px;color:#409EFF;"></i>
        </el-tooltip>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="showSure()">申请</el-button>
    </template>
    <choseProject v-if="diaDisible" ref="choseProject" @getProjectData='handleSelect'></choseProject>
    <chooiseCj v-if="diaDisible" @isSure='isSure' ref="chooiseCj"></chooiseCj>
    <sure v-if="diaDisible" @submit='submit' ref="sure"></sure>
  </el-dialog>
</template>
<script>
import choseProject from '@/views/project/guarantee/tenderGuarantee/form/compoents/choseProject'
import chooiseCj from './chooiseCj'
import sure from './sure'

export default {
  data () {
    return {
      visible: false,
      diaDisible: false,
      dataForm: {
        id: '',
        regionId: '',
        name: '',
        guaranteeAmount: ''
      },
      defaultForm: {},
      regionName: '',
      code: '',
      title: '',
      guaranteeAmount: '',
      restaurants: [],
      dataRule: {},
      regionIds: [],
      props: {
        value: 'code',
        label: 'name',
        children: 'child',
        isLeaf: 'leaf',
        lazy: true,
        lazyLoad: this.lazyLoad,
        collapseTags: true
      }
    }
  },
  components: { choseProject, chooiseCj, sure },
  watch: {
    dataForm: {
      handler (a) {
        if (a.guaranteeAmount || a.guaranteeAmount === 0) {
          this.guaranteeAmount = a.guaranteeAmount / 10000
        }
      },
      deep: true
    }
  },
  methods: {
    init () {
      this.visible = true
      // this.restaurants = this.loadAll()
      this.$nextTick(() => {
        this.regionIds = []
        this.regionName = ''
        this.$refs['dataForm'].resetFields()
      })
    },
    loadAll (data) {
      return data
    },
    querySearch (queryString, cb) {
      var restaurants = this.restaurants
      console.log(restaurants)
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilter (queryString) {
      return (restaurant) => {
        return (
          restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) === 0
        )
      }
    },
    change (val) {
      if (val) {
        this.$set(this.dataForm, 'guaranteeAmount', (val * 1000000) / 100)
      } else {
        this.$set(this.dataForm, 'guaranteeAmount', 0)
      }
    },
    handleSelect (item) {
      console.log(item)
      let { guaranteeAmount, name, id } = item
      this.defaultForm = item
      this.dataForm = {
        ...this.dataForm,
        guaranteeAmount,
        name,
        id
      }
    },
    selectBlur () {
      this.$http
        .get(
          `letter/bgbiddingproject/getInfoByNameAndRegionId?projectName=${this.dataForm.name}&regionId=${this.dataForm.regionId}`
        )
        .then(({ data: res }) => {
          // console.log(res)
          if (!res.data) {
            this.dataForm.guaranteeAmount = 0
            this.dataForm.id = ''
          }
        })
        .catch(() => { })
    },
    verification () {
      return new Promise((resolve, reject) => {
        this.$http
          .get(
            `letter/bgbiddingproject/projectJudgeType`, {
              params: {
                id: this.dataForm.id,
                projectName: this.dataForm.name,
                guaranteeType: this.code
              }
            }
          )
          .then(({ data: res }) => {
            console.log(res)
            resolve(res.data)
          })
          .catch(() => { })
      })
    },
    isSure (code, data, projectId) {
      this.$emit('isSure', code, data, projectId)
    },
    async showSure () {
      if (this.dataForm.id || this.dataForm.name) {
        if (!this.dataForm.guaranteeAmount && this.code === 'tbdbbh') {
          return this.$message.error('担保金额不能为0！')
        }
        let isIdentical = `${this.defaultForm.name}${this.defaultForm.name}` === `${this.dataForm.name}${this.dataForm.name}`
        if (!isIdentical) {
          this.dataForm.id = ''
        }
        // console.log(this.dataForm, this.defaultForm)
        let data = await this.verification()
        console.log(data)
        if (data.flag) {
          this.diaDisible = true
          this.$nextTick(() => {
            this.$refs['sure'].init()
            this.$refs['sure'].dataForm = { ...this.dataForm, ...{ regionName: this.regionName, title: this.title } }
          })
        } else {
          this.$confirm(data.msg, {
            confirmButtonText: '重新申请',
            type: 'warning'
          }).then(() => {
            this.visible = false
          })
        }
      } else {
        this.$message.warning('请选择或输入项目名称！')
      }
    },
    submit () {
      this.$http[!this.dataForm.id ? 'post' : 'put'](
        '/letter/bgbiddingproject',
        this.dataForm
      )
        .then(({ data: res }) => {
          // console.log(res)
          this.diaDisible = true
          this.$nextTick(async () => {
            // this.$refs['chooiseCj'].init()
            let data = await this.verification()
            if (data.flag) {
              this.$refs['chooiseCj'].title = this.title
              this.$refs['chooiseCj'].goC(
                this.code,
                res.data,
                this.dataForm.regionId
              )
            } else {
              this.$confirm(data.msg, {
                confirmButtonText: '重新申请',
                type: 'warning'
              }).then(() => {
                this.$refs['sure'].visible = false
                this.visible = false
              })
            }
          })
        })
        .catch(() => { })
    },
    trimLR (val) {
      if (this.dataForm[val]) {
        this.dataForm[val] = this.dataForm[val].replace(/\s+/g, '')
      }
    },
    regionIdChange (val) {
      console.log(val)
      this.$nextTick(() => {
        this.dataForm.id = ''
        this.regionName = Array.from(this.$refs.region.getCheckedNodes()[0].pathLabels).join(',')
        this.$set(this.dataForm, 'regionId', val.length > 0 ? val[val.length - 1] : '')
        this.$http
          .get(
            `/letter/bgbiddingproject/getInfoByRegionCode?regionCode=${this.dataForm.regionId}`
          )
          .then(({ data: res }) => {
            this.restaurants = res.data ? this.loadAll(res.data) : []
            console.log(this.restaurants)
          })
          .catch(() => { })
      })
    },
    chose () {
      this.diaDisible = true
      this.$nextTick(() => {
        this.$refs['choseProject'].dataForm.regionId = this.dataForm.regionId
        this.$refs['choseProject'].init()
      })
    },
    async lazyLoad (node, resolve) {
      // 点击节点
      console.log(this.code)
      const { level } = node
      let nodes = await this.lazyloadFn(level === 0 ? 100000 : node.value)
      console.log(nodes)
      let nodes2 = nodes.map((item) => ({
        code: item.code,
        name: item.name,
        leaf: this.code === 'tbbhyh' ? level > 0 : item.leaf === 0
      }))
      resolve(nodes2)
    },
    lazyloadFn (parentCode) {
      // 获取node
      return new Promise((resolve, reject) => {
        this.$http
          .get(`/demo/tbregion/regionTree?code=${parentCode}`)
          .then(({ data: res }) => {
            resolve(res.data)
          })
          .catch(() => { })
      })
    }
  }
}
</script>
