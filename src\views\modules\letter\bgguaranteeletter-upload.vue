<template>
    <el-dialog :visible.sync="visible" :title="$t('oss.upload')" :close-on-click-modal="false" :close-on-press-escape="false">
        <el-button type="primary" @click="downloadExcel()">下载模板</el-button>
        <el-upload
                :action="url"
                :file-list="fileList"
                drag
                multiple
                :before-upload="beforeUploadHandle"
                :on-success="successHandle"
                class="text-center">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text" v-html="$t('upload.text')"></div>
            <div class="el-upload__tip" slot="tip">{{ $t('upload.tip', { 'format': 'xls、xlsx' }) }}</div>
        </el-upload>
    </el-dialog>
</template>

<script>
import Cookies from 'js-cookie'
export default {
  data () {
    return {
      visible: false,
      url: '',
      num: 0,
      fileList: []
    }
  },
  methods: {
    init () {
      this.visible = true
      this.url = `${window.SITE_CONFIG['apiURL']}/letter/bgguaranteeletter/upload?token=${Cookies.get('token')}`
      this.num = 0
      this.fileList = []
    },
    // 上传之前需要格式微调
    beforeUploadHandle (file) {
      if (file.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' && file.type !== 'application/vnd.ms-excel') {
        if (file.type === '') {
          let extension = file.name.substring(file.name.lastIndexOf('.') + 1)
          if (extension !== 'xlsx' && extension !== 'xls') {
            this.$message.warning(this.$t('upload.tip', { 'format': 'xls、xlsx' }))
            return false
          }
        } else {
          this.$message.error(this.$t('upload.tip', { 'format': 'xls、xlsx' }))
          return false
        }
      }
      this.num++
    },
    // 上传成功需要微调格式去除空格
    successHandle (res, file, fileList) {
      if (res.data.success !== true) {
        return this.$message.error(res.data.msg)
      }
      this.fileList = fileList
      this.num--
      if (this.num === 0) {
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            this.visible = false
            this.$emit('refreshDataList')
          }
        })
      }
    },
    downloadExcel () {
      var url = `${window.SITE_CONFIG['apiURL']}/letter/bgguaranteeletter/downloadExcel?token=${Cookies.get('token')}`
      window.location.href = url
    }
  }
}
</script>
