<template>
  <div>
       <el-card class="box-card" v-loading='boxLoading'>
         <el-steps
            :active="active"
            simple
            ref='steps'
            >
                <el-step title="选择保函出具机构"></el-step>
                <el-step title="提交申请"></el-step>
                <el-step title='等待审核'></el-step>
            </el-steps>
             <el-form ref="dataForm" label-width="150px" class="demo-dataForm">
                 <h3>支付信息</h3>
                <div class="formCon">
                    <el-row :gutter="25" >
                        <el-col  :xs="24" :lg="24">
                            <el-form-item label="申请编码：" >
                                {{dataForm.code}}
                            </el-form-item>
                        </el-col>
                        <el-col  :xs="24" :lg="12">
                            <el-form-item label="担保金额：" >
                                {{dataForm.guaranteeAmount}}
                            </el-form-item>
                            <el-form-item label="保函服务费：" >
                                {{dataForm.letterPrice}}
                            </el-form-item>
                        </el-col>
                        <el-col  :xs="24" :lg="12">
                            <el-form-item label="投标机构：" >
                                {{dataForm.applyName}}
                            </el-form-item>
                            <el-form-item label="招标机构：" >
                                {{dataForm.bbrName}}
                            </el-form-item>
                        </el-col>
                         <el-col  :xs="24" :lg="12">
                            <el-form-item label="保函开始时间：" >
                                {{dataForm.validStartDate}}
                            </el-form-item>
                        </el-col>
                        <el-col  :xs="24" :lg="12">
                         <el-form-item label="保函结束时间：" >
                                {{dataForm.validEndDate}}
                            </el-form-item>
                        </el-col>
                        <el-col  :xs="24" :lg="24">
                            <el-form-item label="电子保函出具机构：" >
                                {{dataForm.issueName}}
                            </el-form-item>
                        </el-col>
                        <el-col  :xs="24" :lg="24">
                            <el-form-item label="电子保函使用平台：" >
                                {{dataForm.platformName}}
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
             </el-form>
              <div class="tip">
                               <h4>您应缴纳电子保函服务费 <span class="price">{{dataForm.letterPrice}}</span> 元</h4>
                            </div>
       </el-card>
        <div class="foot">
        <span>
          <el-button type="primary" @click="dataFormSubmitHandle()" size="small" :disabled="boxLoading">跳转到个人网银支付</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()" size="small" :disabled="boxLoading">跳转到企业微信支付</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()" size="small" :disabled="boxLoading">微信支付</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()" size="small" :disabled="boxLoading">支付宝支付</el-button>
        </span>
        </div>
  </div>
</template>
<script>

export default {
  name: 'lock',
  data () {
    return {
      active: 2,
      boxLoading: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        guaranteeType: '',
        validEndDate: '',
        validStartDate: '',
        openDate: '',
        guaranteeAmount: '',
        guaranteePrice: '',
        letterPrice: '',
        platformFee: '',
        letterUrl: '',
        applyId: '',
        applyCode: '',
        applyName: '',
        bbrId: '',
        bbrCode: '',
        bbrName: '',
        issueId: '',
        issueCode: '',
        issueName: '',
        platformId: '',
        platformCode: '',
        platformName: '',
        insuranceId: '',
        insuranceCode: '',
        insuranceName: '',
        invoiceId: '',
        mailingId: '',
        payId: '',
        serpriceId: '',
        changeId: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  created () {
    if (this.$route.query.id) {
      this.dataForm.id = this.$route.query.id

      this.init()
    }
  },

  mounted () {
    this.$refs.steps.$children.map((item, index) => {
      item.$el.getElementsByClassName('el-step__head')[0].lastChild.innerHTML = (index + 1)
    })
  },
  props: [],
  methods: {
    dataFormSubmitHandle () {
      this.$router.push({
        name: 'applyPaySus'
      })
    },
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/letter/bgguaranteeletter/info/${this.dataForm.id}`).then(({ data: res }) => {
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    }
  },
  components: {}
}
</script>

<style lang="scss" scoped>
.box-card{
      max-width: 1200px;
      margin: 15px auto;
      margin-bottom: 65px;
       h3{
          border-bottom: 1px dashed rgb(241, 130, 65);
          padding: 15px;
          text-align: center;
        }
        .el-step__head.is-finish{
            color: rgb(241, 130, 65);
            border-color:rgb(241, 130, 65);
        }
        .el-step__title.is-finish{
            color: rgb(241, 130, 65);
        }
         .tip{
             text-align: center;
                 border-top: 1px dashed rgb(241, 130, 65);
            }
        .formCon{
            width: 55vw;
            min-width: 750px;
            margin: 30px auto;

            .itemCon{
              display: flex;
              // justify-content:space-between;
              flex-wrap: wrap;
              .item{
                border: 1px solid #e2e2e2;
                display: flex;
                transition: .3s all;
                padding: 0 20px;
                margin-right: 15px;
                margin-bottom: 15px;
                position: relative;
                cursor: pointer;
                overflow: hidden;
                .el-icon-success{
                  display: none;
                  position: absolute;
                  top: -2px;
                  right: -2px;
                  color: rgb(241, 130, 65);
                }
                span{
                  display: inline-block;
                  height: 70px;
                  line-height: 70px;

                }
                span:first-child{
                  margin: 0 ;
                  img{
                    width: 35px;
                    height: 35px;
                  }
                }
              }
               .active{
                  border: 1px solid rgb(241, 130, 65);
                   .el-icon-success{
                     display: block !important;
                   }
                }
                .item:hover{
                  border: 1px solid rgb(241, 130, 65);
                }
            }

            .disCon{
              width: 100%;
              border: 1px solid #e2e2e2;
              padding:0 15px;
              h4{
                margin: 0;
              }
              .des{

                text-indent: 28px;
                /* word-spacing: 20px; */
                line-height: 32px;
                letter-spacing: 1px;
                font-size: 13px;
              }
              .serTable{
                .el-table__header{
                  line-height: 24px;
                }
                .aui-wrapper .el-table th{
                  background-color: black !important;
                }
              }
            }

        }
}
 .price{
      color: rgb(241, 130, 65);
      font-weight: bold;
      font-size: 20px;
    }
    .foot{
        width: 100%;
        background: #fff;
        position: fixed;
        bottom: 0;
        padding: 10px 0;
        border-top: 1px solid #ddd;
        background: #f9f9f9;
        text-align: center;
        // left: 80px;
        z-index: 999;
        span{
            text-align: center;
        }
    }
</style>
