<template>
  <el-row :gutter="20" style="margin:15px 20px 0;">
    <el-form :model="dataForm" label-position="left" :rules="dataRule" ref="dataForm" label-width="140px">
      <el-card class="box-card" shadow='never'>
        <div slot="header" class="clearfix">
          <span class="title">项目信息（请根据招标文件如实填写）</span>
          <!-- <el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button> -->
        </div>
        <div>

          <el-col :xs="24" :lg="24">
            <div class="formCon">

              <el-row :gutter="40">
                <el-col :xs="24" :lg='24' :xl="16">
                  <el-form-item label="项目所属区域" prop="regionId">
                    <span v-if="dataForm.region&&casCilck">{{dataForm.region | regFil}} &emsp;&emsp;<el-button type="text" @click="casCilck = false">修改</el-button></span>
                   <template v-else>
                     <el-cascader @expand-change='expandChange' :options="regionIds"  v-model.trim="regionId" ref='region' @change='regionIdChange' size="small"  class="wd180" style="width:250px;" :props="props"></el-cascader>&emsp;&emsp;<el-button type="text" v-if="dataForm.region" @click="casCilck = true">取消</el-button>
                   </template>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :xs="24" :lg='24' :xl="16">
                  <el-form-item label="项目标段名称" prop="name">
                    <template v-if="regionId">
                        <el-button  icon="el-icon-plus" size="mini" type="primary" @click="chose()">选择已有项目</el-button> <span style="margin-left:25px;color:rgba(0,0,0,.4);"
                      class="el-upload__tip">选择已有项目自动带出该项目其他项目信息
                    <font style="color:red;">
                    (* 如项目不存在可手动填写)
                </font>
                    </span>
                    </template>
                    <el-input clearable v-model.trim="dataForm.name" @keyup.native="trimLR('name')" size="small" class="wd180" style="width:100%;" placeholder="请输入要办理的项目及标段名称">
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :xs="24" :lg='12' :xl="8">
                  <el-form-item label="投标日期" prop="tbDate">
                    <el-date-picker size="small" class="wd240" @change="tbChange" value-format="yyyy-MM-dd" style="width:100%;" v-model="dataForm.tbDate" type="date" placeholder="选择投标日期" align="right"
                      :picker-options="pickerOptions">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :lg='12' :xl="8">
                  <!-- {{options.platformOptions}} -->
                  <!-- <el-form-item label="所属交易平台" prop="platformCode">
                    <el-select v-model="dataForm.platformCode" placeholder="请选择" size="small" style="width:100%;" class="wd240">
                      <el-option v-for="item in options.platformOptions" :key="item.code" :label="item.name" :value="item.code">
                      </el-option>
                    </el-select>
                  </el-form-item> -->
                </el-col>
              </el-row>
              <!-- <el-col :xs="24" :lg="12">
                  <el-form-item label="开工日期" prop="bidOpenDate">
                    <el-date-picker size="small" style="width:100%;" class="wd240" value-format="yyyy-MM-dd" v-model="dataForm.bidOpenDate" type="date" placeholder="选择开工日期" align="right"
                                :picker-options="pickerOptions">
                    </el-date-picker>
                  </el-form-item>
                </el-col> -->
            </div>
          </el-col>
        </div>
      </el-card>

      <choseProject v-if="visible" ref="choseProject" @getProjectData='getProjectData'></choseProject>
    </el-form>
  </el-row>
</template>
<script>
import choseProject from './compoents/choseProject'
export default {
  components: {
    choseProject
  },
  data () {
    return {
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() < Date.now()
        }
      },
      visible: false,
      radio: 0,
      regionId: '',
      regionIds: [],
      casCilck: true,
      thsAreaCode: '',
      props: {
        value: 'code',
        label: 'name',
        children: 'child',
        isLeaf: 'leaf',
        lazy: true,
        lazyLoad: this.lazyLoad,
        collapseTags: true
      }
    }
  },
  filters: {
    regFil (val) {
      return val
    }
  },
  props: {
    dataForm: {
      type: Object
    },
    options: {
      type: Object
    }
  },
  computed: {
    dataRule () {
      return {
        name: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        regionId: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        tbDate: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        platformCode: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        bidOpenDate: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ]
      }
    }
  },
  methods: {
    trimLR (val) {
      this.dataForm[val] = this.dataForm[val].replace(/\s+/g, '')
    },
    selfo () {
      this.casCilck = false
      this.$nextTick(() => {
        console.log(this.$refs)
      })
    },
    expandChange (val) {
      console.log(val)
    },
    regionIdChange (val) {
      console.log(val)
      this.$emit('clearProAndBBr', val ? val[1] : '')
      this.$set(this.dataForm, 'regionId', val ? val[1] : '')
    },
    async lazyLoad (node, resolve) {
      // 点击节点
      const { level } = node
      let nodes = await this.lazyloadFn(level === 0 ? 100000 : node.value)
      let nodes2 = nodes.map((item) => ({
        code: item.code,
        name: item.name,
        leaf: level > 0
      }))

      resolve(nodes2)
    },
    lazyloadFn (parentCode) {
      // 获取node
      return new Promise((resolve, reject) => {
        this.$http
          .get(`/demo/tbregion/regionTree?code=${parentCode}`)
          .then(({ data: res }) => {
            res.data = res.data.filter(a => a.code !== '130600' && a.code !== '130300' && a.code !== '130400')
            resolve(res.data)
            // this.zongingList = res.data
          })
          .catch(() => {})
      })
    },
    chose () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['choseProject'].dataForm.regionId = this.dataForm.regionId
        this.$refs['choseProject'].init()
      })
    },
    tbChange (val) {
      this.$emit('gettbDate', val)
    },
    getProjectData (obj) {
      this.$set(this.dataForm, 'name', obj.name)
      this.$set(this.dataForm, 'platformCode', obj.platformCode)
      // this.$set(this.dataForm, 'tbDate', obj.tbDate)
      this.$emit('setGuaranteeAmount', obj.guaranteeAmount)
      this.$emit('getBbrId', obj.bbrId)
    },
    push () {
      return new Promise((resolve, reject) => {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
</style>
