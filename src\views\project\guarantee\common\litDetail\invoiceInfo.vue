<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2021-02-04 10:42:05
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-04-06 17:34:46
-->
<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2020-10-13 09:38:40
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-02-01 16:10:33
-->
<template>
  <el-col :span="20">
    <el-col :span="24" class="con-title">
      发票信息{{dataForm.bgguaranteeinvoice.invoiceType}}
    </el-col>

    <el-col :span="draw?24:12">
      <el-form-item label="发票类型">
        {{certType(dataForm.bgguaranteeinvoice.invoiceType,'invoiceTypeoptions')}}
      </el-form-item>
    </el-col>
    <!-- <el-col :span="draw?24:12">
      <el-form-item label="开票对象">
        {{certType(dataForm.bgguaranteeinvoice.invoiceObject,'invoiceObjectoptions')}}
      </el-form-item>
    </el-col> -->
    <el-col :span="draw?24:12">
      <el-form-item label="纳税人类型">
        {{certType(dataForm.bgguaranteeinvoice.taxpayerType,'taxpayerTypeoptions')}}
      </el-form-item>
    </el-col>
    <el-col :span="draw?24:12">
      <el-form-item label="纳税人识别号">
        {{fomate('bgguaranteeinvoice','taxpayerNumber')}}
      </el-form-item>
    </el-col>
    <el-col :span="draw?24:12">
      <el-form-item label="发票抬头" prop="invoiceTitle">
        {{fomate('bgguaranteeinvoice','invoiceTitle')}}
      </el-form-item>
    </el-col>
    <template v-if="dataForm.bgguaranteeinvoice.invoiceType == 2">
      <el-col :xs="24" :lg="24">
        <el-form-item label="银行账户名称" prop="bankAccountName">
          {{fomate('bgguaranteeinvoice','bankAccountName')}}
        </el-form-item>
      </el-col>
      <el-col :xs="24" :lg="draw?24:12">
        <el-form-item label="银行账户号码" prop="bankAccountNo">
          {{fomate('bgguaranteeinvoice','bankAccountNo')}}
        </el-form-item>
        <el-form-item label="税务登记电话" prop="phoneNumber">
          {{fomate('bgguaranteeinvoice','phoneNumber')}}
        </el-form-item>
      </el-col>

      <el-col :xs="24" :lg="24">
        <el-form-item label="税务登记地址" prop="address">
          {{fomate('bgguaranteeinvoice','address')}}
        </el-form-item>
      </el-col>
      <el-col :xs="24" :lg="24">
        <el-form-item label="备注" prop="remark">
          {{fomate('bgguaranteeinvoice','remark')}}
        </el-form-item>
      </el-col>
    </template>
    <div v-if="dataForm.bgguaranteeinvoice.invoiceType != 3">
      <el-col :xs="24" :lg="draw?24:12">
        <el-form-item label="发票接收邮箱" prop="electronicInvoiceEmail">
          {{fomate('bgguaranteeinvoice','electronicInvoiceEmail')}}
        </el-form-item>
      </el-col>
      <el-col :xs="24" :lg="draw?24:12">
        <el-form-item label="发票接收手机号" prop="electronicInvoicePhone">
          {{fomate('bgguaranteeinvoice','electronicInvoicePhone')}}
        </el-form-item>
      </el-col>
    </div>
    <div v-if="dataForm.bgguaranteeinvoice.invoiceType != 1">
      <el-col :xs="24" :lg="draw?24:12">
        <el-form-item label="收件人">
          {{fomate('bgguaranteeinvoice','mailingUser')}}
        </el-form-item>
      </el-col>
      <el-col :xs="24" :lg="draw?24:12">
        <el-form-item label="收件电话">
          {{fomate('bgguaranteeinvoice','mailingTel')}}
        </el-form-item>
      </el-col>
      <el-col :xs="24" :lg="draw?24:12">
        <el-form-item label="收件地址" prop="mailingAddress">
          {{fomate('bgguaranteeinvoice','mailingAddress')}}
        </el-form-item>
      </el-col>
    </div>
  </el-col>

</template>
<script>
import dictionaries from '@/views/project/guarantee/components/dictionaries'
export default {
  mixins: [dictionaries],
  props: {
    dataForm: Object,
    draw: Boolean
  },
  data () {
    return {}
  },
  methods: {
    fomate (key, val) {
      return this.dataForm[key][val] ? this.dataForm[key][val] : '-'
    },
    certType (val, name) {
      // console.log(this.options)
      var aa = this.options[name].filter((a) => a.dictCode === val)
      return aa.length > 0 ? aa[0].dictName : '-'
    }
  }
}
</script>
<style lang="scss" scoped>
.con-title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-bottom: 10px;
}
</style>
