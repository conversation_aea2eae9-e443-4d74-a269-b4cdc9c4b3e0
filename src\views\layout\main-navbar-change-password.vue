<template>
  <el-dialog
    :visible.sync="visible"
    title="请修改您的初始密码"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"  label-width="120px">
      <el-form-item :label="$t('updatePassword.username')">
        <span>{{ $store.state.user.name }}</span>
      </el-form-item>
      <el-form-item prop="password" :label="$t('updatePassword.password')">
        <el-input v-model="dataForm.password" type="text" disabled :placeholder="$t('updatePassword.password')"></el-input>
      </el-form-item>
      <el-form-item prop="newPassword" :label="$t('updatePassword.newPassword')">
        <el-input v-model="dataForm.newPassword" type="password" :placeholder="$t('updatePassword.newPassword')"></el-input>
      </el-form-item>
      <el-form-item prop="comfirmPassword" :label="$t('updatePassword.comfirmPassword')">
        <el-input v-model="dataForm.comfirmPassword" type="password" @change="checkpwd" :placeholder="$t('updatePassword.comfirmPassword')"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        password: 'qwer1234',
        newPassword: '',
        comfirmPassword: ''
      }
    }
  },
  computed: {
    dataRule () {
      var validateComfirmPassword = (rule, value, callback) => {
        if (this.dataForm.newPassword !== value) {
          return callback(new Error(this.$t('updatePassword.validate.comfirmPassword')))
        }
        callback()
      }
      return {
        password: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        comfirmPassword: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: validateComfirmPassword, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
      })
    },
    checkpwd () {
      if (this.dataForm.newPassword.length < 1) {
        this.dataForm.newPassword = ''
        this.dataForm.comfirmPassword = ''
        return this.$message.error('新密码不能为空')
      } else if (this.dataForm.newPassword !== this.dataForm.comfirmPassword) {
        this.dataForm.newPassword = ''
        this.dataForm.comfirmPassword = ''
        return this.$message.error('两次密码不一致！')
      } else if (this.dataForm.newPassword.length < 8) {
        this.dataForm.newPassword = ''
        this.dataForm.comfirmPassword = ''
        return this.$message.error('用户登录密码必须在8位以上')
      } else {
        var value = this.dataForm.newPassword // 得到输入框中的值
        var pattern = /(?!^[0-9]*$)(?!^[a-zA-Z]*$)^([a-zA-Z0-9]{8,})$/ // 创建正则表达式对象
        var flag = pattern.test(value) // 测试匹配

        if (!flag) { // 判断匹配
          this.dataForm.newPassword = ''
          this.dataForm.comfirmPassword = ''
          return this.$message.error('格式错误，请使用数字加字母的格式，请重新输入！')
        }
      }
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http.put('/sys/user/password', this.dataForm).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$router.replace({ name: 'home' })
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
