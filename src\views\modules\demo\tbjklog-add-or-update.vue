<template>
  <el-dialog :visible.sync="visible" title="浏览" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"  :label-width="$i18n.locale === 'en-US' ? '120px' : '106px'">
        <el-row :gutter="20">
            <el-col :xs="24" :lg="colConfig">
          <el-form-item label="客户端类型" prop="terminalcode">
     <el-input v-model="dataForm.terminalcode" placeholder="客户端类型 pc app"></el-input>
</el-form-item>
                <el-form-item label="应用编码" prop="appcode">
     <el-input v-model="dataForm.appcode" placeholder="应用编码"></el-input>
</el-form-item>
                <el-form-item label="接口版本" prop="version">
     <el-input v-model="dataForm.version" placeholder="接口版本"></el-input>
</el-form-item>
                <el-form-item label="接口编码" prop="interfacecode">
     <el-input v-model="dataForm.interfacecode" placeholder="接口编码"></el-input>
</el-form-item>
                <el-form-item label="加密方式" prop="codetype">
     <el-input v-model="dataForm.codetype" placeholder="加密方式"></el-input>
</el-form-item>
                <el-form-item label="请求时间" prop="requesttime">
     <el-input v-model="dataForm.requesttime" placeholder="数据交换请求时间"></el-input>
</el-form-item>
            </el-col>
                <el-col :xs="24" :lg="colConfig">
                <el-form-item label="返回时间" prop="returntime">
     <el-input v-model="dataForm.returntime" placeholder="返回时间"></el-input>
</el-form-item>
                <el-form-item label="响应时间ms" prop="xysj">
     <el-input v-model="dataForm.xysj" placeholder="响应时间"></el-input>
</el-form-item>
                <el-form-item label="请求URI" prop="requestUri">
     <el-input v-model="dataForm.requestUri" placeholder="请求URI"></el-input>
</el-form-item>
                <el-form-item label="请求方式" prop="requestMethod">
     <el-input v-model="dataForm.requestMethod" placeholder="请求方式"></el-input>
</el-form-item>

                <el-form-item label="业务表ID" prop="ywid">
     <el-input v-model="dataForm.ywid" placeholder="业务表ID"></el-input>
</el-form-item>
                <el-form-item label="业务说明" prop="ywremark">
     <el-input v-model="dataForm.ywremark" placeholder="业务说明"></el-input>
</el-form-item>
                            </el-col>
            <el-form-item label="请求参数" prop="requestParams">
                <textarea  rows="10" cols="120" placeholder="请输入"   width="100%"  v-model="dataForm.requestParams" />
                <span class="numberV"></span>
            </el-form-item>
            <el-form-item label="返回编码" prop="returncode">
                <textarea  rows="10" cols="120" placeholder="请输入"   width="100%"  v-model="dataForm.returncode" />
                <span class="numberV"></span>
            </el-form-item>
            <el-form-item label="返回消息" prop="returnmessage">
                <textarea  rows="10" cols="120" placeholder="请输入"   width="100%" v-model="dataForm.returnmessage" />
                <span class="numberV"></span>
            </el-form-item>
        </el-row>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">确定</el-button>
     </template>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      colConfig: 2, // 列配置 1 2列
      visible: false,
      dataForm: {
        id: '',
        terminalcode: '',
        appcode: '',
        version: '',
        interfacecode: '',
        codetype: '',
        requesttime: '',
        dataexchangeid: '',
        returncode: '',
        returnmessage: '',
        returntime: '',
        xysj: '',
        requestUri: '',
        requestMethod: '',
        requestParams: '',
        ywid: '',
        ywtable: '',
        ywremark: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/demo/tbjklog/${this.dataForm.id}`).then(({ data: res }) => {
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    }
  }
}
</script>
