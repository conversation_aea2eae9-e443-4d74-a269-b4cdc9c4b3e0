<template>
  <el-row :gutter="20" style="margin:0px 20px 0;">
    <el-form v-if="dataForm" label-position="left" :model="dataForm" ref="dataForm" :rules="dataRule" label-width="140px">
      <el-card class="box-card" shadow='never'>
        <div slot="header" class="clearfix">
          <span class="title">反担保</span>
        </div>
        <div>
          <el-col :xs="24" :xl="24">
            <div class="formCon">
              <el-row :gutter="40">
                <el-col :xs="24" :lg='24'>
                  <el-form-item label="保证金（元）" prop="bond" >
                    <el-input v-model="dataForm.bond"  size="medium" style="width:100%;" class="wd180" placeholder="保证金"></el-input>
                  </el-form-item>
                </el-col>
                 <el-col :xs="24" :lg='24'>
                  <el-form-item label="保证金比例（%）" prop="bondPercent" >
                    <el-input v-model="dataForm.bondPercent"  size="medium" style="width:100%;" class="wd180" placeholder="第三方担保金额"></el-input>
                  </el-form-item>
                </el-col>
                 <el-col :xs="24" :lg='24'>
                  <el-form-item label="第三方反担保" prop="thirdGuarantee" >
                    <el-input v-model.trim="dataForm.thirdGuarantee" size="medium" style="width:100%;" class="wd180" placeholder="第三方反担保"></el-input>
                  </el-form-item>
                </el-col>
                  <el-col :xs="24" :lg='24'>
                  <el-form-item label="第三方担保金额（元）" prop="thirdGuaranteeAmount" >
                    <el-input v-model="dataForm.thirdGuaranteeAmount"  size="medium" style="width:100%;" class="wd180" placeholder="第三方担保金额"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </div>
      </el-card>
    </el-form>
  </el-row>
</template>
<script>
export default {
  name: 'bgGuaranteeApply',
  data () {
    return {
      getDicListURL: '/sys/dict/type/'
    }
  },
  props: {
    dataForm: {
      type: Object
    },
    pakeage: {
      type: Object
    }
  },
  computed: {
    dataRule () {
      return {
        bondPercent: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        bond: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        thirdGuarantee: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ],
        thirdGuaranteeAmount: [
          {
            required: true,
            message: this.$t('validate.required'),
            trigger: 'change'
          }
        ]

      }
    }
  },
  created () {
  },
  methods: {
    push () {
      return new Promise((resolve, reject) => {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  // padding: 16px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.temp-subtitle {
  font-size: 16px;
  border-bottom: 1px dashed rgb(241, 130, 65);
  padding: 15px;
  text-align: center;
  color: #333;
  line-height: 29px;
  margin: 15px;
  font-weight: bold;
  text-align: center;
}
</style>
