/*
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-10-12 09:46:07
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-12-10 15:07:48
 */
export default {
  data () {
    return {
      websock: null,
      socketOptions: {
        isActive: true, // 此页面是否在激活（进入）时，调用查询数据列表接口？
        isMsg: true
      }
    }
  },
  mounted () {
    if (this.socketOptions.isActive) {
      this.initWebSocket()
    }
  },
  destroyed () {
    if (this.websock) {
      this.websocketclose()
    }
  },
  methods: {
    initWebSocket () {
      // 初始化weosocket
      const wsuri =
        window.SITE_CONFIG['wsURL'] +
        '/websocket/' +
        this.$store.state.user.id
      this.websock = new WebSocket(wsuri)
      this.websock.onmessage = this.websocketonmessage
      this.websock.onopen = this.websocketonopen
      this.websock.onerror = this.websocketonerror
      this.websock.onclose = this.websocketclose
    },
    websocketonopen () {
      let actions = {
        id: this.$store.state.user.id
      }
      this.websocketsend(JSON.stringify(actions))
    },
    websocketonerror () {
      console.log('连接建立失败')
      // this.websock.onclose()
      // this.initWebSocket()
    },
    websocketsend (Data) {
      // 数据发送
      this.websock.send(Data)
    },
    websocketclose (e) {
      // 关闭
      this.websock.close()
      console.log('断开连接', e)
    }
  }
}
