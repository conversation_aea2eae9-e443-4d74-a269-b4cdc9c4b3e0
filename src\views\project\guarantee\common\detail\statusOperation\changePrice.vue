<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON>qiang
 * @Date: 2022-01-18 09:08:45
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-03-22 15:03:31
-->
<template>
  <el-dialog :visible.sync="visible" title="修改保费" :close-on-click-modal="false" :close-on-press-escape="false" width="500">
    <el-form :model="dataForm" :rules="{
        guaranteePrice: [
          {
            required: true,
            message: '请输入保费',
            trigger: 'change'
          }
        ]}" ref="dataForm" label-width="100px" class="demo-ruleForm">
      <el-form-item label="保费" prop="guaranteePrice">
        <el-input-number placeholder="请输入保费" size="medium" v-model="dataForm.guaranteePrice" controls-position="right" :precision="2" :step="100" :min="0" > </el-input-number>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button type="primary"  @click="submita()">提交</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      dataForm: {},
      visible: false
    }
  },
  created () {},
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
      })
    },
    submita () {
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          let { data } = await this.$http.post(
            'letter/guarantee/updateLetterPrice', { letterId: this.dataForm.letterId, guaranteePrice: String(this.dataForm.guaranteePrice) }
          )
          if (data.code === 0) {
            this.visible = false
            this.$message.success('修改保费成功！')
            this.$emit('refresh')
          }
        } else {
          return false
        }
      })
      // eslint-disable-next-line no-unused-vars
    }

  }
}
</script>
<style lang="scss" scoped>
.dialog {
  /deep/.el-dialog__body {
    max-height: none;
  }
}
</style>
