<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2021-03-10 18:54:25
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-02-16 16:46:52
-->
<template>
  <el-table size='mini' stripe v-loading="dataListLoading" :cell-style="TableCellStyle"  :data="dataList" border style="width: 100%;zoom:0.9">
    <el-table-column
        type="index"
        width="50">
        <template slot-scope="scope">
          <span v-show="dataList.length !=scope.$index+1">{{scope.$index+1}}</span>
        </template>
      </el-table-column>
    <el-table-column prop="name" label="招标代理名称" header-align="center" align="center" ></el-table-column>
    <el-table-column prop="guaranteePrice" label="金额（元）" header-align="center" align="center" ></el-table-column>
  </el-table>
</template>
<script>
import { getDict } from '@/utils/index'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins: [ mixinViewModule ],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bgSettlement/agentDetails',
        getDataListIsPage: false,
        exportURL: '/letter/bgguaranteeletter/accountDataExport',
        deleteIsBatch: true
      }
    }
  },
  computed: {
    fomate () {
      var str = ''
      if (this.guaranteeType === 'tbdbbh') {
        str = '投标有效期'
      }
      if (this.guaranteeType === 'lydbbh') {
        str = '担保期间'
      }
      if (this.guaranteeType === 'yfkdbbh') {
        str = '有效期'
      }
      return str
    }
  },
  methods: {
    TableCellStyle ({ rowIndex }) {
      if (rowIndex === this.dataList.length - 1) {
        return 'background-color:#f1824126'
      }
    },
    dict (val) {
      var aa = getDict('电子保函类型').filter((a) => a.dictCode === val)
      return aa.length > 0 ? aa[0].dictName : '-'
    }
  }
}
</script>
