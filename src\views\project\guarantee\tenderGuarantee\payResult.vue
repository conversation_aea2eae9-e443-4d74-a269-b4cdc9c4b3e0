<template>
  <div>
    <el-card class="box-card">

      <el-row :gutter="12">
        <el-col :span="24">
          <el-card shadow="never">
            <div class="img">
              <img src="@/assets/img/success.png" alt="">
              <p class="tip">支付成功</p>

            </div>

            <div class="statusBox">

              <p>支付金额：{{dataForm.payAmount}}元</p>
              <p>支付方式：{{fomatMethod(dataForm.payWaySource)}}</p>
              <p>订单号：{{dataForm.serialNo}}</p>
              <p>保单号：{{dataForm.policyInfoList.policyInfo.policyNo}}</p>
            </div>
            <div class="img">
               <el-button @click="goPath('home')">返回首页</el-button>
               <!-- <el-button @click="surrender">退保</el-button> -->
              <el-button type="primary" @click="goPath('login')">登录/注册</el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
     <bgguaranteeletterSqf-surrender v-if="surrenderVisible" ref="bgguaranteeletterSqfSurrender" ></bgguaranteeletterSqf-surrender>
  </div>
</template>
<script>
import bgguaranteeletterSqfSurrender from '@/views/modules/letter/bgguaranteeletterSqf-surrender'

export default {
  data () {
    return {
      msg: '',
      updateLoading: false,
      surrenderVisible: false,
      getDicListURL: '/sys/dict/type/',
      paymentMethodmaps: '',
      paymentMethodoptions: [],
      dataForm: {}
    }
  },
  created () {
    this.getPaymentMethodInfo()
  },
  components: {
    bgguaranteeletterSqfSurrender
  },
  mounted () {
    this.msg = this.$route.query.payResult
    var jsonObj = this.$x2js.xml2js(this.msg)
    this.dataForm = jsonObj.Request
  },
  methods: {
    fomatMethod (value) {
      return this.paymentMethodmaps[value]
    },
    // 获取支付方式信息
    getPaymentMethodInfo () {
      this.$http.get(this.getDicListURL + 'paymentMethod').then(({ data: res }) => {
        this.paymentMethodoptions = {
          ...this.paymentMethodoptions,
          ...res.data.list
        }
        this.paymentMethodmaps = {
          ...this.paymentMethodmaps,
          ...res.data.map
        }
      }).catch(() => {})
    },
    goPath (val) {
      this.$router.push({
        name: val
      })
    },
    surrender () {
      this.surrenderVisible = true
      this.$nextTick(() => {
        this.$refs.bgguaranteeletterSqfSurrender.init()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.box-card {
  max-width: 1200px;
  margin: 15px auto;
  margin-bottom: 65px;
  .img {
    text-align: center;
    margin: 40px;
  }
  .tip {
    margin-top: 30px;
    margin-bottom: 30px;
    font-size: 15px;
    font-family: '黑体';
  }
  .statusBox {
    width: 400px;
    background: rgba(241, 130, 65, 0.15);
    margin: 30px auto;
    padding: 20px;
    color: rgb(241, 130, 65);
    border: 1px solid rgb(241, 130, 65);
  }
  .el-button + .el-button {
    margin-left: 40px;
  }
}
</style>
