<!--
 * @Descripttion:
 * @Author: liang
 * @Date: 2020-09-08 09:43:29
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-09-01 09:22:15
-->
<template>
  <div class="box">
    <!-- {{pakeage}} -->
    <div id="header">
      <div class="header">
        <img src="@/assets/img/logo2.png">
        <div class="slogen">我的收银台</div>
      </div>
    </div>
    <div class="content">
      <div class="trade">
        <div class="left">
          <p><b>订单号： {{dataForm.code}} </b></p>
          <p>收款方：{{bankInfo.bankAccountName}}</p>
          <p>保函名称：{{dataForm.name}}</p>
          <!-- <p>支付方式：{{payway==='ALIPAY'?'支付宝':'微信'}}</p> -->
          <!--<p> 请您使用 <img src="@/assets/img/zfbicon.png" height="16">&nbsp;<img src="@/assets/img/wxicon.png" height="16"> 扫码支付 </p>-->
        </div>
        <div class="right">
          <div class="price">
            <span>{{dataForm.guaranteePrice}}</span>元
          </div>
        </div>
      </div>
      <div class="article" v-if="!dialogVisible">
        <template v-if="dataForm.payStatus=='10'||dataForm.payStatus=='40'">
          <div class="bank-type">
            <div class="title">请您选择支付方式</div>
            <div>
              <el-radio-group v-model="payway" @input="changePay">
                <el-radio v-for="item in pakeage.paymentMethod" :key="item"  :label="item" border>
                  <img src="@/assets/img/zfbicon.png" v-show="item == 'ALIPAY'" height="24">
                  <span class="name"  v-show="item == 'ALIPAY'" >支付宝</span>
                  <img src="@/assets/img/wxicon.png"  v-show="item == 'WXPAY'" height="24">
                  <span class="name"  v-show="item == 'WXPAY'">微信</span>
                  <img src="@/assets/img/xx.png" v-show="item == 'offline'" height="24">
                  <span class="name" v-show="item == 'offline'">线下支付</span>
                  <img src="@/assets/img/wxicon.png" v-show="item == 'FUIOU_WECHAT'" height="24">
                  <span class="name" v-show="item == 'FUIOU_WECHAT'">微信</span>
                  <img src="@/assets/img/zfbicon.png" v-show="item == 'FUIOU_ALIPAY'" height="24">
                  <span class="name" v-show="item == 'FUIOU_ALIPAY'">支付宝</span>
                  <img src="@/assets/img/xx.png" v-show="item == 'FUIOU_UNIONPAY'" height="24">
                  <span class="name" v-show="item == 'FUIOU_UNIONPAY'">银联</span>
                </el-radio>
                <!-- <el-radio v-if="dataForm.letterStatus !='45'&&" label="ALIPAY" border> <img src="@/assets/img/zfbicon.png" height="24"> <span class="name">支付宝</span>
                </el-radio>
                <el-radio v-if="dataForm.letterStatus !='45'&&" label="WXPAY" border> <img src="@/assets/img/wxicon.png" height="24"> <span class="name">微信</span></el-radio>
                <el-radio v-if="" label="offline" border> <img src="@/assets/img/xx.png" height="24"> <span class="name">线下支付</span>
                </el-radio> -->
              </el-radio-group>
            </div>
          </div>
          <div class="bank-type" v-if="payway==='offline'">
            <div class="title">线下支付</div>
            <el-timeline>
              <el-timeline-item>
                <span slot="dot" class="dot">1</span>
                <div>

                  <div class="offline">
                    <h3>第一步&emsp;打款</h3>
                    <p><span>账户名称：</span>{{bankInfo.bankAccountName}}</p>
                    <p><span>开户银行：</span>{{bankInfo.bankName}}</p>
                    <p><span>账号：</span>{{bankInfo.bankAccountNo}}</p>
                  </div>
                </div>
              </el-timeline-item>
              <el-timeline-item>
                <span slot="dot" class="dot">2</span>
                <h3>第二步&emsp;上传支付凭证</h3>
                <el-upload style="width:500px;" class="upload-demo" drag :on-remove='handleRemove' :action="url+'/letter/bgguaranteeletter/uploadPayVoucher'" :data='zbData' :headers="myHeaders"
                  :before-remove="BeforeRemove" :on-success='successHandle' :before-upload="beforeUpload" :limit="1" :on-exceed="handleExceed" :file-list="fileList">
                  <i class="el-icon-upload"></i>
                  <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                  <div class="el-upload__tip" slot="tip">只能上传.jpg,.JPG,.jpeg,.JPEG,.png,.PNG文件</div>
                </el-upload>
              </el-timeline-item>
            </el-timeline>

          </div>
          <div style="text-align:center;padding-top:30px" v-if="payway==='ALIPAY'">
            <el-button type="primary" @click='pay'>确定</el-button>
          </div>
        </template>
        <template v-else-if="dataForm.payStatus=='20'">
          <div class="result">
            <div class="img">
              <img src="@/assets/img/wait.png" alt="">
              <p class="tip">线下支付审核中</p>
            </div>
            <div v-if="dataForm.dataSource!='hreb'">
              <el-button @click="goPath(dataForm.guaranteeType=='ssdbbh'?'project-letter-litigation-index':'bgguaranteeletterSqf')">返回列表</el-button>
              <!-- <el-button @click="surrender">退保</el-button> -->
              <el-button type="primary" @click="goPath(dataForm.guaranteeType=='ssdbbh'?'litigationGuaranteeDetail':'bgguaranteeletterDetail')">返回订单详情</el-button>
            </div>
          </div>

        </template>
        <template v-else-if="dataForm.payStatus=='30'">
          <div class="result">
            <div class="img">
              <img src="@/assets/img/success.png" alt="">
              <p class="tip">支付成功</p>
            </div>
            <div>
              <el-button @click="goPath(dataForm.guaranteeType=='ssdbbh'?'project-letter-litigation-index':'bgguaranteeletterSqf')">返回列表</el-button>
              <!-- <el-button @click="surrender">退保</el-button> -->
              <el-button type="primary" @click="goPath(dataForm.guaranteeType=='ssdbbh'?'litigationGuaranteeDetail':'bgguaranteeletterDetail')">返回订单详情</el-button>
            </div>
          </div>

        </template>
      </div>
      <div class="article" v-if="payway==='WXPAY'">
        <div class="bg" v-loading='qrload' :class="payway==='ALIPAY'?'ALIPAY':'WXPAY'">
          <img class='core' v-if="!isPay" :src="getCore()" width="200" height="200" />
          <div class="paySuc" v-else>
            <img :src="paySuc()" width="50" height="50" />
            <p class="tip">支付成功</p>
          </div>
        </div>
        <div style="text-align:center;margin:20px auto;">
          <el-button type="primary" @click="isend(1)">已完成支付</el-button>
        </div>
      </div>
      <div class="article" v-if="payway==='FUIOU_WECHAT'||payway==='FUIOU_ALIPAY'||payway==='FUIOU_UNIONPAY'">
        <div class="bg" v-loading='qrload' :class="'WXPAY'">
          <img class='core' v-if="!isPay" :src="fySrc" width="200" height="200" />
          <div class="paySuc" v-else>
            <img :src="paySuc()" width="50" height="50" />
            <p class="tip">支付成功</p>
          </div>
        </div>
        <div style="text-align:center;margin:20px auto;">
          <el-button type="primary" @click="isend(1)">已完成支付</el-button>
        </div>
      </div>
    </div>
    <!-- <el-dialog title="扫码支付" class="dialog" :visible.sync="dialogVisible" width="40%" :close-on-click-modal=false :show-close=false>
      <div class="qrcode" v-loading='qrload'>
        <img class='core' :src="getCore()" width="200" height="200" />
        <div class="tip"> <span>{{dataForm.guaranteePrice}}</span>&nbsp;元</div>
      </div>
      <span slot="footer">
        <el-button @click="chose()">重新选择支付方式</el-button>
        <el-button type="primary" @click="isend(1)">已完成支付</el-button>
      </span>
    </el-dialog>-->
  </div>
</template>
<script>
// import { newWin } from '@/utils'
import Cookies from 'js-cookie'
import QRCode from 'qrcode'
export default {
  data () {
    return {
      payway: 'offline',
      dialogVisible: false,
      qrload: false,
      letterId: '',
      core: '',
      url: '',
      orderType: 1, // 1:未支付 2：已支付
      dataForm: {},
      bankInfo: {},
      zbData: {},
      myHeaders: {
        token: Cookies.get('token') || ''
      },
      pakeage: {},
      fileList: [],
      isPay: false,
      websock: null,
      sockTime: 0,
      fySrc: ''
    }
  },
  components: {},
  created () {
    this.letterId = this.$route.query.letterId
    this.url = window.SITE_CONFIG['apiURL']
    this.zbData.letterId = this.letterId
    this.getInfo()
  },
  mounted () {
  },
  destroyed () {
    this.websocketclose()
  },
  methods: {
    generateQRCode (text) {
      return new Promise((resolve, reject) => {
        QRCode.toDataURL(text, (err, url) => {
          if (err) reject(err)
          resolve(url)
        })
      })
    },
    getpakeage () {
      this.$http
        .get(`/letter/bgissueconfigure/getInfoByIssueCode?issueCode=${this.dataForm.issueCode}&guaranteeTypeCode=${this.dataForm.guaranteeType}`)
        .then(async ({ data: res }) => {
          console.log('配置', res)
          res.data.paymentMethod = res.data.paymentMethod.split(',')
          this.pakeage = res.data
        })
    },
    async pay () {
      if (this.payway === 'ALIPAY') {
        // var { data } = await this.$http.get(`/pay/alipay/precreate?letterId=${this.$route.query.letterId}&key=${this.payway}`)
        // http:// localhost:9099/hzjt-dzbh/pay/order?letterCode=*****************
        window.location.href =
          window.SITE_CONFIG['apiURL'] +
          `/pay/order?letterCode=${this.dataForm.code}&issueCode=${this.dataForm.issueCode}`
        // setTimeout(() => {
        //   this.closeWebPage()
        // }, 500)
      } else {
        this.qrload = true
        this.dialogVisible = true
        // console.log(data)
        setTimeout(() => {
          this.qrload = false
          this.initWebSocket()
        }, 500)
      }
    },
    async getOfflinePayBankName () {
      let { data } = await this.$http.get(
        'letter/bgguaranteeissue/getOfflinePayBankName?code=' +
          this.dataForm.issueCode
      )
      this.bankInfo = data.data
    },
    beforeUpload (file) {
      var extension = file.name.substring(file.name.lastIndexOf('.') + 1)
      var lastName =
        extension !== 'jpg' &&
        extension !== 'JPG' &&
        extension !== 'jpeg' &&
        extension !== 'JPEG' &&
        extension !== 'png' &&
        extension !== 'PNG'

      if (lastName) {
        this.$message.warning('文件要求格式为：.jpg,.JPG,.jpeg,.JPEG,.png,.PNG')
      }

      return !lastName
    },
    successHandle (res, file, fileList) {
      if (res.data.flag !== true) {
        this.fileList = []
        return this.$message.error(res.data.msg)
      }
      this.getInfo()
      this.initWebSocket()
      this.$message.success('上传成功')
    },
    BeforeRemove (file, fileList) {
      console.log(file, fileList)
      let a = true
      if (file && file.status === 'success') {
        a = this.$confirm(`确定移除 ${file.name}？`)
      }
      return a
    },
    handleRemove (file, fileList) {
      if (file && file.status === 'success') {
        // 移除方法
        var deleteId = file.id ? file.id : file.response.data.ossId
        return this.deleteFile(deleteId, 1)
      }
    },
    deleteFile (id, type) {
      return new Promise(async (resolve, reject) => {
        let { data } = await this.$http.get(
          'letter/bgguaranteeletter/deletePayVoucherFile?ossId=' +
            id +
            '&letterId=' +
            this.letterId
        )
        if (data.code !== 0) {
          this.$message.error(data.msg)
          resolve(false)
        }
        this.getInfo()
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
        resolve(true)
      })
    },
    getfile () {
      this.$http
        .get(
          'letter/bgguaranteeletter/getPayVoucherFile?letterId=' + this.letterId
        )
        .then(({ data: res }) => {
          console.log(res.data)
          if (res.data) {
            this.fileList = [{ id: res.data, name: '支付凭证' }]
          } else {
            this.fileList = []
          }
        })
    },
    handleExceed (files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件`
      )
    },
    chose () {
      this.dialogVisible = true
    },
    paySuc () {
      return require('@/assets/img/paysuccess.gif')
    },
    changePay (label) {
      console.log(label)
      if (label === 'FUIOU_WECHAT' || label === 'FUIOU_ALIPAY' || label === 'FUIOU_UNIONPAY') {
        this.getFYcore()
      }
    },
    getCore () {
      this.$set(this.dataForm, 't', new Date().getTime())
      let core =
        this.url +
        `/pay/precreate?letterCode=${this.dataForm.code}-${this.dataForm.t}&key=${this.payway}`
      return core
    },
    getFYcore () {
      this.$set(this.dataForm, 't', new Date().getTime())
      let src = this.url +
        `/pay/scanCodePay?letterCode=${this.dataForm.code}-${this.dataForm.t}&key=${this.payway}`
      this.$http.get(src).then(({ data: res }) => {
        // console.log(res.data)
        this.generateQRCode(res.data)
          .then(url => {
            this.fySrc = url
          })
          .catch(err => {
            console.error('Error:', err.message)
          })
      })
    },
    isend (type) {
      let src = `pay/query?code=${this.dataForm.code}-${this.dataForm.t}&key=${this.payway}`
      this.$http.get(src).then(({ data: res }) => {
        if (res.data) {
          this.getInfo()
          this.pushData()
          this.isPay = true
          setTimeout(() => {
            this.dialogVisible = false
            this.getInfo()
            this.websocketclose()
          }, 3000)
        } else {
          if (type) {
            return this.$message.error('暂未完成支付，请支付！')
          }
        }
      })
    },
    pushData () {
      let src = `letter/guarantee/submitLetter`
      let params = {
        LetterId: this.letterId,
        payType: this.payway,
        OfflinePayTypeOssId:
          this.fileList.length > 0 ? this.fileList[0].id : ''
      }
      this.$http.post(src, params).then(({ data: res }) => {
        if (res.data) {
        } else {
        }
      })
    },
    initWebSocket () {
      // 初始化weosocket
      const wsuri =
        window.SITE_CONFIG['wsURL'] + '/websocket/' + this.dataForm.code
      this.websock = new WebSocket(wsuri)
      this.websock.onmessage = this.websocketonmessage
      this.websock.onopen = this.websocketonopen
      this.websock.onerror = this.websocketonerror
      this.websock.onclose = this.websocketclose
    },
    websocketonopen () {
      // 连接建立之后执行send方法发送数据
      let actions = {}
      this.websocketsend(JSON.stringify(actions))
    },
    goPath (val) {
      if (val === 'bgguaranteeletterDetail') {
        this.$router.push({
          name: val,
          query: {
            seeLetterId: this.$route.query.letterId,
            JumpName: 'bgguaranteeletterSqf'
          }
        })
      } else {
        this.$router.push({
          name: val
        })
      }
    },
    /**
     * 连接建立失败,断开连接
     * 1.查询一次数据库数据
     * 2.查询完后再次建立socket连接
     * */
    websocketonerror () {
      // 连接建立失败重连
      // let _this = this
      console.log('连接建立失败')
      // this.websock.onclose()
      setTimeout(() => {
        if (this.sockTime < 10) { this.sockTime++ }
        this.initWebSocket()
      }, 2000)
    },
    websocketonmessage (e) {
      // 数据接收
      // const redata = JSON.parse(e.data)
      console.log(e.data)
      if (e.data === 'success') {
        this.sockTime = 0
        this.isPay = true
        setTimeout(() => {
          this.dialogVisible = false
          this.getInfo()
          this.websocketclose()
        }, 3000)
      }
      if (e.data === 'failure') {
        this.$message.error('支付失败！')
        this.isend(1)
      }
    },
    websocketsend (Data) {
      // 数据发送
      this.websock.send(Data)
    },
    websocketclose (e) {
      // 关闭
      this.websock.close()
      console.log('断开连接', e)
    },
    // pay/alipay/query?code=123&key=1

    getInfo () {
      this.$loading({
        lock: true,
        text: `获取订单信息中...`
      })
      // 1303131917304668162
      let src = `letter/bgguaranteeletter/${this.$route.query.letterId}`
      this.$http.get(src).then(({ data: res }) => {
        this.$loading().close()

        if (res.data) {
          this.getfile()
          this.dataForm = res.data

          this.getpakeage()
          if (this.dataForm.letterStatus === '45') {
            this.payway = 'offline'
          }
          if (this.dataForm.issueCode === 'TAIBDB') {
            // offline ALIPAY
            this.payway = 'ALIPAY'
          }
          this.getOfflinePayBankName()
          if (this.websock) { this.websocketclose() }
          this.initWebSocket()
        }
      })
    }
  }
}
</script>
<style  scoped>
.box {
  width: 100%;
  height: 100vh;
  background: #f7f7f7;
}
.dot {
  display: inline-block;
  width: 25px;
  height: 25px;
  /* padding: 5px; */
  left: -8px;
  top: -7px;
  position: absolute;
  background-color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #f59600;
  border: 1px solid rgb(241, 130, 65);
}
.bg {
  width: 469px;
  height: 314px;
  margin: 15px auto;
}
.bg .core {
  position: relative;
  top: 20px;
  left: 33px;
}
.paySuc {
  width: 270px;
  text-align: center;
  /* margin-top: 38px; */
  position: relative;
  top: 85px;
}
.ALIPAY {
  background: url(~@/assets/img/ALIPAY.jpg) no-repeat;
  background-size: 100% 100%;
}
.WXPAY {
  background: url(~@/assets/img/WXPAY.jpg) no-repeat;
  background-size: 100% 100%;
}
.result {
  margin: 30px auto;
  text-align: center;
}
.slogen {
  font-size: 15px;
  font-family: '黑体';
  position: relative;
  top: 5px;
  left: 5px;
}
.tip {
  margin-top: 30px;
  margin-bottom: 30px;
  font-size: 15px;
  font-family: '黑体';
}
.core {
  cursor: pointer;
}
.statusBox {
  width: 400px;
  background: rgba(241, 130, 65, 0.15);
  margin: 30px auto;
  padding: 20px;
  color: rgb(241, 130, 65);
  border: 1px solid rgb(241, 130, 65);
}
#header {
  height: 75px;
  background: #fff;
  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
}
.header {
  width: 1200px;
  margin: 0 auto;
  background: #fff;
  overflow: hidden;
}
.header > div {
  line-height: 75px;
  font-size: 16px;
  float: left;
  color: #666;
}
.header > img {
  height: 35px;
  margin: 25px 10px 0 0;
  float: left;
}
.content {
  width: 1200px;
  margin: 0 auto;
}
.trade {
  overflow: hidden;
  padding: 30px 0;
  height: 200px;
}
.trade .left {
  float: left;
}
.trade .left p {
  color: #8b8b8b;
  font-size: 16px;
  line-height: 36px;
  margin: 0;
}
.trade .left b {
  color: #353535;
}
.trade .right {
  float: right;
  padding-top: 50px;
}
.trade .price span {
  font-size: 40px;
}
.trade .price {
  font-size: 16px;
  color: orangered;
}
.article {
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-top: 3px solid #f59600;
  padding: 0 18px 35px 30px;
}
.article .title {
  font-size: 16px;
  color: #666666;
  margin: 25px 0;
}
.article /deep/.el-radio__label img {
  height: 24px;
  position: absolute;
  top: 7px;
}
.bank-type /deep/.el-radio__label .name {
  padding-left: 30px;
}
.qrcode {
  text-align: center;
}
.qrcode .tip {
  font-size: 16px;
  color: #666;
  padding-top: 20px;
}
.dialog /deep/ .el-dialog__header {
  border-bottom: 1px solid #eee;
  padding: 15px 20px;
}
.dialog /deep/ .el-dialog__footer {
  border-top: 1px solid #eee;
  padding: 15px 20px;
}
.offline span {
  display: inline-block;
  width: 80px;
}
</style>
