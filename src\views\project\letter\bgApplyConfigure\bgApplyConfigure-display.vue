<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-23 14:48:23
 * @LastEditors: k<PERSON>weiqiang
 * @LastEditTime: 2021-06-24 09:25:46
-->
<template>
  <el-dialog :visible.sync="visible" title="信息详情" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
    <el-form :model="dataForm" v-loading='loading' ref="dataForm" :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
      <el-row :gutter="20">
        <el-col :xs="24" :lg="colConfig">
          <el-form-item label="编码" prop="code" label-width="190px">
            <span>{{dataForm.code}}</span>
          </el-form-item>
          <el-form-item label="名称" prop="name" label-width="190px">
            <span>{{dataForm.name}}</span>
          </el-form-item>
          <el-form-item label="保函模板类型" prop="path" label-width="190px">
            <span>{{dataForm.path}}</span>
          </el-form-item>
          <el-form-item label="类型" prop="type" label-width="190px">
            <span>{{dataForm.type=='0'?'保函':'出具机构'}}</span>
          </el-form-item>
          <el-form-item label="保函时长" prop="time" v-if="dataForm.type==1" label-width="190px">
            <span>{{dataForm.time}}</span>
          </el-form-item>
          <el-form-item label="出具机构说明" v-if="dataForm.type==1" prop="tips" label-width="190px">
            <span>{{dataForm.tips}}</span>
          </el-form-item>
          <el-form-item label="出具机构可支持的保函类型" v-if="dataForm.type==1" prop="jurisdiction" label-width="190px">
            <span>{{dataForm.jurisdiction}}</span>
          </el-form-item>
          <el-form-item label="状态" prop="status" label-width="190px">
            <span v-if="dataForm.status == 0">停用</span>
            <span v-if="dataForm.status == 1">正常</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template slot="footer">
      <el-button type="primary" @click="visible = false">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      loading: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        icon: '',
        path: '',
        type: '',
        time: '',
        tips: '',
        jurisdiction: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        status: 1
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.loading = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http
        .get(`/letter/bgApplyConfigure/${this.dataForm.id}`)
        .then(({ data: res }) => {
          this.loading = false

          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
        })
        .catch(() => {})
    }
  }
}
</script>
