<template>
    <el-dialog :visible.sync="visible" title="信息详情" :close-on-click-modal="false" :close-on-press-escape="false" width="70%">
        <el-form :model="dataForm" v-loading='loading' ref="dataForm" :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
        <el-row :gutter="20">
                <el-col :xs="24" :lg="colConfig">
                                                                                                                                                    <el-form-item label="结算编码" prop="code" label-width="190px">
                                      <span>{{dataForm.code}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="结算名称" prop="name" label-width="190px">
                                      <span>{{dataForm.name}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="出具机构id" prop="issueId" label-width="190px">
                                      <span>{{dataForm.issueId}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="出具机构编码" prop="issueCode" label-width="190px">
                                      <span>{{dataForm.issueCode}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="出具机构名称" prop="issueName" label-width="190px">
                                      <span>{{dataForm.issueName}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="保函类型" prop="guaranteeType" label-width="190px">
                                      <span>{{dataForm.guaranteeType}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="保额区间" prop="amountRange" label-width="190px">
                                      <span>{{dataForm.amountRange}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="结算类型 1 定额 2按比例" prop="settlementType" label-width="190px">
                                      <span>{{dataForm.settlementType}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="百分比" prop="percent" label-width="190px">
                                      <span>{{dataForm.percent}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="金额" prop="price" label-width="190px">
                                      <span>{{dataForm.price}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="代理开票" prop="agentInvoicingPercent" label-width="190px">
                                      <span>{{dataForm.agentInvoicingPercent}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="代理不开票" prop="agentPercent" label-width="190px">
                                      <span>{{dataForm.agentPercent}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="区域奖金" prop="regionalBonus" label-width="190px">
                                      <span>{{dataForm.regionalBonus}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="服务描述" prop="description" label-width="190px">
                                      <span>{{dataForm.description}}</span>
                                </el-form-item>
                                                                                                                                                            <el-form-item label="排序" prop="orderCode" label-width="190px">
                                      <span>{{dataForm.orderCode}}</span>
                                </el-form-item>
                                                                                                                                                                                                        <el-form-item label="状态" prop="status" label-width="190px">
                                    <span v-if="dataForm.status == 0">停用</span>
                                    <span v-if="dataForm.status == 1">正常</span>
                                </el-form-item>
                                                                                                                                                                                                                                                                        </el-col>
            </el-row>
        </el-form>
        <template slot="footer">
            <el-button type="primary" @click="visible = false">确定</el-button>
        </template>
    </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      colConfig: 1, // 列配置 1 2列
      visible: false,
      loading: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        issueId: '',
        issueCode: '',
        issueName: '',
        guaranteeType: '',
        amountRange: '',
        settlementType: '',
        percent: '',
        price: '',
        agentInvoicingPercent: '',
        agentPercent: '',
        regionalBonus: '',
        description: '',
        orderCode: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  created () {
    this.colConfig = 24 / this.colConfig
  },
  methods: {
    init () {
      this.visible = true
      this.loading = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/letter/bgsettlementconfig/${this.dataForm.id}`).then(({ data: res }) => {
        this.loading = false
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    }
  }
}
</script>
