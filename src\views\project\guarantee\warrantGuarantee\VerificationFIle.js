/*
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-02-01 09:22:09
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-02-18 14:44:34
 */
const VerificationFIle = function (list) {
  return new Promise((resolve, reject) => {
    let fileArr = deep(list)
    let errorArr = []
    console.log(fileArr)
    fileArr.map((a) => {
      // 验证必填文件是否上传
      if (a.isUploadSupported === 1 && (a.isRequired === 1 && !a.ossEntity)) {
        // + str.length > 0 ? ',' + str.join(',') : ''
        // console.log(str.length > 0 ? ',' + str.join(',') : '')

        addArr(a.fileName, `必填文件未上传`)
      } else {
        if (a.isLegalSign === 1 || a.isOfficialSign === 1 || a.isWriteSign === 1) {
          // if (a.isLegalSign === 1 && a.ossEntity && a.ossEntity.applyLegalSign !== 1) addArr(a.fileName, `申请方法人未签章`)
          // if (a.isOfficialSign === 1 && a.ossEntity && a.ossEntity.applyOfficialSign !== 1) addArr(a.fileName, `申请方公章未签章`)
          // if (a.isWriteSign === 1 && a.ossEntity && a.ossEntity.applyWriteSign !== 1) addArr(a.fileName, `申请方手写章未签章`)
        }
      }
    })
    function addArr (name, msg) {
      if (errorArr.filter(a => a.name === name).length > 0) {
        errorArr.filter(a => a.name === name)[0].msg = errorArr.filter(a => a.name === name)[0].msg + `,${msg}`
      } else {
        errorArr.push({
          name: name,
          tip: msg
        })
      }
    }
    // console.log(errorArr)
    resolve(errorArr)
  })
}
const deep = function (node) {
  let stack = JSON.parse(JSON.stringify(node))
  let data = []
  while (stack.length !== 0) {
    let pop = stack.pop()
    data.push(pop)
    let children = pop.children
    if (children) {
      for (let i = children.length - 1; i >= 0; i--) {
        stack.push(children[i])
      }
    }
  }
  return data
}

export { VerificationFIle }
