<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-09-30 10:40:00
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2022-09-19 15:51:53
-->
<template>
  <el-dialog :visible.sync="visible" title="出具发票" :close-on-click-modal="false" :close-on-press-escape="false">
    <!-- <el-button type="primary" @click="downloadExcel()">下载模板</el-button> -->
    <el-form :model="dataForm" v-if="JSON.stringify(dataForm) !== '{}'" label-position="left" ref="dataForm" label-width="145px">
      <el-card class="box-card" shadow='never'>
        <div slot="header" class="clearfix">
          <span class="title">发票信息</span>
        </div>
        <div>
          <el-row>
            <el-col :span="12">
              <el-form-item label="发票类型">
                {{certType(dataForm.invoiceType,'invoiceTypeoptions')}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开票对象">
                {{certType(dataForm.invoiceObject,'invoiceObjectoptions')}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纳税人类型">
                {{certType(dataForm.taxpayerType,'taxpayerTypeoptions')}}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纳税人识别号">
                {{fomate('taxpayerNumber')}}
              </el-form-item>
            </el-col>
            <template >
              <el-col :xs="24" :lg="24">
                <el-form-item label="发票抬头" prop="invoiceTitle">
                  {{fomate('invoiceTitle')}}
                </el-form-item>
                <el-form-item label="银行账户名称" prop="bankAccountName">
                  {{fomate('bankAccountName')}}
                </el-form-item>
              </el-col>
              <el-col :xs="24" :lg="12">
                <el-form-item label="银行账户号码" prop="bankAccountNo">
                  {{fomate('bankAccountNo')}}
                </el-form-item>
                <el-form-item label="税务登记电话" prop="phoneNumber">
                  {{fomate('phoneNumber')}}
                </el-form-item>
              </el-col>
              <el-col :xs="24" :lg="12">
                <el-form-item label="发票接收邮箱" prop="electronicInvoiceEmail">
                  {{fomate('electronicInvoiceEmail')}}
                </el-form-item>
                <el-form-item label="发票接收手机号" prop="electronicInvoicePhone">
                  {{fomate('electronicInvoicePhone')}}
                </el-form-item>
              </el-col>
              <el-col :xs="24" :lg="24">
                <el-form-item label="税务登记地址" prop="address">
                  {{fomate('address')}}
                </el-form-item>
              </el-col>
              <el-col :xs="24" :lg="24">
                <el-form-item label="备注" prop="remark">
                  {{fomate('remark')}}
                </el-form-item>
              </el-col>
            </template>

          </el-row>
        </div>
      </el-card>
    </el-form>
    <el-divider></el-divider>
    <el-upload v-if="dataForm.invoiceType=='1'" :before-remove="beforeRemove" :auto-upload="false" ref="upload" :limit="1" :action="url" :file-list="fileList" drag  :before-upload="beforeUploadHandle" :on-remove='handleRemove' :on-success="successHandle" class="text-center">
      <i class="el-icon-upload"></i>
      <div class="el-upload__text" v-html="$t('upload.text')"></div>
      <div class="el-upload__tip" slot="tip">{{ $t('upload.tip', { 'format': 'jpg，jpeg，png，pdf，zip，rar' }) }}</div>
    </el-upload>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close()">取消</el-button>
      <el-button type="primary" @click="submitUpload">出具</el-button>
    </span>
  </el-dialog>
</template>

<script>
// import uploadMixin from '@/mixins/uploadMixin'
import Cookies from 'js-cookie'
import { getDict } from '@/utils/index'
export default {
  // mixins: [uploadMixin],
  props: {
    // dataForm: Object,
    // options: Object
  },
  data () {
    return {
      letterId: '',
      type: '',
      visible: false,
      url: '',
      dataForm: {},
      options: {
        invoiceTypeoptions: getDict('发票类型'),
        invoiceObjectoptions: getDict('开票对象'),
        taxpayerTypeoptions: getDict('纳税人类型')
      },
      num: 0,
      fileList: []
    }
  },
  methods: {
    init () {
      this.visible = true
      this.url = `${
        window.SITE_CONFIG['apiURL'] + '/letter/bgguaranteeinvoice/uploadInvoice'
      }?letterId=${this.dataForm.letterId}&token=${Cookies.get('token')}`
      this.num = 0
      this.fileList = []
    },
    submitUpload () {
      if (this.dataForm.invoiceType === '1') {
        this.$refs.upload.submit()
      } else {
        this.$http
          .post(`/letter/bgguaranteeinvoice/uploadInvoice?letterId=${this.dataForm.letterId}`)
          .then(({ data: res }) => {
            this.$message.success('出具成功')
            this.close()
          })
          .catch((rej) => {
          })
      }
    },
    close () {
      this.fileList = []
      this.visible = false
      this.$emit('refresh')
    },
    fomate (val) {
      return this.dataForm[val] ? this.dataForm[val] : '-'
    },
    certType (val, name) {
      // console.log(this.options)
      var aa = this.options[name].filter((a) => a.dictCode === val)
      if (name === 'letterStatusoptions') {
        console.log(aa)
      }
      return aa.length > 0 ? aa[0].dictName : '-'
    },
    beforeRemove (file, fileList) {
      // console.log(file, fileList)
      let a = true
      if (file && file.status === 'success') {
        a = this.$confirm(`确定移除 ${file.name}？`)
      }
      return a
    },
    handleRemove (file, fileList) {
      if (file && file.status === 'success') {
        // 移除方法
        var deleteId = file.response.data.ossId
        return this.deleteFile(deleteId, 0)
      }
    },
    deleteFile (id, type) {
      return new Promise(async (resolve, reject) => {
        let { data } = await this.$http.get('letter/bgguaranteeinvoice/deleteInvoiceFile?ossId=' + id + '&letterId=' + this.dataForm.letterId)
        if (data.code !== 0) {
          this.$message.error(data.msg)
          resolve(false)
        }
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
        resolve(true)
      })
    },
    // 上传之前
    beforeUploadHandle (file) {
      let extension = file.name.substring(file.name.lastIndexOf('.') + 1)
      console.log(extension)
      // ,zip,rar
      if (extension !== 'rar' && extension !== 'zip' && extension !== 'jpg' && extension !== 'png' && extension !== 'jpeg' && extension !== 'JPG' && extension !== 'PNG' && extension !== 'JPEG' && extension !== 'pdf' && extension !== 'PDF') {
        this.$message.warning(
          this.$t('upload.tip', { format: 'jpg,png,jpeg,pdf,zip,rar' })
        )
        return false
      }

      this.num++
    },
    handleExceed (files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件`
      )
    },
    // 上传成功
    successHandle (res, file, fileList) {
      if (res.data.flag !== true) {
        return this.$message.error(res.data.msg)
      }
      this.fileList = fileList
      this.$message.success('出具成功')
      this.$emit('refresh')
      this.close()
    },
    downloadExcel () {
      var url = `${
        window.SITE_CONFIG['apiURL'] + this.downloadUrl
      }?token=${Cookies.get('token')}`
      window.location.href = url
    }
  }
}
</script>
<style lang="scss" scoped>
.title {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 15px;
  border-left: 6px solid rgb(241, 130, 65);
  text-indent: 15px;
  font-weight: bold;
  /* padding: 16px 0; */
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
