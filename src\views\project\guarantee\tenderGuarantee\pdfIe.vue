<template>
  <div class="card">
    <el-card class="box-card">
      <steps :active='3'></steps>
      <el-card class="box-card" shadow="never">
        <div style="padding: 3px;">
          <div style="padding: 3px; border-color: grey">
            <object ref="webPDFObj" classid="CLSID:6EE0E832-A96C-480C-AE0A-EB35A9BB9651" v-show="show" width="100%" style="height:500px;float: left">
            </object>

          </div>
        </div>
      </el-card>
      <!-- {{src}} -->
    </el-card>
    <div class="foot">
      <span>
        <!-- <el-button type="primary" @click="backStep()" plain>上一步</el-button> -->
        <el-button type="primary" @click="sing()" v-if="zhangCount===0">签章</el-button>
        <el-button type="primary" @click="submitHandle()" v-if="zhangCount>0">完成签章</el-button>
        <el-tooltip content="点击签章按钮进行盖章" placement="bottom">
          <i class="el-icon-info"></i>
        </el-tooltip>
      </span>
    </div>
  </div>
</template>
<script>
import steps from '@/views/project/guarantee/components/stepA'
export default {
  data () {
    return {
      id: '',
      webPDFObj: '',
      src:
        `${window.SITE_CONFIG['apiURL']}` +
        '/letter/bgguaranteetemplate/applyPDF2/',
      savesrc:
        `${window.SITE_CONFIG['apiURL']}` +
        '/letter/bgguaranteetemplate/savePDF2/',
      height: '900px',
      show: true,
      zhangCount: 0
    }
  },
  created () {
    this.id = this.$route.query.letterId
  },
  mounted () {
    this.init()
    console.log(this.src)
  },
  components: { steps },
  methods: {
    init () {
      try {
        this.$nextTick(() => {
          this.webPDFObj = this.$refs['webPDFObj']
          this.webPDFObj.OpenNetFile(
            this.src + this.id + '?wtsCode=entrustment_contract'
          )
          this.webPDFObj.ShowToolbar(false)
        })
      } catch (e) {
        alert(e)
        // console.log(e)
      }
    },
    backStep () {
      this.$store.state.contentTabs = this.$store.state.contentTabs.filter(
        (item) => item.name !== 'pdf'
      )
      this.$router.push({
        name: this.$route.params.backName,
        params: {
          type: this.$route.params.type,
          id: this.$route.params.tid
        }
      })
    },
    ShowExceptionError (e) {
      if (e.name === 'TypeError') {
        alert('JS错误：' + e.message)
      } else {
        try {
          alert('WebPDF错误：' + this.webPDFObj.GetErrMsg())
        } catch (ee) {
          alert('JS内部错误：' + ee.message)
        }
      }
    },
    sing () {
      try {
        // 关键字签章
        // 参数1：关键字
        // 参数2：可选参数，时间戳使用方式，0表示在配置时间戳试使用时间戳，1表示不使用时间戳
        this.webPDFObj.SealKeyWord('【授权企业盖章位置】', 1)
        this.$message.success('签章成功')
        this.TestGetSeal()
      } catch (e) {
        this.ShowExceptionError(e)
      }
      // this.TestGetSeal()
    },
    TestGetSeal () {
      try {
        // 获取文档中签章数量
        // 返回：文档中签章的数量
        this.zhangCount = this.webPDFObj.GetSealCount()
      } catch (e) {
        alert('错误：' + this.webPDFObj.GetErrMsg())
      }
    },
    submitHandle () {
      try {
        let result = this.webPDFObj.SaveNetFile(this.savesrc + this.id)
        if (result === null || result === '') {
          this.show = false
          this.$confirm('签章完成，返回系统进行下一步操作', '提示', {
            confirmButtonText: '确认',
            showClose: false,
            showCancelButton: false,
            type: 'warning'
          }).then(() => {
            window.open('', '_self')
            window.close()
          })
        } else {
          alert(result)
        }
      } catch (e) {
        this.ShowExceptionError(e)
        return false
      }
      return true
    },
    changePdfPage (val) {
      console.log(val)
      if (val === 0 && this.currentPage > 1) {
        this.currentPage--
        console.log(this.currentPage)
      }
      if (val === 1 && this.currentPage < this.pageCount) {
        this.currentPage++
        console.log(this.currentPage)
      }
    },
    loadPdfHandler (e) {
      this.currentPage = 1
    }
  }
}
</script>
<style lang="scss" scoped>
.el-icon-info {
  font-size: 15px !important;
  cursor: pointer;
  position: relative;
  left: 10px;
  color: #e6a23c;
}
.card {
  min-width: 900px;
  max-width: 1200px;
  margin: 15px auto;
  margin-bottom: 65px;
}
.foot {
  width: 100%;
  background: #fff;
  position: fixed;
  bottom: 0;
  padding: 10px 0;
  border-top: 1px solid #ddd;
  background: #f9f9f9;
  text-align: center;
  left: 0px !important;
  z-index: 999;
  span {
    text-align: center;
  }
}
</style>
