<template>
    <el-dialog :visible.sync="visible" :title="$t('oss.upload')" :close-on-click-modal="false" :close-on-press-escape="false">
        <el-upload
                :action="url"
                :file-list="fileList"
                drag
                multiple
                :before-upload="beforeUploadHandle"
                :on-success="successHandle"
                class="text-center">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text" v-html="$t('upload.text')"></div>
            <div class="el-upload__tip" slot="tip">{{ $t('upload.tip', { 'format': 'jpg、png、gif、txt、doc、xlsx、xls、docx、zip、pdf'  }) }}</div>
        </el-upload>
    </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      url: '',
      num: 0,
      dataForm: {
        projectCode: '',
        projectName: '',
        projectId: '',
        phase: '',
        ywTableName: '',
        ywTableId: ''
      },
      fileList: []
    }
  },
  methods: {
    init (params) {
      this.dataForm = {
        ...this.dataForm,
        ...params
      }
      this.visible = true
      this.url = `${window.SITE_CONFIG['apiURL']}/sys/oss/fileupload?projectCode=${this.dataForm.projectCode}&projectName=${this.dataForm.projectName}&projectId=${this.dataForm.projectId}&phase=${this.dataForm.phase}&ywTableName=${this.dataForm.ywTableName}&ywTableId=${this.dataForm.ywTableId}`
      this.num = 0
      this.fileList = []
    },
    // 上传之前需要格式微调
    beforeUploadHandle (file) {
      if (file.type !== 'application/x-zip-compressed' && file.type !== 'application/x-zip-compressed' && file.type !== 'application/pdf' &&
        file.type !== 'application/msword' && file.type !== 'application/msword' && file.type !== 'application/vnd.ms-excel' && file.type !== 'application/vnd.ms-excel' && file.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' && file.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' && file.type !== 'text/plain' && file.type !== 'text/plain' && file.type !== 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' && file.type !== 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' && file.type !== 'image/png' && file.type !== 'image/jpg' && file.type !== 'image/jpeg' && file.type !== 'image/png' && file.type !== 'image/gif') {
        this.$message.error(this.$t('upload.tip', { 'format': 'jpg、png、gif、txt、doc、xlsx、xls、docx、zip、pdf' }))
        return false
      }
      this.num++
    },
    // 上传成功需要微调格式去除空格
    successHandle (res, file, fileList) {
      if (res.code !== 0) {
        return
      }
      this.fileList = fileList
      this.num--
      if (this.num === 0) {
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            this.visible = false
            this.$emit('refreshDataList')
          }
        })
      }
    }
  }
}
</script>
