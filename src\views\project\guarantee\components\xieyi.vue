<!--
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-04-24 10:09:42
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-04-24 17:43:09
 -->
<template>
 <el-dialog
  title="服务协议"
  :visible.sync="visible"
  width="500px"
  :show-close='false'
  center
  >
 <div class="xieyi">
   <div style="overflow: hidden; margin-bottom: 0.5em;"><div style="width: 1.7em; margin-right: -1.7em; float: left;">1、</div> <div style="margin-left: 1.7em;">本平台为银行、担保公司、保函办理方提供交易撮合通道，旨在降低各方交易成本，提高工作效率。各方交易主体在业务办理过程中注意对接与沟通，自行承担因任何原因导致的的保函业务办理过程出现的问题以及由此产生的各种责任。</div></div>
  <div style="overflow: hidden; margin-bottom: 0.5em;"><div style="width: 1.7em; margin-right: -1.7em; float: left;">2、</div> <div style="margin-left: 1.7em;">由于收件信息填写错误导致的保函不能按期到达保函申请办理方，由保函申请办理方承担责任；因银行或担保公司的原因导致保函不能按期到达保函申请办理方，由银行或担保公司承担责任。</div></div>
  <div style="overflow: hidden; margin-bottom: 0.5em;"><div style="width: 1.7em; margin-right: -1.7em; float: left;">3、</div> <div style="margin-left: 1.7em;">保函申请办理方要保证提供的资料真实、准确、完整、有效。对于因保函申请办理方提供信息不真实或不完整所造成的损失由保函申请办理方自行承担。</div></div>
  <div style="overflow: hidden; margin-bottom: 0.5em;"><div style="width: 1.7em; margin-right: -1.7em; float: left;">4、</div> <div style="margin-left: 1.7em;">保函申请办理方在提交订单后，如不符合要求，银行或担保公司应当在规定的时间内在线拒绝保函申请办理方，否则即视为正常接单。</div></div>
  <!-- <div style="overflow: hidden; margin-bottom: 0.5em;"><div style="width: 1.7em; margin-right: -1.7em; float: left;">5、</div> <div style="margin-left: 1.7em;">如订单被退回，会存在相应的手续费，费用由保函申请办理方自行承担，费率为6‰（支付宝）。</div></div> -->
 </div>
  <span slot="footer" class="dialog-footer">
    <el-button type="primary" size="small" @click="visible = false">确 定</el-button>
  </span>
</el-dialog>
</template>
<script>
export default {
  data () {
    return {
      letterId: '',
      visible: false
    }
  },
  methods: {
    init () {
      this.visible = true
    }
  }
}
</script>
<style lang="scss" scoped>
  .xieyi{
    line-height: 25px;
  }
</style>
