<!--
 * @Descripttion:
 * @Author: kong<PERSON>qiang
 * @Date: 2020-08-15 09:52:29
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2021-06-28 14:29:02
-->
<template>
  <div class="aui-theme-tools" :class="{ 'aui-theme-tools--open': isOpen }">
    <div class="aui-theme-tools__toggle_o bzzx" style="top:60px;font-size:15px;" @click="goPath()">
      <!-- <i class="el-icon-more-outline"></i> -->
      <img src="@/assets/img/<EMAIL>" alt="">

      帮助中心
    </div>
    <div class="aui-theme-tools__content" style="padding: 20px 5px 20px;">
      <!-- <img src="@/assets/img/proess.jpg" style="width:100%;" alt=""> -->
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      isOpen: false,
      themeColor: 'default'
    }
  },
  mounted () {},
  methods: {
    goPath () {
      window.open('http://mindoc.hebhzjt.com/docs/mindoc/register', '_target')
    },
    modeChnage () {
      if (this.$store.state.sidebarMode) {
        this.$store.state.sidebarMenuList = window.SITE_CONFIG['menuList']
      } else {
        this.$store.state.sidebarMenuList =
          window.SITE_CONFIG['menuList'][0].children
        this.$store.state.navbarActive = 0
      }
    },
    themeColorChangeHandle (val) {
      this.$store.state.navbarThemeColor = val
      var styleList = [
        {
          id: 'J_elementTheme',
          url: `${
            process.env.BASE_URL
          }element-theme/${val}/index.css?t=${new Date().getTime()}`
        },
        {
          id: 'J_auiTheme',
          url: `${
            process.env.BASE_URL
          }element-theme/${val}/aui.css?t=${new Date().getTime()}`
        }
      ]
      for (var i = 0; i < styleList.length; i++) {
        var el = document.querySelector(`#${styleList[i].id}`)
        if (el) {
          el.href = styleList[i].url
          continue
        }
        el = document.createElement('link')
        el.id = styleList[i].id
        el.href = styleList[i].url
        el.rel = 'stylesheet'
        document.querySelector('head').appendChild(el)
      }
    }
  }
}
</script>
<style lang="scss">
.bzzx {
  width: 40px;
  height: 130px;
  background: #f18241;
  border-radius: 6px;
  color: white;
  font-size: 16px;
  font-family: SourceHanSansCN-Regular, SourceHanSansCN;
  font-weight: 400;
  color: #ffffff;
  img {
    width: 20px;
    display: inline-block;
    margin: 5px 0 10px;
  }
}
</style>
