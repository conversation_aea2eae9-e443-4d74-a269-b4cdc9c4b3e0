<!--
 * @Descripttion:
 * @Author: kong<PERSON>qiang
 * @Date: 2020-08-26 09:43:09
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-11-05 16:27:08
-->
<template>
    <el-dialog :visible.sync="visible" :title="$t('oss.upload')" :close-on-click-modal="false" :close-on-press-escape="false">
        <el-upload
                :action="url"
                :file-list="fileList"
                drag
                multiple
                :before-upload="dataForm.name=='营业执照'?yybeforeUploadHandle:beforeUploadHandle"
                :on-success="successHandle"
                class="text-center">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text" v-html="$t('upload.text')"></div>
            <div class="el-upload__tip" slot="tip" v-if="dataForm.name=='营业执照'">{{ $t('upload.tip', { 'format': '.jpg,.JPG,.jpeg,.JPEG,.png,.PNG'  }) }}</div>
            <div class="el-upload__tip" slot="tip" v-else>{{ $t('upload.tip', { 'format': 'jpg、png、gif、txt、doc、xlsx、xls、docx、zip、pdf'  }) }}</div>
        </el-upload>
    </el-dialog>
</template>

<script>
import Cookies from 'js-cookie'
export default {
  data () {
    return {
      visible: false,
      url: '',
      num: 0,
      dataForm: {
        projectCode: '',
        projectName: '',
        projectId: '',
        phase: '',
        id: '',
        ywTableName: '',
        ywTableId: ''
      },
      fileList: []
    }
  },
  methods: {
    init (params) {
      this.dataForm = {
        ...this.dataForm,
        ...params
      }
      this.visible = true
      this.url = `${window.SITE_CONFIG['apiURL']}/letter/bgguaranteeapply/registerFileupload?id=${this.dataForm.id}&token=${Cookies.get('token')}`
      this.num = 0
      this.fileList = []
    },
    // 上传之前需要格式微调
    beforeUploadHandle (file) {
      if (file.type !== 'application/x-zip-compressed' && file.type !== 'application/x-zip-compressed' && file.type !== 'application/pdf' &&
        file.type !== 'application/msword' && file.type !== 'application/msword' && file.type !== 'application/vnd.ms-excel' && file.type !== 'application/vnd.ms-excel' && file.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' && file.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' && file.type !== 'text/plain' && file.type !== 'text/plain' && file.type !== 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' && file.type !== 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' && file.type !== 'image/png' && file.type !== 'image/jpg' && file.type !== 'image/jpeg' && file.type !== 'image/png' && file.type !== 'image/gif') {
        this.$message.error(this.$t('upload.tip', { 'format': 'jpg、png、gif、txt、doc、xlsx、xls、docx、zip、pdf' }))
        return false
      }
      this.num++
    },
    yybeforeUploadHandle (file) {
      var extension = file.name.substring(file.name.lastIndexOf('.') + 1)
      var lastName = extension !== 'jpg' &&
        extension !== 'JPG' &&
        extension !== 'jpeg' &&
        extension !== 'JPEG' &&
        extension !== 'png' &&
        extension !== 'PNG'
      if (
        lastName
      ) {
        this.$message.warning('文件要求格式为：.jpg,.JPG,.jpeg,.JPEG,.png,.PNG')
        return !lastName
      }
      this.num++
    },
    // 上传成功需要微调格式去除空格
    successHandle (res, file, fileList) {
      if (res.code !== 0) {
        return
      }
      this.fileList = fileList
      this.num--
      if (this.num === 0) {
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            this.visible = false
            this.$emit('refreshDataList')
          }
        })
      }
    }
  }
}
</script>
