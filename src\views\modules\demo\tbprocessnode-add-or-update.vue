<template>
    <div>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"  :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
          <el-form-item label="节点编码" prop="code">
     <el-input v-model="dataForm.code" placeholder="节点编码"></el-input>
</el-form-item>
                <el-form-item label="节点名称" prop="name">
     <el-input v-model="dataForm.name" placeholder="节点名称"></el-input>
</el-form-item>
                <el-form-item label="审核角色名称" prop="auditRoleName">
     <el-input v-model="dataForm.auditRoleName" placeholder="审核角色名称"  readonly="readonly">
         <el-button slot="append" icon="el-icon-circle-plus" @click="selectShow1" ref="searchId" @refreshDataList="getDataList()"></el-button>
     </el-input>
</el-form-item>
             <!--   <el-form-item label="抄送角色名称" prop="sendRoleName" >
     <el-input v-model="dataForm.sendRoleName" placeholder="抄送角色名称" clearable>
         <el-button slot="append" icon="el-icon-circle-plus" @click="selectShow2" ref="searchId" @refreshDataList="getDataList()"></el-button>
     </el-input>
</el-form-item>-->
                <el-form-item label="节点流程顺序" prop="nodeNo">
     <el-input v-model="dataForm.nodeNo" placeholder="节点流程顺序"></el-input>
</el-form-item>
                <el-form-item label="流程类型" prop="nodeObjectName">
     <el-input v-model="dataForm.nodeObjectName" placeholder="流程类型"></el-input>
</el-form-item>
               <!-- <el-form-item label="节点状态" prop="nodeStatus">
     <el-input v-model="dataForm.nodeStatus" placeholder="节点状态"></el-input>
</el-form-item>-->
                  <el-form-item label="状态" prop="status" size="mini">
   <el-radio-group v-model="dataForm.status">
       <el-radio :label="0">停用</el-radio>
       <el-radio :label="1">正常</el-radio>
   </el-radio-group>
                  </el-form-item>
       <el-form-item label="项目名称" prop="projectName" hidden="hidden">
            <el-input v-model="dataForm.projectName" placeholder="项目名称" >
            </el-input>
        </el-form-item>
        <el-input v-model="dataForm.projectId" placeholder="项目ID" hidden="hidden"></el-input>
        <el-input v-model="dataForm.projectCode" placeholder="项目编码" hidden="hidden"></el-input>
        <el-form-item hidden label="审核角色id" prop="auditRoleId">
            <el-input v-model="dataForm.auditRoleId" placeholder="审核角色id"></el-input>
        </el-form-item>
        <!--<el-form-item hidden label="抄送角色id" prop="auditRoleName">
            <el-input v-model="dataForm.auditRoleName" placeholder="抄送角色id"></el-input>
        </el-form-item>-->
                    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确认</el-button>
    </template>
  </el-dialog>
    <select-role v-if="selectVisible1" ref="selectRole"  @refreshDataList="getDataList()"></select-role>
    <select-role-send v-if="selectVisible2" ref="selectRoleSend"  @refreshDataList="getDataList()"></select-role-send>
    </div>
</template>

<script>
import debounce from 'lodash/debounce'
import SelectRole from '@/views/modules/sys/role-select'
import SelectRoleSend from '@/views/modules/sys/role-selectSend'
export default {
  data () {
    return {
      visible: false,
      selectVisible: false,
      selectVisible1: false,
      selectVisible2: false,
      dataForm: {
        id: '',
        code: '',
        name: '',
        auditRoleId: '',
        auditRoleCode: '',
        auditRoleName: '',
        sendRoleId: '',
        sendRoleCode: '',
        sendRoleName: '',
        nodeNo: '',
        nodeObjectName: '',
        nodeStatus: '',
        deptId: '',
        status: 1,
        creator: '',
        createDate: '',
        updater: '',
        projectId: '',
        projectCode: '',
        projectName: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        code: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        auditRoleName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        nodeNo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        nodeObjectName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        status: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]

      }
    }
  },
  components: {
    SelectRole,
    SelectRoleSend
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/demo/tbprocessnode/${this.dataForm.id}`).then(({ data: res }) => {
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        if (this.dataForm.sendRoleName === '') {
          this.dataForm.sendRoleId = '0'
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/demo/tbprocessnode/', this.dataForm).then(({ data: res }) => {
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      }
      )
    }, 1000, { 'leading': true, 'trailing': false }),
    // 按钮点击事件
    selectShow1 () {
      this.selectVisible1 = true
      this.$nextTick(() => {
        this.$refs.selectRole.init()
      })
    },
    selectShow2 () {
      this.selectVisible2 = true
      this.$nextTick(() => {
        this.$refs.selectRoleSend.init()
      })
    }
  }
}
</script>
