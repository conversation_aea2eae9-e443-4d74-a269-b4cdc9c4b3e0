<!--
 * @Descripttion:
 * @Author: k<PERSON><PERSON>qiang
 * @Date: 2020-08-06 18:47:13
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-09-25 09:01:47
-->
<template>
  <el-dialog class="CAmodel" :visible.sync="visible" title="提示" :before-close='beforeClose' :fullscreen="fullscreen" :close-on-click-modal="false" :close-on-press-escape="false" width="600px">
    <div slot='title'>
      <h3>签章</h3>
      <el-button @click="fullscreen=!fullscreen" class="el-dialog__headerbtn diaFull" type="text"><i class="el-icon-full-screen"></i></el-button>
    </div>
    <div class="min550">
      <iframe v-if="dataForm" :src="src+'ca.html'" :data-form='JSON.stringify(dataForm)' id="caModel" class="min550" style="width:100%;height:100%;border:none;"></iframe>
    </div>
  </el-dialog>
</template>
<script>
export default {
  data () {
    return {
      fullscreen: false,
      visible: false,
      dataForm: {},
      webPDFObj: '',
      businessLicenseId: '',
      biddingDocumentId: '',
      src: ''
    }
  },
  mounted () {
    this.src = process.env.BASE_URL
  },
  watch: {
    dataForm: {
      handler (a) {
        this.dataForm = a
      },
      deep: true
    }
  },
  methods: {
    init () {
      this.visible = true
      this.fullscreen = false
      this.$nextTick(() => {

      })
    },
    beforeClose (done) {
      this.visible = false
      done()
    }

  }
}
</script>
<style lang="scss" scoped>
  .CAmodel .el-dialog__body{
    max-height: none;
    padding: 5px 20px;
    min-height: 550px;
    height: calc(100% - 81px);
  }
  .min550{
    min-height: 550px;
    height: 100%;
  }

</style>
