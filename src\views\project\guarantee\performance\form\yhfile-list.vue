<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2023-03-20 14:20:44
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2023-03-20 14:39:34
-->
<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-oss__oss">
      <el-table
        v-loading="dataListLoading"
        :data="dataList"
        sizi="mini"
        border
        @selection-change="dataListSelectionChangeHandle"
        @sort-change="dataListSortChangeHandle"
        style="width: 100%;">
        <el-table-column prop="typeName" label="目录" header-align="center" align="center"></el-table-column>
        <el-table-column prop="typeName" label="份数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="typeName" label="要求" header-align="center" align="center"></el-table-column>
        <el-table-column prop="typeName" label="备注" header-align="center" align="center"></el-table-column>

      </el-table>

      <!-- 弹窗, 上传文件 -->
      <upload v-if="uploadVisible" ref="upload" @refreshDataList="getDataList"></upload>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import Upload from '@/views/modules/oss/oss-upload'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/letter/bank/findLyYhFile',
        getDataListIsPage: false,
        deleteURL: '/sys/oss',
        deleteIsBatch: true
      },
      dataForm: {},
      configVisible: false,
      uploadVisible: false
    }
  },
  components: {
    Upload
  },
  methods: {
    // 云存储配置
    configHandle () {
      this.configVisible = true
      this.$nextTick(() => {
        this.$refs.config.init()
      })
    },
    // 上传文件
    uploadHandle () {
      this.uploadVisible = true
      this.$nextTick(() => {
        this.$refs.upload.init()
      })
    },
    downloadHandle (id, type, path) {
      // 本地上传
      if (type === 5 || type === 6) {
        var url = `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${id}`
        window.location.href = url
      } else {
        window.location.href = path
      }
    },
    displayHandle (id, type, path) {
      var previewUrl = ''
      if (type === 5 || type === 6) {
        var url = `${window.SITE_CONFIG['apiURL']}/sys/oss/localhostDownload/${id}`
        previewUrl = url + '?fullfilename=' + path
      } else {
        previewUrl = url + '?fullfilename=' + path
      }
      window.open(`${window.SITE_CONFIG['fileView']}` + encodeURIComponent(previewUrl))
    }
  }
}
</script>
