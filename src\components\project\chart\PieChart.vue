<!--
 * @Descripttion:
 * @Author: kongweiqiang
 * @Date: 2020-10-14 17:47:00
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2020-10-23 14:14:48
-->
<template>
  <div :class="'chart'+className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts' // echarts theme
import resize from './mixins/resize'
// import { checkTaskYear } from '@/api/remote-search'
require('echarts/theme/macarons')

export default {
  mixins: [resize],
  props: {
    className: {
      type: String
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    pieChartData: {
      type: Object
    }
  },
  data () {
    return {
      chart: null
      // pieChartData: pieChartData
    }
  },
  watch: {
    pieChartData: {
      deep: true,
      handler (val) {
        this.setOptions(val)
      }
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.initChart()
    })
    window.addEventListener('resize', () => {
      if (this.chart) {
        this.chart.resize()
      }
    })
  },
  beforeDestroy () {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart () {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.pieChartData)
    },
    setOptions (data) {
      console.log(data)
      if (data.seriesData) {
        const arr = []
        data.seriesData.map((a) => {
          arr.push(a.value)
        })
        this.chart.setOption({
          title: {
            text: data.seriesName,
            textStyle: {
              fontSize: 18,
              fontWeight: 300
              // color: '#b6d7ff'
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: `{a} <br/>{b} : {c} ${data.unit}<br/>({d}%)`
          },
          legend: {
            left: 'center',
            bottom: '10',
            data: data.legendData
          },
          series: [
            {
              name: data.seriesName,
              type: 'pie',
              roseType: 'radius',
              radius: [15, 75],
              center: ['50%', '40%'],
              data: data.seriesData,
              animationEasing: 'cubicInOut',
              animationDuration: 2600
            }
          ]
        })
      }
    }
  }
}
</script>
